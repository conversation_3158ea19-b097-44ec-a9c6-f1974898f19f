# 项目进度管理中 BIM 技术的价值及应用

田丽蓉

（西安石油大学经济管理学院，陕西 西安  710065）

摘要：项目进度管理是工程项目的重要组成部分，并与整个工程效益挂钩。随着数字时代的到来和我国建筑业的发展，数字化和信息化已经成为项目进度管理中数字化转型的发展方向，BIM 技术也得到了国家政策层面的支持。项目管理本身的动态性对于整个工程的质量、成本等方面都有影响。有研究显示，随着时代的发展，传统的建筑设计过程已经无法满足人们的需要，BIM 技术则对降低工程延期风险以及为项目进度管理创造效益发挥着重要作用。因此，探讨 BIM 技术在项目进度管理中的优势与方法能够更好地将 BIM 技术与项目建筑实践相融合。本文重点对 BIM 技术在项目进度管理中的价值以及 BIM 技术在项目进度管理中的应用进行阐述。

关键词：项目管理；进度管理；BIM 技术；价值意义；具体应用

# 一、引言

国家统计局2023 年1 月17 日发布信息显示，去年我国全国建筑业总产值达 31 万亿元，同比增长 $6 . 5 \%$ ；建筑业房屋建筑施工面积156 亿平方米，相较去年下降 $0 . 7 \%$ 。BIM 技术在建筑工程中的有效应用可以对建筑施工资源进行优化配置以及模拟施工的整个过程。也有研究显示，BIM 技术的研究及应用在项目管理领域中快速增长。2020 年发布的《关于推动智能建造与建筑工业化协同发展的指导意见》指出，要在建造全过程加大建筑信息模型（BIM）、互联网、物联网、大数据、云计算、移动通信、人工智能、区块链等新技术的集成与创新应用，同年发布的《关于加快新型建筑工业化发展的若干意见》一文提出了大力推广建筑信息模型（BIM）技术。由此可见，BIM 技术在顶层设计和实践方面都已经得到了极大的认可。通常，项目工程进度管理包含对项目进度的计划编制以及项目管理人员对项目进度计划的控制。在项目进度编制中其依据又包括国家对建筑行业的指标和规范、招标文件中的要求、图纸要求以及项目所需资源等，项目进度控制又包括项目进度模拟流程以及BIM 进度的追踪与对比。常用的项目进度管理方法包括横道图、关键路径法、计划评审技术以及关键链法。常用的 BIM 软件包括 Revit 软件、Navisworks 软件。BIM 技术在进度管理中的“先试后建”、降低沟通障碍、减少信息丢失、提供进度信息共享等优势充分体现了其在项目中的价值。

# 二、BIM 技术的基本概述

BIM 是 Building Information Modeling 的 简 称， 指 的 是 建筑信息模型。3D-BIM 是显示施工中详细模型的可视化方法，通过3D-BIM 导航碰撞功能进行优化，4D-BIM 将时间表加入3D-BIM 模型，可以显示施工进度，5D-BIM 则是在3D-BIM 的基础之上集成 4D 之后再加上 5D，即成本的概念形成。在传统项目管理方式下，往往会受到人或者环境等因素的影响导致工期延迟，项目不能及时交付的情况出现。根据国内外研究文献来看，国外 BIM 技术的研究已经相对成熟，而国内则处于初步探索阶段。国外学者研究主要集中在利用 Dynamo、python 等软件对 BIM 进行二次开发，帮助快速建模。

BIM 技术在国内的引入被称为一场技术革命。关于4D模型理论的国内研究，是清华大学特别课题研究小组开始深入研究。国内学者对 BIM 技术的引用经历了三个阶段。分别是理论研究阶段、初步探索阶段以及深入研究阶段。国外对BIM 概念的提出则是在 20 世纪 70 年代，于 2003 年开始投入应用，BIM 应用范围也随着时代的发展和信息化的普及而变得越来越广。美国作为 BIM 技术的先驱者，已在当地政府工程和军用工程中投入使用并获得显著成效。例如，美国总务管理署积极推广引导BIM 作为建筑全生命期的管理技术，退役军人事务部下设的建设与设施管理办公室积极推动退役军人事务部内部和项目承包方的 BIM 转型。我国住建部将 BIM技术看作提高效率、创造收益、降低风险的信息化模型。此外，日本于2009 年正式引入BIM 技术，在闻名已久的清水建设工程中逐步成熟，并发挥了重要作用。德国在2020 年开始，其所有基础设施项目强制实施 BIM 规划和建设。

# 三、项目进度管理中BIM 技术的价值意义

# （一）BIM 技术的关联性

施工建设过程是不断更新的，且各项任务之间是相互关联的，因此在利用 BIM 技术进行施工项目进度管理时，需要项目负责人首先创建工程项目3D 模型，此模型能够根据项目管理人员提供的施工进度方案，对建筑工程施工过程进行模拟分析，以此来达到对项目管理进度的可视化。另外，项目管理人员在利用BIM 技术对施工过程进行模拟时，可以将进度计划与实际进度进行比较分析，不仅能够找到施工进度计划在现实施工过程中实际情况的不足之处，使之做好优化调整，而且能够保证施工进度计划的有效性。其次，传统的项目管理大多依赖于工人及管理人员的经验，而BIM 模型中的各项任务之间是相互关联的，BIM 系统能够对建成的模型信息进行统计分析，并生成直观的图形。各项任务建构在 BIM 技术的系统下，会自动将自身信息从中央数据库加载至所有的平面图、立面图、剖面图、详图、明细表、维护计划等。最后，BIM 系统中的参数建模具有双向联系性和即时性，可以轻松协调所有的图形和非图形数据。在任一视图中对构件的修改都会相应地改变储存在数据库的参数信息，从而构件在其他视图中的模型也会相应地更新。通过BIM 制定项目进度计划，各项任务之间的逻辑关系可以一目了然。

# （二）BIM 技术的可视化

可视化是 BIM 技术最基础的特征。传统的项目管理中通常使用的图纸是基于 CAD 平面图，不仅辨识度较差，而且施工人员需要具备一些相关基础知识才能看懂。BIM 技术的可视化主要指在整个项目管理过程的可视化，包括项目进行投标、招标、企划等过程时的可视化，以及沟通、讨论和决策的可视化。在传统方式的建筑设计管理中，迅速收集项目管理的进度情况对管理人员来说是一件非常困难的事情，而以BIM 技术为依托将工程项目中的相关施工信息导入建筑信息模型，则可以自动生成 BIM 5D 模型，在这个模型中能够直观地反映出工程中的现实情况进展程度与计划施工情况是否产生偏差，如果发生偏差并找出原因，从而为后期施工计划的优化与调整提供指导，实现对施工进度管理工作的及时调整、合理安排和精准优化。例如上海迪士尼乐园项目。上海迪士尼的奇幻童话城堡项目是集会展、娱乐、餐饮为一体的主题建筑，在建设过程中大胆采用了 BIM 技术，用可视的数字模型把设计、建造和运营全过程串联起来，获得了美国建筑师协会的著名奖项。在上海迪士尼乐园项目建设中，项目管理人员不用手拿传统项目建设中的纸质设计图纸入场，而是随身携带一台平板就可进行现场管理，BIM 系统中的三维视图让施工过程中的错项或者漏项能够一目了然，不仅避免了返工浪费的成本和时间，而且能够在第一时间内可以发现错误并及时改正。

# （三）BIM技术的模拟性

项目管理人员可以利用 Revit、Navisworks、Project、BIM-5D 等 BIM 相关软件，在施工前对工程项目进行进度管理，通过这些软件模拟施工进度得到某一时间的工程项目施工实时模型，对每个时间段的施工情况可以直观地显示出来便于进度管理。此外，BIM 模型还能够进行耗能、日照、紧急避难等情况的模拟。例如在伍家岗长江大桥的项目管理建设中采用了 BIM 管理技术，由于桥梁施工项目管理是一个实时变化的动态项目过程，传统的被动管理的模式已经不适用于桥梁施工的项目管理过程。因此管理人员利用BIM 技术建立施工过程各阶段的三维模型，使得施工过程的管理更加精细，项目管理人员利用 BIM 技术将施工进度与三维模型相结合，模拟桥梁的整个施工过程和施工进度，实际进度和计划进度之间的不同可以非常直观地展示出来。其次，桥梁建筑工程是一项危险性比较大的项目，例如在模板吊装过程中可能会对工作人员的人身安全造成威胁，因此项目管理人员选择利用施工模拟动画进行仿真模拟，及时发现设计中存在的不合理现象，保证工人的人身安全。

# （四）BIM 技术的协调性

在传统的项目管理方式中经常出现协调组织能力不足的问题，原因主要是整体项目由众多专业组成，各个项目的任务是由独立的负责人领导分开进行的，因此导致许多项目施工过程中的情况无法使得项目中的各个部门全部知悉。此外，在实际的施工现场中如果发生了现实状况与图纸相矛盾或者背离的状况时，如果管理人员无法及时对图纸进行修改，将可能发生工期推迟的问题。但是通过 BIM 系统则可以使得各个参与方在共同的信息平台上和施工的各个阶段进行沟通，及时了解工程的最新进展以及遇到的问题，进而有效地避免这种问题的发生。例如，长春市新星宇广场项目面临着参与单位众多，且每个参与单位对利润的要求又各不相同的情况，以及单位和部门之间在时间和空间上都可能发生冲突的问题，种种原因导致了这个项目产生了协调困难的问题，对此，项目管理人员采取 BIM 技术来辅助该项工程。新星宇建安公司利用 BIM5D 手机 app 对该项目各部门之间进行横向联动，使得各部门协调分工并进行及时沟通，解决信息传递不及时及传递困难的问题，及时发现项目中存在的问题并解决。

# 四、项目进度管理中 BIM 技术的具体应用

# （一）基于BIM技术的进度计划编制

传统的项目管理中计划编制存在无法及时调整与纠偏的问题。使用BIM 技术对项目工程进行计划编制，可以使项目管理计划更加科学直观。基于 BIM 技术的项目进度计划编制与传统计划编制相比具有所传递的信息更加全面、整体项目的协调性更高以及项目的可视化等优势。计划编制通常包括总进度计划、二级进度计划、周进度计划以及日进度计划等四个方面。首先管理人员要先将工程项目的工作进行层层分解，通过相关软件建立 WBS 分解结构；其次，由于项目中的各项工作是结构是静态的，这时管理人员需要将各项工作之间的逻辑关系整理清晰；再次，管理人员需要对项目工程的活动时间进行预估，在此过程中需要考虑人力的投入以及工作效率高低等因素；最后，构建出基于BIM 技术的施工项目 4D 模型，并利用该模型进行进度模拟与施工进度管理。在此过程中，对计划编制的过程可以进行精简，不影响工程进度的任务可以合并。管理人员在BIM 系统中提取施工阶段的数据，对该阶段资金使用、施工进度以及利用资源等情况进行分析，以此来验证施工进度计划编制的合理性，提高计划编制的质量。

# （二）基于BIM 技术的进度计划模拟

建设施工是一个长期的、动态的、复杂的过程，为了在项目管理中使效益达到最优化，减少不必要的成本浪费，可以通过相关软件实现对项目进度进行模拟。与传统项目管理相比，BIM 技术除了可视化特征之外，还能够对施工进度进行模拟。一般来说，项目进度计划的编制过程主要包括项目工程量的计算、项目WBS 分解、确定工程顺序的时间参数，随之进行进度计划优化工序、逻辑关系优化以及工序时间的优化，最后设置缓冲区。项目管理人员通常通过 Navisworks软件从各个角度和方位对工程进度进行模拟，以便发现项目进度中的问题时可通过软件进行数据调整。例如在长沙市雨花区圭塘街道月塘村的“半岛蓝湾”项目建设中，四期项目由11 栋高层和1 个地下室组成，高层为剪力墙结构，地下室为框架剪力墙结构。由于此项目的负责人不具备项目进度管理的控制意识导致项目进度滞后于工期。随后该项目使用BIM 技术，将此项目的进度计划与合同列表清单输入 BIM 系统，对项目计划进行模拟并发现项目中的主要漏洞和主要矛盾，在 BIM5D 平台中修改实际完成时间，形成实际进度，同时掌握实际工程量完成情况。以此达到了优化工期、节约时间、增强项目管理人员责任意识的效果。

# （三）基于 BIM 技术的进度检查跟踪

BIM技术的进度检查跟踪正是项目管理人员所需要的。通过相关软件提供的可视化工程，可以查看进度计划实施情况。经过 BIM 技术模拟并分析调整之后的计划，以正式文件下发项目各部门作为目标计划执行。通常影响项目进度管理的因素主要包括人为、环境、资源、技术以及风险状况等。传统的项目管理方式是管理人员是将设计好的工程计划下派到各部门的施工队当中，各个部门的负责人按照进度计划在施工现场进行监督管理，并且记录下每天的施工队的作业状况，在施工过程进展到一定程度之后，项目负责人如果发现项目施工进度略微滞后，没有达到理想的效果时就会采取会议纠偏的方式进行进度跟踪。然而这种传统的管理方法在实际过程操作中具有明显的滞后性，对此，项目管理人员可以利用BIM5D系统中的进度跟踪技术，可以将生产进度细化到相关责任人。通过手机端将任务推送至责任人手机端，从而避免造成责任人对进度要求不清楚。责任人可根据手机端推送的任务内容，利用 3D 模型提取出该任务的相关工程量，安排相应的人员组织生产。项目管理负责人可利用 BIM5D 的手机APP的劳动力统计功能，对每日的现场作业人员的工种及人数进行统计反馈，网页端可对每日手机端反馈的现场作业人员进行手机整理，为后续进度分析及调整提供准确数据。

# 五、结语

由于经济的飞速发展与人们对建筑物功能需求的增加，建筑物的体型和外观发生了巨大的变化的同时，项目管理的难度也越来越大，项目管理人员需要考虑的影响因素越来越多。从手持稿纸到二维、三维的信息改革，从 2D、3D 的传统建模到 BIM-4D、BIM-5D 的信息化建模方式，在这个过程中实现了资源共享、信息同步、相互协调的参与方式。从传统的施工过程无法进行可视化模拟与检查到BIM 技术的出现，其不仅为项目工程进度管理的转变提供了信息化方式，更提供了信息的共享、集成与可视，极大地提高了项目经理的管理效率。

# 参考文献：

[1]	 吴佩玲，董锦坤，杨晓林 .BIM 技术在国内外发展现状综述[J].辽宁工业大学学报（自然科学版），2023，43（01）：37-41.  
[2]	 彭慧纯，沈嵘枫 .BIM 技术在我国工程项目进度管理中的进展 [J]. 安徽建筑，2021，28（09）：160-162.  
[3]	 林森.BIM技术在项目进度管理中的应用研究[J].居舍，2022（01）：130-132.  
[4]	 张龙宝.BIM技术在智慧工地项目进度管理中的应用研究 [D]. 太原：太原理工大学，2022.