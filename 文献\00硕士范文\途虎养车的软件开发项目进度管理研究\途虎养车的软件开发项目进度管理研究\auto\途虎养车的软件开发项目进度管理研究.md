学校代码：10255  
学 号：8190147  
中图法分类号：C93

# !"#\$%&'()\*+

# -./012

Research on progress management of software development project of Tuhu

学科专业： 工程管理  
姓 名： 吴家欢  
指导教师： 王素芬  
答辩日期： 2022 年 10 月 27 日

东华大学工程管理

# 学位论文答辩委员会成员名单

<table><tr><td>姓名</td><td>职称</td><td>职务</td><td>工作单位</td><td>备注</td></tr><tr><td>田澎</td><td>教授</td><td>答辩委员会主席</td><td>上海交通大学</td><td></td></tr><tr><td>王爱兵</td><td>高级工程师</td><td>答辩委员会委员</td><td>东方国际（集团）有</td><td></td></tr><tr><td>吴炯</td><td>教授</td><td>答辩委员会委员</td><td>东华大学</td><td></td></tr><tr><td>丁伟</td><td>讲师</td><td>答辩委员会秘书</td><td>东华大学</td><td></td></tr></table>

# !"#\$\$%&'()\*+,

本人郑重声明：我恪守学术道德，崇尚严谨学风。所呈交的学位论文，是本人在导师的指导下，独立进行研究工作所取得的成果。除文中已明确注明和引用的内容外，本论文不包含任何其他个人或集体已经发表或撰写过的作品及成果的内容。论文为本人亲自撰写，我对所写的内容负责，并完全意识到本声明的法律结果由本人承担。

学位论文作者签名：日期： 2022 年 10 月 27 日

# !"#\$\$%&'-./01.2

学位论文作者完全了解学校有关保留、使用学位论文的规定，同意学校保留并向国家有关部门或机构送交论文的复印件和电子版，允许论文被查阅或借阅。本人授权东华大学可以将本学位论文的全部或部分内容编入有关数据库进行检索，可以采用影印、缩印或扫描等复制手段保存和汇编本学位论文。

保密 □，在 年解密后适用本版权书。

本学位论文属于不保密 $\surd$ 。

学位论文作者签名：

指导教师签名：

日期：2022 年 10 月 27 日日期：2022 年 10 月 27 日

# !"#\$%&'()\*+,-./01

# 34

2021年上海市发布了《关于全面推进上海城市数字化转型的意见》这一项重要文件,这个文件标志了上海城市下一阶段发展的方向。在行业全面数字化的大背景下，国内专业的汽车养护平台之一的途虎养车积极拥抱数字化,成为汽车后市场上领域中“数字化”的先行者，为诸多传统企业的数字化转型提供了“样板”。这类企业的软件项目通常安排的任务时间比较紧，非常注重时效性，更早的上线有利于提升企业在行业内的竞争力。标准的软件项目开发流程往往会遇到以下几个问题:开发周期长，难以快速上线;需求变更难度大，难以适应当前快速变化的市场;进度估计不准，难以保障按时上线。因此，标准软件项目开发流程在注重时效性的传统企业数字化转型项目的进度管理和控制中往往存在许多挑战。本文围绕途虎养车的门店管理系统这一软件项目的开发进度流程，就影响项目进度的因素和风险点展开分析，本研究成果有助于小微企业的项目实践参考。

本文首先分析了途虎养车软件项目实施所存在的问题。通过分析途虎养车的公司概况、组织架构和项目团队运作状况，提取了门店管理系统项目与传统软件项目的不同之处。通过对目前项目进度管理、风险管理实施开展情况进行研究，提出了目前项目中延期风险、交付质量不合格的问题。其次，优化了途虎养车软件项目的开发流程。依据项目需求的实际目标，利用WBS的方法来分解项目工序，编制项目计划表，找出工序间的紧前和紧后逻辑关系，参考专家判断和经验估算确定工期，制作各任务理想时间表。在此基础上，利用关键链法来排除人工造成的安全时间，并依据作业与作业的逻辑关联，制定出最优的资源分配方案，确定适合目前项目的关键路径。针对企业的资源限制问题，引入了关键链概念，引入各种缓冲区，以解决在实施中存在的各种风险和不确定性因素。在建设和开发中，各个链路的特征和项目的不确定性，采用熵值法和TOPSIS相结合，对各个计划任务的不确定性进行计算，并采用根差公式测算各链路的缓冲时间。根据BoeHm 的软件风险管理模型，针对项目风险因素的定量研究，计算出项目的风险值。最后，评价及分析了项目实施结果。选用蒙特卡洛模拟方法定量分析了关键链法的实施效果，真实地模拟项目的实施过程，根据项目实际消耗缓冲区的情况得出结论；给出一些有效的解决进度管理和风险管理的方式。提供这一切的保障措施是项目进度管理的最行之有效的方法。这些方法弥补了传统的项目进度管理方法的不足。

本论文试图从项目进度管理角度出发，对关键链理论在软件项目管理进行了较为全面的研究。数字化转型的关键性在于软件工程，由于其大部分工作依赖于开发人员的脑力活动，而由于其“学生综合征”和过量的安全时间等原因，很难在有限的时间内实现低成本高质量的产品。首先对国内和国外的关键链技术、微服务体系进行了研究，并对途虎养车在软件开发过程中存在的问题和现状进行了分析，并根据关键链技术计算缓冲区，达到了项目如期交付的目标。在软件项目的进度管理角度，，阐述了该技术的适用性以及必要性，为我国的网络产业和传统产业的转型发展提供了很好的借鉴价值以及参考，有助于推动相关互联网行业和传统行业的数字化转型。

# !"# 垂直电商 关键链法 微服务

# Research on progress management of software development project of Tuhu

# ABSTRACT

In 2021, Shanghai issued an important document "Opinions on Comprehensively Promoting the Digital Transformation of Shanghai's City", which marked the direction of the next stage of Shanghai's urban development. Under the background of comprehensive digitization of the industry, Tuhu Yangche, one of the domestic professional car maintenance platforms, actively embraces digitization and becomes the pioneer of "digitalization" in the field of the automotive aftermarket, providing services for the digital transformation of many traditional enterprises. "template". In the context of data transformation, the digital transformation of traditional industries relies on the establishment of an effective information platform. Through long-term Internet operation experience, in order to better build this automotive aftermarket e-commerce platform, it must rely on an effective operating system, such as a series of software such as official website, APP, data background, etc., which is different from In the development process of traditional industries, such vertical e-commerce platforms often catch up with each other in the market competition and focus on the timeliness of product development, so the development process of traditional industries is generally not applicable to such enterprises. This paper focuses on the development progress process of the software project of Tuhu Yangche's store management system, and analyzes the factors and risk points that affect the project progress. The research results are helpful for small and micro enterprises' project practice reference.

This paper firstly analyzes the problems existing in the implementation of the Tuhu car maintenance software project. By analyzing the company profile, organizational structure and project team operation status of Tuhu Auto, the differences between the store management system project and the traditional software project are extracted. Through the research on the implementation of the current project progress management and risk management, the problems of delay risk and unqualified delivery quality in the current project are put forward. Secondly, the development process of the Tuhu car maintenance software project was optimized. According to the actual objectives of the project requirements, the WBS method is used to decompose the project procedures, prepare the project schedule, find out the logical relationship between the immediate predecessor and the immediate successor, and determine the construction period with reference to expert judgment and experience estimation, and make the ideal timetable for each task. On this basis, the critical chain method is used to eliminate the safety time caused by manual labor, and according to the logical association between operations and operations, the optimal resource allocation plan is formulated, and the critical path suitable for the current project is determined. Aiming at the resource limitation of enterprises, the concept of critical chain is introduced, and various buffers are introduced to solve various risks and uncertain factors in the implementation. During construction and development, the characteristics of each link and the uncertainty of the project are combined with the entropy method and TOPSIS to calculate the uncertainty of each planned task, and the root difference formula is used to measure the buffer time of each link. According to the software risk management model of BoeHm, the risk value of the project is calculated according to the quantitative research of project risk factors. Finally, the results of project implementation are evaluated and analyzed. The Monte Carlo simulation method is used to quantitatively analyze the implementation effect of the critical chain method, and the implementation process of the project is simulated realistically, and conclusions are drawn according to the actual buffer consumption of the project; some effective solutions to schedule management and risk management are given. Providing all these safeguards is the most effective method of project schedule management. These methods make up for the deficiencies of traditional project schedule management methods.

This dissertation attempts to conduct a more comprehensive research on the critical chain theory in software project management from the perspective of project schedule management. The crux of digital transformation lies in software engineering, as most of its work relies on the mental activity of developers, and due to its "student syndrome" and excessive security time, etc., it is difficult to achieve low cost and high quality in a limited time. The product. Firstly, the domestic and foreign key chain technology and microservice system are researched, and the problems and status quo in the software development process of Tuhu Yangche are analyzed, and the buffer zone is calculated according to the key chain technology, so that the project can be delivered on schedule. The goal. From the perspective of progress management of software projects, this paper expounds the applicability and necessity of this technology, provides a good reference value and reference for the transformation and development of my country's network industry and traditional industries, and helps promote related Internet industries and traditional industries. Digital transformation of the industry.

KEY WORDS Vertical E-commerce, Critical Chain Method, Microservices

# +5

第 1 章 绪论 ...  
1.1 研究背景.  
1.2 研究目的和意义. 2  
1.3 研究方法.. 2  
1.4 国内外研究现状.. 4  
1.4.1 国内外研究现状.. 4  
1.4.2 国内外文献综述.. 5  
1.5 研究内容... 6  
1.5 论文框架.. 7  
第 2 章 相关理论及文献回顾 .. 9  
2.1 软件项目全生命周期介绍.. 9  
2.1.1 软件项目全生命周期概述. 9  
2.1.2 敏捷开发的主流模型.  
2.1.3 敏捷开发的一般步骤.. 12  
2.2 项目进度管理.. 14  
2.2.1 项目进度管理的内容构成. 14  
2.2.2 项目进度管理的作用. 15  
2.3 基于关键链的进度管理. 15  
2.3.1 关键链技术概述.. 16  
2.3.2 项目进度不确定性指标体系构建... 17  
2.3.3 基于熵权法和 TOPSIS 法的不确定性系数计算. 18  
2.4 进度管理方法有效性分析方法概述.. 21  
2.4.1 进度管理计划方法.. 21  
2.4.2 基于蒙特卡洛模拟的有效性分析. 21  
2.4.3 基于风险理论的有效性分析.. 22  
第3章 途虎养车软件项目进度管理的现状分析 23  
3.1 途虎养车简介... 23  
3.1.1 垂直电商行业概述... 23  
3.1.2 汽车后市场电商平台项目简介.. 23  
3.2 途虎养车软件研发团队及研发流程.. .24  
3.2.1 软件研发团队组织架构.. 24  
3.2.2 软件项目进度管理体系.. .25  
3.3 途虎养车软件开发项目进度管理现状.. .26  
3.3.1 基于 CPM/PERT 的进度计划的问题. .27  
3.3.2进度计划制定的问题 . .28  
3.3.3 进度控制的问题 .. .29  
3.3.4 途虎养车软件项目进度管理的不确定性因素. .30  
3.4 本章小结.. 31  
第 4 章 途虎养车软件进度计划制定过程优化 32  
4.1 软件项目任务分配解决方案.. .32  
4.1.1 软件项目工作分解结构... .34  
4.1.2 基于微服务的人员工作分配.. .36  
4.1.3 基于微服务的活动历时估算.. .38  
4.1.4 基于微服务的软件项目改善计划.. .38  
4.2 基于关键链法的软件项目分析... .39  
4.2.1 软件项目关键路径确认.. .39  
4.2.2 软件项目的缓冲区设置.. .41  
4.3 软件项目进度管理控制风险因素分析. .51  
4.3.1 风险因素的优先级划分.. .51  
4.3.2 风险因素的量化结果.. ..51  
4.3.3 风险因素的量化分析.. .53  
4.4 本章小结.. .54  
第5章 软件项目进度控制实施及评价 .56  
5.1 软件项目进度控制实施过程... .56  
5.1.1 软件项目实施前的进度控制... .56  
5.1.2 软件项目实施中的进度控制... .58  
5.2 软件项目进度管理评价.. .60  
5.2.1 蒙特卡洛模拟工期.. .60  
5.2.2 缓冲区消耗情况... .62  
5.3 项目进度管理的保障措施. .64  
5.3.1 定期组织项目管理培训. .64  
5.3.2 规范项目质量管理.. .64  
5.3.3 进度管理改进优化建议. .65  
5.4 本章小结.. .66  
第6章 结论与展望 .67  
6.1 主要结论... ..67  
6.2 未来展望... .68

# 第 1 章 绪论

# 1.1 研究背景

垂直电商是指针对某一领域的细分用户开展经营的电商平台模式。垂直电商的企业所提供的产品都是相同类别的产品。这类平台一般为同类商品提供的服务主要有商家对个人或者商家对商家的业务两种服务方式，其服务的人群也是针对这类产品的。

这类垂直电商平台在同质化的影响下，SKU已经不是唯一标准，要想打动费者，主要还是靠平台的特色，是否与其他平台有差异。流量是互联网企业最关注的问题，但是对于垂直电商平台来说，与其花费巨资投放广告不如多花点心思培养忠诚用户，通过一系列的福利措施提升用户粘度。

途虎养车是一家汽车保养O2O服务网站，在汽车后服务市场的垂直行业发展近十年，目前已经发展为国内最大的一个汽车后服务市场 O2O 平台。2014 年开始国内的互联网企业飞速发展，人们对网络消费的接受程度不断升高，网络交易的总量不断增加，经过分析研究获知，这一增量大多来源于垂直电商领域。汽车后服务市场的垂直电商模式在平台经济下具备一些典型的特征，而垂直电商行业中汽车后服务市场的大数据系统也开始建设起来，逐渐成熟。该公司主要分为B2C 和B2B两大部分业务，B2C业务主要旨在为国内庞大的汽车用户群体提供标准化的价格透明的养车服务；而B2B业务则是更好的服务于各类汽修门店，提供更好的产品和零配件。在这样的业务背景下电商平台业务运行系统需要提供以下服务：

（1）打破目前国内4s店维修贵的现状，解决普通汽修店维修水平良莠不齐的问题。该企业借助电商平台加线下门店合作的方式整合线上以及线下得所有资源，不仅实现了线下设备的最大化利用，而且还拓宽了线上的销售渠道。（2）推动电商平台上的购物场景以及线下维修检测的流程获取各种有价值的数据，并应用数据进行更有效的产品服务推荐。（3）帮助一些路边汽修店建立持续而稳定的线上业务流量，建立线下标准化的检修业务，进而对汽车后市场进行一定程度的整合。

（4）以电子商务为载体，树立汽车后市场的企业品牌和性向，提升企业和市场核心竞争力。随着互联网技术的发展，电子商务的普及，以及物流、订单、培训、检修等一系列系统建设，从而更好的为用户服务。

为了更好的搭建这个汽车后市场电商平台，必须借助于一套有效的运营系统，如官方网站、APP、数据后台等一系列的软件，而不同于传统行业的开发流程，这类垂直电商平台往往在市场竞争中你追我赶，非常注重于开发产品的时效性，所以一般传统行业的开发流程对于这类企业并不适用。

# 1.2 研究目的和意义

在实际的软件项目开发过程中由于开发人员能力和经验参差不齐，任务分配对项目管理者来说成为了一个难点，项目拆分后各任务模块不够独立，项目经常延期等问题。因此本文针对上述问题展开途虎养车软件开发流程优化的研究。

本研究的理论意义：在当下电子商务垂直电商领域的大力发展下，催生了一大批垂直电商平台的小微企业，通过以途虎养车电商平台软件项目开发的过程研究，结合进度管理相关的理论，有助于丰富小微企业软件开发流程优化的研究成果。

从实践意义上来说，途虎养车作为垂直电商领域的一家比较代表性的企业，目前已经成为行业中的领头羊，其开发流程中积淀的经验对于其他小微企业来说是有参考价值的。通过更系统的研究分析，可以更好的帮助小微企业有效的开展项目开发。

# 1.3 研究方法

# （1）文献研究法

通过使用网络搜索引擎、学校图书馆提供的电子文献资源的方式，大量阅读各种与项目管理中的进度管理相关的文献，学习国内外专家学者的先进经验，学习了解了进度管理在各个领域的使用和研究，对进度管理及其应用有了一定的理论基础。利用已有文献资料进行归纳总结，学习了相关的知识，丰富了专业领域的应用经验。以下是基本的研究路线：制定大纲、查找和鉴别相关的资料、仔细查阅相关资料，边阅读边摘录、按照大纲对所摘录的资料分项分点加以整理和归纳总结研究材料，形成报告等一系列的步骤。

（2）调查研究法

通过对公司内部开发项目的数据资料进行整理分析，归纳总结在公司软件开发项目运行过程中的使用的进度管理的方法。通过对相关人员的调查访问，对项目管理中使用的进度管理的方法进行分类汇总，根据不同类型的进度管理方法，归纳出各方法的优缺点。

通过参与调查法来调查对象正常活动情况，参与观察是一种直接参与被调查对象的活动中的研究方法，从正面或侧面了解被调查者的活动。采用这种方法了解的情况比较具体、真实，便于了解关键问题。现场观察需要明确的目的性，要有意识地区观察问题。调查者应具备高度的敏感性，做到处处有心，见微知著，善于把各种现象进行有机联系，在现象中发现和抓住问题。

# （3）案例验证法

案例研究法也叫个例研究法，其研究步骤可以拆分成以下几个步骤。首先，案例研究的目标一般与研究范围以及调查要解决的课题密切相关，这就决定了什么数据能为案例研究提供最有价值的信息。其次，获取数据资料，案例研究的参考资料一般包含纸质文件资料、归档记录、一对一访谈记录、直接观察记录、参与观察记录等。再次，对获得的参考资料进行数据分析，在进行分析数据资料以前，必须明确研究对象的分析方法，也就是先要主导资料调查了什么以及为什么要调查的优先级。最终的研究结果报告应当包含相对独立的几个部分：背景说明、针对问题和结果的说明和解释、分析过程和得出结论、对于结论的总结和提出的建议。

根据进度管理的理论基础以及项目实践中的调查研究，结合途虎养车开发项目的实际情况，应用相关理论进行任务分解和进度管理，验证进度管理相关方法在软件开发项目中的可行性。

# 1.4 国内外研究现状

# 1.4.1 国内外研究现状

项目管理的理论研究在国内刚刚起步，经过三十多年的发展，项目管理在工程项目和IT 项目中的运用愈加规范和广泛的，一些大型项目的实践也逐渐与国际接轨，并积累了丰富的理论和实践经验，促进了我国项目管理工作的专业化、国际化发展。

项目管理是指能够保证项目按时交付所需要经过的流程，它的核心是把控整个项目的流程，控制好项目执行过程中产生的成本，最大化的利用资源服务于项目管理，从而促进特定目标的实现。

项目进度管理是指能够有效把控项目执行过程中的里程碑事件以及重要的活动，并且最大化利用资源，以及确定项目可以在规定时间交付。通过项目管理，可帮助项目的所有参与人员了解在项目的不同结点自身所承担的责任，明确自身的工作内容。因为归属于同一项目，所以很多活动之间会存在相互依赖关系，因此可以依据需求进行不同的分析，比如，分析现有资源的利用率、探索风险分析的关键路径，探究项目延期的原因等等。

对于关键链进行相关研究的有胡维涛、邓雅尹、马成等人，具体研究的内容归纳为：

《改进关键链挣值技术在软件工程及集成项目进度控制中运用探讨》一问中，胡维涛、邓雅尹、马成认为挣值法是有缺陷的，它的偏差指标并不能反映出项目的进度和费用是否能够得到有效的控制，一但发生重大问题，这种方法就失去了作用。

许文雯 2019 年在《基于 WBS 的工程项目进度管理研究》提出，要建立一个全面的WBS 技术工作包和编码字典，使WBS工作覆盖了整个项目的所有工作，不论工作的规模，这种工作包能够确保项目的正常实施，从而建立一个企业的管理规范。

龙海 2016 年在《SASS系统在国内成功实施的关键因素分析》中指出，大型企业 ERP 系统往往会使用关键成功因素法（KSF）。KSF（Key success

factors）的理念是由哈佛大学 William Zani 博士提出，并在 Holland &Ligh的ERP系统中得到广泛的应用。

祁艳丽、李瑛、王荣贵等学者基于风险控制方向进行了一些理论和实践研究，研究内容有：

祁艳丽 2019 年在《民办高校工程项目管理中的风险控制研究》中，提出实施项目风险必须建立在企业的自身利益基础上，不同的公司存在着一定的偏差，只有充分理解其需要，才能保证项目的工作量和评估工作时间。

李瑛 2019 年在《IT 项目管理中的风险认识》中提到了进度、成本和技术，在项目管理中，应重视风险的处理和对策，以防止项目延误。

王荣贵 2018 年在《如何实行有效的软件项目管理》一文中，他认为在关键链法中，的监控缓冲区是不全面的，对于两个同样消耗了 $3 0 \%$ 的缓冲时间的任务，如果一个完成了 $8 0 \%$ ，而另一个只完成了 $2 0 \%$ ，那么这两个任务的风险程度完全不一样，除此之外，对于缓冲时间的设定的合理性也会影响到项目的风险，从而带来项目的延期等问题。

瞿英、范默苒、刘滨、曹树贵在 2020 年 3 月发表的《基于文本分析的软件项目风险研究演化脉络解析》中，运用互联网技术，收集了近 15 年来国外 1487 份与项目进度规划相关的论文。当前，在软件工程中，时间管理和进度控制是两大热点，但由于时间管理主体的制约因素对进度的影响较小，相应的研究课题还在继续研究。

# 1.4.2 国内外文献综述

结合国内外研究现状，本文根据国际上对于大型软件项目的调研情况，提出了三种在世界上应用最为广泛的工程项目的进度规划与管理方法。

第一个是按照工业准则指导，例如美国电气与电子工程师协会制定的软件项目管理计划标准、卡内基梅隆大学软件工程研究所研发的 CMM/PSP/TSP 软件项目管理改进系统。这两种标准分别规定了项目的进度和时间的控制。

第二个采用诸如美国公司最好的软件项目规划管理软件 PrimaveraProject Planner、AllFusion Process Management Suite 等。

第三个则是项目经理根据以往项目的经理，编制项目计划、实施项目过程的控制。

不难看出，管理者关心的是，在大规模的软件工程实施过程中，大多数都是以常规的程序和高级的软件体系为对象，而在项目的进度规划与控制上，则侧重于特定的环节，对项目的进度计划和口供之的整体研究缺少全面的调查研究。例如 Chatzoglou PD，Macaulay LA 在《Software Engineering APractitioner’S Approach》一书中就如何编写软件工程的进度规划，给出了甘特图、PERT 和 CPM 三种方法。在 CPM 法的基础上，又提出了挣值法、关键链法和类比法。S.Pressman 在其著作《Software Engineering APractitioner’S Approach》中，对软件工程的进度安排进行了细致的分析，并提出了一种类比法。此外，美国的威格斯教授，其工作重点放在了项目执行中的各个环节。阳王东认为，在软件实现中的特点是可以同时进行多个工作。目前基于软件项目进度计划和项目进度控制缺乏更全面、更深入的研究，本文从途虎养车公司实施的 SASS 项目为研究基础，系统的研究和分析软件项目进度计划与项目进度控制的实施过程和管理方法。

# 1.5 研究内容

途虎养车作为目前汽车后市场垂直平台的领军企业，从一家10人不到的公司经过不到10年的时间成长为一家拥有近5000人的大企业。在业务开发和功能上线的过程当中积累了许多丰富的经验，同时其开发流程中的一些优点和不足也可以给一些相关企业所借鉴。针对该企业的项目管理发展进程，主要面临了开发流程进度难以控制，经常出现员工加班甚至项目延期的风险。具体研究内容主要分为以下几个：

# 一、任务分解及优化

在市场需求背景下，由产品经理整理并设计出了对应的开发需求，根据不同的企业的人员配备，具体软件开发人员的技术能力及项目经验水平的不同往往会影响到项目实施的整体进度。这里通过WBS的方法，将项目任务拆分成一个个较小的可交付的子任务，并落实到具体的责任人。同时也借鉴了微服务架构的思想，将一个个独立的子任务通过微服务的方式提供到整个项目中。通过这种方式可以更好的将子任务分配到对应的开发人员，并且有效的将各个模块高质量的产出，更好的开展项目工作。

# 二、关键缓冲区分析与设计

在项目运行的过程中难免会遇到一些不可控因素导致项目的延期。由此设置合理的缓冲区能够有效的保障项目的按时交付。通过对项目的最长路径进行分析，找出路径中的关键步骤，这依赖于项目分解任务的耗时、逻辑关系、滞后量等因素进行推导，从而保证项目分解的任务可以在该时段内有效实施。设置和计算缓冲区，首先根据链路特征和项目特征，利用熵值法和TOPSIS法对各个环节进行不确定性系数的确认。再根据根差法求出各链路的缓冲期，并将它们分别添加至各链路末端。缓冲区包括项目缓冲区、接驳缓冲区和资源缓冲区三大类。在项目的末端，项目缓冲是为了减少或削弱主要的链路的延期风险。接驳缓冲是处于关键链和非关键链的交接点，解决了非关键链上的风险因素对于整体项目的延期风险。资源缓冲不会花费项目时间，在任何时候都要保证关键链上的资源要求。在项目中添加了上述三种缓冲区，减少了风险因素对于整体项目工期的影响，保证了项目的顺利进行。

# 三、进度控制流程设计

在实际的项目开发过程当中，开发人员经常会出现前期对项目评估部准确，在前期浪费了一些开发资源，而到项目后期发现时间不够，导致加班严重甚至项目延期。所以在项目管理的过程中对于进度控制是非常重要的。进度控制的常态一般分为计划、执行、审查、行动。如果发现项目执行过程中的某些行为可能会对后期项目的执行产生影响，应当及时的调整方案，尽可能降低项目延期的风险。这可以通过进度编制计划中总时差和自由时差来进行正确的判断和规划。

# 1.5 论文框架

本文共分六章，论文框架如下：

第一章详细的阐述了本论文的研究背景、研究目的以及研究意义，并通过分析国内外现状，总结了关于大型软件项目的进度计划和控制办法。

第二章是相关理论及文献回顾，首先介绍了软件项目全生命周期理论、敏捷开发基础理论，并对软件进度管理的内容构成及作用进行介绍，基于关键链技术的学习，介绍柔性关键缓冲区设置方法。

第三章是对途虎养车软件项目的现状分析，从软件项目的任务拆解问题、微架构的任务分配问题、关键路径下的缓冲区设置问题，依次对问题进行详细描述。

第四章涉及到任务分解的优化，通过WBS方法来进行任务工作结构梳理，通过微服务的概念，将子任务拆分成一个个独立可交付的工作。根据子任务的耗时估算建立软件项目进度计划安排。

在项目缓冲区的分析及设置的过程中，通过关键路径的方法进行分析，对相关任务进行一定时间的缓冲预留以保证项目的按时交付，也解决了人力资源的利用问题。

在进度控制相关的优化方面，主要对途虎养车软件项目的工序时间进行估计，基于关键链技术制定项目编制计划。最后进行进度控制效益进行分析总结。

第五章主要通过改进前后的一些数据对比，从任务分解绩效、缓冲区利用情况、进度控制改善、实施情况进行整体评价。

第六章是对整个论文研究过程的一个回顾和总结，同时对一些下一步的研究确定了一些方向。

# 第 2 章 相关理论及文献回顾

# 2.1 软件项目全生命周期介绍

软件项目管理概念最初来源于 20 世纪 70 年代，随着欧美资本主义自有企业市场规模的迅速扩大，而各个公司自身信息化发展已不能适应越来越丰富的企业管理要求，加速推动公司信息化发展成了当前亟待解决的主要矛盾。随着计算机技术的使用规模的扩大逐步拓展至金融、商业、医学、通讯等领域，人类的工作对于计算机技术的依赖程度越来越高。

随着社会的不断发展，人们对于计算机的需求日益增加。人们也逐渐发觉，原来的计算机软件开发方法已不能适应社会日益增长的需求，工程费用超标、工程效率低下等许多棘手的技术难题严重限制着计算机软件开发的进步，也限制着社会经济发展。

为了处理好这些问题，当时的计算机行业已经将计算机软件项目作为系统性的项目管理加以实施，并把现代的企业管理技术与计算机信息技术相结合，以大大降低企业经营风险。

近年来，经过不断学习国外前沿理论知识和多元化实际项目经验，为我国在信息系统项目管理的实施和研究领域取得了不错的成果，并在管理信息系统软件项目的实践中逐渐完成了理论沉淀、具有中国特色的中小企业的管理实战经验。途虎养车是一家全面服务广大车主的汽车后服务市场的企业，注重于公司信息化建设，特别是在系统软件系统开发中，每年的都保持着较高增长投入。虽然管理系统信息化、互联网化已是公司顺利运营发展的基本保障，但缺乏信息化的现代化软件解决产能落后、员工队伍素质低下等各种问题，成为当下中国传统公司的数字化转型待以解决的核心问题。

# 2.1.1 软件项目全生命周期概述

# （一）建设规划阶段

在全生命周期的理论基础下进行传统企业向数字化平台转型，首先必须要对企业发展作出合理规划，以明确不同的发展阶段战略和企业的要求是否目标一致，对提高企业项目管理数字化程度和数字湖平台建成后的应用效益意义很大。而在建设规划阶段中，企业首先必须提高企业项目管理数字化平台意识，以确定信息化平台建设最终目标，如为提升企业员工效率和运营管理水平，以及保持企业在市场中的竞争力水平等，必须把企业管理工作中所发生的问题和数字化平台建设目的有机地融合在一起，对企业人员管理工作中所反映的问题进行集中剖析，并针对性设计信息管理软件，以更科学合理的方式来建设信息化平台。需要重点关注的是，企业信息平台建设需要有关人员不断奋斗，相关管理人员在设置信息化平台建设时必须采取分阶段根据优先级进行建设规划，首先明确公司发展中存在的主要矛盾，优先解决公司发展所存在的关键管理问题，以现实需要为大前提阶段性地制定企业发展战略目标。

# （二）建设实施阶段

实施阶段是中国中小企业管理信息化平台建设的核心阶段。将以公司的全生命周期为基准搭建信息平台的基础架构，在建设实施阶段不但需要考虑企业综合能力进行全面的评估，还要选择科学有效的技术方式方法，还需要健全组织架构，完善企业管理体系。

在数字化平台建设实施阶段，企业各级管理人员首先必须对其经营能力和管理人员水平有明确的了解，确定公司数字化建设能够提供给企业的可观的收益，再进行具体的实施操作。若企业目前处在规模较小、盈利能力不高、或经营资金不足的经营状态，盲目投入数字化转型将使公司的经营受阻，对企业的长久发展构成威胁。

其次还必须正确选用信息化科学技术，在提升企业日常工作管理和运营管理效率的同时对企业风险实施控制。绝大多数企业在开展信息化系统工程建设时，通常会考虑将企业的信息系统产品通过外包的方式交给专业的信息技术公司来完成，通过引入国外企业的优秀相关技术和管理软件进行企业项目管理，以提升信息化系统的实际效果，并保证管理软件的针对性和适用性。将信息化工程外包给第三方企业虽然可以大大降低信息化建设工程中的各种困难，但同时也会给企业的信息安全增加一定的危险，因此要求相关管理人员在引入第三方提供的信息化技术后增加企业信息安全意识，并委托专业的技术机构对其软件的安全性完成安全性风险测试评估，由企业自建信息化工程建设可以更有效地防止企业关键信息出现安全泄漏的问题，从而加强企业网络安全管理。

最后，为了推动企业管理信息化现代化的进程，公司高级管理人员也要简历科学有效的管理体系，确保有关员工在信息化平台建设中的权力与义务，完善企业信息化平台的使用方法说明，经常为公司信息化系统的基础设施做好运营维护工作。公司必须按照信息化改造计划制订并严格执行每个时期的战略计划，提高公司高级管理人员的信息化水平学习，把原有的管理知识水平和信息化的相关技术有机结合，做到对信息化业务操作流程的熟练掌握，提高项目品质与效益。另外还应注重信息化管理体系的简历，做好公司人员的日常培养计划，保证员工的能力适应公司信息化的要求，同时要求公司发展阶段不断完善管理体系，增强公司管理体系与经营情况的匹配，提升公司管理水平的信息化程度。

# 2.1.2 敏捷开发的主流模型

敏捷思想在国内的经典应用方法有Scrum和精益这两种，XP和特性驱动开发也是两种比较常见的基于敏捷思想的敏捷开发方法。每个方法都是基于不同的现实问题所产生的，拥有着不同的特征。国内外企业所使用最多的方法中，极限编程 XP 被广泛使用，其反馈速度非常快，常被用于测试驱动开发、结对编程等模式。Chandramouli 的研究指出 XP 方法存在片面性，对普适性不强，这是它存在的问题。Scrum在国外最早是用在描述橄榄球队员的团队合作精神，这是一种团队协作、共同进退的策略，由此引申到项目管理中。如表 2-1所示。

表 2-1 主要敏捷方法之间的区别  

<table><tr><td></td><td>Scrum</td><td>极限编程XP</td><td>精益 (Lean)</td></tr><tr><td>中心思想</td><td>自组织管理</td><td>早起发现错误以及 降低复杂度</td><td>消除浪费</td></tr><tr><td>研究对象</td><td>人或Team</td><td>实践方法</td><td>过程</td></tr></table>

<table><tr><td>项目时间</td><td>两到四周</td><td>一到两周</td><td>尽快交付</td></tr><tr><td>交付质量</td><td>客户的参与和沟通</td><td>工程实践保障项目 质量</td><td>过程中要求高质量</td></tr><tr><td>需求优先级</td><td>可调整</td><td>严格遵守</td><td>根据需求决定</td></tr><tr><td>需求调整</td><td>当前迭代不调整，或 开启新的迭代</td><td>允许替换需求</td><td>尽量延迟替换需求</td></tr><tr><td>工程实践方法</td><td>没有固定的方法</td><td>结对编程、TDD</td><td>尽量保证质量</td></tr><tr><td></td><td></td><td></td><td></td></tr><tr><td>学习成本</td><td>容易入门，难以掌握对于人员素质要求</td><td>高</td><td></td></tr><tr><td></td><td></td><td></td><td>难以实践</td></tr></table>

# 2.1.3 敏捷开发的一般步骤

从整体产品开发的初期，到整体的战略和产品的增量，都需要 Scrum 团队的所有人来制定和决策。对繁重的工作进行合理的甄别，产品负责人根据项目任务列表中的处理顺序优先级进行合理的排序,然后由项目研发人员来完成产品的细化以及评估, 由预期目标是否具有可行性来决定是否开展实施该活动。团队负责人需要依据团队的工作能力来处理和分解每个产品所代表的用户故事以及产品需求，通常情况下，分解后的任务可以由八人到十六人的团队完成最佳，这也是开发计划组成的重要依据和目标任务。

于此同时，在实践过程中会发生各种各样的变化，前期的计划可能不够全面，需要团队成员具体实施的时候，再进行细化和完善。例如，PBI 的初值是由用户故事的体现和整个团队的生产效率的选取来计算。

在当下的软件开发过程中，首要目标是完成产品的设计以及研发并且尽可能的创造更多的价值，其中比较重要的课题是在敏捷方面的本质问题上创造价值和设计产品的过程的定义。在跟进项目开发的过程中，通过了解问题，分析并解决相关问题，进而提升软件自身的生产力以及质量。Scrum敏捷开发方法能够完成増量式的迭代构建和开发，可以大大优化软件项目的管理以及开发流程。

Scrum 敏捷开发的迭代周期一般选择两周到三周，通过一个个小的迭代，来构建整个敏捷开发模式，即 Sprint，并通过 Product Backlog 来有效管理用户需求，将用户需求依据优先级进行排序，并将排名比较靠前，优先级比较高，用户关注度比较高的需求进一步进行切分，细化，进而有助于后续的跟进处理，最后的认识领会以及开发创造部分由 Sprint Backlog 自身的用户部分来进行，然后由Scrum开发团队进行完善和开发。整个改进过程的开发流程图如图2-1所示：

![](images/7d9766e8286e447ba5a003bad822f32b59f05faea14ac6dbd1186eca01b7797d.jpg)  
图 2-1 敏捷开发流程图

Sprint 自己的迭代式工作坊在 Scrum 的 Agent 开发流程中执行的一个主要的项目。Scrum Master 的主要功能是确保 Sprint 在执行的时候一直保持着一个完整的运行，这样它就不会受到任何的影响和改变，也不会改变它的目的， Sprint 本身就是一项日常工作，每天的工作，包括评审会、回顾会和策划会议。它的优点就体现在了它的发展趋势和变化之中，它的特点就体现了它的优点，它可以用14到21天的迭代时间，也就是 Sprint，它可以用14到21天的迭代时间，这就导致了它的周期相对较小，因此它可以很好的解决这个问题。。

以天为单位的燃烬图，其目的是通过对各个阶段的工作进行真实的展现。作为一张可以清晰地看到 X 轴和 Y 轴线的图，图中显示了队伍的计划和实际的任务，并以每个任务的任务值为基准，而每日的任务是用横向坐标来表示，这两条直线构成了两条直线，表示出了任务完成后的任务。而在这个过程中，我们可以用二维的时间线和横向轴线的交叉处来反映出一个小队的整体进度。在这张图表上，交叉的位置可以很好的预测出时间的长短，时间短的话，时间短一点，时间长一点，慢一点就慢一点。下面的图表 2-2 是 Sprint 的两周的图表：

![](images/716a42e961291f40efcc6fc941e3e5eb14e8c9e5927454fea50c80dd53e66d16.jpg)  
图 2-2 Spring 燃尽图

# 2.2 项目进度管理

项目进度管理是一种保证工程如期实现的一种有效的管理方法。按照“目标导向”的方针，制定了一套科学、合理的路线图和时间表，同时要强化督导和督促，并对相关的工作进行及时的反馈。实施项目进度管理的目的在于制定合理、经济的进度，并在完成过程中对进度进行改进，纠正实际进度与计划进度的偏离，确保整体的任务完成。

进度计划的关键在于制定符合现实的进度计划，实施合理的进度控制，确保工程进度的按时、有效地完成工程的需求。

# 2.2.1 项目进度管理的内容构成

项目进度管理是项目管理中的一个关键问题，它是以一种科学化的方法来制定项目目标，通过细分任务来编制项目进度计划表，通过项目资源的储备情况来编制项目资源供应计划表。对于项目进度控制来说，项目的进度管理的总目标是项目的按时完工，需要考虑的因素有质量和成本费用，采用监督和控制的方式，并建立监督和监督的制度，密切注意工程的实际进展和工程计划之间的差异，当出现延迟问题时，进行研究和研究，发现问题的根源，并衡量其对工程总工期的影响，及时的调整工程的进度，使工程按期完工。

项目进度管理能否顺利实施，除了要保证工程按期完工、确保工程早日完工外，还与工程造价密切相关，而实施科学、高效的工程进度计划可以节约时间、人力和物力，从而实现节约投资的目的，因此项目如果能够圆满结题，那么项目进度管理一定发挥着巨大作用。

# 2.2.2 项目进度管理的作用

项目进度管理包括两个方面：

（1）制定项目进度计划。项目规划的目标是使各活动任务的工期得到合理的分配，并根据所能提供的资源和对活动的影响因素，确定活动任务、任务的排序、任务完成所需的时间，从而建立对整个项目的进度计划。项目计划是项目进度管理的前置工作，为项目进度管理的重要活动确定目标、提供方向，为项目活动中每个环节设置工作安排，确保了项目总目标的按时完成的概率。

（2）有效控制项目进度计划。在建立了项目进度计划后，通过参考项目进度计划，分析和反馈，及时调整项目进度，确保项目进度计划进行的各项工作，是项目进度计划管理的基础工作。项目进度控制可划分为项目详细进度控制、项目主进度控制以及项目总进度控制。利用问卷反馈和建立有效的反馈机制，保证项目的顺利进行。

# 2.3 基于关键链的进度管理

进度管理领域的享有盛誉的 Goldratt 教授提出了约束理论（TOC），约束理论聚焦于改善生产技术，并指出系统的产出水平取决于系统的制约因素。在项目管理领域首次应用约束理论是在1997年，关键链项目管理的研究方法也在这一年被提出。Goldratt 认为关键链应当满足：在最短时间周期的决策过程中，对资源的依赖性和工作之间的相关性同样起到了同样的作用。关键链项目管理是约束理论在项目管理领域的有效方法。其主要内容包括：第一，确定限制条件，确定项目中的项目的关键链；第二是要充分了解和运用好关键链；第三是协同其它工作，确保关键链工作平稳进行，确保关键链安全；四是提升关键链，使资源与项目时间直接的矛盾达到高效的协同，从而缩短工期；最后，持续寻求制约因素并持续改善。正确的确定关键链是一个项目的成功与否的重要因素。

# 2.3.1 关键链技术概述

关键链项目管理主要包括如下三个部分：

一是确定关键链。和关键路径方法不同，关键链技术是通过分解工作结构分解、挖掘关键路径、分析资源的约束情况，在平衡资源的情况下，识别出最长路径作为关键链。

二是删减项目安全时间，对关键节点设置项目缓冲。Goldratt提出二分之一法，即把项目的工作时间缩减到一半，然后用减半的时间作为安全时间。这是由于工程中的各种工作都有可能受到安全时间的影响，所以很难准确地估算出各个工作的具体时长。缓冲区分为三类，分别是资源缓冲（RB）、汇入缓冲（FB）和项目缓冲（PB）。当一个关键链上的工作与以前的工作所用到的资源不一样时，需要将一个资源缓存冲进去；在非关键链和关键链的交汇点，引入了一个汇入缓冲区，能够缓解在非关键链路上的工作进度的延迟，并降低了非关键链延迟对关键链造成的不利的后果。在完成了关键链的最终工作之后，工程缓冲区将会减少在关键链上的工作延迟，从而减少整个工程的时间延迟。在项目建设中，通过建立缓冲区来进行工程的项目进度管理。

三是依据缓冲消耗量的监测实施对项目进度的监控。Goldratt将缓冲区平均分为三个区域，大于三分之二为红色区域，表示项目存在严重问题，极大可能会延期；三分之一到三分之二为黄色区域，说明项目存在潜在风险，可能会延期，小于三分之一为绿色区域，项目进度正常。

正确计算出缓冲区大小，并对缓冲区进行分块监控，可以有效保证项目的运行。

# 2.3.2 项目进度不确定性指标体系构建

项目进度的不确定性因素包括链路特征和项目特征的不确定性。

（1)在链路特征上确定进度不确定性指数

$\textcircled{1}$ 作业占链路持续时间的比重

链路中单个作业持续时间越多，其对链路的作用越大，所对应的缓冲区就越大。公式（2.3）中，i表示链路中di 占链路时间的百分比:

$$
\alpha _ { i } = \left. \frac { d _ { i } } { \strut } \right/ _ { \sum { d _ { i } } }
$$

$\textcircled{2}$ 作业资源占用的紧张度

资源并非是无限制的，如果某一作业需要的资源超出了项目的资源总量，那么由于资源的限制，这一作业将导致项目的延期。用 $\beta _ { i }$ 来表示作业 i 的所占资源的紧张度：

$$
\beta _ { i } = \operatorname* { m a x } \left\{ \sum _ { i = 1 } ^ { m } { \LARGE \ r _ { i t } ^ { r _ { i } } } \Big / _ { R _ { t } } \right\} , t \in \mathrm { ~ \Lt ~ } ( S _ { t i } , S _ { t i } + d _ { i } )
$$

在公式（2.4）中， $R _ { t } \ \cdot \ r _ { i t }$ 是指在 t 时间点上项目总的资源和作业所需的资源， $\mathfrak { m }$ 是处于 t 时间点的活跃的作业数量， $s _ { t i }$ 和 $d _ { i }$ 为作业 i 开始时间和持续时间

$\textcircled{3}$ 作业复杂性

各作业的关联性越强，那么对于项目俩说，不确定性越高，所需要的缓冲区也就越多。在公式（2.5）中，作业i的复杂性由 $\tau _ { i }$ 表示：

$$
\tau _ { i } = N _ { p } / N _ { t } \mathrm { ~  ~ { ~ \left( ~ 2 . ~ 5 \right) ~ } ~ }
$$

$N _ { p }$ 和 $N _ { r }$ 是指目前作业 i 所处链路上，前置作业个数和链路上作业总数。

$\textcircled{4}$ 位置系数

在常规情况下，链路起始端距离越长，作业的不确定性越高，就越有可能发生延期。在公式（2.6）中，作业的位置系数用 $\rho _ { i }$

$$
\rho _ { i } = S _ { i } / D ~ \left( 2 . 6 \right)
$$

在公式（2.6）中，iS 是作业 i 在链路上开始到当前作业的差值，D 为当前链路所需的总时间。

（2）确定项目进度的根据项目特征的不确定性指标

项目特征的不确定性因素包括：需求因素、管理因素、技术因素、计划因素和人员因素，具体的细节跟实际项目有关，会在后面的章节中详细介绍。

（3）在对不确性定性指标进行定量时，必须按照出现的几率进行分割，根据 $0 \%$ 、 $2 0 \%$ 、 $4 0 \%$ 、 $6 0 \%$ 、 $8 0 \%$ 、 $1 0 0 \%$ ，对项目进度的不确定性因素分为5个等级。在此前提下，将不确定性系数对项目进度的影响程度分为 5 个等级，I级（非常小）、Ⅱ级（比较小）、Ⅲ级（一般）、Ⅳ级（一般严重）和Ⅴ级（非常严重）。在此基础上，将不确定性系数的分配区间设定为 1-9，并给了出分配图 2.1。不确定性系数的得分与其出现的几率及影响程度有显著的相关性。在对步骤 i 的不确定性系数 $\mu$ 进行定量的过程中，假设 p 个事件出现，根据上面的赋值图得到各作业的不确定性因素值 $C _ { p k }$ ，定量的值加总后的结果如公式（2.7）所示：

$$
\begin{array} { r } { C ( \mu ) = \sum _ { k = 1 } ^ { p } c _ { p k } \ \left( 2 . \ 7 \right) } \end{array}
$$

![](images/034578a41012f00355a679880ebe861867f562884c8ec6e8653f6aa5e5c7b59b.jpg)  
图 2.1 赋值对照图

# 2.3.3 基于熵权法和 TOPSIS 法的不确定性系数计算

（1) 评价指标的制定通过对 n 个作业中影响的的 m 个指标，得出了以下的评估矩阵:

$$
\left[ \begin{array} { c c c } { x _ { 1 1 } x _ { 1 2 } } & { \cdots } & { x _ { 1 m } } \\ { \vdots } & { \vdots } & { \vdots } \\ { x _ { n 1 } x _ { n 2 } } & { \cdots } & { x _ { n m } } \end{array} \right] \quad ( i = 1 , 2 \dots , n ; j = 1 , 2 , \dots , m )
$$

（2) 标准化矩阵的构建

将向量规范法用于以上的矩阵 X，并将其进行如下的处理：

$$
y = { \left[ \begin{array} { l l l } { y _ { 1 1 } y _ { 1 2 } } & { \cdots } & { y _ { 1 m } } \\ { \vdots } & { \vdots } & { \vdots } \\ { y _ { n 1 } y _ { n 2 } } & { \cdots } & { y _ { n m } } \end{array} \right] } \quad ( i = 1 , 2 \ldots , n ; j = 1 , 2 , \ldots , m )
$$

这里， $y _ { i j } = x _ { i j } / \sqrt { \sum _ { i = 1 } ^ { n } x _ { i j } ^ { 2 } }$

（3) 估算各作业的特征权重

$$
\begin{array} { r } { p _ { i j } = \frac { y _ { i j } } { \sum _ { i = 1 } ^ { n } y _ { i j } } ~ ( j = 1 , 2 , \ldots , m ) } \end{array}
$$

指标内部的数值与所含的信息呈比例关系，并且对于当前作业也具有正影响，公式（2.10）为第 j 项指标下工序 i 的特征比重为 $p _ { i j }$

（4)计算熵值

熵值与其包含的信息量呈反比例关系，数值越高，所包含的信息越少，评估时所占的比重越小。用以下公式来计算熵值hj：

$$
\begin{array} { r } { h _ { j } = \left\{ \begin{array} { l l } { - \frac { 1 } { \ln n } { \sum _ { i = 1 } ^ { n } p _ { i j } \ln p _ { i j } } , p > 0 } \\ { \qquad 0 , p _ { i j } = 0 } \end{array} \right. } \end{array}
$$

（5)计算差异系数

指标的熵值越低，说明指标的信息量和所在权重越高，指标间差异越大，其权重也越高，两者之间呈正比例关系，差值 $g _ { j }$ 的计算公式如下：

$$
g _ { j } = 1 - h _ { j } ( 2 . 1 2 )
$$

（6)计算权重

权重X+的计算公式如下:

$$
\begin{array} { r } { w _ { j } = \frac { g _ { j } } { \sum _ { j = 1 } ^ { m } g _ { j } } } \end{array}
$$

（7)对标准化矩阵加权计算

对已完成的第（2)步的完成结果，乘以权重，从而得到一个新的矩阵:

$$
Z = \left[ \begin{array} { c c c } { y _ { 1 1 } w _ { 1 } y _ { 1 2 } w _ { 2 } } & { \cdots } & { y _ { 1 m } w _ { m } } \\ { \vdots } & { \ddots } & { \vdots } \\ { y _ { n 1 } w _ { 1 } y _ { n 2 } w _ { 2 } } & { \cdots } & { y _ { n m } w _ { m } } \end{array} \right]
$$

（8)正、负理想解的确定

在该矩阵中，各结果的最大值就是正理想解：

$Z ^ { + } = ( \operatorname* { m a x } \{ y _ { i 1 } w _ { 1 } \} , \operatorname* { m a x } \{ y _ { i 2 } w _ { 1 } \} , \cdots , \operatorname* { m a x } \{ y _ { i m } w _ { m } \} ) , i = 1 , 2 \cdots , n$ 在该矩阵中，各结果的最小值就是负理想解：

$$
Z ^ { + } = ( \operatorname * { m i n } \{ y _ { i 1 } w _ { 1 } \} , \operatorname * { m i n } \{ y _ { i 2 } w _ { 1 } \} , \cdots , \operatorname * { m i n } \{ y _ { i m } w _ { m } \} ) , i = 1 , 2 \cdots , n
$$

（9)欧几里得间距的确定

欧几里得间距的计算公式如下:

$$
\begin{array} { r l } & { D _ { i } ^ { + } = \sqrt { \sum _ { j = 1 } ^ { m } \mathrm {  ~ \psi ~ } ( Z _ { j } ^ { + } - Z _ { i j } ) ^ { 2 } } , i = 1 , 2 , \cdots , n \mathrm {  ~ \psi ~ } ( 2 . } \\ & { D _ { i } ^ { - } = \sqrt { \sum _ { j = 1 } ^ { m } \mathrm {  ~ \psi ~ } ( Z _ { j } ^ { - } - Z _ { i j } ) ^ { 2 } } , i = 1 , 2 , \cdots , n \mathrm {  ~ \psi ~ } ( 2 . } \end{array}
$$

（10)计算近似程度

所谓的近似程度就是各作业中的不确定性因素和最大的不确定性因素的间隔

$$
C _ { i } = D _ { i } ^ { - } / ( D _ { i } ^ { + } + \ D _ { i } ^ { - } ) , i = 1 , 2 , \cdots , n
$$

（11)安全时间的计算

根据估计的工期 $E _ { i }$ 和系数 $C _ { i }$ 按照以下公式可以得出安全时间 $t _ { i }$

$$
t _ { i } = C _ { i } E _ { i } ~ \left( 2 . ~ 2 0 \right)
$$

（12)各作业的缓冲区计算

对于每个作业的安全时间，这里采用根差法求出缓冲区 PB 和非关键链FB。根据公式（2.21）和公式（2.22）计算得关键链和非关键链缓冲时间:

$$
\begin{array} { r } { P B = \sqrt { \sum _ { i = 1 } ^ { n } ( t _ { i } ) ^ { 2 } } } \end{array}
$$

$$
\begin{array} { r } { F B = \sqrt { \sum _ { i = 1 } ^ { n } ( t _ { i } ^ { \prime } ) ^ { 2 } } } \end{array}
$$

# 2.4 进度管理方法有效性分析方法概述

# 2.4.1 进度管理计划方法

关键路径法（CPM) 和PERT 是五十年代后期几乎在同时期产生的两个重要的项目计划研究方法。当时由于科技和工业的快速发展，产生了不少大型并且复杂的科学与工程项目，这些项目大都包含大量的工序，工作范围也较广，往往要调动大批人力、物力、财力。所以如何正确而高效地使用这些生产要素，使其相互配合，在有限的工期和有限的资金下，从而达到更好的实施效果。CPM 与 PERT 也在此大的历史环境中诞生。尽管两种方案的研究方式是在两种不同的环境下进行的，但是它们的基本原则都是一样的，即通过一个网络图，来描述各个项目的进程以及它们的相互关联；通过对网络的分析，对各个活动的时长进行统计，并对主要的活动和路径进行判断，并按照不同的时序对网络进行相应的调整；最后，最终得到最短的网络周期。同时可以把成本与资源问题作为参考依据，计算出综合优化过的项目计划方案。因为这两种研究都是采用网络图和相关的网络分析来描绘项目总体情况，也被称之为网络计划技术。

# 2.4.2 基于蒙特卡洛模拟的有效性分析

蒙特卡洛法是一种通过随机试验和统计计算的仿真模拟方法，它采用合理的模型，把复杂的实验目标转化为随机数和数字特征的仿真模拟和数值运算能够得到有优异表现的近似结果。工程进度的模拟计算，其基本思想是将工程中的重要事件估计的时间和风险因素等用随机变量构建成与实际状况相符的特定的分配，然后利用蒙特卡洛法来进行直接或间接的模拟，由此来说明风险特性。运用蒙特卡罗方法进行投资决策分析，通常可分成三个阶段，第一阶段是将风险因子转换成对应的随机性，再在此基础上对各个变数进行适当的分配，再经过大量的仿真试验，确定其变动区间；这是一个评价的方法。

# 2.4.3 基于风险理论的有效性分析

美国著名的软件工程领域的学者巴利玻姆于 1991 在 IEEE Software 上发布了《软件风险管理：原则和实践》，并提出了软件风险管理思想体系。风险评估与风险控制构成了这一思想体系。

风险评估分为三部分：风险确认、风险分析以及划分优先级：

（1)风险确认是可以清楚地列出可能导致项目失败的相关要素。通常，较为普遍的因素包括审查决策因素、清单检查、结构分析和以前的经历进行对比。

（2)风险分析是对所识别的各种危险进行评价，从而对其产生的可能及其所造成的后果进行评价，在综合多个风险因素和多个风险互动的相互作用下，进行综合评价。常见的风险分析方法有成本模型、网络分析、统计分析等。

（3)优先级划分是根据特定的方法，对已知的风险因素进行排序。常见的分析手段包括风险影响分析、成本效益分析。

风险控制包括风险管理方案、风险解决方案和风险检测：

（1)风险管理方案是通过信息、风险规避、风险转移和风险控制等手段来控制风险，并对风险要素进行综合协调和掌握。风险控制策略主要有成本效益分析、风险解决方案和风险管理路径。

（2)原型测试、模拟运行、性能评估、人事安排等方法是风险解决方案常用的手段，用来去除或者缓解风险因素造成的影响。

（3)风险监控的主要工作有监控项目风险因素消除的具体时间，以及制定相应的保障措施。一般的监控方法都是先建立阶段性节点来追踪过程的结果，或是设置一个当前风险列表，然后采用定期例会更新当前的风险列表，从而确定下一阶段的风险管理目标。

# 第 3 章 途虎养车软件项目进度管理的现状分析

# 3.1 途虎养车简介

垂直电商是指在某一个产业领域或细分市场上深化运营的商业模式。垂直电商网站旗下运营的产品通常都是相同类别的产品。这类网站多是专门经营同类产品的商家对个人或是商家对商家业务，其服务对象都是专门针对这类产品的。

# 3.1.1 垂直电商行业概述

国内电商市场在开始阶段诞生了不少多元化的电商网站，将线下的传统大百货商店搬到线上，最初也只为各种商品提供了统一的线上服务。随着中国电商行业的迅速发展，垂直化服务的概念逐渐被平台所重视。

其实电商的垂直化运营模式在国外已经形成了完善的体系。全球最大的购物平台亚马逊在提供各种品类的商品的同时，针对于每个商品类别均由不同的业务团队通过定制化的服务来满足不同用户的需求。

垂直化运营的核心在于专注和专业，可以创造满足于某个群体的品牌需求，符合某一场景消费者的特定习惯，从而可以更轻易获得消费者信赖，进而提升品牌的形象和影响力，建立品牌自己的特定价值，这也符合是中小企业的创新的必经之路。

# 3.1.2 汽车后市场电商平台项目简介

自从改革开放以来，中国人民的收入的日益增长，虽然因为疫情的原因，对汽车行业造成了一些影响，但是在 2021 年，汽车销量也达到了 2628 万量，蝉联全球第一。

国内消费者对汽车的需求和要求正在不断改变。在以前看来，大众普遍都觉得汽车是奢侈品，一定程度上提现了社会地位，但是目前汽车已演变成了生活用品和代步工具，汽车已经回到了原来的工具属性上。与此同时，大众对购置车辆的后续服务更加重视，线上汽车电商网站所提供的服务更加个性化、人性化和透明化。

从易观网上得到的数据，在汽车行业中的电商占到投资融资比例中，汽车后市场占比约占到七成，电商购车业务占到不到两成，二手车业务则占到一成左右。汽车后市场大有发展前景，伴随着我国汽车保有量的持续提升，汽车后市场必将出现快速的腾飞。

# 3.2 途虎养车软件研发团队及研发流程

项目进度管理严重制约着公司的发展，不能满足公司转型升级的需求，不利于新目标的实现，因此公司对目前的现状进行了一个系统的梳理，以求能够有所发现以及突破。

# 3.2.1 软件研发团队组织架构

![](images/f4d2dac58f9f91fa2ee4ba81632b853090f863f80e746e46581a3b9a99d7f908.jpg)  
图 3-1 门店管理系统组织架构

途虎养车门店管理系统的组织架构主要由研发部和运营两部分组成。研发部下有项目经理、移动端开发、服务端开发、功能测试工程师、接口测试工程师等。运营部分有门店技师和门店督导。

# 3.2.2 软件项目进度管理体系

公司的进度计划管理系统比较简单，由一位项目经理担任，隶属于研究开发运营部门。32岁的项目曾经从事过8年的软件开发者。经过多年的自我学习，逐渐转岗到公司的项目经理，具有一定的专业背景和丰富的工作经历；但是，在软件项目管理的理论体系方面，目前还没有形成系统的研究。公司规模小，人员少，新的项目一旦开工，公司的负责人就会按照工程规模和人员的需求，由公司的负责人来指定。挑选专门的开发人员，组建一个项目组。项目经理对项目整体进度进行管理，编制项目进度计划，实施进度计划，监督项目进度，并对项目的各个部门进行人力、物力的交流与协调。项目经理一人，项目多、时间重叠时，项目经理要承担多个项目的工作，多项目的管理工作，要承担多个项目的工作，而项目经理属于研究开发运营部门，项目管理往往会被项目的结构所束缚。

企业通过活动定义、活动排序及时间计算、编制进度计划、进度控制的流程实施项目进度计划。项目经理按照业务部门提供的软件需求文档，对软件开发任务进行定义、划分工作结构，由技术部门的小组 Leader 和项目经理一起对分解后的项目任务根据优先级排序和根据技术难度估算任务时间，利用Tower 等项目软件进行记录，形成项目进度计划，通过甘特图反应项目中任务之间的先后依赖关系、任务持续的时间以及对应任务的执行人，采用网络图技术确定项目管理中的关键路径。目前企业中负责该项的软件工程师只有8位，包含需求分析师和三位测试工程师，软件开发中主要编码工作只有五位软件工程师负责，当遇到多个任务所需要的工程师资源冲突时，只能限制在一到两个编码任务同时进行，最终可能导致项目延期。

在项目进展情况执行的过程中,由项目经理组牵头统筹项目管理的各团队和所有部门,通过定期和不定期的宣传活动监控项目进展状况,每天对各部门工作开展状况做出汇总,并按照项目工作进展情况检查各项任务能否按时完成,同时通过每周的团队组长碰头会议通报反馈项目工作开展状况,并对进展缓慢超期风险较高的项目工作开展重点监督。根据工程进展执行的具体状况、周边各种要素的变动和用户的要求变化,适时变更工程进行方案,对计划实施过程中的错误加以修正,确保工程如期进行。在具体工作开展过程中，项目经理会直接与业务部门联系，降低业务部门需求变更导致影响到项目进度计划的变更，更好的控制项目进度。

# 3.3 途虎养车软件开发项目进度管理现状

根据途虎养车的组织架构以及管理体系的概况，目前途虎养车主要遇到了下面几个问题：

（1）途虎养车在行业里面算是比较数字化转型的标杆，将纸质的数据输入转化为电子系统的方式是行业内的首创，所以对于项目没有可以参加的经验，对项目进度的把控缺乏对应的参考，只能依靠自己的探索。

（2）项目技术选型主要有Java后端、Vue、iOS和安卓等主流技术应用。但目前这些技术更新迭代速度非常快，新型技术的广泛应用还会给项目带来巨大的风险。

（3）途虎养车从业务出发来看，针对技术难点和项目管理的复杂性，存在研发、管理、需求、计划及项目成员等不确定性因素项目的影响。

（4）工作人员受到“帕金森定律”和“学生综合症”等心理因素的影响。对以往的计划进行分析，结果显示为使计划人员有充足的时间来完成各项工作，预期的活动所需的时间要比一般的工作时间长得多。在前期，时间和精力充足的情况下，需求的变化看似不会对项目的进度造成什么影响，但实际上却会消耗大量的人力、物力和时间，这就造成了后期的需求更改，因为时间和成本的关系，不得不这么做。

（5）在项目执行期间，随着企业的实际情况而不断扩展。在项目开始时，业务部门仅仅提供了项目的基本要求，而许多要求都是在开发和利用的同时进行的。随着项目的进行，新的要求不断地被提出来，从而使项目的范围不断扩大，所花费的时间和费用也随之增大，从而使项目不能按时完成。

（6）项目沟通管理低效是项目管理中遇到的问题。 在 SASS项目中，由于系统涉及多个业务领域，产品经理、开发人员、业务人员会因自身利益的差异而发生内耗，特别是在流程再造、业务电子化改造的任务中冲突明显，因此必须建立健全的沟通机制，才能有效地化解和控制这些问题。

途虎公司制定了项目交流制度，从会议规则到邮件回复都有详细的规定，从途虎公司的 SASS项目交流模式来看，每个星期都会召开一次会议，项目负责人周例会，项目成员周例会（单周项目经理主持，公司双周副总经理），各模块小组周例会，开发团队周例会；项目进展情况汇报，项目周报等。项目经理的目的是尽可能地将所有的责任都落实到每个人身上，防止在做出决定时出现的拖沓和拖沓，并保证每个项目都有专人负责，从而达到闭环。

但现实生活中，很多时候都要在会议上交流，等着各个级别的高层做出决定，留给顾问、 IT、业务人员处理特定工作的时间大大缩短，以至于整个团队都要加班到半夜，一年多的项目执行期间，一线开发人员、业务关键用户、模块顾问都离开了，这就给整个项目的执行增加了危险，最后也导致了项目的推迟。。

# 3.3.1 基于 CPM/PERT 的进度计划的问题

途虎养车开发项目以前一般采用了 CPM/PERT 进度管理方法。活动工期估算采用了“三点估算法”，通过悲观工期 F 、乐观工期O 和最可能工期 M ，利用式 $( O + 4 * M + F ) / 6$ ，计算得到期望工期作为工序预估工期，得到活动工期估算表如表 3.2-3.5 所示。

表 3-1 项目预估时间表  

<table><tr><td>编码</td><td>任务名称</td><td>乐观工期0</td><td>最有可能工 期M</td><td>悲观工期F</td><td>理想工期</td></tr><tr><td></td><td>门店系统项</td><td></td><td></td><td></td><td></td></tr><tr><td></td><td>目</td><td></td><td></td><td></td><td></td></tr><tr><td>1</td><td>需求调研</td><td>5</td><td>7</td><td>8號</td><td>7</td></tr><tr><td>2</td><td>产品设计</td><td>4</td><td>5</td><td>5</td><td>5</td></tr><tr><td>3</td><td>方案评审 门店数据采</td><td>2</td><td>2</td><td>2</td><td>2</td></tr><tr><td>4</td><td>集</td><td>7</td><td>10</td><td>12</td><td>10</td></tr><tr><td>5</td><td>UI设计</td><td>15</td><td>20</td><td>22</td><td>20</td></tr><tr><td>6號</td><td>数据库设计</td><td>12</td><td>14</td><td>14</td><td>14</td></tr><tr><td>7</td><td>服务端开发</td><td>30</td><td>32</td><td>35</td><td>32</td></tr></table>

<table><tr><td>8</td><td>OM后台开发</td><td>12</td><td>14</td><td>16</td><td>14</td></tr><tr><td>9</td><td>APP开发</td><td>60</td><td>62</td><td>70</td><td>63</td></tr><tr><td>10</td><td>联调</td><td>5</td><td>7</td><td>8</td><td>7</td></tr><tr><td>11</td><td>接口测试</td><td>10</td><td>10</td><td>12</td><td>10</td></tr><tr><td>12</td><td>功能测试</td><td>27</td><td>30</td><td>31</td><td>30</td></tr><tr><td>13</td><td>服务器部署</td><td>2</td><td>3</td><td>3</td><td>3</td></tr><tr><td>14</td><td>实施</td><td>3</td><td>3</td><td>3</td><td>3</td></tr><tr><td>15</td><td>培训</td><td>5</td><td>7</td><td>8</td><td>7</td></tr><tr><td>16</td><td>上线</td><td>5</td><td>5</td><td>5</td><td>5</td></tr></table>

这种估算方式容易造成活动工期估算过大。途虎养车系统的开发项目技术难度大，风险因素多，为了保证项目进度，项目工期安排中常常会设定足够的安全工期。但是在项目执行期间，由于工序结束后的剩余的工作没有得到充分利用，从而造成了项目的时间浪费。

# 3.3.2 进度计划制定的问题

由于途虎养车软件开发项目是一个具有创新意义的工程，特别是门店日常下单系统，它对系统的实时性和处理能力都有很大的不同；可借鉴的工程实例和缺乏的项目实践很少。在这种情况下，采用常规的项目进度管理方式进行项目进度计划，很容易导致项目工期估算过大，没有考虑到资源的限制，从而导致项目的实施效果不佳。

（1) 没有考虑人力资源和软件资源的约束。在编制项目进度计划时，没有充分利用人力、软硬件的资源，如果同时有两个或更多的作业，则必须等待其他作业的资源来完成下一阶段的工作，从而使该活动推迟。

（2) 在项目进度计划的编制过程中使用活动的乐观时间，缩短了活动预计工期，与 CPM/PERT 法相比提高了按时完工的概率。在项目中，关键链法充分利用了资源的矛盾，研发和测试资源相对紧张，而关键链法则会针对项目的实际资源进行动态调整各链路活动，从而避免了资源的限制。在关键链的末尾插入项目缓冲区，以减缓由于项目不确定因素导致的延期风险；为保证关键链稳定运行，避免其他因素干扰，在非关键链与关键链交叉点插入缓冲区，进而消除一些因素会对关键链产生的影响，降低项目延期的可能性。关键链法利用项目缓冲和接驳缓冲来降低活动工期减少带来延期的可能。在项目实施阶段，缓冲区管理、项目执行阶段的缓冲实时监测、缓冲消耗警报等方面，实施有针对性的项目进度管理措施， 从而实现项目的进度控制，确保项目按时完工。

# 3.3.3 进度控制的问题

软件工程交付能力是指一个集设计、技术、协作、管理业务为一身的综合交付能力,是指项目的基本原理与办法在软件工程应用领域中的有效运用。软件交付的质量，依赖于软件交付能力的水平和建设交付能力的软件基础。

软件交付能力的构建是一个随着“需求”而变化、不断优化、不断提高的周期。比如，一个交付团队，第一次发现需求变化，发布管理比较薄弱，就从这两个方面着手，利用传递的理论和知识，利用传递的工具和方法；建立并完善合作与服务的能力。在开发的过程中，发现功能设计和客户端开发能力相对薄弱，因此，设计能力与技术能力有待进一步的优化与提高。在此不断的优化中，开发人员可以合理地构建各种交付能力，并将其整合并均衡，最终实现软件的集成交付。

因此，从某种程度上说，交付团队的成长过程就是合理建设软件交付能力的过程，软件交付能力是软件交付的命脉。

在软件交付的建立阶段中应该关注这样二个方面。

（1）在不同过程的交付力量水平上是不平衡的,这也就是软件交付的总生产力是由整个软件交付队伍的总效能决定的,而交付队伍的总效能则由整个软件交付过程中最弱的力量水平决定。

（2）由于软件交付能力的建设思路模式为先局部后总体,导致整个软件产品生命周期中出现了较多的能力竖井。这由于不同的工作团队采用了不同的业务流程,而不同的业务流程又由不同的工具完成,最后造成各个工具之间的消息无法互通,整个软件产品生命周期中没有信息可追踪性,从而限制了端到端的信息管理,也削弱了软件产品交付能力。

因此，合理建设软件交付能力是做好软件交付的基础，是交付团队的内在基本功。管理者监管不足，由于各项目组的沟通不足，未能在未对可能导致工程延迟的各个方面做出反应，等到实际出现延迟时才会采取相应的对策。尤其是对于软件工程来说，时间长，技术难度大，若不及时交货，管理部门对整个工程的进度控制不力，势必造成后续工作的延误，从而导致工程的整体延迟。

# 3.3.4 途虎养车软件项目进度管理的不确定性因素

由途虎养车软件开发项目的项目特点分析可知，根据项目特点，软件开发的技术、管理、需求、计划及成员是影响软件开发的主要原因。

（1）技术的不确定性因素。

与将技术外包的传统公司模式相比，途虎的汽车养护系统是完全自主研发的。对门店订单系统、实时通知系统等都有很高的需求。许多新的技术，比如Vue，都是第一次在公司的工程中使用，因为缺乏足够的技术人员，所以大部分人都是在学习和实践中摸索出来的。缺少Vue的专业人员在技术问题上要绕远道，浪费大量的时间，在某种意义上也会对工程的进程产生不利的作用。

（2）管理的不确定性因素。

该项目采用矩阵结构，从其它业务单位中抽取研发、产品和测试人才，其中工程经理由项目管理部负责，各参与方之间的功能联系仍然由原单位承担。而对于参与方而言，管理者更难以进行有效的管理，缺少相应的激励和处罚机制，这也是其弊端所在。项目管理人员缺乏一定的领导权限，在项目设计、人员调配、流程调度等环节中，往往会发生决策错误或长时间、项目相关方不配合、资源配置不到位等问题，而项目管理者若不能及时采取有效措施，极大可能造成项目的推迟。

（3）需求的不确定性因素。由于门店业务数字化转型，在行业里也没有很好的实践经验，在开发计划中，产品要求常常发生变化。例如，项目的开发进度是按原设计的，包含了技术选型、软件架构设计等方面的，当项目的要求发生重大变化时，就会影响到目前的进度，因此必须针对项目的整体架构、开发内容和工期进行调整。

（4）进度计划的不确定性因素。途虎养车软件开发项目中作业单位的拆分问题是否最小、作业周期估计的合理性、作业进度计划的多重可变性；和缺乏针对项目的应急预案，是在执行中可能存在的风险。

（5）项目成员的不确定性因素。项目人员的影响很大，包括项目人员自身的工作能力和工作状态，项目经理、研发架构师、研发人员与测试者的交流是否顺利、高效；同时，通过实施业绩考核来提高各有关部门的工作热情，确保完成工作的高质量完成度。

通过这些不确定性因素可以让项目经理更好的对项目进度控制。

# 3.4 本章小结

本章通过对途虎养车门店管理系统的公司概况、组织架构和项目团队，分析了项目所处的行业情况，以及公司在行业内的地位。分析了公司项目进度管理的目前在进度计划制定、工期预估和进度控制中村子的问题，提出了通过关键链法来进度管理优化。具体的说，本章完成了以下工作：

首先是介绍了途虎养车所处的行业背景，介绍了研发团队的结构以及管理体系，指出面临高速迭代的业务需求，软件项目的进度管理的必要性。

其次是对途虎养车软件开发项目的现状进行分析，解决进度计划制定不合理的问题，运用传统的项目进度管理方法制定项目进度计划，很可能出现活动工期估算过大，未去考虑资源约束的问题。所以这里采用关键链法，充分考虑资源冲突，调整各链路的位置。

再次是进度控制的问题，在软件项目中可以充分利用其可逆的特点来有效的调整项目进度的缓冲，强调管理者在进度过程中尽早干预，对于项目的风险要素优先级有着足够的把握。

最后是对项目不确定性因素的分析，主要分为技术、管理、需求、进度计划和成员几个方面来进行分析，为管理者的控制提供依据。

# 第 4 章 途虎养车软件进度计划制定过程优化

为解决途虎养车软件项目进度管理中存在的问题，提出采用关键链技术来优化软件项目进度管理过程。软件项目进度管理优化的过程围绕项目进度计划制定和缓冲区设置、进度控制三部分进行。

进度计划制定包含WBS工作分解、工作分配、活动历时估算。

缓冲区设置包含关键路径确认、缓冲区设置和进度控制风险因素分析。

图4-1 是整个技术方案的流程图。

![](images/71430561081719aeffaa0ce6542c73b908af09026076fab9d7cbd25b7f879138.jpg)  
图 4-1 进度管理优化与实施的流程图

# 4.1 软件项目任务分配解决方案

项目的硬件资源以及人力资源是目前途虎养车门店系统软件所需的主要资源。一般人力资源包含产品经理、UI 设计师、前端软件工程师、后端软件工程师、数据库运维人员、测试工程师和美工设计师等。软件项目中产品经理、测试工程师、美工设计师的资源一般不太缺乏，但前端工程师、后端工程师、数据库运维人员等相互协作人员容易造成资源约束，项目所运用的硬件设备一般也比较充分，意味着硬件资源的约束是不必考虑的。制约途虎养车门店系统项目进度的影响因素有：项目需求变更的频次；软件开发中工程师对于技术的熟练程度；项目活动的前后依赖；项目人力资源的进度等，如下:

（1）项目需求变更的频次

在软件项目运行之前，客户向软件工程师描述自身的开发需求，但是因为软件在开发之前是无法被感知，被看见的，所以在软件逐步成型的过程中，客户往往会再次依据自身的真实体验，对软件需求进一步的修订，甚至会推翻之前的需求，增加全新的需求，这对于软件开发工程师来说无疑是一个巨大的挑战，同时由于需求的多变性，给整个项目的进度管理带来了巨大的挑战，并且很有可能会导致项目延期。

（2）软件开发中工程师对于技术的熟练程度

项目负责人在软件开发初期应该选择合适的项目技术，因为选择项目技术的难易程度直接关系到整个项目周期的长度，而非项目技术越高越好，因为项目技术越高，对于软件开发工程师、项目的运营负责人等的要求也会越来越高，也更容易出现未知的难题。

（3）项目活动的前后依赖

不同项目之间可能会存在前后的依赖关系，因此在通过WBS进行活动分解的过程中，需要确定这种依赖关系，当发现前面某一活动的开展情况会严重影响后面活动的开展时，应尽可能的保证前面活动的顺利进行，否则就会对后面的活动产生风险，甚至导致整个项目具有延期的风险。

# （4）项目人力资源的进度

在进行项目分解的过程中，不仅应当关注活动的前后依赖关系，还应当对每个项目资源的使用情况有一个系统的把控，防止出现同一时间，不同活动需要使用同一资源的情况，最大化的保证资源的有效利用。

因此途虎养车的项目经理在日常的项目经营中，会根据实际情况，进行系统地剖析，并选取了关键链技术作为基本方法，解决引资源冲突而导致的延期问题，确保项目如期完工。

# 4.1.1 软件项目工作分解结构

工作分解结构是根据项目可交付成果和任务活动之间的逻辑关系和实施过程中的先后逻辑，按照层层分解的方式得到的结构图。WBS可以从以下几个方面进行分解任务：

（1）按照组企业物理结构、组织结构分解  
（2）按照产品功能模块分解  
（3）按照项目的地区分布分解  
（4）按照项目进程分解  
（5）按照项目的目标、范围分解

除了上述的几种分解方法，项目工作分解还可以把多个要素结合起来分解，例如将项目的进度、范围、部门等多个维度进行分解。

途虎养车公司的 SASS项目在进行工作分解任务时首要处理的是传统企业复杂的组织架构和交叉的业务流程。2万多个业务部门及分店分布在不同的城市和郊区，项目团队通过艰苦的努力，完成了这个任务根据以上流程。

途虎养车门店系统的软件项目开发工作流程如下：

（1）需求分析与确认。需求团队与门店进行初步交流，通过沟通获得门店线下操作流程，运用专业知识根据门店技师的实际需求列出软件的几大功能模块和界面原型，抽象出每个功能模块中的细小功能，形成需求规格说明书，并再次与门店技师沟通，直至各个功能模块及其界面符合线下实际操作场景，满足实际运营需求，并最终确认形成文档。UI设计师根据产品文档进行界面设计和交互设计，并与产品经理确认一致后，与技术团队负责人以及核心开发人员开展方案评审会，在方案评审会中一致通过后，进入排期研发。

（2）后端研发人员根据产品文档和UI设计稿进行数据库设计，形成数据库表和字段的文档，与产品经理和前端开发同学确认一致后，进入接口定义阶段，接口文档完成后交付到前端同学。后端同学正式进入接口开发阶段，这时候运营团队可以开始进行数据采集，前端研发人员同时进入开发阶段。

（3）前端研发人员主要由Web前端和移动端两部分组成，Web前端研发人员主要负责后台设置相关的功能开发，在整个产品中相对前置。移动端研发人员开发的内容是产品的主要功能，这一部分开发时间相对来说需要的时间比较长，对于产品质量要求也比较高。最后的联调阶段主要由移动端团队发起，作为整个开发阶段的收尾工作。

（4）服务端研发人员完成功能后，接口测试可以提前介入开始进行测试。在移动端研发人员交付产品后，功能测试人员可以介入测试，进行全流程的测试工作。

（5）测试通过后，进入上线前的准备工作，这一阶段的主要工作有服务器部署、实际门店操作、门店技师培训、正式全量上线等。

具体的任务分解图见下图4-2。

![](images/ad91d49ea62fe48fb08eb1b7687d0564b29da5ba9454bf90c2af3b0e243f379c.jpg)  
图 4-2 任务分解图

对分解后项目的各项任务进行排序、编号，明确任务的前后逻辑顺序，形成项目任务分解表如下表 4-1 所示。

表 4-1 门店系统项目任务分解表  

<table><tr><td>工作名称</td><td>编码</td><td>任务名称</td><td>人员分工</td><td>前置任务</td></tr><tr><td>需求分析与确认</td><td></td><td>门店系统项目</td><td></td><td></td></tr><tr><td></td><td>1</td><td>需求调研</td><td>产品经理</td><td></td></tr><tr><td></td><td>2</td><td>产品设计</td><td></td><td>1</td></tr><tr><td></td><td>3</td><td>方案评审</td><td>研发负责人</td><td>2</td></tr><tr><td></td><td>4</td><td>门店数据采集</td><td>运营人员</td><td>3</td></tr><tr><td></td><td>5</td><td>UI设计</td><td>UI 设计师</td><td>3</td></tr><tr><td>后端开发</td><td>6</td><td>数据库设计</td><td>服务端开发人员</td><td>5</td></tr><tr><td>前端开发</td><td>7</td><td>服务端开发</td><td>服务端开发人员</td><td>6</td></tr><tr><td></td><td>8</td><td>OM后台开发</td><td>Web 端开发人员</td><td>6</td></tr><tr><td></td><td>9</td><td>APP开发</td><td>移动端开发人员</td><td>7,8</td></tr><tr><td></td><td>10</td><td>联调</td><td>开发人员</td><td>9</td></tr><tr><td>测试阶段</td><td>11</td><td>接口测试</td><td>测试人员</td><td>7</td></tr><tr><td></td><td>12</td><td>功能测试</td><td>测试人员</td><td>4,10</td></tr><tr><td>上线准备</td><td>13</td><td>服务器部署</td><td>服务端开发人员</td><td>12</td></tr><tr><td></td><td>14</td><td>实施</td><td>产品经理</td><td>13</td></tr><tr><td></td><td>15</td><td>培训</td><td>产品经理</td><td>14</td></tr><tr><td></td><td>16</td><td>上线</td><td>产品经理</td><td>15</td></tr></table>

# 4.1.2 基于微服务的人员工作分配

一个项目的发展，最重要的就是人才。技术和风格各有各的特点，组成一个队伍，必须要有一个合适的分工和组织。就整个软件组织而言,参与的角色一般分为:项目管理、产品主管、系统设计者、美工、开发、以及检测技术人员。除了项目管理和产品主管,设计师、美工、开发。

除项目经理、技术经理外，设计师，美术设计师，开发人员；测试人员会按照项目的规模来进行分配，也许一人多一人，而通常情况下，开发者会同时进行多人合作，如下图 4-3。

![](images/f3c483cc7790b9aa985b8ee1cba39c72b55dfc3bd7e2dbe5209adccd9413c565.jpg)  
图 4-3 组织架构图

下面简单介绍一下各个角色的具体任务，如下表 4-2

# 表 4-2 角色职责表

<table><tr><td>序号</td><td>角色</td><td>具体任务</td></tr><tr><td>1</td><td>项目经理</td><td>项目经理主要是对项目进度的负责，处理各部门间的协调 问题，以及关键人员的开发进度的把控。</td></tr><tr><td>2</td><td>技术经理</td><td>技术经理既要掌握基本的专业知识，又要善于管理研发人 员，主要负责技术选型和整体技术框架的定型。</td></tr><tr><td>3</td><td>设计师</td><td>设计师需要根据产品经理的产品设计，通过专业技能设计 出吸引用户的页面。</td></tr><tr><td>4</td><td>美工</td><td>美工主要是负责对于一些素材设计上的工作。</td></tr><tr><td>5</td><td>软件开发者</td><td>软件开发人员作为项目中的核心环节，其专业技能水平很 大程度上决定了项目的交付质量。</td></tr><tr><td>6</td><td>测试人员</td><td>测试人员作为项目交付的最后一环，作为开发人员 的兜底保障，是项目交付质量不可或缺的一环。</td></tr></table>

# 4.1.3 基于微服务的活动历时估算

途虎养车公司 SASS项目的估算时间估计如下：

（1）参照中国一家拥有超过 20 年的车辆系统执行顾问经验的专业顾问团队，德勤在汽车工业领域内的工作经历和对车辆的了解和对车辆工程的了解，因此，对工期的估算是按照德勤咨询的专家在汽车行业的经验和理解中计算的。

（2）类比估算，借鉴汽车业的最好的管理模式，根据不同的项目实例，对项目进行评估项目估算工期。（3）将公司目前的工时计算方法与实际工作时间相联系，可以直接计算公司内部经营活动。（4）参考以前项目实践，为项目预留一些安全时间，提供足够的灵活性。

综上所述，基于 SASS 实施项目工期弹性大，风险高的特点。

# 4.1.4 基于微服务的软件项目改善计划

途虎养车项目以整个计划执行过程所经历的九个步骤为依据,并以项目分析为辅,可导出整个SASS项目进行计划,这九个步骤依次为开始、规划、定义、设计、创建、测试、部署、交付、关闭。

4.2 软件项目的缓冲区分析与设计

SASS 进度管理的风险是指在项目执行过程中出现的不确定性因素，可能会对工期造成一定的影响，从而使项目的工期延误。途虎养车公司 SASS项目执行期间，计划进度延迟会导致下列各节点进展出现风险：

（1）关键路径上的活动（2）前置任务的后置活动（3）链路短的活动（4）工期估算短的活动（5）有外部依赖的活动（6）可能的计划外的活动

# （7）多方同时进行的活动

上述要素也会在技术上对项目延迟风险有一定的影响：商业需求，开发能力，工作效率等，这些要素都与项目有关，这些要素的频繁变化会对准时的完成和达到项目的目的造成一定的冲击。没有切实的行动来减少或者大大减少这种不确定的危险，那么他们就会变成一个时间延迟的危险。

# 4.2 基于关键链法的软件项目分析

# 4.2.1 软件项目关键路径确认

在对关键链只是和技巧的掌握和应用到项目管理的时间控制中，可以根据任务分解和关键链的确定来识别关键链。在此基础上，将项目的工作分解基于WBS方法、按照不同的资源等级进行分级、按工作所需要的工时进行分配。根据任务之间的逻辑相关性，判定出各工作之间的顺序，确定各工作所需要的时间。在此基础上，将关键链的确定分为三个步骤，消除任务评估中的安全时间，建立网络计划，解决资源冲突，并通过三个步骤来最终确定该项目的关键链。

# （1）消除任务安全时间

由项目经理、项目团队组长等对任务持续时间的初步评价可能会出现工期超长的问题，原因在于任务评估中添加了大量的安全时间，但是这种额外的安全时间病没有确保项目如期完工，反而因为项目工作人员的“学生综合症”等心理因素，造成项目延迟。在对工作时间进行评估后，关键链的重点在于消除额外的大量安全时间。

对于门店系统开发，因为规划过与紧张，会导致团队在心理上带来很大的压力，从而影响到整个工程的进度。与此同时，紧张的项目规划也有潜在的影响项目的质量。因此，当门店管理系统项目组设定好了任务时间，项目经理会与项目人员进行多次沟通，确定最合适的世界，这样就减少了认为增加的安全时间。理想的时间估算不仅能简化规划编制过程，而且能充分调动各成员的工作热情，从这一点出发，得出的任务时间估算情况，如表 4-2 所示。

表 4-2 各任务理想工期估算  

<table><tr><td>编码</td><td>任务名称</td><td>理想工期</td><td>前置任务</td></tr><tr><td></td><td>门店系统项目</td><td></td><td></td></tr><tr><td>1</td><td>需求调研</td><td>5</td><td></td></tr><tr><td>2個</td><td>产品设计</td><td>4個</td><td>1</td></tr><tr><td>3</td><td>方案评审</td><td>2個</td><td>2個</td></tr><tr><td>4</td><td>门店数据采集</td><td>7</td><td>3</td></tr><tr><td>5</td><td>UI设计</td><td>15</td><td>3</td></tr><tr><td>6號</td><td>数据库设计</td><td>12</td><td>5</td></tr><tr><td>7號</td><td>服务端开发</td><td>30</td><td>6號</td></tr><tr><td>8號</td><td>OM后台开发</td><td>12</td><td>6號</td></tr><tr><td>9號</td><td>APP开发</td><td>60</td><td>7,8</td></tr><tr><td>10</td><td>联调</td><td>5</td><td>9號</td></tr><tr><td>11</td><td>接口测试</td><td>10</td><td>7</td></tr><tr><td>12</td><td>功能测试</td><td>27</td><td>4,10</td></tr><tr><td>13</td><td>服务器部署</td><td>2個</td><td>12</td></tr><tr><td>14</td><td>实施</td><td>3個</td><td>13</td></tr><tr><td>15</td><td>培训</td><td>5</td><td>14</td></tr><tr><td>16</td><td>上线</td><td>5</td><td>15</td></tr></table>

（2）建立网络计划

依据任务间的逻辑依赖关系，可以创建下列确定关键链的网络计划，如图4-4所示。从图中，可以看出，关键路径为 $1 \  \ 2 \  \ 3 \  \ 5 \  \ 7 \  \ 9 \ - \rangle$ $1 0 \  \ 1 3 \  \ 1 4 \  \ 1 5 \  \ 1 6$ ，关键路径对应的完成工期为133天

![](images/92763314e72a39dd32ec31820078494ae0a02ecedc9c3fcbd94976133c446f23.jpg)  
图 4-4 项目进度计划网络图

（3）识别关键链

关键链的识别就是对门店系统项目的资源进行分析，以实现对资源的最优分配。分析门店系统项目中各个任务的逻辑相关性和资源情况。根据图4-4可以发现关键链为 $\begin{array} { r } { \mathrm { ~ 1 ~ - > ~ 2 ~ - > ~ 3 ~ - > ~ 5 ~ - > ~ 7 ~ - > ~ 9 ~ - > ~ 1 0 ~ - > ~ 1 3 ~ - > ~ 1 4 ~ - > ~ 1 5 ~ - > ~ } } \end{array}$ 16；非关键链 $4  1 1  1 2$ 和 $6  8$ 。

# 4.2.2 软件项目的缓冲区设置

4.2.2.1 项目进度不确定性指标确定

项目进度的不确定因素包括链路特征和项目特征两部分构成，以下分别根据不确定性指标，对着两部分特征进行体系构建。

（1)确定项目进度的不确定性因素

以链路特征为基础的不确定性因素包括活动持续时间比例、活动资源紧张度、活动复杂度和活动位置系数等。

（2) 基于项目特征的不确定性因素

途虎养车软件开发项目的不确定性因素主要是需求、管理、技术、计划及人员五个方面的内容。

（3）项目进度不确定性指标体系构建

项目进度不确定性指标体系包括链路特征和项目特征的不确定性因素，如表 4.4 所示。

表 4.4 项目进度不确定性指标体系  

<table><tr><td>指标</td><td>指标类型 指标性质</td></tr></table>

<table><tr><td colspan="3">活动持续时间比例</td></tr><tr><td>活动资源紧张度</td><td>定量指标</td><td>值越大，不确定性越大</td></tr><tr><td>活动复杂度</td><td></td><td></td></tr><tr><td>活动位置系数</td><td></td><td></td></tr><tr><td>需求</td><td></td><td></td></tr><tr><td>组织管理</td><td></td><td>值越大，不确定性越大</td></tr><tr><td>技术</td><td>定性指标</td><td></td></tr><tr><td>进度计划</td><td></td><td></td></tr><tr><td>成员</td><td></td><td></td></tr></table>

# 4.2.2.2 项目进度不确定性指标值计算

（1) 基于链路特征的不确定性指标值计算

由公式（2.1-2.4）式确定基于链路特征的不确定性指标值，结果见表4.5：

表 4.5 基于链路特征的不确定性指标值  

<table><tr><td>持续时间比例</td><td>资源紧张度</td><td>复杂度 位置系数</td></tr><tr><td>0.025</td><td>0.021 0.000</td><td>0.000</td></tr><tr><td>0.020</td><td>0.043</td><td>0.091 0.025</td></tr><tr><td>0.010</td><td>0.043 0.091</td><td>0.010</td></tr><tr><td>0.034</td><td>0.021</td><td>0.091 0.062</td></tr><tr><td>0.074</td><td>0.021</td><td>0.091 0.070</td></tr><tr><td>0.059</td><td>0.085 0.182</td><td>0.088</td></tr><tr><td>0.147</td><td>0.021 0.091</td><td>0.101</td></tr><tr><td>0.059</td><td>0.085 0.091</td><td>0.123</td></tr><tr><td>0.294</td><td>0.210 0.182</td><td>0.141</td></tr><tr><td>0.025</td><td>0.021 0.182</td><td>0. 225</td></tr><tr><td>0.049</td><td>0.021 0.182</td><td>0.322</td></tr><tr><td>0.132</td><td>0.130 0.273</td><td>0.475</td></tr><tr><td>0.010</td><td>0.085 0.091</td><td>0.511</td></tr><tr><td>0.015</td><td>0.064 0.091</td><td>0.872</td></tr></table>

<table><tr><td>0.025</td><td>0.064</td><td>0.091</td><td>0.974</td></tr><tr><td>0.025</td><td>0.064</td><td>0.091</td><td>0.238</td></tr></table>

（2) 基于项目特征的不确定性指标值计算

通过对公司资深项目经理和资深研发人员的谈话研究，对各种活动不确定性情形的发生和影响进行了研究，并得出以下情况分析表，如表 4.6。

表 4.6 不确定性情况分析表  

<table><tr><td>类别</td><td>内容</td><td>影响程 度</td><td>可能性</td><td>影响活动</td></tr><tr><td>需求的不确定</td><td>项目目标不明确</td><td>III</td><td>15%</td><td>1、2</td></tr><tr><td></td><td>用户参与少或用</td><td>IV</td><td>20%</td><td>1、15、16</td></tr><tr><td></td><td>户沟通少 对需求理解不够</td><td>IV</td><td></td><td></td></tr><tr><td>管理的不确定性</td><td></td><td></td><td>20%</td><td>1、2、4、 5、7、8</td></tr><tr><td></td><td>组织机构安排不 合理</td><td>II</td><td>30%</td><td>5、8、12</td></tr><tr><td></td><td>管理层决策失误</td><td>IV</td><td>15%</td><td>1、3、6、</td></tr><tr><td></td><td></td><td></td><td></td><td>8、9、12</td></tr><tr><td></td><td>非技术第三方支</td><td>III</td><td>25%</td><td>4</td></tr><tr><td></td><td>持工作不到位</td><td></td><td></td><td></td></tr><tr><td>技术的不确定性</td><td>技术错误带来的</td><td>IV</td><td>10%</td><td>6、7、8、9</td></tr><tr><td></td><td>实施困难</td><td></td><td></td><td></td></tr><tr><td></td><td>测试计划不可行</td><td>IV</td><td>10%</td><td>10、11</td></tr><tr><td></td><td>研发人员水平参</td><td>IV</td><td>10%</td><td>6、7、8、</td></tr><tr><td></td><td>差不齐</td><td></td><td></td><td>9、12</td></tr><tr><td>计划的不确定性</td><td>对关键研发人员</td><td>III</td><td>15%</td><td>6、7、8、</td></tr><tr><td></td><td>进度监控不够及</td><td></td><td></td><td>9、12</td></tr><tr><td></td><td>时</td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td>工作内容分解地</td><td>II</td><td>25%</td><td>7、8、9、</td></tr><tr><td></td><td>不够彻底</td><td></td><td></td><td>10、12</td></tr></table>

<table><tr><td rowspan="7">成员不确定性</td><td>对意外事件没有</td><td>IV</td><td>5%</td><td>7、8、9、</td></tr><tr><td>制定相关的应对</td><td></td><td></td><td>10、11、</td></tr><tr><td>方案</td><td></td><td></td><td>12、13、</td></tr><tr><td></td><td></td><td></td><td>14、15</td></tr><tr><td>开发人员与管理</td><td>III</td><td>35%</td><td>6、7、8、</td></tr><tr><td>层缺乏有效沟通</td><td></td><td></td><td>9、12</td></tr><tr><td>激励力度偏弱使</td><td>II</td><td>50%</td><td>6、7、8、</td></tr><tr><td></td><td>得成员效率不高</td><td></td><td></td><td>9、12</td></tr><tr><td></td><td>项目进行时发生</td><td>III</td><td>10%</td><td>6、7、8、</td></tr><tr><td></td><td>人员流失，难以</td><td></td><td></td><td>9、10、11、</td></tr><tr><td>及时补充</td><td></td><td></td><td></td><td>12</td></tr></table>

根据表（4.6）和式（2.5）计算，得到以下的指标数值，见表 4.7。

表 4.7 基于项目特征的不确定性指标值  

<table><tr><td></td><td>需求</td><td>管理</td><td>技术</td><td>计划</td><td>成员</td></tr><tr><td>1</td><td>11</td><td>4號</td><td>0</td><td>0</td><td>0</td></tr><tr><td>2個</td><td>3</td><td>0</td><td>0</td><td>0</td><td></td></tr><tr><td>3</td><td></td><td>4號</td><td></td><td>0</td><td></td></tr><tr><td>4號</td><td>4號</td><td>4號</td><td>0</td><td>0</td><td>0</td></tr><tr><td>5</td><td></td><td>3</td><td></td><td></td><td></td></tr><tr><td>6號</td><td>0</td><td>0</td><td>8</td><td>3個</td><td>12</td></tr><tr><td>7號</td><td>4號</td><td>0</td><td>8號</td><td>10</td><td>12</td></tr><tr><td>8號</td><td>4號</td><td>7</td><td>8號</td><td>10</td><td>12</td></tr><tr><td>9號</td><td>0</td><td>4號</td><td>8號</td><td>10</td><td>12</td></tr><tr><td>10</td><td>0</td><td>0</td><td>4號</td><td>7</td><td>3</td></tr><tr><td>11</td><td></td><td></td><td>4號</td><td>4號</td><td></td></tr><tr><td>12</td><td></td><td>8號</td><td>4號</td><td>10</td><td>12</td></tr><tr><td>13</td><td>0</td><td>0</td><td>0</td><td>4號</td><td>0</td></tr><tr><td>14</td><td></td><td>0</td><td></td><td>4號</td><td></td></tr><tr><td>15</td><td>4</td><td>0</td><td>0</td><td>0</td><td>0</td></tr></table>

<table><tr><td>16</td><td></td><td></td><td></td><td></td></tr><tr><td></td><td>4</td><td>0</td><td></td><td></td></tr></table>

# 4.2.2.3 基于熵权法和 TOPSIS 法的不确定性系数计算

（1) 通过公式（2.6）建立如下矩阵，如表 4.8 所示

表 4.8 评价指标矩阵  

<table><tr><td>0.025</td><td>0.021</td><td>0.000</td><td>0.000</td><td>11</td><td>4</td><td>0</td><td>0</td><td></td></tr><tr><td>0.020</td><td>0.043</td><td>0.091</td><td>0.025</td><td>3</td><td>0</td><td>0</td><td>0</td><td></td></tr><tr><td>0.010</td><td>0.043</td><td>0.091</td><td>0.010</td><td>0</td><td>4</td><td>0</td><td></td><td></td></tr><tr><td>0.034</td><td>0.021</td><td>0.091</td><td>0.062</td><td>4</td><td>4</td><td>0</td><td>0</td><td></td></tr><tr><td>0.074</td><td>0.021</td><td>0.091</td><td>0.070</td><td>0</td><td>3</td><td>0</td><td>0</td><td>0</td></tr><tr><td>0.059</td><td>0.085</td><td>0.182</td><td>0.088</td><td>0</td><td>0</td><td>8</td><td>3</td><td>12</td></tr><tr><td>0.147</td><td>0.021</td><td>0.091</td><td>0.101</td><td>4</td><td>0</td><td>8</td><td>10</td><td>12</td></tr><tr><td>0.059</td><td>0.085</td><td>0.091</td><td>0.123</td><td>4</td><td>7</td><td>8</td><td>10</td><td>12</td></tr><tr><td>0.294</td><td>0.210</td><td>0.182</td><td>0.141</td><td>0</td><td>4</td><td>8</td><td>10</td><td>12</td></tr><tr><td>0.025</td><td>0.021</td><td>0.182</td><td>0.225</td><td></td><td></td><td>4</td><td>7</td><td>3</td></tr><tr><td>0.049</td><td>0.021</td><td>0.182</td><td>0.322</td><td>0</td><td>0</td><td>4</td><td>4</td><td>0</td></tr><tr><td>0.132</td><td>0.130</td><td>0.273</td><td>0.475</td><td>0</td><td>8</td><td>4</td><td>10</td><td>12</td></tr><tr><td>0.010</td><td>0.085</td><td>0.091</td><td>0.511</td><td></td><td>0</td><td></td><td>4</td><td></td></tr><tr><td>0.015</td><td>0.064</td><td>0.091</td><td>0.872</td><td>0</td><td>0</td><td>0</td><td>4</td><td></td></tr><tr><td>0.025</td><td>0.064</td><td>0.091</td><td>0.974</td><td>4</td><td>0</td><td>0</td><td>0</td><td></td></tr><tr><td>0.025</td><td>0.064</td><td>0.091</td><td>0.238</td><td>4</td><td>0</td><td>0</td><td>0</td><td>0</td></tr></table>

（2)通过公式（2.7)计算得出归一化矩阵，如表 4.9 表示

表 4.9 归一化矩阵  

<table><tr><td>0.053</td><td>0.000</td><td>0.000</td><td>0.000</td><td>1. 000</td><td>0.500</td><td>0.000</td><td>0.000</td><td>0.000</td></tr><tr><td>0.035</td><td>0.116</td><td>0.333</td><td>0.026</td><td>0.273</td><td>0.000</td><td>0.000</td><td>0.000</td><td>0.000</td></tr><tr><td>0.000</td><td>0.116</td><td>0.333</td><td>0.010</td><td>0.000</td><td>0.500</td><td>0.000</td><td>0.000</td><td>0.000</td></tr><tr><td>0.085</td><td>0.000</td><td>0.333</td><td>0.064</td><td>0.364</td><td>0.500</td><td>0.000</td><td>0.000</td><td>0.000</td></tr><tr><td>0.225</td><td>0.000</td><td>0.333</td><td>0.072</td><td>0.000</td><td>0.375</td><td>0.000</td><td>0.000</td><td>0.000</td></tr><tr><td>0.173</td><td>0.339</td><td>0. 667</td><td>0.090</td><td>0.000</td><td>0.000</td><td>1. 000</td><td>0.300</td><td>1. 000</td></tr></table>

<table><tr><td>0.482</td><td>0.000</td><td>0.333</td><td>0.104</td><td>0.364</td><td>0.000</td><td>1. 000</td><td>1. 000</td><td>1.000</td></tr><tr><td>0.173</td><td>0.339</td><td>0.333</td><td>0.126</td><td>0.364</td><td>0.875</td><td>1.000</td><td>1.000</td><td>1.000</td></tr><tr><td>1.000</td><td>1.000</td><td>0.667</td><td>0.145</td><td>0.000</td><td>0.500</td><td>1.000</td><td>1.000</td><td>1. 000</td></tr><tr><td>0.053</td><td>0.000</td><td>0.667</td><td>0.231</td><td>0.000</td><td>0.000</td><td>0.500</td><td>0.700</td><td>0.250</td></tr><tr><td>0.137</td><td>0.000</td><td>0. 667</td><td>0.331</td><td>0.000</td><td>0.000</td><td>0.500</td><td>0.400</td><td>0.000</td></tr><tr><td>0.430</td><td>0.577</td><td>1. 000</td><td>0.488</td><td>0.000</td><td>1.000</td><td>0.500</td><td>1.000</td><td>1. 000</td></tr><tr><td>0.000</td><td>0.339</td><td>0.333</td><td>0.525</td><td>0.000</td><td>0.000</td><td>0.000</td><td>0.400</td><td>0.000</td></tr><tr><td>0.018</td><td>0.228</td><td>0.333</td><td>0.895</td><td>0.000</td><td>0.000</td><td>0.000</td><td>0.400</td><td>0.000</td></tr><tr><td>0.053</td><td>0.228</td><td>0.333</td><td>1.000</td><td>0.364</td><td>0.000</td><td>0.000</td><td>0.000</td><td>0.000</td></tr><tr><td>0.053</td><td>0.228</td><td>0.333</td><td>0.244</td><td>0.364</td><td>0.000</td><td>0.000</td><td>0.000</td><td>0.000</td></tr></table>

（3)通过公式 （2.8) 计算得出特征比重矩阵，如表 4.10 所示

表 4.10 特征比重矩阵  

<table><tr><td>0.018</td><td>0.000</td><td>0.000</td><td>0.000</td><td>0.324</td><td>0.118</td><td>0.000</td><td>0.000</td><td>0.000</td></tr><tr><td>0.012</td><td>0.033</td><td>0.048</td><td>0.006</td><td>0.088</td><td>0.000</td><td>0.000</td><td>0.000</td><td>0.000</td></tr><tr><td>0.000</td><td>0.033</td><td>0.048</td><td>0.002</td><td>0.000</td><td>0.118</td><td>0.000</td><td>0.000</td><td>0.000</td></tr><tr><td>0.028</td><td>0.000</td><td>0.048</td><td>0.015</td><td>0.118</td><td>0.118</td><td>0.000</td><td>0.000</td><td>0.000</td></tr><tr><td>0.076</td><td>0.000</td><td>0.048</td><td>0.017</td><td>0.000</td><td>0.088</td><td>0.000</td><td>0.000</td><td>0.000</td></tr><tr><td>0.058</td><td>0.097</td><td>0.095</td><td>0.021</td><td>0.000</td><td>0.000</td><td>0.182</td><td>0.048</td><td>0.190</td></tr><tr><td>0.163</td><td>0.000</td><td>0.048</td><td>0.024</td><td>0.118</td><td>0.000</td><td>0.182</td><td>0.161</td><td>0.190</td></tr><tr><td>0.058</td><td>0.097</td><td>0.048</td><td>0.029</td><td>0.118</td><td>0.206</td><td>0.182</td><td>0.161</td><td>0.190</td></tr><tr><td>0.337</td><td>0.285</td><td>0.095</td><td>0.033</td><td>0.000</td><td>0.118</td><td>0.182</td><td>0.161</td><td>0.190</td></tr><tr><td>0.018</td><td>0.000</td><td>0.095</td><td>0.053</td><td>0.000</td><td>0.000</td><td>0.091</td><td>0.113</td><td>0.048</td></tr><tr><td>0.046</td><td>0.000</td><td>0.095</td><td>0.076</td><td>0.000</td><td>0.000</td><td>0.091</td><td>0.065</td><td>0.000</td></tr><tr><td>0.145</td><td>0.164</td><td>0.143</td><td>0.112</td><td>0.000</td><td>0.235</td><td>0.091</td><td>0.161</td><td>0.190</td></tr><tr><td>0.000</td><td>0.097</td><td>0.048</td><td>0.121</td><td>0.000</td><td>0.000</td><td>0.000</td><td>0.065</td><td>0.000</td></tr><tr><td>0.006</td><td>0.065</td><td>0.048</td><td>0.206</td><td>0.000</td><td>0.000</td><td>0.000</td><td>0.065</td><td>0.000</td></tr><tr><td>0.018</td><td>0.065</td><td>0.048</td><td>0.230</td><td>0.118</td><td>0.000</td><td>0.000</td><td>0.000</td><td>0.000</td></tr><tr><td>0.018</td><td>0.065</td><td>0.048</td><td>0.056</td><td>0.118</td><td>0.000</td><td>0.000</td><td>0.000</td><td>0.000</td></tr></table>

# （4)通过公式（2.9）计算各指标熵值。

$\mathrm { ~ h ~ } = \ [ 0 . 7 5 1$ 0.754 0.946 0.807 0.663 0.681 0.683 0.758

0.622]

（5) 通过公式（2.10）计算差异系数。$\mathrm { ~ g ~ } = \ [ 0 . 2 4 9$ 0.246 0.054 0.193 0.337 0.319 0.317 0.2420.378]

（6) 通过公式（2.11）计算各指标权重。

$\mathrm { ~ w ~ } = \ [ 0 . \ 1 0 7$ 0.105 0.023 0.083 0.144 0.137 0.136 0.104   
0.162]

（7) 通过公式（2.12）确定加权归一化矩阵，见表 4.11。

表 4.11 加权归一化矩阵  

<table><tr><td>0.006</td><td>0.000</td><td>0.000</td><td>0.000</td><td>0.144</td><td>0.068</td><td>0.000</td><td>0.000</td><td>0.000</td></tr><tr><td>0.004</td><td>0.012</td><td>0.008</td><td>0.002</td><td>0.039</td><td>0.000</td><td>0.000</td><td>0.000</td><td>0.000</td></tr><tr><td>0.000</td><td>0.012</td><td>0.008</td><td>0.001</td><td>0.000</td><td>0.068</td><td>0.000</td><td>0.000</td><td>0.000</td></tr><tr><td>0.009</td><td>0.000</td><td>0.008</td><td>0.005</td><td>0.052</td><td>0.068</td><td>0.000</td><td>0.000</td><td>0.000</td></tr><tr><td>0.024</td><td>0.000</td><td>0.008</td><td>0.006</td><td>0.000</td><td>0.051</td><td>0.000</td><td>0.000</td><td>0.000</td></tr><tr><td>0.018</td><td>0.036</td><td>0.015</td><td>0.007</td><td>0.000</td><td>0.000</td><td>0.136</td><td>0.031</td><td>0.162</td></tr><tr><td>0.051</td><td>0.000</td><td>0.008</td><td>0.009</td><td>0.052</td><td>0.000</td><td>0.136</td><td>0.104</td><td>0.162</td></tr><tr><td>0.018</td><td>0.036</td><td>0.008</td><td>0.010</td><td>0.052</td><td>0.120</td><td>0.136</td><td>0.104</td><td>0.162</td></tr><tr><td>0.107</td><td>0.105</td><td>0.015</td><td>0.012</td><td>0.000</td><td>0.068</td><td>0.136</td><td>0.104</td><td>0.162</td></tr><tr><td>0.006</td><td>0.000</td><td>0.015</td><td>0.019</td><td>0.000</td><td>0.000</td><td>0.068</td><td>0.073</td><td>0.040</td></tr><tr><td>0.015</td><td>0.000</td><td>0.015</td><td>0.027</td><td>0.000</td><td>0.000</td><td>0.068</td><td>0.042</td><td>0.000</td></tr><tr><td>0.046</td><td>0.061</td><td>0.023</td><td>0.040</td><td>0.000</td><td>0.137</td><td>0.068</td><td>0.104</td><td>0.162</td></tr><tr><td>0.000</td><td>0.036</td><td>0.008</td><td>0.043</td><td>0.000</td><td>0.000</td><td>0.000</td><td>0.042</td><td>0.000</td></tr><tr><td>0.002</td><td>0.024</td><td>0.008</td><td>0.074</td><td>0.000</td><td>0.000</td><td>0.000</td><td>0.042</td><td>0.000</td></tr><tr><td>0.006</td><td>0.024</td><td>0.008</td><td>0.083</td><td>0.052</td><td>0.000</td><td>0.000</td><td>0.000</td><td>0.000</td></tr><tr><td>0.006</td><td>0.024</td><td>0.008</td><td>0.020</td><td>0.052</td><td>0.000</td><td>0.000</td><td>0.000</td><td>0.000</td></tr></table>

（8) 通过公式（2.13）和公式（2.14）计算，分别得出正理想解和负理想解。

Z+ = [0.107 0.105 0.023 0.083 0.144 0.137 0.136 0.104

# 0.162]

$$
\mathrm { z ^ { - } } ~ = [ 0 , ~ 0 , ~ 0 , ~ 0 , ~ 0 , ~ 0 , ~ 0 , ~ 0 , ~ 0 , ~ 0 , ~ 0 , ~ 0 ]
$$

（9)通过公式（2.15）和公式（2.16），然后计算的出欧几里得间距。

表 4.12 关键链各活动欧几里得间距  

<table><tr><td>活动序号</td><td>Di+ Di-</td></tr><tr><td>1</td><td>0.298 0.16</td></tr><tr><td>2個</td><td>0.333 0.042</td></tr><tr><td>3</td><td>0.328 0.07</td></tr><tr><td>5</td><td>0.328 0.057</td></tr><tr><td>7號</td><td>0.217 0.247</td></tr><tr><td>9號</td><td>0.175 0.288</td></tr><tr><td>10</td><td>0.292 0.11</td></tr><tr><td>13</td><td>0.326 0.07</td></tr><tr><td>14</td><td>0.325 0.089</td></tr><tr><td>15</td><td>0.316 0.101</td></tr><tr><td>16</td><td>0.322 0.062</td></tr></table>

表 4.13 非关键链 I 各活动欧几里得间距  

<table><tr><td>活动序号</td><td>Di+</td><td>Di-</td></tr><tr><td>4號</td><td>0.309</td><td>0.087</td></tr><tr><td>11</td><td>0.311</td><td>0.087</td></tr><tr><td>12</td><td>0.181</td><td>0.261</td></tr></table>

表 4.14 非关键链 II 各活动欧几里得间距  

<table><tr><td>活动序号</td><td>Di+</td><td>Di-</td></tr><tr><td>6個</td><td>0. 251</td><td>0.218</td></tr><tr><td>8</td><td>0.164</td><td>0.272</td></tr></table>

10）通过公式（2.17）计算各活动的不确定性系数。

关键链各活动不确定性系数，见表 4.15。非关键链各活动不确定性系数，见表 4.12-4.14。

表 4.15 关键链各活动的不确定性系数  

<table><tr><td>活动序号</td><td>Ci</td></tr><tr><td>1</td><td>0.349</td></tr><tr><td>2個</td><td>0.112</td></tr><tr><td>3個</td><td>0.175</td></tr><tr><td>5</td><td>0.149</td></tr><tr><td>7</td><td>0.532</td></tr><tr><td>8號</td><td>0. 624</td></tr><tr><td>9個</td><td>0. 622</td></tr><tr><td>10</td><td>0. 274</td></tr><tr><td>13</td><td>0.178</td></tr><tr><td>14</td><td>0.214</td></tr><tr><td>15</td><td>0.243</td></tr><tr><td>16</td><td>0.161</td></tr></table>

# 非关键链 1 上作业的不确定系数如表 4.16 所示。

表 4.16 非关键链 1 各活动的不确定性系数  

<table><tr><td>活动序号</td><td>Ci</td></tr><tr><td>4號</td><td>0. 220</td></tr><tr><td>11</td><td>0.218</td></tr><tr><td>12</td><td>0.590</td></tr></table>

# 非关键链 2 上活动的不确定系数如表 4.17 所示。

表 4.17 非关键链 2 各活动的不确定性系数  

<table><tr><td>活动序号</td><td>Ci</td></tr><tr><td>6個</td><td>0.464</td></tr><tr><td>8號</td><td>0.624</td></tr></table>

# 4.2.2.4 缓冲时间计算

通过公式（2.20）计算得到各链路上活动的安全时间，关键链与非关键链中各活动安全时间见表 $4 , 1 8 { - } 4 . 2 0$ 。

表 4.18 关键链各活动安全时间  

<table><tr><td>活动序号</td><td>预估时间/天</td><td>不确定性因素值</td><td>安全时间取整</td></tr><tr><td>1</td><td>5</td><td>0.349</td><td>2</td></tr><tr><td>2個</td><td>4</td><td>0.112</td><td>1</td></tr><tr><td>3</td><td>2個</td><td>0.175</td><td>1</td></tr><tr><td>5</td><td>15</td><td>0.149</td><td>3</td></tr><tr><td>6號</td><td>12</td><td>0.464</td><td>6號</td></tr><tr><td>7</td><td>30</td><td>0.532</td><td>16</td></tr><tr><td>8號</td><td>12</td><td>0. 624</td><td>8號</td></tr><tr><td>9個</td><td>60</td><td>0. 622</td><td>18</td></tr><tr><td>10</td><td>5</td><td>0.274</td><td>2</td></tr><tr><td>13</td><td>2</td><td>0.178</td><td>1</td></tr><tr><td>14</td><td>3個</td><td>0.214</td><td>1</td></tr><tr><td>15</td><td>5</td><td>0.243</td><td>2個</td></tr><tr><td>16</td><td>5</td><td>0.161</td><td>1</td></tr></table>

表 4.19 非关键链 I 各活动安全时间  

<table><tr><td>活动序号</td><td>预估时间/天</td><td>不确定性因素值</td><td>安全时间取整</td></tr><tr><td>4號</td><td>7號</td><td>0. 220</td><td>2</td></tr><tr><td>11</td><td>10</td><td>0.218</td><td>3</td></tr><tr><td>12</td><td>27</td><td>0.590</td><td>16</td></tr></table>

表 4.20 非关键链 II 各活动安全时间  

<table><tr><td>活动序号</td><td>预估时间/天</td><td>不确定性因素值</td><td>安全时间取整</td></tr><tr><td>6號</td><td>12</td><td>0.464</td><td>6號</td></tr><tr><td>8號</td><td>12</td><td>0. 624</td><td>8</td></tr></table>

对于每个活动的安全时间，由公式（2.21）和公式（2.22）求出关键链和非关键链缓冲时间，将它们置于各链路末端，以缓冲可能造成项目延期的风险因素。如表4.21所示

表 4.21 各链路缓冲时间  

<table><tr><td>缓冲名称</td><td>缓冲时间</td></tr><tr><td>PB</td><td>23</td></tr><tr><td>FB1</td><td>16</td></tr><tr><td>FB2</td><td>13</td></tr></table>

设置缓冲后，本项目的总工期为 $1 0 0 \substack { + 2 3 } = 1 2 3$ 天。

# 4.3 软件项目进度管理控制风险因素分析

# 4.3.1 风险因素的优先级划分

BoeHm的软件风险管理的基本思路是：将风险的实施分成两大阶段：一是风险评估，二是风险控制。风险控制是指在风险管理过程中运用集体决策分析以及风险影响分析，并借助成本效益分析，使用风险控制杠杆。风险评估则主要包括三个方面，分别为风险确认、风险分析和风险优先级排序。

依据Boehm理论，通过检查列表的方式确定分线因素，并根据风险发生的可能程度以及可能产生的影响大小，对风险进行分析打分，最后依据打分对风险因素进行优先级别的划分。

以途虎养车门店系统为例，进行详细大的说明，具体步骤如下。

Boehm 模型的风险影响公式为:

$$
R E = P \times L
$$

其中，P 是风险发生的可能程度大小； $\mathrm { L }$ 是风险可能造成的损失，即权重，RE 是风险产生的影响。

# 4.3.2 风险因素的量化结果

风险的权重值即 L 的累加值为 100。

首先，通过检查列表方式找出可能会对进度管理造成风险的因素，并给风险因素中的每一项进行P和L值打分。打分小组由项目负责人和技术总监构成。

表 4-22 门店系统进度管理风险因素量化结果  

<table><tr><td rowspan="2">风险因素</td><td rowspan="2">低（0.0- 0.3)</td><td rowspan="2">中（0.4- 0.6)</td><td rowspan="2"></td><td rowspan="2">P</td><td rowspan="2">L</td></tr><tr><td>高 (0.7-1.0)</td></tr><tr><td>项目管理</td><td>相对简单的 项目，实施</td><td>中等项目复 杂度，实施</td><td>大型项目。实施周期</td><td>0.410</td><td></td></tr><tr><td rowspan="2"></td><td>周期比较短</td><td>周期在一个 月内</td><td>一般在三个月以上</td><td></td><td></td></tr><tr><td>对于硬件没</td><td>需要依赖一</td><td>对于硬件有专业的要</td><td>0.210</td><td></td></tr><tr><td>硬件配置</td><td>有要求</td><td>部分硬件</td><td>求</td><td></td><td></td></tr><tr><td>项目类型</td><td>一般项目</td><td>比较专业的 项目</td><td>非常专业的项目</td><td>0.1</td><td>5</td></tr><tr><td rowspan="4">技术选型</td><td>在公司已有</td><td>已经在成熟</td><td></td><td></td><td></td></tr><tr><td>项目曾使用</td><td>商业项目中</td><td>没有在项目中使用过</td><td>0.515</td><td></td></tr><tr><td>过的技术</td><td>使用过的技</td><td>的技术</td><td></td><td></td></tr><tr><td></td><td>术。</td><td></td><td></td><td></td></tr><tr><td>研发资源</td><td>稳定的技术 人员</td><td>相对稳定的 技术人员</td><td>不确定的技术人员</td><td>0.2</td><td>5</td></tr><tr><td>研发人员</td><td>高级工程师</td><td>高级和中级</td><td></td><td>0.5</td><td>15</td></tr><tr><td>配置</td><td>组成</td><td>工程师组成</td><td>以初级工程师为主</td><td></td><td></td></tr><tr><td>管理人员</td><td>有过大型项</td><td>参与过中大</td><td>仅参与过一些小型项</td><td></td><td></td></tr><tr><td>配置</td><td>目的管理经</td><td>型项目的管</td><td>目的管理</td><td>0.3</td><td>10</td></tr><tr><td></td><td>验</td><td>理</td><td></td><td></td><td></td></tr><tr><td>软件开发</td><td>比较稳定的</td><td>新兴开发语</td><td></td><td></td><td></td></tr><tr><td>语言</td><td>开发语言</td><td>言与成熟开</td><td>以新兴开发语言为主</td><td>0.2</td><td>5</td></tr></table>

<table><tr><td colspan="5">发语言混合</td><td rowspan="2">10</td></tr><tr><td rowspan="3">组件化方 案</td><td>公司项目已</td><td>开发 公司项目完</td><td></td><td rowspan="3"></td></tr><tr><td>经有成熟的</td><td>成了一部分</td><td>没有完成组件化方</td><td>0.2</td></tr><tr><td>组件化方案</td><td>组件化方案</td><td>案，需要从零开发</td><td></td></tr><tr><td rowspan="4">研发设备 可用性</td><td></td><td>开发设备基</td><td rowspan="2">大部分设备需要采</td><td rowspan="4">0.3</td><td rowspan="4">10</td></tr><tr><td>开发设备齐</td><td>本齐全，遇</td></tr><tr><td>全，有一定</td><td>到一些特殊</td><td>购，到位时间不确定</td></tr><tr><td>的灾备</td><td>情况需要采</td><td></td></tr><tr><td rowspan="2">基础建设</td><td>很完备的基</td><td>购</td><td rowspan="2">基本没有基础建设</td><td rowspan="2">0</td><td rowspan="2">5</td></tr><tr><td>础</td><td>部分完备的 基础</td></tr></table>

表4-22 中，各风险因素的P 值按照低（0.0-0.3）、中（0.4-0.6）、较高（0.7-1.0）三档的标准进行打分；11个风险因素的L值，依据其处于项目中的重要程度分为三档，分别是 5分、10分、15分，依次对不同因素填值。

其次，根据风险影响工时计算出RE值。

最后，使用概率加权平均数，将所有的RE加权平均，作为风险因素的变动评估值：

$$
\overline { { R E } } = \sum _ { i = 1 } ^ { n } P _ { i } \times L _ { i } / 1 0 0
$$

在这个例子中，整体风险因素的风险影响值为 $\overline { { R E } } = 0 . 2 8 6$ ，应用于计划进度分析。

# 4.3.3 风险因素的量化分析

根据表中的各风险因素量化结果，对其按照RE值高低进行排序，依据其排序，做各风险因素的量化分析。

表 4-23 门店系统项目的进度管理风险因素量化分析  

<table><tr><td>风险因素 P</td><td>L</td><td>RE</td></tr><tr><td>技术选型 0.5</td><td>15</td><td>7.5</td></tr><tr><td>项目规模</td><td>0.4 10</td><td>4</td></tr><tr><td>研发人员配置</td><td>0.5 15</td><td>7.5</td></tr><tr><td>研发资源</td><td>0.3 10</td><td>0.6</td></tr><tr><td>组件化方案</td><td>0.2 5</td><td>1</td></tr><tr><td>软件开发语言</td><td>0.2 5</td><td>1</td></tr><tr><td>研发设备可用性</td><td>0.2 10</td><td>2</td></tr><tr><td>管理人员配置</td><td>0.3 10</td><td>3</td></tr><tr><td>硬件配置</td><td>0.2 10</td><td>2</td></tr><tr><td>项目类型</td><td>0.1 5</td><td>0.5</td></tr><tr><td>基础建设</td><td>O 5</td><td></td></tr></table>

从表 4-23 中可以看出，门店系统项目的影响项目风险的 11 个要素中，首要解决的风险因素是技术路线，其次是研发人员经验，这对于进度管理的影响程度最重要，应当最优先安排处理。

门店系统项目的在进度管理上的风险要素，其优先级按以下顺序排列：技术选型 > 研发人员配置 > 项目规模 > 管理人员配置 > 硬件配置 > 研发设备可用性 > 组件化方案 > 软件开发语言 > 研发资源 > 项目类型 > 基础建设。

根据表 4-23 我们可以发现，基础建设对该项目不产生项目风险，其 P 值为 0，计算出 RE 值为 0，从而优先级最低，可以不考虑这个风险要素。

# 4.4 本章小结

本章根据软件项目管理的特征，采用关键链法中的乐观时间估算法对途虎养车软件项目进度计划进化了优化，通过 WBS 任务分解，确定关键路径，根据任务特征以及项目的不确定性指标计算出各链路的缓冲时间，对影响项目进度的11个风险因素进行了量化分析，具体的工作如下：

首先，对项目的人员以及负责的任务进行WBS分解，分配对应人员的工作任务计划，并通过任务之间的依赖关系，得到任务的紧前紧后关系，制作项目任务分解表。解决资源冲突后制得项目的网络进度计划图，确定关键路径就是途虎养车项目的一条关键链。

其次，使用关键链法预估各任务的乐观完成时间，针对项目进度的不确定性找出定量的四个指标和定性的五个指标，使用熵权法得到不确定性指标的基础数据，并通过TOPSIS法计算各活动的不确定性系数，先得到链路的不确定性指标值，再得到项目特征的不确定性值，然后通过对标准化矩阵进行归一化处理，最后计算不确定性因素的熵值以及差异系数，利用根差法来计算各链路的缓冲时间。

最后，为了对引起项目延期风险的因素进行定量分析，运用列举检查列表的方式对影响项目进度控制的 11 个风险因素的可能性和风险产生后的影响权重指标进行加权平均得出每个风险因素的概率值，依据风险因素的概率值得出优先级顺序，以上指标表明了技术选型、研发人员配置和项目规模是途虎养车项目最需要关注的三个风险因素的观点，为项目经理调控资源提供参考依据。

# 第 5 章 软件项目进度控制实施及评价

# 5.1 软件项目进度控制实施过程

# 5.1.1 软件项目实施前的进度控制

安全经济学中提到事前控制的重要性，与事后弥补相比，事前控制的更加重要，所以在事后的弥补投入带来的收益和事前投入的收益相比，两者的收益差别非常巨大。因为需要在项目执行前期对项目的风险进行预测以及规避，所以事前控制是一种前瞻性的风险控制，在这个管理过程中，对项目产生影响的包含了因素、技术、风险和制度这四个方面。

# 5.1.1.1 因素控制

软件项目进度控制受主客观两方面的制约，通常有两种，一种是人为因素，另一种是由软件技术、硬件设备、企业资金链组成的客观因素。在企业的经营活动中，人力资源占主导地位，对公司的发展起着举足轻重的作用，其他都是辅助作用，这些资源也都是依附于开发人员的。

# （1)人为因素

因为门店项目关系到公司的战略发展，是企业目前的一个重大变革机会，因此它的重要性在公司层面是非常重视的。公司投入了经验丰富的开发和管理人员投入到该项目中，公司管理层也是十分关注改项目的动态，把控项目进度，协调解决项目过程遇到的问题。

# （2）程序和技术因素

途虎养车成立于 2011 年，总部位于上海市，该公司在汽车后市场领域深耕多年，技术力量雄厚，具有良好的信誉和形象。超过2万多家汽修门店在途虎养车的门店管理方案中获益。途虎养车的门店管理软件被很多线下的门店使用检验过，具有稳健性以及适用性，即使偶尔会出现系统bug，也都可以通过技术人员迅速解决修复。而在技术选型上，门店管理软件采用了非常多先进的技术。

# 5.1.1.2 计划控制

计划是项目管理的关键，一个好的计划是项目顺利实施的有效保障，是控制活动的依据，是组织协调项目的前提。一般情况下，项目的计划控制主要包括以下三个方面。

（1）编制项目实施总进度计划

依据项目的时间安排以及公司的战略发展方向，制定总体进度计划，进而宏观把控项目安排，保证项目可以顺利，保质保量的交付。

（2）监控有效的进度计划

通过对途虎养车项目进度的监控，如果发现有对项目进度产生影响的地方，尽快安排对应的人员解决问题，进而在控制成本以及满足客户需求的前提下保证整个计划的顺利完成。

（3）审核实施单位提交的实施方案

依据实施单位提供的相关方案，来对项目实施过程中的细节有清晰的了解。

# 5.1.1.3 风险控制

风险是指可能会对项目的进度、质量等产生不良影响的事件或者结果。因为在执行过程中，会发生诸多变化，所以风险在很多项目中都普遍存在，在项目过程中，应当正确认识风险，并且在力所能及的范围内，去控制风险，或者降低风险所产生的影响。

在项目启动前，需要依据以往的经验，或者将团队相关人员召集在一起集思广益，头脑风暴，通过对潜在的风险进行归纳和分析，并制定出一套完整的风险预防系统来防止这种风险的发生，在项目准备前期就将这些风险产生的影响降到最低，进而能够在控制成本，保证质量的前提下，保障项目的顺利运行。

同时，在项目执行过程中，应该正确认识风险，增强防范风险的意识，即使遇到没有预估到的风险，也能够从容应对。

# 5.1.2 软件项目实施中的进度控制

在项目前期制定计划的过程中，就对每一阶段的目标进行了清晰的定义，但是在实际执行过程中不可能完全按照计划来落实，总会因为各种各样的不确定因素出现变动，因此，在具体执行过程中需要及时发现这些因素，并针对性的解决，进而保障整体的进度安排。

# 5.1.2.1 进度控制的主要工作

事前控制主要集中在项目准备前期，而事中控制则主要集中在项目管理过程中，因为实际过程中遇到的问题多种多样，所以事中控制也最为繁琐，具有承上启下的作用。一般事中控制主要包括两个方面，一个是加强对于日常工作的监督检查，及时发现问题，并有针对性的解决，另一个是及时收集项目过程中的数据，通过分析数据，来为进一步的决策提供科学的依据。事中控制主要包括以下五个工作内容：

（1）明确而固定的检查点计划

一个完整的项目往往需要较长的时间，可以依据项目的工期划分为日检查、周检查、月检查、季度检查、里程碑检查以及年检查等。因为日检查是最基本的检查，所以项目进度管理专员应当如实记录，一旦发现问题，应当及时记录以及上报反馈。

小组组会在每天下午四点之前召开，会上组员汇报个人负责工作的完成情况及第二天的工作计划，由组长和实施顾问对其进行评价，并汇总整个组内的计划完成情况，在每日下午五点的日例会中进行汇报。

（2）按合同要求及时进行项目阶段工作的验收

每个阶段的完成标志以及交付物都在项目都在项目章程中进行了明确的规定，在每个阶段结束后，需要进行阶段性的验收。在实施方提交交付物以及相关交付文件后，途虎养车项目组需要在 5 个工作日内进行交付物验收，一旦发现交付物存在问题，则应该及时将修改意见返回给实施组，待他们修正后再次交付途虎养车项目组，如果交付物还存在问题，则继续返回修改意见，让实施组修正，直至交付物无误，从而有效保证每个阶段的交付物的质量。

（3） 项目进度的动态管理

当实际进度与计划进度之间有偏差时，应分析造成这种情况的原因，并有针对性的进行计划调整，尽可能的消除这种偏差产生的影响，保证进度可以按照最初的目标进行。

比如，在项目执行过程中需要完成“客户主数据收集”，即收集100个门店的数据信息，但是在9月5 号的周检查过程中发现，当前只完成了进度的$9 5 \%$ ，剩余5家门店的数据并未收集到，经过检查日志获知，有两家门店的技师没有对数据收集引起重视，并未收集：另外三个门店则是涉及新、老门店前台的人事变动，工作交接，导致数据收集工作延后。在渠道经理和相关线下门店协调后，双方达成一致，于9 月6号加班完成数据收集工作，保证下一周工作的顺利开展。

（4） 组织现场协调会

组织现场会议，集中解决小组组内无法解决的问题，同时收集上次协调会上相关问题的解决情况。

5）定期向一把手报告有关项目的进度情况

通常情况下，项目经理需要每周向自己的上级领导汇报上周的进度安排，项目执行情况，遇到的问题，解决的措施，以及待解决的问题等。

# 5.1.2.3 沟通交流控制

在项目执行过程中，沟通无处不在，小组成员之间需要沟通，上下级之间需要沟通，沟通可以是书面的也可以是口头的，可以是面对面的也可以借助一些媒介，比如会议、电话等，即可以是非正式的也可以是正式的沟通。

# （1）人员沟通

可以通过语言或非语言，比如身体语言，肯定的身体语言比如点头、微笑、手势以及前倾等，表示否定的比如皱眉、招头、乱写乱画、心不在焉、无精打采、打哈欠或者东张西望等。

项目经理做为项目沟通管理的重要人员，需要具有很好的沟通技巧，能够和上级领导、项目内部成员以及实施方进行及时有效的沟通。

在项目执行过程中，项目组成员进行非正式的沟通可以加深对于彼此的了解，增加彼此之间的感情，进而有效提升团队凝聚力，提高团队人员之间的默契程度，提升团队效率。

# （2）会议沟通

定期召开项目会议，向项目相关人员介绍项目的有关进展，以及遇到的有关问题，通过对比当前项目进度和计划进度的偏差，找到问题的关键所在，并针对这一问题，提出合适的解决措施，例如缩短某一方面的进度安排，保障整体进度。因为项目的会议形式以及种类多种多样，会议的目的也有所差异。

为了保证项目会议顺利进行以及后期项目的整体材料把控，对项目会议的会议记要进行了规范化处理，明确规定项目会议纪要中应该包括参加人员、组别、地点、时间、议题、议程、预期目标、会议进程记录、准备交付的文档说明、会议评估、小结以及下次会议时间，除此之外，还应标明本会议纪要报送以及抄送的范围。

（3）报告

报告主要分为书面报告以及口头报告两种。项目经理需要每天向上级领导口头汇报相关工作内容，每一周或者每个阶段结束后，向领导提报书面报告，对这一周或者这一阶段的工作进行一个总结，主要包括：工作完成情况、己解决的问题、本阶段发现的问题或潜在的问题情况、计划采取的解决措施、下一阶段的工作计划。如途虎养车公司 SASS项目某一阶段的进度报告等文件。

# 5.2 软件项目进度管理评价

# 5.2.1 蒙特卡洛模拟工期

蒙特卡罗（Monte—Carlo)方法，是一种常用的模拟方法。首先对每一个随机变量进行模拟抽样，然后将抽样后的数据带入到模型中，得到一个函数值，以此迭代，通过多次抽样，即可获得多个函数值，进而获得一组数据，进而就可以依据这组数据判断函数的概率分布特点，得到函数的期望、方差以及分布曲线。它的基本原理主要是：假设函数 $\mathrm { Y = f }$ （X1,X2.X3…)，每一个变量的概率分布已知，但是函数自身的概率分布未知，或者因为函数的关系式较为复杂，很难通过公式推导获知。通过模拟抽样，获得多组（X1,X2.X3…)的值，其中，X 可以表示各种不确定的因素，以此获得多组 Y 的值，通过模拟 1000次、10000次等，就可以获得Y的一组抽象数据曲线。依据大数定理，当模拟数量足够多时，模拟的情况就会和实际情况高度相似。同时，因为可以对复杂的函数进行模拟，所以当不能用公式很好的推导概率分布特征时，蒙特卡洛模拟具有巨大的优势。

本章假设WBS因素都服从标准正态分布，它们的均值、标准差、乐观值、悲观值以及最可能的值如下表所示，并且整个项目是相对完整的，所以要素的工期之和构成了整个项目工期。选用蒙特卡洛模拟方法定量的分析工期进度风险，这里借助了Excel来模拟项目实施，实施思路是：

（1）输入每一个WBS要素的工期值。  
（2）累加每个 WBS 要素的工期值获得整个项目的工期值。  
（3）重复（1）（2）500次，得到500个项目总工期的模拟值。

第四步：通过这 500 次的模拟总工期数值进行统计分析，得到项目总工期估计的概率分布。

表 5-5 三点法预估工期表  

<table><tr><td>编号</td><td>任务</td><td>乐观</td><td>最可能</td><td>悲观</td><td>均值</td><td>标准差</td></tr><tr><td>1</td><td>需求调研</td><td>4</td><td>7</td><td>9號</td><td>6.67</td><td>2.5</td></tr><tr><td>2號</td><td>产品设计</td><td>4</td><td>5</td><td>6號</td><td>5.00</td><td>1</td></tr><tr><td>3</td><td>方案评审</td><td>2</td><td>2</td><td>3</td><td>2.33</td><td>0.5</td></tr><tr><td>4</td><td>门店数据采 集</td><td>7</td><td>10</td><td>12</td><td>9.67</td><td>2.5</td></tr><tr><td>7a</td><td>服务端开发</td><td>12</td><td>16</td><td>20</td><td>16.00</td><td>4</td></tr><tr><td>9b</td><td>APP开发</td><td>8號</td><td>11</td><td>15</td><td>11. 33</td><td>3.5</td></tr><tr><td>10</td><td>联调</td><td>6號</td><td>7</td><td>10</td><td>7. 67</td><td>2</td></tr><tr><td>11</td><td>接口测试</td><td>10</td><td>10</td><td>15</td><td>11. 67</td><td>2.5</td></tr><tr><td>12</td><td>功能测试</td><td>25</td><td>30</td><td>35</td><td>30.00</td><td>5</td></tr><tr><td>13</td><td>服务器部署</td><td>2</td><td>3</td><td>5</td><td>3.33</td><td>1.5</td></tr><tr><td>14</td><td>实施</td><td>3</td><td>3</td><td>5</td><td>3. 67</td><td>1</td></tr></table>

<table><tr><td>15</td><td>培训</td><td>5</td><td>7</td><td>10</td><td>7.33</td><td>2.5</td></tr><tr><td>16</td><td>上线</td><td>4</td><td>5</td><td>6</td><td>5.00</td><td>1</td></tr></table>

通过蒙特卡洛模拟该项目的实际工期500次，从图4-3中可以看到，预估工期频次符合正太分布，预估工期在105天到120天之间，与研究结论相符。

![](images/d8ccea50a3e374e877ff2ef3cc24a62df92e90bd79a9df2a0a06804502218aae.jpg)  
图 4-4 预估工期频次图

# 5.2.2 缓冲区消耗情况

软件项目开发依托于关键链的项目进度管理方式，项目的时间工期是110天，预估工期是120天，开始时间是 $2 0 2 0 / 8 / 1$ ，完成日期是 $2 0 2 0 / 1 0 / 2 0$ 。严格遵守业务部门设定的交付日期，保质保量的如期交付。在此情况下，结合项目实际情况采用了关键链技术和相应的项目管理制度对本项目进行进度管理，减少了项目交付时间，解决了项目如期交付的难题。

表5-2 是项目经理在10月初对于项目使用缓冲区情况的统计，积极做好应对错误，把控好项目进度过程。

表 5-2 途虎养车软件开发项目 10 月初缓冲时间消耗表  

<table><tr><td>缓冲名称</td><td>缓冲时间</td><td>缓冲消耗</td><td>活动进度</td></tr><tr><td></td><td></td><td></td><td></td></tr></table>

<table><tr><td>PB</td><td>23</td><td>73%</td><td>70%</td></tr><tr><td>FB1</td><td>16</td><td>78%</td><td>100%</td></tr><tr><td>FB2</td><td>13</td><td>75%</td><td>100%</td></tr></table>

由表可以看到项目缓冲监控过程在 10月初关键链上的缓冲出现了一次危险区，通过开会调整等方式将缓冲区情况恢复到安全区内，项目进度在可控范围内，没有延期的问题。

基于关键链的项目进度管理对于项目的管理主要体现以下几方面：

（1) 通过 WPS切分项目任务，将宏观的大的层面的任务，划分为小的容易实现的任务，便于软件开发工程师更好的理解需求，实现功能，同时降低了互相之间的沟通成本，极大的提升了效率。

（2）对项目执行过程中的各个任务进行优先级排序，在资源有限的情况下优先处理优先级别高的任务，并且应该避免在同一时间段同一资源需要同时服务于 2 个及以上的项目任务的情况，保证资源的高效利用。

（3）保证团队成员能够清楚的辨析非关键链路径和关键链路径，将资源尽可能分配到关键任务上，减少关键与非关键之间的冲突，进而保障项目的顺利运行。

（4）关键链技术和非关键链技术相辅相成，关键链技术不仅可以增加任务预估时间的准确性和合理性，还可以降低团队人员对项目产生的影响，非关键技术在合适的时间被启动，可以很好的避免资源浪费，能够有效推进项目运行。

（5）在项目执行过程中，因为会受到很多不确定因素的影响，所以使用关键链技术对关键任务的时间进行了一定的缓冲放宽，于此同时，为了不对整体项目产生影响，未对各个分支任务设置安全时间，这样在一定程度上可以把控时间安排，又充分考虑了资源和任务之间的相互关系，使得整个项目有条不紊的进行，极大的降低了团队所承担的延期风险，同时大大提升了效率。

（6）为了避免多个分支任务同一时间使用同一资源，在项目开始之前就安排好每一个资源的分配以及排期。因为已经确定每一个资源在每一天的归属情况，所以极大的避免了各个分支人物之间的无序竞争，达到了资源以及时间利用的最大化，但是如果采用传统的模式，这一现象将不可避免。

（7）因为关键链技术明确了非关键路径以及关键路径，使得项目执行更加高效，所以大大缩短了项目的工期，如上文所述，采用传统方式的预期工期是133天，并且实际远远高于133 天，但是使用关键链技术的预期工期仅有120天，并且因为执行高效，实际工期比预估工期还缩短了10天，大大缩短了项目工期。

# 5.3 项目进度管理的保障措施

# 5.3.1 定期组织项目管理培训

从途虎养车的调查可以发现，在项目研发过程中缺乏标准化的工作程序，得出了两个基本认识，第一，对于技术人员的培训是必不可少的，有利于保证公司的整体技术水平。第二，可以让员工产生对公司的归属感，定期组织一些基本的管理知识屁培训，可以让公司的项目管理更加科学。对于公司而言，一种完善的项目管理体系能够为公司输送高素质的项目管理人员，降低项目管理中出现的意外，提高项目交付效率。

# 5.3.2 规范项目质量管理

以途虎养车项目为参照，结合门店管理系统项目的实际情况，制定交付标准以及文字材料。首先，项目经理与用户方进行对接，确定用户方的具体需求，然后结合途虎养车项目交付物的标准，制定适合门店管理系统项目的标准。依据这一标准，对交付物进行切分，形成一个交付清单，明确每一阶段的交付标准。

在项目执行过程中，需要进行内部以及外部两方的评审，只有当内外部都通过时，项目才会推进到下一个阶段。目前项目交付主要包括数据中台系统、可视化显示子系统、产品制作子系统三个部分，每一个系统在编码测试阶段都要进行严格测试，在需求阶段以及设计阶段都要进行独立评审。

# 5.3.3 进度管理改进优化建议

通过分析关键链技术在途虎养车项目中管理的优势，提出了以下优化建议：

（1）合理安排公司的内部资源，使用资源池模式达到资源利用的最大化。比如，项目负责人将负责相同任务的员工放在同一资源池中，比如研发人员放在研发部资源池、测试人员放在测试部资源池、项目负责人放在项目资源池、产品设计人员放在产品部资源池便于集中调配以及管理。最后在项目执行过程中，依据项目需求，对这些资源池进行统一调配，统一管理。

（2）减少公司的管理层级，采用管理加作业的扁平化管理模式，提升公司的管理模式，降低管理成本。同时因为其管理层级较少，所以当外界发生变化时，可以及时响应以及应对，也更容易给员工提供好的发展平台，增进公司管理层与基层之间的关系。

随着时代的不断发展，越来越多的企业开始使用扁平化管理，扁平化管理有如下的特点： $\textcircled{1}$ 企业的扁平化管理是按工作的流程对企业内部员工进行划分。因为都是以工作流程开展工作，所以先前设立的职能部门也随之消失。 $\textcircled{2}$ 因为扁平化管理模式使得员工可以直接与管理层进行沟通，避免层层审批导致响应不及时情况的出现，提高了工作效率。 $\textcircled{3}$ 扁平化管理模式大大缩短了沟通的成本，提升了沟通的效率，软件开发工程师可以与客户进行沟通，了解他们的真实需求，上下级之间也可以面对面沟通，避免信息传达的延误。 $\textcircled{4}$ 企业实施扁平化管理在一定程度也是一种有针对性的目标管理。公司领导下放一部分自主决策权给企业员工，要求其以团队为出发点，为自己做出的关于团队的决定负责到底。

（3）对公司员工进行绩效考核。员工自身在目标责任书中设立合适的目标，然后管理层在一定时间点对这一目标的完成情况进行考核，进而可以整体把握项目的进度，同时对能够认真完成目标，或者超额完成目标的员工给予一定的奖励，从而大大增加员工工作的积极性，当员工的目标完成情况不佳时，及时了解产生这种情况的原因，并给予相应的解决措施。

# 5.4 本章小结

本章在上一章的基础上，对途虎养车门店管理系统的开发流程实施过程中的进度控制进行总结，分为实施前的进度控制和实施后的进度控制。同时使用蒙特卡洛对项目实施工期进行模拟，对缓冲区消耗情况进行监控。提出了一些进度管理的保障措施。具体的工作有：

首先，介绍了项目实施前的进度控制方法，主要有因素控制、计划控制、风险控制三个方面入手。在实施过程中，使用了一些固定的检查计划、间断性的工作验收、进度的动态管理以及组织协调会等方式来确保项目进度，同时强调了沟通对于项目进度控制的重要性。

然后，根据三点法的预估工期，借助蒙特卡洛法来模拟 500 次的工期实施，得到一个工期频次图，得出理论上的项目实施时间预估，预估时间都在计划的时间内，验证了项目的可行性。并且通过实际项目消耗缓冲区的情况加以佐证。

最后，对于进度管理的保障措施提出了一些建议，主要包括组织项目管理培训、规范项目质量管理，对于人员资源池和组织内管理模式进行优化，更好地使用人才，增进组织内的沟通。

# 第 6 章 结论与展望

# 6.1 主要结论

本文通过系统性学习关键链技术的内容、流程以及技术优势，以及在项目管理中的应用，搭建了基于关键链技术的项目进度管理模式。并以途虎养车项目为例，分析在项目管理过程中出现的问题，并详细的阐述了关键链技术在缩短项目工期，合理分配关键资源，避免不必要竞争方面的优势，能够有效保证整个项目的顺利推进。

在实际的软件项目开发过程中由于开发人员能力和经验参差不齐，任务分配对项目管理者来说成为了一个难点，项目拆分后各任务模块不够独立，项目经常延期等问题。本文针对上述问题展开途虎养车软件开发流程优化的研究。

（1）针对途虎养车门店管理系统的公司情况、组织架构和团队组成等方面分析了项目进度流程的现状，通过 WBS的方法对项目进行任务分解，通过专家判断来估算确定工期。

（2）通过对门店管理系统的任务分解，罗列了团队内部的人工分工情况，分析了一般影响项目进度的原因，制定项目的任务和人员分配，建立网络计划时间表，对有风险的任务节点进行优化。对项目的进度控制在实施前和实施中严格控制，有效的保证了项目的按时交付。

（3）对门店管理系统的开发流程优化的实施情况进行了归纳和总结，首先从缓冲资源使用的角度进行了分析，其次是对项目进度管理提出了一些建议，最后，提出了一系列的保障措施，有序的开展项目。

运用关键链的相关技术，对途虎养车的软件项目进度管理进行了优化分析，并提出了相应的改进措施。关键链技术的应用极大地缩短了项目的工期，利用关键链技术来找出关键路径和非关键路径，并通过项目实践来验证该方法，使用PERT 法预估的项目工期是131天，而实际只消耗了105天，得出关键链法可以有效保障项目的按时完工。

# 6.2 未来展望

因为企业面临转型升级的需要，而当前企业的管理流程仍存在较多缺陷，具有很大的提升空间，所以项目管理引起了企业管理层的高度重视。通过规范化的项目管理流程，提升企业工作效率迫在眉睫。企业高层通过一定的措施鼓励企业员工不断学习项目管理相关的工作内容，加大公司员工对于项目管理的重视程度，进而便于推动软件项目的顺利开展，并借鉴途虎养车项目模式的先进经验，在项目进度管理过程中运营关键链技术，为企业的数字化转型升级提供良好的理论基础。

!" #\$%&'(  
[1]张旭,刘浩驰.基于 CMMI 的量化管理在项目中的应用与研究[J].电脑与电信,2016（04):62-65.  
[2黄振宇,张文雅.面向关键链的敏捷软件开发项目进度管理研究[J].科技创新与应用,2016（34):281.  
[3]陈李萍.高职科研项目时间管理应用研究[J].价值工程,2016,35（16):201-202.  
[4]范伟达,曾莎洁.资源约束条件下工程项目进度管理方法研究[J].建筑经济,2016,37（10):35-38.  
[5]冯晓兰.基于关键链的T项目进度计划优化分析[J].经济研究导刊,2017（10):176-180.  
[6]马丽.浅谈在敏捷开发中 QA 的质量管理[J].电脑迷,2017（12): $1 2 + 1 5$ .[7]陈赟,张营慧,朱文喜.基于不确定性因素的柔性关键链缓冲设置方法[J].长沙理工大学学报（自然科学版),2017,14（03):61-67.  
[8]李辉山,魏焘.基础设施PPP项目 VFM评价影响因素分析[J].项目管理技术,2017,15（10):47-52.  
[9]吴丽娜.探析计算机软件工程项目管理[J].科技资讯,2017,15（06): $1 3 4 + 1 3 6$ .[10]王雯萱.基于关键链的信息系统项目开发进度风险分析[J].中国新通  
信,2017,19（10):29-31.  
[11]牛媛媛.经济全球化背景下我国电子商务的创新发展研究[J].商业经济研究,2017（19):75-77.  
[12]张静文,乔传卓,刘耕涛.基于鲁棒性的关键链二次资源冲突消除策略[J].管理科学学报,2017,20（03):106-119.  
[13]卢会春.影响软件项目管理的关键因素及管理对策研究[J].中国新通  
信,2018,20（15):142-143.  
[14]徐平.浅谈软件开发团队建设[J].科技经济导刊,2018,26（23): $2 0 2 { + } 2 0 4$ .[15]曾晓雪,和冬梅,谢华.敏捷项目管理方法应用研究[J].石油规划设  
计,2018,29（02):44-47.[16]孙新波,苏钟海.数据赋能驱动制造业企业实现敏捷制造案例研究[J].管理科学,2018,31（05):117-130.  
[17]赵博.“她经济”消费升级背景下垂直电商发展探讨[J].商业经济研究,2019（01):79-82.  
[18]苑学贺,贾冀芳,文建军.基于 CMMI和ISO25000的软件研发质量管理体系设计[J].网络安全技术与应用,2019（02):27-30.  
[19]基于改进关键链方法的MRCPSP 的鲁棒性优化[J]. 田旻,张光军,刘人境.系统工程学报. 2019（02)  
[20]宁方旭.关键链技术在信息系统开发项目进度管理中的运用[J].中阿科技论坛（中英阿文),2020（03):46-47.  
[21]蒋丹,刘永吉.基于模型的敏捷软件架构设计方法[J].电子技术与软件工程,2019（04):31-33.  
[22]芮斌.信息化背景下软件开发项目管理[J].电子技术与软件工程,2019（05):42.  
[23]何桐.微服务架构应用前景研究[J].计算机产品与流通,2019（07):46.[24]李乾源,戴长华.基于JIRA的软件项目细化管理方法研究[J].项目管理技术,2019,17（10):97-100.  
[25]高翔,朱杰媛.持续集成在软件项目管理中的作用[J].机电信息,2019（17):177-178.  
[26]白富强.基于组件的软件开发方法探讨[J].信息技术与信息化,2020  
（10):28-30.  
[27]薛松,向康,李恒滨,张敏.新型项目管理方法的探索与实践[J].管理观察,2019,737（30):36-38.  
[28]陈伟强,李丽媛,陈华静,吴健.基于重大项目进度管理的里程碑控制模型[J].项目管理技术,2020,18（06):40-44.  
[29]黄晓芳.决策分析和决定在军用软件开发中的应用[J].项目管理技  
术,2020,18（10):123-126.  
[30]基于层次分析法的企业多项目优先级排序问题研究[J]. 王梓伍,朱美光.科技与经济. 2020（03)[31]陈国强,王玉伟.建筑工程管理中的进度管理分析[J].散装水泥,2020  
（05):18-19.  
[32]冯海荣.建筑施工项目进度、成本和质量管理的综合优化[J].智能城  
市,2020,6（12):137-138.  
[33]徐之恒.国际工程项目物流进度管理方法研究[J].物流科技,2020,43  
（03):58-60.  
[34]阮红梅. 汇通达公司软件开发质量管理优化研究[D].兰州大学,2020.  
[35]王红丹. 基于关键链技术的 SQXX开发项目进度管理研究[D].山东大  
学,2020.  
[36]基于 Scrum 的敏捷测试研究与应用[J]. 王倩,唐兰文,吴海燕.科技视界.2020（33)  
[37]敏捷开发在信息管理系统设计中的应用研究[J]. 贾勇.电脑知识与技术.2021（19)  
[38]齐太民.基于蒙特卡洛法的石油建设项目投标报价决策分析[J].中国石油和化工标准与质量,2021,41（16):75-76.  
[39]Azar Izmailov,D,A. Project Management Using the Buffers of Time and Resources.2016, 235:189-197.  
[40] Zhenhong Li, Yankui Liu, Guoqing Yang. A new probability model for insuringcritical path problem with heuristic algorithm. 2015, 141:129-135.  
[42] Narjes S, Hamed R. T, Erik D, et al. Determining the timing of project controlpoints using a facility location model and simulation. 2015, 61:69-80.  
[43] Amol S. Resource Constrained Multi-project Scheduling with Priority Rules &Analytic Hierarchy Process. 2014, 69:725-734.  
[44] Adrialdo A, Ana T T T A, Joao B. Agile project management with Scrum. 2017, 10（1):121-142.  
[45]K.A;V.P.E.Improved Whale Optimization with Buffer Setup Time [J]  
International Journal of Innovative Technology and Exploring Engineering,2019[46] Xingyue Zhang,Wenjie Liu.Complex Equipment Remanufacturing ScheduleManagement Based on Multi-Layer Graphic Evaluation and Review TechniqueNetwork and Critical Chain Method[J].IEEE Access.2020

# 致谢

时光荏苒，三年的研究生生涯将告一段落。在这段学习过程中，我需要感谢许多人。

首先，我要感谢我的校内导师王素芬在我撰写论文的过程中给了我很多指导和帮助，论文的完成离不开老师一次又一次耐心的引导。同时我也要感谢我的校外导师王泰琳女士，她同样在我的实践过程中给予我帮助。

其次，我要感谢所有旭日工商管理学院的授课老师，是他们的认真教导，夯实了我的理论基础，为我的论文写作提供了基石。

再次，我要改写校内外盲审的专家教授，是他们的耐心指正，让我的论文一次次的完善。

最后，感谢我的同学和家人，在我的学习过程和生活上给予了很多支持，让我能够顺利的完成论文。