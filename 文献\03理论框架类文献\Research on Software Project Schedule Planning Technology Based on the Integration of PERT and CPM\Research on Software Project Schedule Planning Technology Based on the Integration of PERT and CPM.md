# Research on Software Project Schedule Planning Technology h on Software Project Schedule Planning TecBased on the Integration of PERT and CPM

Yongchang Rena, \* and <PERSON>

a College of Information Science and Technology, Bohai University, Jinzhou, China b School of Physical Education, Bohai University, Jinzhou, China

# Abstract

Make a feasible schedule planning for software project, is the foundation to carry out an orderly software project activity, is the Make a feasible schedule planning for software project, is the foundation to carry out an orderly software project activity, is the key to success of the project. To solve the difficulties of schedule planning, this article studies the science technology and key to success of the project. To solve the difficulties of schedule planning, this article studies the science technology and methods, including four aspects: First, using network chart to show the dependencies between activities, including Precedence methods, including four aspects: First, using network chart to show the dependencies between activities, including Precedence Diagram Method and Arrow Diagram Method; Second, Program Evaluation and Review Technique is used to evaluate the time Diagram Method and Arrow Diagram Method; Second, Program Evaluation and Review Technique is used to evaluate the timeof the project activity; Third, the critical path method of determining the project total duration, including deduce from calculation of the project activity; Third, the critical path method of determining the project total duration, including deduce from calculation formula of active time and determine the critical path; Fourth, duration compression method based on progress compression formula of active time and determine the critical path; Fourth, duration compression method based on progress compression factor method. According to the situation of many technologies and methods, studied the main factors considered by selecting factor method. According to the situation of many technologies and methods, studied the main factors considered by selectingtechniques and methods. Results show that using scientific technology and method to make the schedule planning, will reduce techniques and methods. Results show that using scientific technology and method to mthe workload of planning, improve the accuracy of the plan and have high practical value.

$©$ 2023 The Authors. Published by Elsevier B.V.e workload of planning, improve the accuracy o   
This is an open access article under the CC BY-NC-ND license (https://creativecommons.org/licenses/by-nc-nd/4.0)© 2023 The Authors. Published by ELSEVIER B.V.   
Peer-review under responsibility of the scientific committee of the 3rd International Conference on Machine Learning and This is an open access article under the CC BY-NC-ND license (https://creativecommons.org/licenses/by-nc-nd/4.0) Big Data Analytics for IoT Security and PrivacyPeer-review under responsibility of the scientific c

Keywords: Program Evaluation and Review Technique; Critical Path Method; PERT; CPM; Software Project; Schedule; Compiling Technique

# 1. Introduction

With the rapid development of information technology, software project management has been separated from ftware engineering and has become an important research field, which pays more attention to methods, techniques, software engineering and has become an important research field, which pays more attention to methods, techniques, tools and processes. Software project schedule planning is one of the research directions of software project tools and processes. Software project schedule planning is one of the research directions of software project management [1]. Software project schedule planning, is the schedule protection of software development, is the commitments of developers for customers, is the developer's work instructions, is the basis for tracking check plan, is the basis of software project management. Therefore, making a thorough, concise and accurate software project plan is the key to successfully develop software products. A large software project plan is a complete system consisting of several specific plans, each of which plays a role in a different direction. Among them, schedule plan is the most important plan, because schedule control is the difficulty of software project management, schedule plan is to solve this difficulty and play a role.

Software project schedule planning provides a reasonable basis and feasible work plan process for the operation of software engineering and software project activities management. How to formulate a scientific software project schedule planning is always the problem of software project management personnel. This article studies and formulates the effective scientific methods of software project schedule planning , on the one hand, it provides theoretical basis for researchers to plan software projects; on the other hand, it provides solutions for software project management and implementation, and improves the success rate of software projects.

# 2. Network Chart

Usually using the form of a network chart to show the dependencies between activities. Network chart is the important output of activities sort, showing all project activities, indicating the dependence between activities, indicating how the task will be and in what order. When complete the task lasted estimate, the network chart can represent the time required for tasks and projects, and when activity lasted changes, network chart can also reflect this changes. Monitoring and control project work is both critical and challenging [2]. Traditional monitoring and control methods for projects have been very unsuitable for today's complex software development projects. Many technologies and tools have been produced to adapt to the development of The Times and play an important role in solving the monitoring and control work of projects. Network diagram is one of them. [3]. Commonly network chart method has the following two:

# 2.1. PDM (precedence diagram method)

PDM is also known as node diagram or single-arrow network diagram. The basic element to constitute network chart is node, node means activity (task), arrow means the logic relationship between activities (tasks). There is a logical relationship between tasks in the preceding diagram. The completion of the preceding task causes the start of the subsequent task. If the preceding task cannot be completed, the subsequent task cannot be started. Take a software development project as an example, the whole software life cycle from the beginning to the end is composed of 12 tasks, among which there are intricate relationships, including parallel relationship and serial relationship. Example shown in Fig. 1.

![](images/9f244d2117e5b1bd628f69912e122e2531d4ba3dd1b1309f7ae1ed4b2ff045c3.jpg)  
Fig.1. PDM of software project

# 2.2. ADM (arrow diagram method)

ADM is also known as double-arrow network diagram. Arrow means activity (task), node means incident, and means the end of the previous activity and also the start of the after activity. Example shown in Fig. 2. Dotted line means dummy activity and the successively relations of events, in figure, the event 4 must occur after event 1. In fact, virtual activity does not actually occur, but it cannot be missing in the ADM diagram.

![](images/225a346232d36c95716df884a2470dfa9a0e6a3cb29f9436d6f68d091428f80a.jpg)  
Fig.2. APDM of software project

# 3. PERT (Program Evaluation and Review Technique)

PERT normally used for estimates the time of project activities. PERP estimates the completion time of the activity as follows: [4-6]:

(1) Minimum completion time: All the work is carried out according to the plan, and all the project team members can work closely together, under the circumstance of everything is smooth, the time required to complete a task; (2) Medium-length completion time: In the process of project execution, accidents and risks are inevitable, but these accidents and risks are within the scope of control, will not have a great impact on the total project duration, the project can be completed in the expected time content; (3) Maximum completion time: In the process of project implementation, unfavorable situations often occur, such as staff turnover, the other party does not cooperate with the work, the required resources are short, and the equipment fails. These factors affect the project duration, so that the project can not be completed according to the plan, and the completion time has increased significantly.

Assume three estimates obey $\beta$ distribution, then make the three time estimates into a single time estimate as the expected value of each activity and the computation formula of three time estimation method is:

$$
t _ { i } = { \frac { a _ { i } + 4 m _ { i } + b _ { i } } { 6 } }
$$

Among them: $t _ { i }$ means the duration time of activity $i , \ a _ { i }$ means the optimistic time of activity $i , \ m _ { i }$ means the most likely time of activity i, $b _ { i }$ means the pessimistic time of activity $i .$ . Generally, the two $a _ { i }$ and $b _ { i }$ are estimated by statistical methods.

Three time estimation method calculates by transforming the non-positive-type problem into a positive type problem, using the view of probability theory bias is unavoidable. In order to perform the time deviation analysis (i.e., the distribution of discrete process), we can estimate it with variance.

The Beta distribution can be regarded as a probability distribution of probabilities, which gives the probability of all probabilities occurring when the specific probability is not known. The mean and variance are derived as follows:

Set, $X \sim B e ( a , b )$ , then:

$$
\begin{array} { l } { { f ( x ) { = } \displaystyle \frac { \Gamma \big ( a + b \big ) } { \Gamma \big ( a \big ) \Gamma \big ( b \big ) } x ^ { a - 1 } \big ( 1 - x \big ) ^ { b - 1 } } } \\ { { { = } \displaystyle \frac { 1 } { B \big ( a , b \big ) } x ^ { a - 1 } \big ( 1 - x \big ) ^ { b - 1 } ~ , 0 { \leq } x { \leq } 1 } } \end{array}
$$

In the above formula,      a b B a b   , 

$$
B { \bigl ( } a , b { \bigr ) } = \int _ { 0 } ^ { 1 } x ^ { a - 1 } { \bigl ( } 1 - x { \bigr ) } ^ { b - 1 } d x
$$

The mean value is calculated as follows:

$$
\begin{array} { l } { E X = \displaystyle \frac { 1 } { B ( a , b ) } \int _ { 0 } ^ { 1 } x x ^ { a - 1 } \big ( 1 - x \big ) ^ { b - 1 } d x } \\ { \displaystyle \quad = \frac { 1 } { B ( a , b ) } \int _ { 0 } ^ { 1 } x ^ { ( a + 1 ) - 1 } \big ( 1 - x \big ) ^ { b - 1 } d x } \\ { \displaystyle \quad = \frac { 1 } { B ( a , b ) } B \big ( a + 1 , b \big ) } \\ { \displaystyle \quad = \frac { \Gamma \big ( a + b \big ) } { \Gamma \big ( a \big ) \Gamma \big ( b \big ) } \cdot \frac { \Gamma \big ( a + 1 \big ) \Gamma \big ( b \big ) } { \Gamma \big ( a + b + 1 \big ) } = \frac { a } { a + b } } \end{array}
$$

The square of the mean is calculated as follows:

$$
\begin{array} { l } { E X ^ { 2 } = \displaystyle \frac { 1 } { B \big ( a , b \big ) } \int _ { 0 } ^ { 1 } x ^ { 2 } x ^ { a - 1 } \big ( 1 - x \big ) ^ { b - 1 } d x } \\ { \displaystyle = \frac { 1 } { B \big ( a , b \big ) } \int _ { 0 } ^ { 1 } x ^ { \big ( a + 2 ) - 1 } \big ( 1 - x \big ) ^ { b - 1 } d x } \\ { \displaystyle = \frac { 1 } { B \big ( a , b \big ) } B \big ( a + 2 , b \big ) } \\ { \displaystyle = \frac { \Gamma \big ( a + b \big ) } { \Gamma \big ( a \big ) \Gamma \big ( b \big ) } \cdot \frac { \Gamma \big ( a + 2 \big ) \Gamma \big ( b \big ) } { \Gamma \big ( a + b + 2 \big ) } } \\ { \displaystyle = \frac { a \big ( a + 1 \big ) } { \big ( a + b + 1 \big ) \big ( a + b \big ) } } \end{array}
$$

The variance is calculated as follows:

$$
\begin{array} { c } { { V a r ( X ) { = } \displaystyle \frac { a ( a + 1 ) } { ( a + b + 1 ) ( a + b ) } { - } \biggl ( \frac { a } { a + b } \biggr ) ^ { 2 } } } \\ { { { = } \displaystyle \frac { a b } { ( a + b ) ^ { 2 } ( a + b + 1 ) } } } \end{array}
$$

According to the above reasoning,, the duration time variance of activity $i$ is:

$$
\sigma _ { i } ^ { 2 } = \frac { ( b _ { i } - a _ { i } ) ^ { 2 } } { 3 6 }
$$

Standard deviation is:

$$
\sigma _ { i } = \sqrt { \frac { { ( b _ { i } - a _ { i } ) } ^ { 2 } } { 3 6 } } = \frac { b _ { i } - a _ { i } } { 6 }
$$

# 4. CPM (Critical Path Method)

For a software development project, there are multiple paths from the beginning to the end. The path that takes the longest time is called the critical path, and the jobs on the critical path are called critical jobs. The operation on the non-critical path exceeds the planned time, which does not necessarily have an impact on the total construction period. However, if the jobs on the critical path exceed the planned time, the total duration will definitely be extended. Therefore, for software project managers, the most important things to pay attention to are critical activities and critical paths [7-9].

# 4.1 calculate activity time

Need to calculate the earliest start time $t _ { E S }$ , earliest finish time $t _ { E F }$ , latest start time $t _ { L S }$ , latest finish time $t _ { L F }$ time difference R.

(1) Duration time. Show with t, also known as duration, means the requirement time to complete a task, is the result of estimate the task. Time unit can be hours, days, weeks, months, quarters, etc., according to the actual situation.

(2) The earliest start time. Show with $t _ { E S }$ which is the largest value of time that all the tight former tasks finish first. The formula is:

$$
t _ { _ { E S } } = \operatorname* { m a x } _ { k } \{ t _ { _ { E F } } ( k ) \}
$$

$k$ is the number of tight former tasks for this task.

(3) The earliest finish time. Show with $t _ { E F }$ , The formula is:

$$
t _ { E F } = t _ { E S } + t
$$

(4) The latest finish time. Show with $t _ { L F }$ which is the minimum value of time of all the tight after tasks start last. The tight after tasks of each task as not to delay the entire periods as the principle. The formula is:

$$
t _ { _ { L F } } = \operatorname* { m i n } _ { k } \{ t _ { _ { L S } } ( k ) \}
$$

$k$ is the number of tight former tasks for this task.

(5) The latest start time. Show with $t _ { L S }$ which is the latest finish time minus the duration time of the task. The formula is:

$$
t _ { L S } = t _ { L F } - t
$$

(6) Time difference. According to the properties can be divided into the task total time difference $R$ and task free time difference $F .$ , the formula is:

$$
R = t _ { L F } - t _ { E S } - t
$$

$$
\mathrm { o r } \qquad R = t _ { L F } - t _ { E F } = t _ { L S } - t _ { E S }
$$

The free time difference of task show with $F .$ , means the amplitude peak of this task starting time that can be delayed under the case without affecting the earliest starting time of each tight after tasks, which value is equal to the minimum value of the earliest start time of all the tight after tasks minus the earliest finish time, the formula is:

$$
F = \operatorname* { m i n } _ { k } \{ t _ { E S } ( k ) \} - t _ { E F }
$$

# 4.2 Determine the Critical Path

A task that the total time difference is 0, is called critical path, critical path can also be defined by the path composed of nodes that representative the critical tasks.

# 5. Duration Time Compression Method

Duration time compression method is a mathematical analysis method, looking for ways to shorten the duration time of the project approach under the premise of not change the scope of the project. Emergency method and parallel operation method are duration time compression method. Once the project adopts appropriate working methods and tools, we can simply progress schedule compression by increasing personnel and overtime to shorten the schedule. During progress schedule compression, there exist a definite relationship of schedule compression and fees increase, many people put a different ways to estimate the relationship schedule compression and fees increase, here we study the schedule compression factor method.

The relationship of schedule compression and fees increase is not always show as proportion, when the schedule is compressed out of "normal" range, the workload will increase dramatically, and the fees will rise rapidly. Moreover, software project exists a schedule in the shortest possible which is not break through, shown in Fig. 3. In some cases, adding more software developers will slows down the development speed rather than accelerates development speed, as there exists more exchange and management time. Software project has the shortest schedule point, no matter how hard work, how clever work, how to seek creative solutions, and no matter how large development team be organized, all can not break through the shortest schedule point.

![](images/0ec9865f13dbe7e9499cb259fb86af41877ed9b1381e72300e20e802da5c8aab.jpg)  
Fig.3. The relationship of project schedule and fees

A method of estimating the schedule compression fees proposed by Charles Symons, a well-known software metrics experts, is considered as a kind of relatively high accuracy. First calculate the schedule compression factor, the formula is:

$$
C I = H P { \big / } _ { E P }
$$

In the formula, $C I$ means the schedule compression factor, $H P$ means the expect schedule, $E P$ means the estimate schedule.

And then calculate the workload of compression schedule, the formula is:

$$
C P V = E V \%
$$

In the formula, $C P V$ means the workload of compression schedule, $E V$ means the estimate workload.

This method first estimates the initial workload and the initial schedule, then combines the estimates with the expectations of schedule, and using equation to calculate the compression schedule factor and the workload that after compression schedule.

# 6. The Choice of Technology and Methods

Schedule planning is not only very important in the software project management, but also is basis of project running, and it is the key point of project success [10]. Using different schedule planning methods, the time and cost required are different. CPM analyses each activity, if activities is more , need use computer to calculate the total duration and the critical path, so it takes more time and fees; PERT is the most complex to develop project schedule planning, and the time and fees are also most.

Which schedule planning method should be adopt , mainly consider the following factors: (1) The project size. Small project should use a simple schedule planning method, large projects in order to fulfill the goals according to the duration and quality, need to use a more complex schedule planning method. (2) The complexity of the project. Generally speaking, the larger the project, the higher the complexity, but the project scale is not necessarily proportional to complexity. The more complex the project, the more difficult to control, then should adopt a more complex schedule planning to make methods.

(3)The urgency of the project. When the project is urgently needed, especially in the starting stages, need to   
arrange a lot of work, so as to work as soon as possible. At this time if the progress of development of more complex   
methods, schedule, take a long time been completed, it will delay the time. (4) The degree of master the details of project. If cannot clear the details at the beginning of the project, CPM and   
PERT will not be applied. (5) Whether exist bottlenecks. If the project has one or two tasks in the process take a long time, and this period   
we can arrange other preparatory work, so these other work needn't prepare for complex schedule planning. (6) Whether has the schedule planning required technology and equipment. If there are no computers, CPM and   
PERT of complex project are difficult to use; if there are no well-trained and qualified technical staffs, also can not   
competent to use the complex method to compile the schedule planning. In addition, consider the schedule planning compile budget, the cooperate with customers and other factors. In

the end which method to be adopt, should consider above factors.

# 7. Example of Software Project Schedule Preparation

For the software project PDM diagram shown in Fig. 1, a total of 12 jobs are included, and the duration of each job and the dependencies between jobs (represented by close-preceded tasks) are shown in Table 1. The duration of the operation is measured in months.

Table 1. The duration of a project and its dependency   

<table><tr><td>No</td><td>Job code</td><td>Job name</td><td>Duration time</td><td>tight former tasks</td></tr><tr><td>1</td><td>A</td><td>Project plan</td><td>1</td><td></td></tr><tr><td>2</td><td>B</td><td>Requirement analysis</td><td>3</td><td></td></tr><tr><td>3</td><td>C</td><td>Requirement confirmation</td><td>1</td><td>B</td></tr><tr><td>4</td><td>D</td><td>Plan review</td><td>1</td><td>AC</td></tr><tr><td>5</td><td>E</td><td>Summary design</td><td>2</td><td>D</td></tr><tr><td>6</td><td>F</td><td>Database design</td><td>3</td><td>D</td></tr><tr><td>7</td><td>G</td><td>Detailed design</td><td>4</td><td>F</td></tr><tr><td>8</td><td>H</td><td>Software coding</td><td>6</td><td>D,F</td></tr><tr><td>9</td><td></td><td>Integrated testing</td><td>3</td><td>H</td></tr><tr><td>10</td><td>J</td><td>Conform testing</td><td>2</td><td>I</td></tr><tr><td>11</td><td>K</td><td>Network integration</td><td>3</td><td>H</td></tr><tr><td>12</td><td>L</td><td>System testing</td><td>1</td><td>K</td></tr></table>

Time calculation based on the fusion of PERT and CPM can be carried out directly on the network diagram or in the form of a table. The table calculation method is adopted here, which is calculated directly on the network diagram, and the calculation results are shown in Table 2.

Table 2. The table of active time calculation   

<table><tr><td>No</td><td>Job code</td><td>Duration time</td><td>Earliest start time</td><td>Earliest finish time</td><td>Latest finish time</td><td>Latest start time</td><td>Total time difference</td><td>Free time difference</td></tr><tr><td>1</td><td>A</td><td>1</td><td>0</td><td>1</td><td>4</td><td>3</td><td>3</td><td>3</td></tr><tr><td>2</td><td>B</td><td>3</td><td>0</td><td>3</td><td>3</td><td>0</td><td>0</td><td>0</td></tr><tr><td>3</td><td>C</td><td>1</td><td>3</td><td>4</td><td>4</td><td>3</td><td>0</td><td>0</td></tr><tr><td>4</td><td>D</td><td>1</td><td>4</td><td>5</td><td>5</td><td>4</td><td>0</td><td>0</td></tr><tr><td>5</td><td>E</td><td>2</td><td>5</td><td>7</td><td>12</td><td>10</td><td>5</td><td>5</td></tr><tr><td>6</td><td>F</td><td>3</td><td>5</td><td>8</td><td>8</td><td>5</td><td>0</td><td>0</td></tr></table>

<table><tr><td>7</td><td>G</td><td>4</td><td>8</td><td>12</td><td>12</td><td>8</td><td>0</td><td>0</td></tr><tr><td>8</td><td>H</td><td>6</td><td>12</td><td>18</td><td>18</td><td>12</td><td>0</td><td>0</td></tr><tr><td>9</td><td>I</td><td>3</td><td>18</td><td>21</td><td>21</td><td>18</td><td>0</td><td>0</td></tr><tr><td>10</td><td>J</td><td>2</td><td>21</td><td>23</td><td>23</td><td>21</td><td>0</td><td>0</td></tr><tr><td>11</td><td>K</td><td>3</td><td>18</td><td>21</td><td>22</td><td>19</td><td>1</td><td>1</td></tr><tr><td>12</td><td>L</td><td>1</td><td>21</td><td>22</td><td>23</td><td>22</td><td>1</td><td>1</td></tr></table>

In this case, the key job is "B, C, D, F, G. H, I, J", By "Requirement analysis, Requirement confirmation, Plan review, Database design, Detailed design, Software coding, Integrated testing, Conform testing" The path formed by these four jobs is the critical path. The data calculated in Table 2 provide a basis for the software project schedule.

# 8. Conclusion

Making a reasonable software project schedule is the key to ensure the successful completion of software projects. There are many ways to make software project schedule, and the combination of PERT and CPM is the most widely used method at present. The research content of this paper provides a complete solution for the development of software project scheduling tools based on the integration of PERT and CPM, which can improve work efficiency and reduce the possibility of failure.

# Acknowledgments

This work is supported by Industry-school Cooperative Education Program of Ministry of Education in 2022 (No: 221000501145602); Industry-school Cooperative Education Program of Ministry of Education in 2022 (No: 221002304145429).

# References

[1] Sousa Abraham L. R.,Souza Cleidson R. B.,Reis Rodrigo Q.. A 20‐year mapping of Bayesian belief networks in software project management. IET Software, 2021, 16(1): 14-28.   
[2] Mamatha R.,Suma K.G.. Role of Machine Learning in Software Project Management. Journal of Physics: Conference Series, 2021, 2040(1): 1-5.   
[3] Couto Julia Colleoni, Kroll Josiane, Ruiz Duncan Dubugras, Prikladnicki Rafael. Extending the Project Management Body of Knowledge (PMBOK) for Data Visualization in Software Project Management. SN Computer Science, 2022,3(4): 6-15.   
[4] Figueroa–García Juan Carlos,Hernández–Pérez Germán,Ramos–Cuesta Jennifer Soraya. Uncertain project network analysis with fuzzy– PERT and Interval Type–2 fuzzy activity durations. Heliyon, 2023, 9(5): e14833-e14833.   
[5] Francesco Lolli, Antonio Maria Coruzzolo, Matteo Zironi. A PERT model based on the Dampster and Shafer's theory of evidence – application to product development. International Journal of Productivity and Quality Management, 2023, 38(2): 143-165.   
[6] Akan Ercan,Bayar Sibel. Interval type-2 fuzzy program evaluation and review technique for project management in shipbuilding. Ships and Offshore Structures, 2022, 17(8): 1872-1890.   
[7] Ramani Prasanna Venkatesan, Selvaraj Ponnambalam, T. Shanmugapriya, Gupta Anshul. Application of Linear Scheduling in Water Canal Construction with a Comparison of Critical Path Method. Journal of Construction in Developing Countries, 2022, 27(1): 189-212.   
[8] Zakia, Febrianti Dian. The Critical Path Method in Construction Project Rescheduling. IOP Conference Series: Earth and Environmental Science, 2021, 832(1): 1-8.   
[9] Aljumaili Othman. The automation of critical path method using machine learning: A conceptual study. International Journal of Engineering and Management Research, 2021, 11(3): 237-240.   
[10] Y. C. Ren, T. Xing, D. C. Liu. Schedule planning of software project based on network chart. Journal of Jilin University(Information Science Edition), 2011, 29(02): 128-134.