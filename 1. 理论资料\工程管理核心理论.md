工程管理中四大核心理论——**CPM、PERT、CCPM、EVM**——是进度与成本控制的基础工具，它们分别解决了不同场景下的**计划、不确定性、资源约束、绩效测量**问题。以下用一句话定义+关键区别+适用场景的方式快速对比：

---

### 1. **CPM（关键路径法）**

- **定义**：通过**最长路径**（关键路径）确定项目最短工期，重点管理**零浮动时间**的任务。
- **核心公式**：
  - **最早开始（ES）/最晚开始（LS）** → 浮动时间 = LS - ES
- **适用场景**：工序逻辑清晰、工期确定的**传统工程**（如土建主体结构）。
- **局限**：假设资源无限，忽略不确定性。

---

### 2. **PERT（计划评审技术）**

- **定义**：用**概率模型**（三点估算）应对不确定性，计算**期望工期**和按时完成概率。
- **核心公式**：
  - **期望时间 Tₑ = (O + 4M + P)/6**（O=乐观，M=最可能，P=悲观）
  - **标准差 σ = (P - O)/6** → 计算工期在±σ内的概率（68%）。
- **适用场景**：研发或**创新工程**（如首台套设备研发），工期波动大。
- **局限**：计算复杂，对数据敏感，需历史经验支撑三点估算。

---

### 3. **CCPM（关键链项目管理）**

- **定义**：在**资源冲突**下，通过**缓冲管理**（项目缓冲/接驳缓冲）保护关键链，解决**多任务并行**导致的延误。
- **核心工具**：
  - **关键链**（最长路径+资源约束）→ 非关键路径任务提前完成时间被**缓冲吃掉**。
- **适用场景**：资源紧张或**多项目共享**（如EPC总承包商同时管理多个标段）。
- **优势**：比CPM减少30%~50%工期，但对团队执行力要求极高。

---

### 4. **EVM（挣值管理）**

- **定义**：用**货币化指标**综合衡量进度与成本绩效，回答“花了多少钱，干了多少活”。
- **核心指标**：
  - **SV（进度偏差）= EV - PV**（>0=超前）
  - **CPI（成本绩效指数）= EV/AC**（>1=节省）
- **适用场景**：**政府/大型工程**（如高铁、核电站），需严格审计绩效。
- **局限**：依赖准确的**WBS分解**和成本分摊，小项目可能过度管理。

---

### 一句话总结对比表：

| 理论           | 核心问题           | 关键工具          | 典型场景            |
| -------------- | ------------------ | ----------------- | ------------------- |
| **CPM**  | 确定最短工期       | 关键路径+浮动时间 | 常规施工总进度      |
| **PERT** | 应对工期不确定性   | 三点估算+概率     | 研发类项目          |
| **CCPM** | 解决资源冲突       | 缓冲管理          | 多项目并行/资源稀缺 |
| **EVM**  | 量化进度与成本偏差 | 挣值指标          | 政府审计/大型基建   |

---

### 进阶思考：

- **CPM vs CCPM**：CPM假设“工人不拖延”，CCPM默认“学生会综合征”（任务拖延到最后一刻），通过缓冲吸收不确定性。
- **PERT vs EVM**：PERT用于**计划阶段**估算工期，EVM用于**执行阶段**监控偏差。

实际工程中常**组合使用**：例如用**CCPM排总进度**（考虑塔吊共享），**EVM监控分包绩效**，**PERT评估高风险作业工期**。
