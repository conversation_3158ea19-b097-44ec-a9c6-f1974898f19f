密级：公开

# 硕士学位论文

![](images/da93fc9cd10913b3e6a3d0778c64e6e19b810dbb090566cdf2c3a3cd394156f6.jpg)

题目：基于敏捷开发的J公司平台项目进度管理研究

学号：2022180428

姓名：_许连旭

学科专业：工程管理]

导师：_王长峰

学院：经济管理学院

密级：公开

# 北京郵電大學

硕士学位论文(专业学位)

题目：基于敏捷开发的J公司平台项目进度管理研究

学 号： 2022180428

姓 名： 许连旭

学科专业： 工程管理

培养方式： 非全日制

导 师： 王长峰

学 院： 经济管理学院

# BEIJING UNIVERSITY OF POSTS AND TELECOMMUNICATIONS

Master Thesis

Research on Progress Management of J Company's Platform Project

Student ID: 2022180428

Author: Lianxu Xu

Subject: _ Engineering Managment

Supervisor:  Changfeng Wang

Institute: School of Economics and Management

# 答辩委员会名单

<table><tr><td>职务</td><td>姓名</td><td>职称</td><td>工作单位</td></tr><tr><td>主席</td><td>张彬</td><td>教授</td><td>北京邮电大学</td></tr><tr><td>委员</td><td>谢雪梅</td><td>教授</td><td>北京邮电大学</td></tr><tr><td>委员</td><td>张晓航</td><td>教授</td><td>北京邮电大学</td></tr><tr><td>委员</td><td>王琦</td><td>教授</td><td>北京邮电大学</td></tr><tr><td>委员</td><td></td><td></td><td></td></tr><tr><td>秘书</td><td>唐菊华</td><td></td><td>北京邮电大学</td></tr><tr><td>答辩日期</td><td colspan="3">2024年9月12日</td></tr></table>

# 摘要

随着市场竞争的加剧和客户需求的快速变化，敏捷开发模式已成为软件行业快速响应市场变化的重要手段。然而，如何在敏捷开发环境下有效进行项目进度管理，仍然是项目管理领域面临的挑战之一。

J公司是一家在国际市场上富有竞争力的软件服务提供商，近年来业务不断拓展，导致项目数量和复杂度日益增加。本文以J公司平台项目为研究对象，探究如何在敏捷开发的背景下进行有效的项目进度管理。通过深入分析敏捷开发的理论框架和Scrum方法，结合项目进度管理的经典理论，如网络分析法（PERT）和甘特图，本研究确定了项目进度管理的关键因素，并提出了一套针对敏捷开发的项目进度管理优化方案。方案包括项目开发模式的优化、项目团队人员配置的优化以及迭代开发过程管理的优化三个方面。通过实施项目案例分析，利用PERT网络分析法和甘特图，本文系统地评估了J公司平台项目在实施敏捷开发模式前后的进度管理情况。数据显示，采用优化方案后，J公司项目进度的可控性和预测性得到了显著提高，交付周期缩短，缺陷率下降，客户满意度提升。

最后，文章预测了敏捷项目进度管理的未来发展趋势，包括深度学习算法在项目管理中的应用潜力，以及针对跨文化项目管理的新策略。本研究的结论对于那些旨在改进敏捷开发环境下项目进度管理效率的企业具有借鉴意义。

关键词：敏捷开发；项目进度管理；Scrum方法；PERT；甘特图

# ABSTRACT

With the intensification of market competition and the rapid changes in customer demands, agile development has become a significant approach for the software industry to swiftly respond to market changes. However, how to effectively manage project progress in an agile development environment remains one of the challenges in the field of project management.

J Company is a competitive software service provider in the international market that has continuously expanded its business in recent years, leading to an increase in both the number and complexity of projects. In this study, we use J Company's platform projects as a research subject to explore how to effectively manage project progress within the context of agile development. By conducting an in-depth analysis of the theoretical framework of agile development and the Scrum methodology, in conjunction with classic theories of project progress management, such as the Program Evaluation and Review Technique (PERT) and Gantt, this research identifies key factors in project progress management and proposes a set of optimized solutions for project progress management in agile development. The proposal includes the optimization of project development models, project team configuration, and iterative development process management. Through the implementation of project case analysis and the use of PERT network analysis and Gantt charts, this thesis systematically evaluates the progress management of J Company's platform projects before and after the implementation of agile development models. Data shows that after adopting the optimized solutions, the controllability and predictability of J Company's project progress have been significantly improved, delivery cycles have been shortened, defect rates have decreased, and customer satisfaction has increased.

Lastly, the article predicts future trends in agile project progress management, including the potential application of deep learning algorithms in project management, and new strategies for cross-cultural project management. The conclusions of this study are meaningful for those enterprises aiming to improve project progress management efficiency in an agile development environment.

KEY WORDS: Agile development; Project schedule management; Scrum methodology; PERT; Gantt

# 目录

第一章绪论  
1.1 研究背景及其意义.  
1.1.1研究背景.  
1.1.2 研究意义 .2  
1.2研究内容和研究方法.. ..2  
1.2.1 研究内容. .2  
1.2.2研究方法. ..3  
1.3国内外研究现状. .4  
1.3.1国外研究现状.. ..4  
1.3.2国内研究现状.. .5  
1.4研究框架及创新点.. ..5  
1.4.1 研究框架. ..5  
1.4.2 创新点 .6  
1.5小结. .6  
第二章项目进度管理理论和文献综述  
2.1软件项目管理. .7  
2.2 Scrum 方法 .  
2.3 进度分析工具9  
2.3.1PERT网络分析法. .  
2.3.2甘特图. ..10  
2.4小结.. .11  
第三章J公司项目现状 ..  
3.1J公司概述及项目背景介绍. .13  
3.2项目现状介绍... .1  
3.2.1 WBS结构. ….1  
3.2.2团队人员， ..14  
3.2.3进度计划. .15  
3.3项目问题调研.  
3.3.1问卷调查.. .18  
3.3.2专家访谈. .18  
3.4项目问题成因分析.. ..1  
3.4.1WBS分解结构不合理.  
3.4.2进度计划不合理及任务估时不准确. ..21  
3.4.3团队人员进度控制能力不足.. .2  
3.4.4需求变更流程混乱 .22  
3.4.5开发方法存在漏洞. ..22  
3.4.6缺少合适的风险预警机制 ..23  
3.5小结.. ..23  
第四章平台项目进度管理优化方案设计 ..25  
4.1优化WBS任务分解.. ..25  
4.2任务活动历时估算修改. .  
4.3调整项目进度计划.. ..29  
4.4增强人员的进度控制能力 ..0  
4.5需求变更控制优化.. ..32  
4.6敏捷开发模式推进. .33  
4.7建立风险预警机制. .36  
4.8小结. ..37  
第五章平台项目进度管理优化方案实施效果 ..9  
5.1实施难点及对应解决方案. ..39  
5.2实施过程中的困难及解决方案 .0  
5.3实施后的结果检验.  
5.4小结. ..41  
第六章结论和展望 ..43  
6.1结论. ..43  
6.2不足和展望. ..44  
参考文献. ..45  
附录A项目进度基础问卷. ..47  
附录B专家人员访谈问题 .48  
附录C执行人员访谈问题 ..49

# 第一章绪论

# 1.1研究背景及其意义

# 1.1.1研究背景

2000年以来，软件行业在我国逐步发展起来，不少传统软件公司在2000至2010年间研发出了自己的软件产品，并靠着当时前沿的软件技术不断站稳自己的脚跟、维持公司的发展和扩大用户的基数。而软件行业本身从2010年以后进入高速发展期，各种软件技术不断更新迭代。在当前比较成熟的云服务等技术的支持下，软件成品的性能、用户体验等得到了飞跃式地提升。这就导致，随着时间的推移，传统软件公司在20世纪早期，比如2010年前推出的软件产品，越来越赶不上当今的潮流，而为了不被时代所抛下，传统软件公司亟需进行软件产品的升级。

随着软件技术的更新迭代，对应的项目管理开发流程也有了显著的变化，主流的开发流程从瀑布式开发逐渐变更为迭代式、敏捷式开发，这就要求项目本身支持快速响应和频繁更新，从而导致项目管理，尤其是项目进度管理的问题层出不穷，项目管理方面的研究也日益增多[1]。

软件项目支持快速响应，背后需要一个足够自洽、可以循环使用的流程体系，这样才能在短时间内完成一个版本从需求到上线这一完整流程。而这是离不开项目进度管理流程的。不同于传统软件项目的一次交付，迭代式、敏捷式开发把交付这个动作也拆分到了每个小版本中。这就导致项目团队在每个小版本中还需要给交付、上线分资源，如何让项目团队能够快速适应这一变化、如何通过尝试不同的项目管理方法和软件技术来提高项目管理水平是需许多团队的迫切需求。

J公司平台项目是国内典型的互联网管理平台项目，该项目的所有功能都来自于了公司运行了10年以上的上一代平台，也是J公司的核心项目。该项目是一款涉及多个子系统的平台项目，当前使用较为流行的代码托管平台，完成从开发到上线运维的流水线开发模式。项目目前采用的是迭代开发模式，按月规划项目任务，按照当月完成需求、下月完成开发、再下月完成测试的顺序推动每个月度版本。但是随着时间推移，软件开发方法不断更新，目前的开发方法遇到的问题也愈发严重：需求数量越来越多，内容越来越独特，需要变更的代码越来越多，导致需求落地的成本越来越高，线上风险越来越大；团队间沟通不彻底，月度版本结束后仍有遗留任务等。与此同时，根据测试澄清、用户反馈等途径，经常发生需求变更，导致开发工作量越来越大，经常需要赶工来保证项目进度。

基于以上情况，J公司启动了新的平台项目，旨在使用新的项目进度管理方法，对现有的平台项目进行升级改造。而敏捷开发 Scrum方法是在这些年是最重要的一种开发方法之一，加上它在灵活响应、快速迭代方面上的出色表现，了公司决定结合该方法，针对当前已经暴露出的严重的项目进度管理问题进行分析，作为平台项目的指导方针，更好地定义计划、组织、提高项目开发效率，最后根据平台项目的这次经验，总结出一套可以继续使用在J公司其他项目上的项目进度管理方法，供J公司参考。

# 1.1.2研究意义

敏捷开发作为软件工程领域的一个革命性变革，自诞生起便以其强调变化应对能力和增强客户参与度等特点，在瞬息万变的市场环境中得到了广泛的应用和认可。J公司作为一家积极响应市场变化，急需提高软件开发速度与质量的企业，引入敏捷项目管理不仅是时代的选择，也是企业竞争力提升的必然要求。通过对J公司现有的项目管理模式与流程的深入分析，及时引入更为灵活高效的敏捷项目管理方法，将有助于J公司在激烈的市场竞争中快速响应客户需求，提升产品开发迭代速度，从而实现降低项目成本、缩短交付周期、提高客户满意度的目标。

敏捷项目管理背后的核心价值观和原则提倡“以人为本”，鼓励团队自管理与交互，这在项目管理实践中表现为提高团队成员的责任心与主人翁精神。J公司平台项目的敏捷化改造，将以高效的自组织团队为核心，杜绝传统的上至下的管理模式，通过明确团队成员的角色和责任，促进项目成员间的有效沟通和合作，最大限度发掘每个项目成员的潜能，并极大地增强团队对项目结果的共同承担感。这一转变对于提升了公司的项目执行效率、增强团队协作氛围和创新能力意义重大。

通过本研究的深入探讨，结合敏捷项目管理和传统项目管理的优势，以及扎根理论对实际敏捷实践问题的系统分析，能够为J公司搭建起一个科学合理的项目进度管理体系。这一体系将会让管理层对整个软件开发周期有更全面的掌控，同时保证团队成员能在充分的信任和授权下发挥最大的工作动力。在理论和实践的双重指导下，J公司平台项目的进度管理和控制将更为精细，能更有效地利用有限的资源，提升项目成功率，为公司带来长期的经济效益和品牌价值。

# 1.2研究内容和研究方法

# 1.2.1研究内容

研究内容的确立对于本文的深入分析具有至关重要的作用。J公司作为当前信息技术行业中的重要参与者，其平台项目的进度管理不仅关系到企业内部的资源配置，成本控制和项目交付，也直接影响到公司在激烈市场竞争中的地位和客户满意度。因此，本文将重点研究三个方面的内容：

首先，本文将对敏捷项目管理的概念、原理、特点进行系统的梳理，结合敏捷宣言所提倡的价值观和原则，分析敏捷项目管理适应变化快速需求的本质特征；

其次，本文将研究敏捷开发中的核心实践，例如 Scrum和Kanban等敏捷方法在项目进度管理中的具体应用，特别是Scrum方法在实际项目中的迭代过程、角色分配以及流程管理方面的作用和效果；

最后，本文将结合J公司的平台项目案例，探究在该公司实践中运用敏捷项目管理方法的具体过程，分析在项目进度管理方面的效能与不足，提出结合了公司实际情况的优化建议和改进建议。

在研究方法上，本文运用案例研究法对J公司实际完成的项目案例进行详尽的分析，通过对这些案例历史数据的搜集，包括但不限于项目进度计划、实际完成时间、成本使用、团队反馈等，识别出项目管理中的关键成功因素以及影响项目进度的潜在障碍。本文还采用扎根理论的研究方法，通过对J公司的项目管理人员和参与开发的团队成员开展深入访谈，挖掘敏捷项目管理在实际操作过程中的隐性知识和经验总结，从而不断优化和完善理论架构。

具体到项目进度管理，本文会分析J公司在采用敏捷开发模式时，如何进行有效的任务分解、优先级排序、时间估算和进度跟踪，研究敏捷框架如Scrum中Sprint规划、Sprint回顾和每日立会如何助力项目进度的可视化和及时调整。同时，本文会特别关注在持续集成、自动化测试等敏捷实践工具支持下，项目进度管理的策略和实施效果，以探讨这些工具如何协助项目管理团队应对需求变更和预期偏差，从而优化项目的交付周期和提升最终产品的质量。

通过实证研究和理论探讨，本文意图建立一个结合敏捷管理理念和了公司实际情况的项目进度管理模型，并预期该模型能够帮助J公司及类似企业更有效地对项目进行管理，尤其是在应对项目范围变更、利益相关者需求调整以及技术挑战时，能够以更灵活、更敏捷的方式保障项目目标的实现。

# 1.2.2研究方法

以J公司实际情况为基础，本文采用了案例分析法、文献分析法、调查分析法等方法。

# （一）案例分析法

案例分析法专注于J公司的成功案例与失败案例，深挖各类案例在项目进度管理方面的经验与教训。通过构建案例数据库，对比不同案例中采取的策略和产生的结果，揭示问题的本质和解决问题的关键措施。本文以J公司平台项目为主要研究对象，系统分析之前的月度开发版本，得到每个开发版本中导致交付延期的问题，再针对每个问题尝试从Scrum方法中获取对应的解决方案，然后结合J公司相关开发团队的实际情况，对平台项目的后续版本进行分析，并针对性的提

出进度管理优化意见。

# （二）文献研究法

文献研究法是对已有学术研究成果的梳理，确保J公司项目进度管理的研究具有前瞻性和创新性。研究过程中广泛借鉴国内外研究者在敏捷开发和项目管理方面的研究，分析它们在J公司的适用性和改进空间，为构建理论框架和实证研究提供支持。在收集并研究相关文献及理论资料后，围绕 Scrum 方法在各个项目中的应用、传统软件公司在项目产品进度管理上的难点、痛点继续搜集资料，整理成果并完善研究思路。根据所收集到的资料，归纳总结本研究的理论框架，并结合J公司平台项目这一实际案例进行统一分析。通过对多个Scrum 方法的实际应用的搜集，再单独对每个应用方法进行分类整理，即可得到一系列 Scrum方法的实际优化方案，然后结合J公司平台项目现状，对一系列落地方案进行整合，最终提出针对性的改进方案。

# （三）调查分析法

调查分析法包含访谈、问卷等形式，在本中主要使用访谈的方式，访谈对象包括平台项目的项目经理、技术经理、测试经理及开发团队人员，访谈顺序大致如下：首先同平台项目的项目经理进行访谈，得到该项目经理认为的最亟待解决的问题和优化方案，同时会把文献研究法中得到的一系列优化方案同该项目经理进行商讨，最终得到几个比较适合的方案；然后同其他项目经理进行访谈，主要目的是继续优化上一步的几个方案，进行查缺补漏；其次是同开发团队人员进行访谈，收集他们做为实际 Backlog（对需求人员是User Story）的执行者，所感知到的最真实的项目问题和他们提出的解决方案；最后和平台项目的项目经理一起，同公司副总经理进行访谈，在对每个方案进行详细介绍后，由副总经理挑选出实际要执行的方案。

# 1.3国内外研究现状

# 1.3.1国外研究现状

在全球范围内，敏捷开发方法作为一种提高软件开发效率和响应市场变化的重要工具，已被广泛接受和实践。特别是在软件项目进度管理领域，敏捷方法因其灵活性和适应性，已成为推动项目有效进行的核心策略之一。从国外研究现状来看，敏捷项目管理(AGM)日益受到国际学术界和业界的关注。

在欧美国家，敏捷项目管理得到了快速发展和广泛的应用，研究者们在敏捷管理模式，如 Scrum、Kanban 等框架上进行了诸多的深入探讨。特别是在敏捷软件开发峰会Agile Alliance上，相关的最佳实践和经验教训被不断分享和讨论。同时，PMI（项目管理协会）在其体系中也强调了敏捷方法的重要性，并推出了专门的敏捷认证项目。

Reifer咨询顾问有限责任公司曾发布了一份基准管理报告[2]，给出了一份分析了100家组织1500个项目（其中有500个使用了敏捷方法）的报告，这份报告根据敏捷方法的使用与否，比较了不同软件开发项目间的生产率、成本和质量绩效，最终发现，敏捷方法在需求变换较多的项目里表现十分优异。

其他公司在进行新项目开发时也经常会选择敏捷开发方法，一个可以支持和增强晚期癌症患者及其家庭护理人员的能力的、可自我管理的心理教育电子健康计划，在整个项目期间使用了Scrum方法，并最终按时交付项目[34]。

除此之外，进度管理相关方法的优化也是国外常有的研究方向，Pablo等人就曾对PERT分析法进行研究创新，期望能够更加精细地创建PERT网络[5]。而早在1999年Larry P.Leach 就曾论述过关键链项目管理会提高项目效率[6]。

# 1.3.2国内研究现状

随着我国改革开放和市场经济的快速发展，企业管理现代化的步伐日益加快，项目管理作为现代科学管理的重要组成部分，其重要性逐渐被国内企业和学术界所认识。特别是在软件工程领域，敏捷开发由于其灵活性和高效性，已经成为项目管理的重要趋势，体现在为企业带来较高的项目成功率和更好的客户满意度上。在这一背景下，敏捷项目管理理论和实践在国内持续获得深入研究和广泛应用。

回顾国内在项目进度管理方面的研究现状，可以发现从上个世纪90年代末至今，关于项目管理概念和工具的引入和研究已取得显著进展[7-9]。中国的企业开始逐步采纳项目管理方法，如项目评审技术(PERT)[10]等，提高项目执行的效率和效果。近些年以来，国内大量的软件项目的执行开展以来，也逐步形成了一套相似的项目进度管理知识体系[Ⅲ，我国的软件项目进度管理逐渐成熟和规范化，形成了一系列具有中国特色的理论成果。例如，研究者们对敏捷项目管理模式和传统项目管理模式进行了对比分析，探索二者在国内企业转型升级过程中的具体应用情况及效果表现[12]。一些企业建立了完整的项目管理体系，采用敏捷方法、迭代式开发等先进的软件开发方法，能够更好地适应不断变化的市场需求和技术趋势。其中华为集团发展出独特的IPD 管理体系当属中国先进项目管理系统[13]。

# 1.4研究框架及创新点

# 1.4.1研究框架

本文共包含六个章节，第一章为绪论，介绍研究背景及意义、研究内容和方法、国内外研究现。第二章为文献综述，内容包括软件项目管理理论和相关工具方法。第三章介绍J公司及平台项目现状，同时使用相关研究方法对平台项目的进度问题进行分析调查、分析和整理。第四章为优化方案设计，根据第三章的分析结果，结合J公司平台项目实际，提出优化方案。第五章为优化方案实施前后碰到的难点、解决方案和最汇的实施方案结果验证。第六章为结论与展望，对本文进行整理概括，分析本文需要改进的地方，对未来发展提出期望。论文整体框架如图1-1所示。

![](images/1bf843d8a488653acf2135dd8e116fa09597a94cd07fb305884ea06dce8724e0.jpg)  
图1-1论文结构图

# 1.4.2创新点

在J公司尝试使用PERT方法时，项目经理对该方法中的最可能时间、最乐观时间和最悲观时间的把控上不够熟练，绝大多数活动的三个时间都是一个值。本文参考J公司实际，把PERT方法的三个时间估算分开处理，分别由不同人员给出最可能时间、最乐观时间和最悲观时间，以求达成更加准确的项目耗时估算。

本文在使用调查分析法时，先使用问卷调查，全面调研平台项目在进度管理上的各个情况，再根据调研结果设计面对面访谈问题，力求能够准确地分析出影响平台项目进度的关键问题。

# 1.5小结

本章论述了本文的研究背景、意义，阐述了研究内容、研究方法和国内外研究现状，给出了本文的主要框架结构和穿心点描述。

# 第二章 项目进度管理理论和文献综述

# 2.1软件项目管理

项目管理[I14]是一个复杂而系统的过程，其核心在于引导团队在限定的时间框架内达成既定目标和成功标准。这一过程中，关键挑战在于有效应对时间、范围、预算等多重约束条件，同时确保资源的最优配置。项目管理聚焦于独立且具临时性的项目对象，每个项目都旨在创造独特的成果，这些成果可能表现为产品、服务或特定结果。

项目管理不仅仅是达成最终目标，它还涉及对项目全周期的精细管理，从启动到结束，每一步都需精心策划与执行。在此过程中，需兼顾时间、资金与人力等要素，实施一系列管理活动，如范围界定、进度控制、质量保证、沟通协调、风险评估与应对、资源采购以及干系人关系处理等。这些管理领域相互交织，共同支撑起项目的顺利实施与成功交付。

具体而言，项目范围管理确保项目工作始终围绕既定目标展开；项目进度管理通过合理规划与监控，保障项目按时推进；项目质量管理强调过程与成果的高标准；项目沟通管理促进信息透明与团队协作；项目风险管理则是对潜在威胁的识别与预防；项目采购管理确保所需资源及时到位；干系人管理则是维护各方利益，促进项目和谐推进。这些管理活动的综合应用，构成了项目管理工作的全面框架。

项目进度管理[15]是软件项目管理中的关键组成部分，它涉及对项目时间、资源和成本等要素的综合管理，旨在确保项目可以按计划高质量地完成。在探讨J公司平台项目的进度管理时，核心关注点便是建立一个既能适应敏捷开发环境，又能保证项目按时交付的有效进度管理体系。

值得注意的是，在敏捷环境下，传统的项目进度管理工具也需要相应的调整，以适应快速变化的项目需求和频繁的迭代。网络图法(PERT)等工具的使用与之兼容，能帮助管理者有效地处理复杂的依赖关系，通过优化决策实现项目进度的动态调整。同时，考虑到J公司所面对的复杂多变的市场需求，项目进度管理不仅要兼顾技术分析和实用工具的使用，还需要强调团队协作与沟通，确保在迭代开发过程中实现高效率和高适应性。

J公司的项目进度管理需要在坚持敏捷原则的基础上，合理运用PERT、甘特图以及Scrum相关技术，结合项目实际情况不断优化进度管理策略。通过这样的方式，J公司的项目进度管理能够更加科学、灵活，确保项目成功按时交付，同时提高项目团队的工作效率和满意度。

项目进度监控是项目管理中的核心环节，它直接关系到项目的成功与否。在实施过程中，我们首先要根据项目的具体情况，详细规划每个阶段的进度目标和时间表。这份计划需具备清晰性和合理性，确保它能够满足项目的整体要求。随着项目的推进，我们需频繁对照计划与实际进展，实施严密的监控。一旦发现实际进度与计划不符，应立即分析问题根源，并迅速采取补救措施，可能包括调整后续任务或修订进度计划，以确保项目能够顺利推进至最终完成。

项目进度管理的核心在于确保项目能在预定的时间框架内达成目标，同时保持成本与质量的平衡。为此，我们需明确各阶段的具体进度指标，制定详尽的进度和资源分配计划。通过持续监控和对比实际与计划的差异，我们能够灵活调整策略，优化资源配置，从而在不牺牲项目质量的前提下，确保项目按时完成。项目进度监控不仅是项目管理的基石，也是推动其他管理领域高效运作的关键。通过精细化的进度管理，我们能够奠定坚实的基础，确保项目的整体成功。

# 2.2Scrum 方法

在探讨Scrum方法及其在J公司平台项目进度管理中的运用之前，必须先对Scrum 的基本框架及其核心要素进行详细阐述。

Scrum作为敏捷开发方法论的代表，致力于提升团队协作和快速响应变化的能力。Scrum 源于英文词汇“Scrummage”，意为橄榄球比赛中争夺球权时的激烈冲撞。这一名称形象地体现了Scrum 的核心思想：团队成员紧密合作，共同应对挑战。该方法起源于20世纪90年代，是一种灵活且高效的敏捷软件开发框架，它由 Jeff Sutherland 和Ken Schwaber 共同提出，并在之后的时间里不断演进与完善[16]。

作为一种敏捷方法，Scrum 强调团队合作、客户需求快速反馈以及持续改进。Scrum框架中，产品代理（ProductOwner）负责定义项目的优先级和需求，并形成产品积压（Product Backlog）——这是一个根据商业价值和需求优先级排序的任务列表。而Scrum团队（Scrum Team）则分成多个跨功能小组，这些小组在定期的时间段内（通常为一个月或更短，称为Sprint）集中精力完成产品积压中的任务。

每个Sprint的开始，会有一个Sprint计划会议，团队在这个会议上决定这个Sprint将实施的任务（Sprint Backlog）以及预计的交付物。为保证项目进展的透明性和监控，团队会进行每日站会（Daily Stand-up），这个15分钟的快速会议使得团队成员能够分享他们的进度、计划以及面临的任何阻碍。

在一个Sprint结束时，团队会展示其成果，即 Sprint Review，并进行Sprint回顾（Sprint Retrospective），这是团队回顾Sprint 过程并讨论持续改进的机会的会议。Scrum方法鼓励团队通过这种自我反思来不断进步，增强团队的凝聚力

以及提高工作效率。

在了公司平台项目中应用Scrum方法时，可以看到Scrum 带来了哪些优势。例如，通过迭代开发与反馈循环，项目能快速响应变化，有效地满足客户需求并适应市场变化。敏捷团队的自我组织和对工作的高度承诺也大大提高了团队成员的积极性和项目交付的质量。

将Scrum应用于J公司具体项目的过程中，首要任务是构建高效的Scrum团队。团队成员需要了解并接受敏捷文化和 Scrum原则，同时通过培训和实践来掌握Scrum方法。Scrum团队的建立不但要求每个成员具备跨功能的能力，还要求他们能够在没有传统项目经理的情况下自组织和自主管理。Scrum团队还需要定期审视和调整自身的工作流程，以确保持续改进和提升效率[17]。

在实际操作中，对于项目进度管理来说，Scrum方法的引入使得原本可能因需求变化而导致进度混乱、资源分配不合理的问题得到了缓解。Scrum通过灵活的任务分配（例如，每次Sprint选择最优先的任务进行实施）和时间盒限制（每次Sprint的固定时间框架），能够更好地管理项目进度，确保项目能够按时交付。

Scrum方法为J公司提供了一种全新的项目进度管理方式。该方法的应用不仅提高了团队成员的自主性和项目适应性，也大大增强了J公司应对市场快速变化和客户需求的能力。下一步的研究将更深入地探讨 Scrum 在特定项目环境中的实施细节和面临的挑战，以及如何进一步优化Scrum团队与传统项目管理流程的融合。

# 2.3进度分析工具

# 2.3.1PERT 网络分析法

在项目管理领域，进度分析工具的选择与应用对于确保项目按时交付、避免成本超支和资源浪费具有至关重要的作用。PERT网络分析法（Program Evaluationand ReviewTechnique）是广泛应用于项目进度管理中的重要技术之一，它通过网络图的形式表现项目的工作流程与时间评估，对项目进行全面系统的规划与控制。

PERT网络分析法[18]是一种项目管理工具，用于评估和优化项目进度。它通过构建项目任务的网络模型，对各个任务之间的关系和潜在影响进行定量分析，从而确定关键路径和时间优化点。

PERT网络分析法将项目分解为一系列任务，并标明任务之间的关系。每个任务都有一个预期的完成时间，包括最可能时间、最短时间和最长时间。通过这些时间参数，可以计算出关键路径，即决定项目总工期的任务链。

PERT网络分析法的优势在于其能够综合考虑任务之间的逻辑关系和时间因素，帮助项目经理更好地把握项目进度。通过分析关键路径，项目经理可以确定项目的瓶颈和优化潜力，从而制定更为有效的进度计划。此外，PERT网络分析法还可以用于风险评估和资源分配，提高项目管理的全面性和准确性。

通过构建项目的PERT图表，项目经理能够识别影响项目进度的关键任务，并据此优先分配资源和注意力，以确保这些任务能够按计划执行。同时，PERT网络分析法也支持项目经理进行风险评估和应变计划的制定，进而提高对不确定性的管理能力，并最终提升项目成功的可能性[19]。在项目实际操作过程中，考虑到每项活动的完成时间往往存在不确定性，PERT通过概率模型帮助管理者估计总项目完成时间的概率分布，为项目决策提供科学依据。

然而在PERT 网络分析法的应用也存在一定的局限性[20]。由于方法依赖于时间估计的准确性，如果项目团队对项目任务的时间估计存在较大偏差，则整个分析结果的可靠性将大大降低。因此，提高任务时间估计的准确性，对提升PERT网络分析法的有效性具有关键意义。

J公司平台项目作为一个具有多个依赖任务和多个团队协作的大型项目，对进度管理的要求异常严格。运用PERT网络分析法，项目经理能够明确每个活动的起始和结束时间，合理安排项目里程碑，同时预测不同工作流程可能带来的风险，从而为项目提供更为细致和适应性的进度管理策略。

比如，在设计阶段，通过对关键设计任务进行PERT分析，项目经理发现了某个设计过程可能会造成后期开发任务的延期。这时通过调整设计团队的资源分配和工作优先级，确保了该设计任务的顺利完成，避免了潜在的项目延期风险[21]。此外，不断根据项目实际进展更新PERT图表，使得项目团队能够及时应对和调整项目进度计划，对确保整体项目按时交付发挥了重要的作用。

值得一提的是，敏捷项目管理中强调的灵活性与变更的欢迎理念，也与PERT分析法相互补充。在敏捷管理之下，进行定期的迭代回顾和进度调整，在每次迭代中都可以使用PERT网络分析法对下一阶段的活动进行重新评估和规划，从而使得项目管理既保持了敏捷性，又不失精确度和控制力。

总结而言，PERT网络分析法作为一种科学的项目进度管理工具，已经在J公司平台项目中得到了有效的应用，成为了项目成功交付的重要保障手段。通过与敏捷开发理念的结合，它为项目管理实践提供了更为灵活和精确的进度管理解决方案，是现代项目管理中不可或缺的一部分。

# 2.3.2甘特图

在项目管理领域，甘特图作为一项历史悠久的进度分析工具，已经成为项目管理人员规划、协调和追踪项目进度的重要手段。

甘特图通过图表形式展示项目的时间轴和任务安排，通常包括水平时间轴和条形图两部分。时间轴表示项目的时间范围，条形图则代表各个任务，其中条形的长度表示任务的持续时间。此外，甘特图还可以标注任务之间的逻辑关系和依赖关系。

甘特图以其直观的时间轴和分明的任务划分，为项目团队提供了一个清晰的视图以展示项目的进度信息和关键活动间的依赖关系。在J公司推行敏捷开发的过程中，甘特图能够有效地辅助敏捷迭代的计划和跟踪，有助于提升项目透明度，并及时反馈项目进度的偏差。

甘特图可以详细展示项目中每一个任务的开始、结束时间和阶段性成果，通过色块的长短和位置关系，管理者可以迅速了解某一任务的持续时间以及与其他任务的并行或序列关系。在实际应用中，该工具已经在HR公司的X项目中得到了成功的应用。J公司平台项目在采用甘特图后，管理者能够从中识别潜在的冲突和资源分配问题，及时做出调整和优化。

J公司在进行项目进度管理时，首先会基于WBS[22]（项目的工作分解结构），将较大的工作任务细分成小的可管理任务单元，这样的操作不仅有利于甘特图的制作，也便于日后进度跟踪和控制。在此基础上，利用甘特图展示了各个子任务及其相互之间的逻辑关系，以便于全面考虑项目所需要的资源和时间限制，进而提升资源使用的效率。此外，通过与网络计划图结合，在甘特图的条形图基础上，增加关键路径的识别，这有助于项目团队顾及整个项目交付的关键任务，确保这些任务能够按期完成，从而保障整体项目的按时交付。

J公司在实际操作中，采取将不同功能团队或小组的任务结果合并到统一的甘特图中，这使得跨团队的协作和沟通变得更为高效。例如，在进行一个新的功能开发时，从设计、编码到测试的每个阶段都明确在甘特图中表示出来，每个阶段完成的实际时间与计划时间进行比较，一旦发现偏差，便能快速地调动资源和调整计划，避免进度延误对项目整体交付造成较大影响。

最终，J公司通过在甘特图中加入迭代周期和检查点，让团队能够定期回顾和反思项目进度管理的效果，并在此过程中积极查找潜在的改进机会。该方法将传统的甘特图和敏捷开发的思维模式有效结合，为公司提供了一个更加健全和高效的进度管理手段，有助于不断提升项目管理的水平和效果。

# 2.4小结

本章从理论概念上介绍了项目进度管理、Scrum开发方法，并针对后续使用的PERT、甘特图进行了介绍，同时在相关方法中结合J公司实际情况进行了分析。

# 第三章J公司项目现状

# 3.1J公司概述及项目背景介绍

J公司自2009年成立以来，一直致力于为某国家单位提供卓越的一站式产品管理运营解决方案。在十多年的发展历程中，公司积累了丰富的相关产品开发经验，其用户群体遍布全国，包括总局中心和各级省市中心的管理中心用户，以及约10万左右的门店用户。公司以稳定、安全为项目的第一要务，确保线上运营期间未出现任何重大业务问题。

作为一家拥有800余名在职员工的中型企业，J公司采用业务线的模式管理公司产品和相关职员，每个业务线在人员分配上都有独立完成一个项目的能力。J公司业务线丰富多样，其中3条主要业务线承载着公司3个核心平台项目产品的开发与运营，每个核心平台在近几年都准备了全面升级计划。在本文中，我们选取作者亲身参与研发的其中一个平台项目进行深入剖析。

该平台项目并非一个“全新”的项目，它的原型是公司于2012年上线并一直升级维护的“老”项目。因为上线时间超过10年，随着时间流逝，“老”项目越来越难以使用，出现的问题越来越多，修复的难度也越来越大，同时各省每年也会提出不少的个性化需求，根据开闭原则，每次新增的需求都会破坏原有的代码结果，使程序的冗余代码日益增多，线上风险逐年增加。为解决以上困境，同时为提高公司产品的处理能力并加强用户体验，随着软件技术水平的上升和用户需求的增加，在经过技术预研验证后，J公司于2022年开启新一代平台项目计划。

该平台项目作为公司业务的重要核心组成部分，其进度管理的方方面面都备受关注和考验，尤其在项目运行期间，出现过延期、测试不合格的情况。为了更好地应对这些挑战，J公司开始探索引入敏捷开发方法，希望以其灵活性和快速响应市场变化的能力，为J公司提供了新的项目管理思路，提高项目进度管理的效率和质量，增强企业的市场竞争力。同时，本次通过对项目进度管理内容的深度挖掘，结合项目管理中的进度管理知识体系，期望能够发现其中潜在的问题，分析问题的根源，并提出切实可行的解决方案。

# 3.2项目现状介绍

# 3.2.1WBS结构

在业务层面，该项目分为门户管理系统、产品运营管理系统、中心管理系统、门店终端系统和后台交易系统5个子系统，这些子系统各司其职，共同构建并维系整个平台项目的运行。具体来说，门户管理系统、产品运营管理系统以及中心管理系统均通过直观易用的页面操作，为管理中心用户提供便捷高效的服务体验。门店终端系统通过复用公司已有的移动端设备，补充平台项目相关功能。而后台交易系统则通过提供一系列相关业务接口，支撑门店用户和管理中心用户的各类业务操作，确保整个平台的顺畅运行和高效协作，系统WBS结构如下图3-1。

![](images/7a6c67e549f5aad7c32473017943df53cf8836e02796813cb985330886e71062.jpg)  
图3-1系统WBS结构图

# 3.2.2团队人员

在团队协作方面，项目组采用分业务模块的开发模式。每个业务模块对应一个WBS活动，根据模块所属系统分配具体的开发团队，每个开发团队由1名组长统筹安排协调每个团队的开发工作。2021年项目初期团队共涉及89人，由本业务线的23人和其他业务线的66人组成，人力资源信息如下表3-1。

表3-1项目人力资源表  

<table><tr><td>人员类型 人数</td></tr><tr><td>项目经理 1</td></tr><tr><td>技术经理 1</td></tr><tr><td>产品经理 1</td></tr><tr><td>测试经理 1</td></tr><tr><td>架构师 1（软件架构）+1（数据库专家）</td></tr><tr><td>业务专家 2</td></tr><tr><td>需求人员 3</td></tr><tr><td>门户管理开发 1（组长）+7（组员）</td></tr><tr><td>产品运营开发 1（组长）+8（组员）</td></tr><tr><td>中心管理开发 1（组长）+15（组员）</td></tr><tr><td>门店终端开发 1（组长）+13（组员）</td></tr><tr><td></td></tr><tr><td>后台交易开发 1（组长）+7（组员）</td></tr><tr><td>测试环境组 2</td></tr><tr><td>功能测试 19 性能测试 2</td></tr></table>

因项目在种类上属于使用新技术全面升级旧系统，基本没有新的需求，故在项目人力资源表上需求人员仅有3人，负责和业务专家直接交互，并按照平台项目的进度计划输出需求文档。开发人员的比重较大，共55人，并按照不同系统分成了5个团队。

# 3.2.3进度计划

平台项目采用迭代式开发模式[23]，迭代周期为一个月，这种模式以预研时的系统架构为基础，严格按照需求、研发、测试的顺序推进。每个月，项目团队都会提交阶段成果物至下一阶段，形成紧密的工作闭环。需求阶段，需求人员根据制定好的需求范围编写需求说明书，明确功能需求和业务逻辑；研发阶段，研发团队根据需求说明书进行应用开发，并提交应用发布列表，详细列出本次迭代的功能点和改动内容；测试阶段，测试团队针对开发出的功能进行全面测试，并提供详细的测试报告，确保软件质量。

项目的排列活动顺序方面，以产品经理为核心，联合项目经理、技术经理、测试经理，根据业务功能的重要性、项目人力资源实际情况，参考已有项目[24-25]使用PERT网络分析法，对图3-1内活动的顺序进行排列，最终结果如下表3-2。

表3-2WBS活动表  

<table><tr><td>序号</td><td>活动名称</td><td>活动编号</td><td>前置活动</td><td>预计工期（日）</td></tr><tr><td>1</td><td>莱单管理</td><td>A</td><td></td><td>21</td></tr><tr><td>2</td><td>权限管理</td><td>B</td><td>A</td><td>22</td></tr><tr><td>3</td><td>用戶管理</td><td>C</td><td></td><td>40</td></tr><tr><td>4</td><td>角色管理</td><td>D</td><td>B、C</td><td>22</td></tr><tr><td>5</td><td>产品管理</td><td>E</td><td>B</td><td>43</td></tr><tr><td>6</td><td>公告管理</td><td>F</td><td>E</td><td>23</td></tr><tr><td>7</td><td>兑奖管理</td><td>G</td><td>E</td><td>132</td></tr><tr><td>8</td><td>容器管理</td><td>H</td><td>E</td><td>45</td></tr><tr><td>9</td><td>系統管理</td><td>1</td><td>E</td><td>23</td></tr><tr><td>10</td><td>仓库管理</td><td>J</td><td>E</td><td>45</td></tr><tr><td>11</td><td>采购管理</td><td>K</td><td>J</td><td>43</td></tr><tr><td>12</td><td>库存管理</td><td>L</td><td>J</td><td>21</td></tr><tr><td>13</td><td>调拨管理</td><td>M</td><td>J</td><td>43</td></tr><tr><td>14</td><td>订单管理</td><td>N</td><td>J</td><td>65</td></tr><tr><td>15</td><td>中心兑奖</td><td>0</td><td>E</td><td>132</td></tr><tr><td>16</td><td>异常管理</td><td>P</td><td>E</td><td>23</td></tr><tr><td>17</td><td>工单管理</td><td>Q</td><td>B</td><td>22</td></tr><tr><td>18</td><td>门店信息管理</td><td>R</td><td>E</td><td>45</td></tr><tr><td>19</td><td>门店运营管理</td><td>S</td><td>R</td><td>60</td></tr><tr><td>20</td><td>门店订单管理</td><td>T</td><td>N、R</td><td>130</td></tr><tr><td>21</td><td>门店兑奖</td><td>U</td><td>R</td><td>130</td></tr></table>

续表3-2WBS活动表  

<table><tr><td>序号</td><td>活动名称</td><td>活动编号</td><td>前置活动</td><td>预计工期（日）</td></tr><tr><td>22</td><td>每日统计</td><td>V</td><td>J、R</td><td>43</td></tr><tr><td>23</td><td>定时同步</td><td>W</td><td>J、R</td><td>43</td></tr><tr><td>24</td><td>第三方调拨管理</td><td>X</td><td>M</td><td>44</td></tr><tr><td>25</td><td>第三方订单管理</td><td>Y</td><td>T</td><td>43</td></tr><tr><td>26</td><td>第三方兑奖</td><td>Z</td><td>U</td><td>43</td></tr><tr><td>27</td><td>第三方信息管理</td><td>AA</td><td>R</td><td>43</td></tr></table>

根据表3-2WBS活动表中的任务编号及迁至活动关系，整理出的网络图如下图3-2。

![](images/ce02dcf681f4e9d0a35a89127ff40d13308b9dd7f0408b0b871ab9c868165cb0.jpg)  
图3-2项目网络图

根据表3-2WBS活动表和图3-2项目网络图，平台项目的WBS任务间的前后关联性并不强，编写项目计划时的主要制约因素是项目人员安排，需要考虑不同开发团队间的业务差距和学习成本。

因平台项目按月进行迭代开发，表3-2中的预计工期（日）列实际是按照自然月进行粗略估算的。参考图3-2，项目初期根据以上数据绘制的进度计划甘特图如图3-3。

![](images/0d0a5fa68ef4dbd3bddae8e059f8b70c272c61b0d8b8c01d020283b3730cd90b.jpg)  
图3-3进度计划甘特图

控制进度方面，每个月度版本开始前，项目组根据制定好的进度计划确定当月版本内容。版本迭代开始后，为了确保项目进度的顺利进行，项目组每周召开周例会进行当月版本的进度追踪和异常处理。当遇到异常情况时，项目团队会灵活应对，通过变更需求范围来保证每个月度版本的正常交付。这种变更可能包括新增紧急需求以应对突发事件，或者延后一些非核心需求以优化资源配置。

# 3.3项目问题调研

该项目自2022年启动，截至2023年6月，平台项目的未按期完成活动情况如下表。

表3-3WBS未完成活动表  

<table><tr><td>序号</td><td>活动名称</td><td>完成百分比</td></tr><tr><td>1</td><td>产品管理</td><td>70%</td></tr><tr><td>2</td><td>兌奖管理</td><td>80%</td></tr><tr><td>3</td><td>调拨管理</td><td>80%</td></tr><tr><td>4</td><td>订单管理</td><td>50%</td></tr><tr><td>5</td><td>中心兑奖</td><td>20%</td></tr><tr><td>6</td><td>门店订单管理</td><td>0%</td></tr><tr><td>7</td><td>门店兑奖</td><td>40%</td></tr><tr><td>8</td><td>第三方调拨管理</td><td>30%</td></tr><tr><td>9</td><td>第三方兑奖</td><td>0%</td></tr></table>

表3-3为2023年6月时，项目进度计划中未按时完成活动，而在6月之前，包括门店信息管理等活动也存在延迟，结合项目延迟完成情况，通过调查分析法进行深入分析。

本次执行调查分析法时，采用问卷调查和专家访谈的方式进行[26]，专家访谈则按照管理层和执行层两个方向，分别准备访谈问题的选择受访人员。

# 3.3.1问卷调查

本次问卷调查采用匿名问卷的方式，参考其他项目进度管理文献中的内容，结合上一小节的突出问题，针对当前平台项目的所有成员，准备了调查问卷，内容详见附录A。

本次调查问卷共收到73份反馈，通过对问卷中的答案进行分类整理，结果如下表。

表3-4问卷回答数据整理  

<table><tr><td>问题分类</td><td>具体问题</td><td>同意数</td></tr><tr><td>个人</td><td>技术能力不足</td><td>30</td></tr><tr><td>个人</td><td>时间估计或时间安排不合理</td><td>25</td></tr><tr><td>个人</td><td>工作量过大</td><td>56</td></tr><tr><td>个人</td><td>需求理解有问题</td><td>40</td></tr><tr><td>流程</td><td>需求变更频繁</td><td>63</td></tr><tr><td>流程</td><td>临时插入新需求照成进度影响</td><td>56</td></tr><tr><td>流程</td><td>研发的需求不统一影响开发</td><td>50</td></tr><tr><td>进度管理</td><td>进度变动没有采取措施</td><td>40</td></tr><tr><td>进度管理</td><td>进度计划不合理</td><td>24</td></tr><tr><td>进度管理</td><td>进度跟踪不及时</td><td>38</td></tr></table>

对调查问卷的结果汇总分析可以得出，对项目有较大影响的因素包括如下几个方面：

（1）需求变更过多，影响整个项目的进度管理。  
(2) 项目末期仍在接收用户的新需求，开发时间紧张。  
(3) 过于追求按期完成月度版本开发，质量有所下降。  
(4) 项目成员沟通交流过少，很多业务概念在理解上没有达成统一。  
(5）人员分配可以优化，有些项目成员的空闲时间大于1周。  
（6）项目进度管理相关文档更新不够及时，导致后期存在遗漏。

# 3.3.2专家访谈

参考表3-1项目人力资源表的资源分配情况，分别对平台项目的层级管理人员和欧通团队成员进行了面对面访谈，从管理层和执行人员两个方向探讨平台项目进度管理方面的问题。

# 1．管理层级专家访谈

以下表3-4提供了层级管理受访人员的相关信息。名单如下表所示。

表3-5层级管理受访人员表  

<table><tr><td>序号</td><td>姓名</td><td>岗位</td><td>年龄 工龄</td></tr><tr><td>1</td><td>韦**</td><td>项目经理</td><td>37 13</td></tr><tr><td>2</td><td>顾**</td><td>产品经理</td><td>49 24</td></tr><tr><td>3</td><td>马*</td><td>技术经理</td><td>41 17</td></tr><tr><td>4</td><td>李**</td><td>测试经理</td><td>47 23</td></tr><tr><td>5</td><td>韩**</td><td>门户管理开发组长</td><td>36 14</td></tr><tr><td>6</td><td>牛**</td><td>产品运营开发组长</td><td>32 10</td></tr><tr><td>7</td><td>杨*</td><td>中心管理开发组长</td><td>34 12</td></tr><tr><td>8</td><td>魏**</td><td>门店终端开发组长</td><td>40 12</td></tr><tr><td>9</td><td>王**</td><td>后台交易开发组长</td><td>29 7</td></tr><tr><td>10</td><td>张*</td><td>功能测试组长</td><td>43 20</td></tr></table>

对平台项目内需要关注进度的相关干系人展开访谈，访谈包含问题详见附录B。

# 2.执行人员访谈

以下表3-5提供了部分执行人员的相关信息。名单如下表所示。

表3-6团队普通受访人员表  

<table><tr><td>序号</td><td>姓名</td><td>岗位</td><td>年龄</td><td>工龄</td></tr><tr><td>1</td><td>王*</td><td>需求人员</td><td>32</td><td>8</td></tr><tr><td>2</td><td>李*</td><td>门户管理开发组员</td><td>30</td><td>6</td></tr><tr><td>3</td><td>陈*</td><td>门户管理开发组员</td><td>24</td><td>2</td></tr><tr><td>4</td><td>高*</td><td>产品运营开发组员</td><td>27</td><td>5</td></tr><tr><td>5</td><td>曲*</td><td>产品运营开发组员</td><td>36</td><td>14</td></tr><tr><td>6</td><td>牛*</td><td>中心管理开发组员</td><td>34</td><td>12</td></tr><tr><td>7</td><td>许**</td><td>中心管理开发组员</td><td>29</td><td>6</td></tr><tr><td>8</td><td>蒋**</td><td>门店终端开发组员</td><td>40</td><td>12</td></tr><tr><td>9</td><td>李**</td><td>门店终端开发组员</td><td>29</td><td>6</td></tr><tr><td>10</td><td>耿**</td><td>功能测试人员</td><td>25</td><td>2</td></tr></table>

续表3-6团队普通受访人员表  

<table><tr><td>序号</td><td>姓名</td><td>岗位</td><td>年龄</td><td>工龄</td></tr><tr><td>11</td><td>徐*</td><td>功能测试人员</td><td>34</td><td>10</td></tr><tr><td>12</td><td>胡**</td><td>性能测试人员</td><td>43</td><td>20</td></tr></table>

对上表中抽选的团队普通成员的访谈问题详见附录C。

# 3.结果整理

通过以上两类访谈，结合管理层和执行层两方面对平台项目的进度管理现状有了更加深刻的理解。对访谈结果分析总结，当前平台项目在进度管理上的问题主要表现为以下6点。

(1)开发流程问题。

流程上研发分配的任务研发人员知细节不知整体，相关信息的流向只是从项目经理层到开发组长层，甚至只有项目经理知晓，其结果导致绝大多数项目成员对当前阶段、所属小组甚至整个项目进度不甚了解，无法对整体成果有清晰认识，而这份流程信息的缺失实际上的确导致了一些本不该出现的问题的发生，最终验收整个最终效果迟缓。

如中心管理系统2022年10月版本提测时，忽略了上个月门店终端系统上线的版本内容，导致两个系统在业务数据处理上出现了纰漏，中心管理系统的开发人员不得不额外花费1人日梳理并还原数据变化。

# (2)需求变更问题。

因为有上一代平台项目参考，本次平台项目在需求管理上的工作接近“翻译”，团队中的3名需求人员都是新人，对上一代平台项目的各项业务了解的不多，再加上功能的数量又大，最终导致在后期月度版本中经常出现需求变更的情况。

截至目前的项目周期内出现频率最高的3类是：补充需求范围使之更加满足用户使用、补充前一代平台项目遗漏的功能、重构整个功能的需求用例，这些需求变更带来的最直观问题是工作量的增加，需要研发团队和测试团队通过赶工的方式追平进度。

(3)团队沟通问题。

该问题分成团队内沟通问题和团队外沟通问题两类。团队内沟通问题主要发生在开发团队，其中最严重的问题是每个月度版本临近结束时才会进行代码评审会议，而 $30 \%$ 左右的代码评审结论是不通过，需要代码负责人通过赶工的方式保证月度版本进度正常；团队外沟通问题发生在开发团队和测试团队之间， $73 \%$ 的问题是对同一段需求的理解有差异，导致测试阶段需要反复进行澄清。2023年4月后，测试BUG在分类上甚至专门增加“澄清”这一选项，用来记录开发团队和测试团队在需求理解上的差异性。

除此之外，沟通管理上的问题成因还有如下几点：项目成员之间不熟悉，在找人和沟通实际问题时需要额外花费时间；后期项目进度紧张，绝大部分沟通以口头为主，出问题时只能靠记忆回溯；每个人了解到的项目的实际情况都不相同，存在信息差，这些信息差在问题排查时带来了时间损耗；部分变更通知不到位，使得问题处理人的不得不通过赶工的方式保证项目进度。

# (4)技术架构问题。

平台项目在2021年进行过项目预研，当时后端使用了最新的SpringCloud框架，但在项目正式开始后，因实际项目团队中对该框架熟悉的人过少，最后采

用的还是按照Spring项目搭建的底层框架，导致在一些核心业务的性能调优上花费了很多时间。

项目前端使用VUE+ANTDV架构，专门搭建了一套项目专用前端框架，但项目前期缺少前端架构师，框架内部的基础组件过于基础，导致每个团队相当于从零开始使用VUE+ANTDV编写代码。

# (5)人员不足问题。

该问题在项目周期内发生了2次，分别是功能测试人员和中心管理开发人员临时调走1个月，直接影响是表3-3WBS未完成活动表中产品管理、兑奖管理活动的测试未完成和订单管理活动的开发未完成。

# (6)人员能力问题。

在开发环节出现，表现为人员实际能力和负责的开发任务不匹配。不匹配的方式有两类，一类是让后端开发人员跨域兼职前端页面开发，因对前端架构体系的不熟悉，导致相关功能在测试阶段的问题层出不穷；另一类是技术能力水平不满足开发任务要求，导致开发进度缓慢。

# 3.4项目问题成因分析

# 3.4.1WBS分解结构不合理

由于工作时间和经验的差异，项目团队中的开发人员可能需要不同的工作时间来实现相同的功能。在确定活动持续时间的过程中，有必要充分考虑项目团队的技术和学习能力。每项活动的持续时间应根据个体差异逐一考虑持续时间。否则，如果不考虑成员间的差异性，极可能出现能力强的人已经空闲很长时间了，能力茶的成员还只能加班加点慢慢赶进度。这种情况会给团队的稳定和发展埋下隐患。

同时，不同成员间的能力也有适配性，对于学习能力强、爱钻研的人适合框架工作或者技术特性研究工作，经验丰富的人适合那些大量重复的功能开发，能力弱和刚来不久的新人成员就多安排重复性比较多、技术需求较低的内容、逻辑简单的工作，这样一来，让每个人都能体会到在工作中实现自已能力的价值机会，增强每个人的成就感与价值感。

# 3.4.2进度计划不合理及任务估时不准确

计划编制问题。在进度计划管理方面，我们面临的主要挑战是透明度不足以及员工参与度的欠缺。当前的进度计划未能清晰展现任务的紧迫性，导致团队成员对于自身工作的重要性认识不足。此外，计划制定过程中缺乏团队开发人员的直接参与，这种缺乏集体智慧的现象可能影响计划的合理性与可执行性。

为了增强进度计划的透明度和有效性，应鼓励团队成员积极参与计划的制定过程。通过引入团队讨论环节，可以汇聚多方视角，集思广益，确保计划既符合项目整体需求，又充分考虑了实际操作中的细节与难点。这种参与式决策模式不仅能够提升计划的合理性和可操作性，还能增强团队成员的责任感和归属感，促使他们在执行任务时更加谨慎细致，减少因理解偏差导致的返工情况。

因为有上一代平台项目参考，本次平台项目的进度计划在编制时直接以上一代平台的所有功能为最小单元编排顺序、预估耗时。而实际在执行时发现虽然在功能上和上一代基本一致，但因为技术架构发生了变化，部分功能在开发时才发现相关编排出现了偏差，必须通过调整变更范围和进度计划才能保证项目的按时交付，最终导致项目延迟交付。

# 3.4.3团队人员进度控制能力不足

员工组织层面，主要靠在公司的工龄分配的组长，其中4位对平台项目的相关业务并不熟悉，没有具体的管理能力。

研发流程可以更优化，首先对于活动执行情况没有很好的进度监控，每次只靠类似“完成了吗？”“完成了”这样的沟通直接判定任务完成情况，经常出现测试阶段才发现实现有问题的情况。其次开发版本计划的内容，现在的分配方式是颗粒度较粗，出现过几次分配不均衡的情况。在项目管理领域内，每个人一定时间里只能完成一定的工作量，出现这种情况会使进度滞后。第三，沟通和协作不畅的问题，月度版本内容只靠简单文本进行记录，已完成的版本也没有落实成有效文档。

# 3.4.4需求变更流程混乱

控制范围变更的流程问题。3.1章节有提到，本次平台项目有上一代平台这一参考模板，实际上本项目的需求范围直接参考上一代平台项目的需求范围和平台功能，理论上不会有频繁的需求变更。但实际上因为参与的需求人员之前没有接触上一代平台，两代平台项目的技术架构又不同，再加上业务专家和产品经理又不能按功能依次讲解，本次平台项目的需求范围在每个月度版本中都出现了需求变更，表3-3WBS未完成活动表中的中心兑奖活动甚至出现了需求重构的情况，最终导致项目进度延期。

没有坚持老员工带新员工的模式。在2022 年项目初期时，开发团队和测试团队还是一个团队老人带一个新人的模式，方便新人能够更快加入实际工作中，但在2023年4月后，因项目进度延后，项目经理申请增加开发和测试资源，全面追赶项目进度，导致新加入的团队成员除了最开始的1周里搜了培训，剩下的时间只能自己一点一点熟悉相关工作任务，再加上临近项目后期，所有工作任务比较复杂，需要额外的时间和相关需求、开发、测试人员了解详情，最终导致项目进度依然延后。

# 3.4.5开发方法存在漏洞

开发进度控制方面，本次平台项目的开发进度控制以3.2章节中的进度计划为核心，该计划中每个活动的开发耗时参考上一代平台的开发耗时。实际执行时只靠开发组长通过口头询问的方式检查对应开发任务是否完成，导致很多功能在提测后发现需要重新编写代码。

需求频繁的变更时，没有严格把控，随意变更延长任务的时长，对于低延时的任务随意延长的不加以控制，进度缓慢，对于延时长的任务没有进一步的切分，分配不合理。领导对于延期任务的并不知情，具体执行的策划与程序直接延长任务的时间，而对于上级领导仅仅是策划领导的知情权，程序领导没有知情，没有进一步的引导，仅凭初中级程序员的自我控制。

的时间和相关需求、开发、测试人员了解详情，最终导致项目进度依然延后。

# 3.4.6缺少合适的风险预警机制

在进度计划出现偏离时，没有及时使用相关进度管理工具方法进行分析，导致对月度版本延期的情况敏感度不高，没能及时处理进度问题。如下图是截至6月份时平台项目的剩余工作量燃尽图分析，可以明显发现在22年7月份已经开始出现进度偏差了，但当时没有进行相关处理。

![](images/ebe7dd252f95d11d91c2ac9455e7f4a1f1a5ab61f4de59a389ed5763bc9db7bc.jpg)  
图3-3燃尽图

# 3.5小结

本章介绍J公司和平台项目的基本情况，并用调查分析法等方式对项目进度进行分析队中的开发人员可能需要不同的工作时间来实现相同的功能。在确定活动持续时间的过导的知情权，程序领导没有知情，没有进一步的引导，仅凭初中级程序员的自我控制。

在进度计划出现偏离时，没有及时使用

# 第四章 平台项目进度管理优化方案设计

平台项目作为新一代供全国10万用户使用的系统，被精细地划分为五个核心子系统：门户管理系统、产品运营管理系统、中心管理系统、门店终端系统以及后台交易系统。这些子系统各自承载着独特的职责，通过协同作用，共同维系了整个平台项目的运行。门户管理系统、产品运营管理系统及中心管理系统均采用了直观且用户友好的页面设计，旨在为管理中心的用户提供便捷、高效的服务体验。它们通过精细化的功能划分与流程设计，实现了对平台各项关键业务的有效管理与监控。门店终端系统则巧妙地复用了公司现有的移动设备资源，通过集成与定制化开发，补充了平台项目在门店层面的相关功能，从而确保了门店用户能够享受到与平台整体无缝对接的使用体验。而后台交易系统作为整个平台的业务支撑核心，通过提供一系列稳定、高效的业务接口，支撑了门店用户和管理中心用户的各类复杂业务操作。这不仅确保了整个平台的交易流程的顺畅无阻，也实现了各子系统间的高效协作与数据共享，为平台的长期稳定运行奠定了坚实的基础。

本章根据上一章分析得出的问题及成因，针对平台项目剩余系统功能，提供以下方案设计。

# 4.1优化 WBS任务分解

本次平台项目的WBS任务分解过于依赖上一代平台项目，直接以业务功能作为WBS活动包，任务颗粒度大。在实际执行时相关小组需要先手动把相关功能拆分为需求、开发、测试三部分，然后再额外花时间商量该如何分配时间，造成了资源浪费。绝大部分情况下资源分配是不均匀的，需要压缩需求人员和开发人员的时间，导致测试阶段需求澄清较多，最终导致需求变更频繁发生，最终进度缓慢。

表4-1WBS未完成活动表  

<table><tr><td>序号</td><td>活动名称</td><td>完成百分比</td></tr><tr><td>1</td><td>产品管理</td><td>70%</td></tr><tr><td>2</td><td>兑奖管理</td><td>80%</td></tr><tr><td>3</td><td>调拨管理</td><td>80%</td></tr><tr><td>4</td><td>订单管理</td><td>50%</td></tr><tr><td>5</td><td>中心兑奖</td><td>20%</td></tr><tr><td>6</td><td>门店订单管理</td><td>0%</td></tr><tr><td>7</td><td>门店兑奖</td><td>40%</td></tr><tr><td>8</td><td>第三方调拨管理</td><td>30%</td></tr></table>

续表4-1WBS未完成活动表  

<table><tr><td>序号</td><td>活动名称</td><td>完成百分比</td></tr><tr><td>9</td><td>第三方兑奖</td><td>0%</td></tr><tr><td>10</td><td>第三方订单管理</td><td>0%</td></tr></table>

作为项目进度管理的基础，本小节对表4-1平台项目未完成的任务活动进行优化分解，根据Scrum开发方法，把WBS工作包的维度从系统功能变更为功能需求、功能页面、后台接口或功能测试，供后续按照Backlog的方式逐项推进进度。最终输出如下：

![](images/de5cfd37339688b3c17ec6da1efb7d1c892b67c76562e403c7f64eb55c528815.jpg)  
图4-1新WBS任务图

重新划分WBS活动后，结合原表3-2WBS活动数据，根据图4-1新WBS任务图及实际业务关系，输出如下表4-2新WBS活动表。

表4-2新WBS活动表  

<table><tr><td>序号</td><td>活动名称</td><td>活动编号</td><td>紧前活动</td></tr><tr><td>1</td><td>产品导入管理功能测试</td><td>A1</td><td></td></tr><tr><td>2</td><td>产品运营管理功能测试</td><td>A2</td><td>A1</td></tr><tr><td>3</td><td>批量兑奖功能测试</td><td>B1</td><td></td></tr><tr><td>4</td><td>异常调拨管理功能测试</td><td>C1</td><td></td></tr><tr><td>5</td><td>跨省调拨管理功能測试</td><td>C2</td><td>C1</td></tr><tr><td>6</td><td>快速交易列表页开发</td><td>D1</td><td></td></tr><tr><td>7</td><td>快速交易编辑页开发</td><td>D2</td><td>D1</td></tr><tr><td>8</td><td>快速交易功能测试</td><td>D3</td><td>D2</td></tr><tr><td>9</td><td>省中心兌奖功能需求</td><td>E1</td><td></td></tr><tr><td>10</td><td>省中心兑奖单页面开发</td><td>E2</td><td>E1</td></tr><tr><td>11</td><td>省中心兑奖功能测试</td><td>E3</td><td>E2</td></tr><tr><td>12</td><td>门店订单管理功能需求</td><td>F1</td><td></td></tr><tr><td>13</td><td>门店订单管理列表页开发</td><td>F2</td><td>F1</td></tr></table>

续表4-2新WBS活动表  
根据表4-2输出新的网络图4-2  

<table><tr><td>序号</td><td>活动名称</td><td>活动编号</td><td>紧前活动</td></tr><tr><td>14</td><td>门店订单管理编辑页开发</td><td>F3</td><td>F2</td></tr><tr><td>15</td><td>门店订单管理功能测试</td><td>F4</td><td>F3</td></tr><tr><td>16</td><td>门店兑奖单页面开发</td><td>G1</td><td></td></tr><tr><td>17</td><td>门店兑奖单功能测试</td><td>G2</td><td>G1</td></tr><tr><td>18</td><td>第三方调拨管理列表页开发</td><td>H1</td><td></td></tr><tr><td>19</td><td>第三方调拨管理编辑页开发</td><td>H2</td><td>H1</td></tr><tr><td>20</td><td>第三方调拨管理功能测试</td><td>H3</td><td>H2</td></tr><tr><td>21</td><td>第三方兌奖功能需求</td><td>11</td><td></td></tr><tr><td>22</td><td>第三方兑奖后台接口</td><td>12</td><td>I1</td></tr><tr><td>23</td><td>第三方兌奖接口测试</td><td>13</td><td>12</td></tr><tr><td>24</td><td>第三方订单管理功能需求</td><td>J1</td><td>F1</td></tr><tr><td>25</td><td>第三方订单管理后台接口</td><td>J2</td><td>J1</td></tr><tr><td>26</td><td>第三方订单管理接口测试</td><td>J3</td><td>J2</td></tr></table>

![](images/b40dc7ad7ae7da729bc9db612008e9c0a1a989c82208f180623eedf129bd07b6.jpg)  
图4-2新项目活动图

# 4.2任务活动历时估算修改

本小节使用PERT网络分析法对表4-2新WBS活动表中的活动进行耗时估算，进而优化项目进度安排。

在项目进度管理中，直接和时间打交道的PERT方式是用起来最直观且学习成本最低的方法。该方法能有效识别项目中各个活动的最早开始时间和最晚开始时间，以及活动所允许的时间浮动空间。通过计算项目活动的最乐观时间、最可能时间和最悲观时间，能够估算出每个活动可能的持续时间，并据此构建出网络计划图。

项目使用PERT方法进行工期估算时[27，定义中的最可能时间使用业务专家给出的时间，最乐观时间使用项目经理给出的估算时间，最悲观时间使用各模块团队给出的估算时间，按照定义公式（最乐观时间 $+ 4 \times$ 最可能时间 $^ +$ 最悲观时间）/6的平均权重值计算得出。

表4-3新WBS活动表任务估算表  

<table><tr><td>活动 编号</td><td>活动名称</td><td>最可能 （日）</td><td>悲观估计 （日）</td><td>乐观估计 （日）</td><td>预计工期 （日）</td></tr><tr><td>A1</td><td>产品导入管理功能测试</td><td>10</td><td>15</td><td>13</td><td>12</td></tr><tr><td>A2</td><td>产品运营管理功能测试</td><td>5</td><td>6</td><td>5</td><td>6</td></tr><tr><td>B1</td><td>批量兑奖功能测试</td><td>10</td><td>15</td><td>7</td><td>11</td></tr><tr><td>C1</td><td>异常调拨管理功能测试</td><td>10</td><td>13</td><td>4</td><td>10</td></tr><tr><td>C2</td><td>跨省调拨管理功能測试</td><td>5</td><td>5</td><td>5</td><td>5</td></tr><tr><td>D1</td><td>快速交易列表页开发</td><td>10</td><td>14</td><td>5</td><td>10</td></tr><tr><td>D2</td><td>快速交易编辑页开发</td><td>10</td><td>12</td><td>4</td><td>10</td></tr><tr><td>D3</td><td>快速交易功能测试</td><td>15</td><td>24</td><td>13</td><td>17</td></tr><tr><td>E1</td><td>省中心兌奖功能需求</td><td>5</td><td>9</td><td>3</td><td>6</td></tr><tr><td>E2</td><td>省中心兑奖单页面开发</td><td>15</td><td>22</td><td>5</td><td>15</td></tr><tr><td>E3</td><td>省中心兑奖功能测试</td><td>10</td><td>18</td><td>11</td><td>12</td></tr><tr><td>F1</td><td>门店订单管理功能需求</td><td>5</td><td>9</td><td>5</td><td>6</td></tr><tr><td>F2</td><td>门店订单管理列表页开发</td><td>10</td><td>18</td><td>4</td><td>11</td></tr><tr><td>F3</td><td>门店订单管理编辑页开发</td><td>5</td><td>5</td><td>4</td><td>5</td></tr><tr><td>F4</td><td>门店订单管理功能测试</td><td>15</td><td>27</td><td>15</td><td>17</td></tr><tr><td>G1</td><td>门店兑奖单页面开发</td><td>20</td><td>36</td><td>19</td><td>23</td></tr><tr><td>G2</td><td>门店兑奖单功能测试</td><td>15</td><td>22</td><td>15</td><td>17</td></tr><tr><td>H1</td><td>第三方调拨管理列表页开发</td><td>5</td><td>6</td><td>4</td><td>5</td></tr><tr><td>H2</td><td>第三方调拨管理编辑页开发</td><td>5</td><td>8</td><td>4</td><td>6</td></tr><tr><td>H3</td><td>第三方调拨管理功能测试</td><td>10</td><td>13</td><td>9</td><td>11</td></tr><tr><td>11</td><td>第三方兑奖功能需求</td><td>5</td><td>7</td><td>5</td><td>6</td></tr><tr><td>12</td><td>第三方兑奖后台接口</td><td>15</td><td>28</td><td>14</td><td>17</td></tr><tr><td>13</td><td>第三方兑奖接口测试</td><td>10</td><td>10</td><td>9</td><td>10</td></tr><tr><td>J1</td><td>第三方订单管理功能需求</td><td>5</td><td>5</td><td>3</td><td>5</td></tr></table>

续表4-3新WBS活动表任务估算表  

<table><tr><td>活动 编号</td><td>活动名称</td><td>最可能 （日）</td><td>悲观估计 （日）</td><td>乐观估计 （日）</td><td>预计工期 （日）</td></tr><tr><td>J2</td><td>第三方订单管理后台接口</td><td>20</td><td>27</td><td>15</td><td>21</td></tr><tr><td>J3</td><td>第三方订单管理接口测试</td><td>10</td><td>13</td><td>10</td><td>11</td></tr></table>

在实施过程中，PERT网络分析法为J公司提供了以数据为依据的决策支持，通过考虑任务完成时间的不确定性，帮助项目经理制定应变计划，提高对不确定性的管理能力，帮助项目管理团队更准确地识别可能发生的风险；对项目进行全面系统的规划，确保所有关键任务和依赖关系都被考虑在内，使得项目经理的决策更加科学和合理，减少了决策的盲目性和主观性；可以按照任务估算表合理安排项目资源，最大化减少未知因素对项目进度的影响；通过综合考虑项目的时间、成本和资源约束，PERT网络分析法有助于项目经理制定更加切实可行的项目计划，从而提高项目的成功率。通过网络图的形式，直观展现了项目进度的全貌，增强了各个小组成员对项目总体目标和自身工作任务的清晰认识，方便项目团队成员能够更好地理解项目进度和计划，增强项目的透明度。尽管存在技术挑战和不确定性因素，J公司通过PERT网络分析法对项目进度的精准管理，不仅优化了团队协作流程，也显著提升了项目交付的效率和质量。

# 4.3调整项目进度计划

本小节通过重新编排项目进度计划和设置里程碑的方式优化设计方案。

在制定项目进度计划时，在进度时间表上设立一些重要的时间检查点，这样一来，就可以在项目执行过程中利用这些重要的时间检查点来对项目的进程进行检查和控制。这些重要的时间检查点被称作项目的里程碑。

版本里程碑计划的可以使整个团队在过程中就预见到产品完成时将呈现的样子，这对于调动团队热情有十分重要的意义。为团队人员坚持执行、效率执行奠定了基础。

通过对未完成活动及整个平台项目的进度计划的详细分析，产生如下项目里程碑计划：

表4-4平台项目新里程碑计划表  

<table><tr><td>里程碑</td><td>完成日期</td><td>交付物</td></tr><tr><td>产品运营测试完成</td><td>2023/7/26</td><td>《产品运营管理系统测试报告》</td></tr><tr><td>中心门店测试完成</td><td>2023/10/23</td><td>《中心管理系统测试报告》、《门店终端系 统测试报告》</td></tr><tr><td>第三方接口测试完成</td><td>2023/12/6</td><td>《后台交易系统测试报告》</td></tr></table>

上表中的里程碑计划实际上是根据未完成活动设定的，这三个里程碑的本质分别是：未测试完的功能、未开发完的功能、未输出需求文档的功能。项目经理

希望通过这样的划分方式，简化各个小组执行时的工作安排传达。结合表4-1、表4-2、表4-3，输出如下新进度计划甘特图。

![](images/dd2c46dda48603d251df347be1db1f1f66d4dccd73331ee88936015fdd468210.jpg)  
图4-3新进度计划甘特图

因项目接近尾声，公司有其他要求，平台项目的人力资源有所减少，需求人员缩减为2人，开发人员缩减为23人，测试人员缩减为11人。图4-3展示的新进度计划甘特图在任务编排上直接按照需求组、开发组、测试组进行排列。

# 4.4增强人员的进度控制能力

在对平台项目已经暴露出来的进度问题经过分析后，公司项目管理团队逐渐意识到，高效运作的项目团队不仅需要合理的流程和工具，还要拥有能力达标的团队成员。平台项目组人员复杂，来自不同业务线的不同人员在沟通不畅时，很多原因不是技能强弱的问题，而是因为每个人在一件事上的观点不同，立场也就不同，那么这个项目就是把人们聚集在一个组织中，朝着一个统一和基线范围的目标努力。针对人员配置和角色定位，作者将再次提出关于人员管理的几点改进，以确保资源充分利用，进而提升项目进度与质量。

(1)制定培训机制。在优化J公司平台项目进度管理的过程中，构建一套全面而系统的培训机制是至关重要的环节。该培训机制被精心设计为多维度、分层次的教育体系，旨在全方位提升项目团队的专业技能与流程管理能力，从而有效克服因能力不足及流程不熟悉所导致的项目延期问题。培训体系划分为专业技能培训与进度流程培训两大核心板块。专业技能培训聚焦于提升团队成员的专业素养与实战能力，采用按需定制的教学模式，确保培训内容紧密贴合项目实际需求。每两周一次的密集技术培训，不仅涵盖了由行业专家精心设计的专项技能课程，还深入探讨了项目当前阶段所需的底层架构知识，确保团队成员能够紧跟技术前沿，高效解决项目开发中的技术难题。此外，通过引入培训反馈机制，项目团队成员可通过匿名问卷形式，就学习效果、课程难度及后续建议等方面提出宝贵意见，为培训内容的持续优化提供有力支撑。另一方面，进度流程培训则侧重于强化团队成员对项目管理流程的理解与执行能力。这一板块的培训以部门组长为核心，通过定期培训的方式，引导组员逐步养成遵循既定流程工具执行工作的良好习惯。在此过程中，组长不仅需传授项目进度管理的理论知识与实践技巧，还需及时发现并纠正组员在流程执行过程中的偏见与误解，确保每位成员都能准确把握项目进度，减少因流程不畅所导致的延误与冲突。

(2)定期同步项目进度和相关文件资料。这一流程的核心在于通过扁平化管理[28]，确保项目经理能够及时将项目的最新进度信息传达给各小组组长，再由组长将这些信息准确同步给所有组员。具体而言，项目经理将根据项目实际情况，制定项目进度同步的时间表，确保信息的时效性和准确性。在每个同步节点，项目经理会整理并汇总项目的关键进展、遇到的问题、解决方案以及下一步计划等详细信息，通过邮件、项目管理软件或内部通讯工具等渠道发送给各小组组长。小组组长在收到项目经理的信息后，将立即组织小组会议或利用线上工具，将项目进度、任务分配、风险评估等关键内容传达给每位组员。这一过程不仅确保了信息的全面覆盖，还促进了组员之间对项目全局的共识和理解。通过定期同步项目进度和相关文件资料，项目组旨在实现信息的透明化和共享化。每一位团队成员都能实时了解到项目的最新动态，明确自己的任务和责任，同时也能从全局视角出发，思考如何更好地与其他团队成员协作，共同推动项目向前发展。此外，这一流程还促进了项目成员项目管理知识和能力的提升。通过参与项目进度的同步与讨论，项目成员能够学习到如何评估项目进展、识别潜在风险、制定应对策略等实用技能，进而在日常工作中更加得心应手，提高团队协作的整体效率和质量。

(3)加强团队内部沟通。为了进一步加强团队内部的沟通与协作，提升项目执行效率与质量，项目组在小组周例会上实施一系列改进措施包括：会议中将增设每位团队成员的工作汇报环节，确保每位成员都有机会分享自已的工作进展、遇到的问题以及解决方案。这一环节不仅关注个体任务的完成情况，还特别强调与其他小组的协作状况，旨在促进跨部门、跨功能的沟通与理解。在汇报过程中，团队成员需详细说明本周内与其他小组的协作情况，包括信息交换的流畅度、任务对接中的难点与解决方案等。此举有助于及时发现并解决协作中可能存在的问题，优化跨部门工作流程。同时，针对本周内遇到的难题，鼓励团队成员提出具体的挑战与困惑，集体探讨解决方案，共同寻求最佳实践。为了确保会议成果的有效传承与回顾，项目组采用会议纪要的形式详细记录会议内容，包括每位成员的汇报要点、讨论的重点问题及达成的共识等。这些纪要将作为项目资料的一部分，供团队成员随时查阅与参考，促进知识共享与经验传承。最后，在版本的中期与末期，每个小组需要组织版本回顾会议，让每个成员向整个小组详细汇报这段时间内的实际工作内容与成果。这一举措旨在增强团队的整体意识，确保每位成员都能全面了解项目全局，及时查缺补漏，共同推动项目向预期目标稳步前进。

(4)建立员工关怀。在构建员工关怀体系的过程中，项目组深刻认识到每位员工的独特性与多样性，他们各自承载着不同的性格特质、专业背景及丰富的工作经验。为塑造一个和谐、高效且充满战斗力的团队环境，充分发挥管理者的主观能动性显得尤为至关重要。管理者需具备敏锐的洞察力，细心观察并深刻理解每位员工的具体需求，特别是针对新加入的成员，他们在融入初期往往面临对业务标准及企业文化认知的缺失。在此情境下，管理者应采取温情而细致的管理策略，以平和耐心的态度，引导新员工逐步适应团队节奏。通过一对一的深入交流机制，深入了解新员工的职业规划、个人优势及潜在挑战，从而提供量身定制的指导与支持，助力其迅速掌握业务精髓，激发工作热情与信心。同时，管理者还应展现出深厚的人文关怀能力，不仅关注员工的工作状态，更应全方位考量其职业发展路径、心理健康状况等多方面因素，构建全方位的支持体系。随着项目团队的日益稳定，这种人文关怀的力量愈显珍贵，让每位成员感受到归属与温暖，进而更加全身心地投入到工作之中。为了将这一理念具体落实，平台项目创新性地实施了按小组分配经费、自主组织团建活动的举措，期望增强团队成员间的相互理解与信任，提升团队的凝聚力和战斗力。

# 4.5需求变更控制优化

考虑到平台项目已发生过多次需求变更，本小节针对需求变更控制流程[29]提出优化步骤。

在项目管理过程中，准确识别并妥善处理项目执行期间可能的需求变更是确保项目顺利推进的关键环节。针对了公司平台项目，项目组目前确认其需求变更的主要成因，包括用户追加、补充遗漏以及整体重构三大块。然而，在项目接近尾声阶段，项目团队主要面临的是用户追加和补充遗漏这两部分变更的处理。

首先，为了确保项目能够在可控范围内应对这些变更，项目经理和需求人员必须具备高度的敏锐性和前瞻性，及时识别并准确评估潜在的变更需求。这要求他们不仅要密切关注市场动态和用户反馈，还需要建立有效的沟通机制，以便第一时间获取变更信息。在此基础上，项目经理和需求人员应根据项目的实际情况，合理设定问题阈值，明确何种程度的变更是可以接受的，何种程度是需要通过正式流程来处理的。具体而言，当项目范围发生变化超出预设阈值、项目工期和工作量显著增加、项目投入资源及工时远超预算、项目团队成员发生重大变动，或项目中发现严重的技术问题（如关键 bug、重大效率提升需求）时，均应及时触发变更流程。

其次，为了优化项目变更的流程管理，J公司需要建立一套规范、高效且灵活的变更管理体系。这一体系应明确界定变更申请、审核、审批及执行的各个环节，确保变更流程的有序进行。具体而言，当项目触发需求变更时，变更发起者需首先向变更申请人提交详细的变更申请，明确变更原因、内容、影响范围及预期效果等信息。随后，变更申请人需将这些信息汇总并上报给上一级管理者进行初步审核。在此过程中，管理者需仔细评估变更对项目整体进度、成本及质量的潜在影响，确保变更的合理性和必要性。若变更超出预设阈值或涉及重大调整，则需进一步上报至项目经理进行最终裁决。项目经理在综合考量各方意见后，将做出是否批准变更的决定，并将结果及时反馈给变更发起者和申请人。一旦变更获批，项目团队需立即着手制定详细的变更计划，明确变更任务的实施步骤、责任分配及时间节点，确保变更内容的顺利制作和集成。

同时，为了确保变更流程的有效执行和持续改进，J公司还需建立完善的变更记录机制。所有经审批通过的变更均需详细记录在案，包括变更请求、审批过程、执行结果及反馈意见等关键信息。这些记录不仅有助于项目团队在项目回顾和总结时查找问题根源、提炼经验教训，还能为未来的项目管理和变更处理提供宝贵的参考资料。

最后，充分吸收和利用每一次需求变更的经验教训对于提升项目管理水平至关重要。了公司应鼓励项目团队成员积极参与变更流程的讨论和反思活动，共同分析变更产生的原因、处理过程中遇到的问题及解决方案的有效性等关键因素。通过持续的经验积累和知识分享活动，项目团队可以不断提升自身的变更管理能力，形成一套适应性强、灵活高效的变更管理规范。同时，公司还可以通过定期组织培训和学习交流活动等方式提升团队成员的专业素养和综合能力水平，为项目变更管理的持续优化提供有力支撑。

综上所述，针对J公司平台项目在执行期间可能发生的需求变更情况，项目团队应通过建立有效的识别机制、优化变更流程管理、完善变更记录制度以及充分利用变更经验等措施来确保项目变更的顺利处理。这些措施的实施将有助于提升项目团队的应变能力和管理水平，为项目的成功交付奠定坚实基础。

# 4.6敏捷开发模式推进

# 4.6.1采用敏捷开发方式

J公司在面对日益复杂的市场环境和灵活多变的客户需求时，亟需对其平台项目的进度管理模式进行根本性的优化。传统的项目开发模式已不能满足快速迭代和灵活调整的需求，故结合敏捷开发方法对项目开发模式进行优化，成为推动项目成功的关键步骤。

在实施优化方案的过程中，采取了融合传统项目管理与敏捷开发方法的双轨模式，即在保留项目规划和控制的基础上，引入敏捷方法中高效的迭代增量开发策略，优化平台项目已有的月度开发模式。这一变革的核心在于，每一次迭代不仅涵盖需求分析、设计、编码、测试、维护等传统软件开发周期中的全部阶段，而且要按照产品的优先级，将最重要的特性或需求优先开发。通过这种方式，能够确保项目按部就班地向前推进，同时对市场变化或客户需求的变动做出迅速反应。

为了最大限度地发挥敏捷开发方法的优势，J公司在优化项目开发模式时，特别注重迭代内各个环节的紧密结合。在传统开发模式中，需求往往在项目初期集中收集和定义，而敏捷模式强调的是持续不断的需求管理，以保证在整个开发过程中随时能对需求进行调整和补充。此外，通过采用Scrum和极限编程（XP）的实践核心，把定期的迭代计划会议、短周期的发布和频繁的代码集成，内化进平台项目流程当中，强化了项目过程对变化的适应能力，并提升了项目交付的频率与质量。

在具体操作上，组织团队按照自组织的模式进行优化，其中团队成员之间平等互信、团结互助，这样的开发环境大大提升了成员的工作积极性和整体工作效率。项目管理者们认识到，只有通过鼓励和培养自组织团队，才能达到最佳的设计、最匹配的需求与最完整的架构。为了提升团队协作和沟通的效率，项目组实施了定期的站立会议和回顾会议，以及持续的技术优化和设计改进。通过这种方式，平台项目团队在保障进度和质量的同时，显著降低了项目风险。

平台项目团队通过引入敏捷开发中的迭代增量模式和自组织团队理念，将传统项目管理和敏捷方法有效地结合在一起。优化后的项目开发模式不仅应对了多变的市场需求，还提高了开发流程的灵活性和适应性，这对于J公司实现项目管理的高效率和高质量目标至关重要。尽管还有些困难和挑战需要克服，但J公司在项目开发模式优化方面迈出了坚实的步伐，未来有望更好地满足复杂多变的项目要求。

# 4.6.2利用自动化工具优化流程线

参考公司其他项目组，平台项目组通过引入自动化工具Codding来实践敏捷开发方法。

# （1）统一需求、开发、测试流程管理

为实现项目管理的规范化与透明化，J公司统一采用Codding平台作为月度版本需求、开发、测试计划及相关文件的管理工具。该平台不仅提供版本控制功能，还支持多人协同编辑与实时更新，确保项目团队成员能够即时获取并共享最新的项目信息。通过Codding平台，项目经理可以清晰规划每个版本的需求范围与开发任务，测试团队则能同步获取测试计划，从而提升整体的沟通与协作效率。

# (2）自动化构建过程的优化

针对各子系统的开发特点，J公司在Codding平台上新建并管理项目代码自动化的构建过程[30]。这一举措极大简化了开发人员的日常构建工作，减少了因手动构建而导致的错误与重复劳动。自动化构建过程不仅确保了构建环境的一致性，还通过并行处理提高了构建速度。开发人员只需提交代码至指定仓库，构建系统即可自动触发构建任务，生成可执行的文件或包，为后续测试与部署环节奠定基础。

# (3）自动化测试的实施

为提升测试效率与质量，J公司显著增加了自动化测试的比重。通过引入专业的测试框架与开发类型的测试人员，项目团队能够编写高效的自动化测试脚本，对软件产品进行全面的自动化测试。自动化测试不仅覆盖了单元测试、集成测试等多个阶段，还实现了对关键功能的持续监控与验证。测试人员可利用测试框架快速编写测试用例，并利用自动化测试工具执行测试脚本，及时捕捉并修复潜在的缺陷，确保软件产品的质量。

# (4)DevOps平台在部署与上线中的应用

为了加速软件开发过程并实现无缝上线[31]，J公司利用自研的DevOps平台对版本发布物进行自动化打包与部署。该平台集成了代码构建、测试验证、部署发布等多个环节，通过流程化的方式简化了版本上线的复杂度。每个版本的发布物均以发布单的形式进行明确标记，便于测试人员直接进行版本测试与问题追踪。测试通过后，DevOps平台可自动将发布物推送至生产环境，实现快速部署与上线。这一过程不仅减少了人工干预与潜在错误，还显著缩短了版本迭代的周期。

以上是平台项目为推进敏捷开发模式，在项目自动化工具上进行的优化方案设计，旨在通过引入自动化工具与DevOps理念，实现平台项目研发效率的显著提升的目标。期望自动化工具的应用能够简化项目管理的复杂度，以及提高流程执行的规范性与透明度。DevOps平台的引入则准备进一步加速软件开发过程，实现从需求到部署的全流程自动化。未来，随着技术的不断发展与项目管理的持续优化，J公司将继续探索更多创新实践，以应对更加复杂多变的市场需求与技术挑战。

# 4.7建立风险预警机制

对于项目控制的主要负责人，需要及时识别项目风险，以便以后对项目的控制。而进度管理中的风险因素有很多，诸如技术难题，时间短，沟通占用时间等等。项目经理制作项目风险表，识别相应的风险，并进行制作相应的风险应对措施，避免风险转移成实际问题，当将要成为问题之前及时出现问题警告，项目经理及时加以引导修复。平台项目起初只是应对技术风险比较多，项目经理出身于技术，对技术风险加以了合理控制管理，但是遇到非技术问题时也应该合理管控。项目中除了技术，还有测试、基础设施等人员都要管理者对部门管理入员提出风险列表，加以汇总管理控制。

现对平台项目改进方法提出以下日常的管理风险的制度如下：

# 1.日常监控和检查

对于一个用新技术升级老平台的项目本身来说，它有着很强的复杂性和不确定性，这会导致项目进度计划执行实施过程当中，会受到很多因素影响，项目人员、设备、技术、环境等因素也会影响工作的实际进度。为此，在平台项目的实际过程中，一定要密切关注项目的实际进度情况，不然做的进度工作等于白费，而且也要及时确定项目的实际任务的交付情况。目前是有周相关任务的周报，总结相关工作内容，以保证信息齐全，同时将相关信息提交给项目部。每周的例会相关模块人员都会自行拉起会议，对上周完成的内容和本周要处理的工作进行简要介绍和整理。

对于日常的项目监控，主要是将项目工作任务分解成小任务，并根据相关人员确定各自的职责，每个任务都会有自己独立的文档，并与其父任务相关。在平台项目进度的日常监控过程中，公司提供项目管理相关软件的技术。所有项目参与者都可以看到他们日常任务的显示。可以看到每个任务何时处于该阶段，是否延迟，并快速预览甘特图以查看任务的整体情况，为后续进度偏差分析提供适当的保障。

# 2.定期监测和检查

通过监控平台项目的周期，项目经理可以更好地了解项目的进度。每个小组都可以按照规定的工期适当确定小组任务的进度。在日常督导中，主要是根据各自的职能进行详细的统计和分析，并根据进度督导确定相关项目的进度。在帮助项目经理的前提下，项目经理进行相关的统计和总结，从而达到对它们的及时分析。由于项目例行监控更多是基于项目的完成程度，因此项目例行监控只能判断每个项目的进度。在例行监督项目时，还必须将各种工作转换为整个项目计划。为此，有必要判断各种工作任务的完成情况，项目进度状态等。使项目部更好地了解项目的实际运转，以便将资源合理地调配调度及相关任务比例的安排。

通过对平台项目执行情况分析，检测周期可以确定为“月”，然后项目经理可以汇总出项目的进度，并结合工作进度，确定相应的总结报告。通过总结项目不同工作的实际进度，确定项目实施情况，对相关进度内容做出合理的预测和评价。

# 4.8小结

本章介绍项目进度优化方案的各项内容，包括从最开始的任务分解到增加监控预警，以期望给J公司平台项目的进度管理提供参考。

# 第五章 平台项目进度管理优化方案实施效果

# 5.1实施难点及对应解决方案

在】公司推行基于敏捷开发的平台项目进度管理优化方案时，团队遭遇了多个实施难点。其中，文化适应性挑战尤为突出，因为公司内部长期以来形成的传统项目管理习惯与敏捷开发的灵活性存在冲突。为克服这一难点，推行了由高层领导主导的组织文化变革，建立起支持敏捷方法的环境与价值认同，并逐步让员工接受定期的敏捷开发培训，确保团队成员理解敏捷理念及其实践要点。

技术适应性也是一个不可忽视的难题，尤其是在持续集成（ContinuousIntegration，CI）和自动化测试方面。J公司采取的是渐进式方法，首先在小范围内试行新工具，评估工具在现实项目环境中的适应性和有效性。随后，根据实验结果优化工具配置，并扩大其适用范围至更大的团队或项目。

项目管理工具的融合使用也备受重视。项目团队采用了Scrum工具集，包括燃尽图与敏捷看板，以加强对进度的实时监控。这些工具的使用有效提高了任务跟踪的透明度，增强了团队对项目状态的认知，从而及时调整工作计划或策略以适应变化。

与客户的持续互动和沟通是敏捷成功的关键。通过持续获得反馈，项目团队能够快速响应需求变更，并以最小的成本进行调整。这需要团队与客户之间建立牢靠的信任关系和沟通机制，才能确保信息流畅且准确。J公司在客户参与层面采取了透明化策略，通过事先沟通和业务培训，邀请客户参与测试过程，确保他们的声音能够被听取并在项目实施过程中得到体现。

敏捷团队组成与角色分配也是实施中面临的挑战，因为传统的职位划分与敏捷团队自组织的特性有所冲突。J公司在这方面的策略是进行角色重塑与职责清晰化，设定清晰的期望，为团队提供必要的培训和工具。项目经理负责定义产品愿景，并与客户保持紧密联系；产品经理、技术经理和业务专家共同担任 ScrumMaster 角色，确保敏捷实践在团队中得以实施，并解决团队在项目过程中遇到的障碍；而开发团队则聚焦于具体的产品开发与交付，测试团队负责验证产品的质量。

实施效果表明，通过以上难点应对策略，J公司的平台项目管理优化取得了可观的成效。项目完成时间较优化前缩短了 $20 \%$ ，顾客反馈时间缩短了 $30 \%$ o此外，公司对于需求变更的响应速度提高了 $1 5 \%$ ，从而减少了由于过时需求导致的返工和浪费。这些积极的指标明确地证明了优化方案的有效性，同时表明对于敏捷和传统方法相结合的项目管理模型对企业带来的积极改变。

# 5.2实施过程中的困难及解决方案

在J公司平台项目的敏捷开发实践中，团队在实施进度管理优化方案的过程中遭遇了不少挑战。其中一个显著的难点在于项目团队对敏捷开发理念的认知和接受度不一。为了解决这个问题，J公司采用了一系列的措施，例如组织专门的敏捷开发培训，邀请敏捷开发经验丰富的外部顾问来辅导团队，以及在项目中实行敏捷教练制度。通过阐明敏捷开发的价值观和实践方法，帮助团队成员建立起正确的敏捷理念，提升了项目参与者对敏捷开发流程的接受度和参与积极性。

针对迭代过程中需求变更频繁所带来的问题，J公司采取了优化需求管理流程的策略。在每个迭代周期开始前，举行需求澄清会议，确保所有团队成员对需求有清晰的理解和共识。同时，将需求变更流程正式化，对于提出的每个变更需求进行评估，考虑其对项目进度的影响，通过合理的排期适应这些变更，有效控制了需求的波动对项目进度的负面影响。

在实施过程中，了公司发现原有的项目管理工具不足以支持敏捷开发的高效运作。因此，项目团队引入了一系列敏捷兼容的工具如上文提到的Codding，来支持敏捷过程中的任务跟踪和文档管理。通过这些工具的使用，极大地提高了信息透明度和团队的沟通效率，从而提升了项目进度的管理效果。

软件质量也是一个重要关注点。为了确保在敏捷开发模式下仍能保持软件质量，了公司引入了持续集成和持续部署的实践。通过自动化的构建和测试流程，项目团队能够快速发现和修复缺陷，保证了代码质量的稳定性和项目进度的可控性。

总体而言，J公司在实施平台项目进度管理优化方案的过程中，通过一系列切实有效的策略以及不断的实践和调整，成功地解决了敏捷开发实施中遇到的困难。这些措施提高了团队的敏捷实践能力，保障了项目进度和软件质量，最终实现了项目管理的可持续改进和企业敏捷化转型的目标。

# 5.3实施后的结果检验

在实施J公司平台项目的敏捷进度管理优化方案后，为了检验方案的实际效果，项目团队采用了多种方法来收集和分析数据。这些数据主要集中在项目的交付时间、质量控制以及团队成员的工作效率等关键绩效指标（KPI）上。对于项目的交付周期，经过敏捷优化实施后，J公司的项目团队成功缩短了原定的交付时间，平均提前了 $20 \%$ 的时间完成项目里程碑的交付。在项目质量控制方面，实施敏捷管理后，缺陷率降低了 $30 \%$ ，从原先的每千行代码中平均存在1.5个缺陷，降低到每千行代码的1个缺陷以下，显著提高了软件产品的质量和用户的满意度。

为确保评估结果的客观性和准确性，项目团队运用了敏捷方法中常用的燃尽图和迭代评审会议来对进展进行监控与审视。通过燃尽图，能够清晰地看到每一个迭代期的工作进度和剩余任务量，及时调整迭代目标和工作计划，保证项目进度符合预期目标。同时，在每次迭代结束后召开的评审会议中，团队成员之间进行了充分的沟通与反馈，这不仅增加了团队协作的效率，而且还有助于及时发现和解决问题，确保项目按时交付。

项目组还借鉴了敏捷开发环境下用户需求的动态管理策略，采用了用户故事映射和比较优先级的手段来持续调整产品特性。用户故事映射的使用帮助项目团队视觉化需求构建过程，并在开发周期内动态调整优先级，确保最终交付的产品更加贴近客户的实际需求。

除此之外，项目团队通过引入自动化测试和持续集成工具来提高工作效率。这些工具不仅加速了开发流程，还降低了由于人为错误引起的缺陷。自动化测试保证了在每次代码提交后进行测试，确保新追加的代码不会影响到已有功能的稳定性。而持续集成工具则支撑了开发、测试与部署流程的无缝连接，大大提升了开发过程的灵活性和响应速度。

总的来说，敏捷优化方案的实施为J公司带来了显著的项目管理效益。优化后的项目不仅在周期和成本上有所控制，还在团队效率和产品质量上获得了提升。通过这次的实施和检验，J公司也积累了宝贵的敏捷开发经验，为未来可能涉及的更多复杂多变项目打下了坚实的基础。

# 5.4小结

在通过采用敏捷开发理论指导J公司平台项目实施后，项目团队价值观与工作流程都发生了显著变化，项目的进度得到了有效的控制和优化。在结合项目管理的传统方法和敏捷方法实施的过程中，构建了一个高效透明的项目进度监控体系，通过引入Scrum框架，项目团队能够快速响应变化，并利用迭代与持续集成的方法，确保项目按时交付，质量得到保障。

在项目进度控制方面，J公司明确了项目的迭代目标并且每日进行进度同步。通过产品积压列表的优化，提高了WBS 活动任务的精细度，使得团队对于每一次迭代的工作内容和预期目标有了更清晰的认识，从而提升了工作效率和质量。利用燃尽图和甘特图，团队及时监控迭代进度和资源使用情况，确保项目按照计划顺利推进。在组织文化上的适应，项目管理层对于敏捷方法的支持，以及对团队进行敏捷实践的持续培训，增强了团队成员的敏捷意识和自组织能力。

各迭代阶段的反馈和优化作为敏捷方法的一个重要环节，J公司项目团队在每次月度版本结束时，都会及时举行回顾会议讨论在项目中遇到的问题和挑战。这种持续改善的模式使团队能够从错误中学习，并迅速调整策略和工作方向，敏捷方法的应用极大提升了项目的适应性和客户满意度。

项目交付后，通过与客户的深入沟通，客户对项目的满意度得到了显著提升。项目的关键绩效指标（KPI）表明，项目的交付周期比优化前减少了 $15 \%$ ，缺陷率下降了 $20 \%$ ，这充分验证了敏捷项目管理在加快项目交付速度、提升项目质量方面的积极效果。此外，项目团队对于敏捷实施的接受度也逐渐升高，团队成员在自我管理、协作和交流方面的能力得到了极大的增强。

纵观整个项目的实施情况，能够发现项目团队在解决技术难题和跨部门协作中表现出了更高的效率和灵活性。敏捷实践的持续深入，不仅优化了项目进度管理，更促进了团队文化的转变，将敏捷思想深植于团队成员的日常工作之中。在此基础上，J公司未来的软件开发项目还可以进一步探索敏捷方法与公司现有流程的结合，不断调整和完善敏捷实践，以便更好地适应市场变化和客户需求，提升企业核心竞争力。

# 第六章 结论和展望

# 6.1结论

在对J公司平台项目实施敏捷开发的进度管理进行深入分析和优化实践之后，本研究得出了一些关键性的结论。首先，通过引入敏捷管理模式，项目整体的灵活性和响应速度得到了明显的增强。对比传统的项目管理模式，敏捷管理在应对不断变化的市场需求和技术挑战方面表现出更高的效率和适应性。另外，本研究结合J公司项目实际，提出的敏捷与Scrum结合的进度管理策略，不仅为项目的顺利推进提供了有力保障，同时也为管理团队提供了丰富的项目管理经验和方法论指导。

在敏捷团队形成和运营方面，通过角色职责的优化和团队合作模式的调整，加强了团队内部成员之间的沟通和协作，形成了自组织和跨功能的协作环境，这在很大程度上提升了项目进度的控制能力和交付的质量。在项目中应用明确的里程碑，并采用迭代式的开发模式，加快了产品功能的逐步完善和交付。此外，研究中使用的改进后的进度计划，确保了每次迭代的目标和成果得到有效管理和验证，对于不断改进项目管理流程和提高敏捷实施效果起到了重要作用。

值得一提的是，在项目进度的具体管理上，利用PERT网络分析法对项目进度的计划工期进行了细致的分析和优化，提高了对于项目进度不确定性因素的控制能力。通过调整技术栈和优化开发过程，成功降低了技术适应性问题对项目的影响，有效提升了研发流程的自动化水平和效率，为敏捷开发理念在J公司平台项目中的落地打下了坚实的基础。

在组织文化方面，项目管理的改革和进度管理的优化凸显了组织文化对于敏捷管理成功实施的重要性。通过管理层的大力支持和员工的积极参与，建立了一种基于信任、尊重和协作的企业文化氛围。这种文化的改变不仅促进了内部团队的高效运作，也使外部客户能够更加密切地参与到项目的开发过程中来，形成了良好的项目生态环境。

实施效果检验表明，采用本文提出的进度管理优化方案后，项目的交付周期得到显著缩短，软件系统的缺陷率也有明显下降。客户和团队成员对于敏感能够带来的变化表示认可与满意。这些成果的取得，既彰显了敏捷项目管理理论在实际操作中的可行性，也证实了优化方案在提高项目管理效率方面的有效性。

展望未来，敏捷项目管理作为一种有效的项目执行方法，其应用领域和深度将持续扩展。随着人工智能、大数据等先进技术的进一步融入，敏捷项目管理亦将与之相融合，推动管理模式和工具的进一步创新。同时，跨文化项目管理也将面临新的挑战，如何有效地整合不同文化背景下团队成员的工作方式和价值观，将是未来研究的重点方向之一。

# 6.2不足和展望

在本研究的过程中，J公司平台项目在敏捷开发的背景下，所展现的进度管理优化取得了显著的成效。通过引入敏捷开发方法等手段，项目在响应需求变更、保持进度活性以及提高交付效率方面有了不小的提升。然而，本研究同样发现存在一系列的不足和需要未来深入研究的领域。

在项目管理中，敏捷与传统方法相结合的研究仍然是一个值得探讨的领域。尽管敏捷方法被广泛认可和应用，在某些情况下结合传统的项目管理方法可能会更加高效。例如，该研究利用敏捷开发加强了进度管理和团队沟通，通过敏捷的迭代开发和交付，缩短了产品上市时间，从而提高市场竞争力。然而，在面对大规模和复杂程度较高的项目时，如何平衡敏捷的灵活性与传统管理严格控制项目范围和预算的能力，仍需要更加深入的实证研究。

另一方面，本文通过案例分析换证了敏捷项目管理中的应用效果及挑战。尽管敏捷方法可以减少项目延期的风险，并保持进度偏差在较低水平，但在实际应用中，项目团队依然面临各种挑战。例如，项目成员不稳定或人员流动大，常常导致进度计划执行效率低下，从而影响整个项目的交付质量和时间。如何培养和维护一个稳定高效的敏捷项目团队，是敏捷项目管理应该着重考虑的问题。

基于机器学习和人工智能在项目管理中的潜在应用也是一个令人兴奋的研究领域。随着技术的发展，越来越多的数据分析工具和算法被开发出来。这些工具和算法能够帮助项目经理准确预测项目风险，优化资源分配等。例如，项目经理可以利用先进的预测模型，对项目进度和成本进行更准确的预测。

跨文化项目管理同样值得关注。在全球化的今天，项目团队往往由来自不同文化背景的成员组成，如何管理这样的多元化团队，确保团队成员之间有效沟通和协作，是敏捷项目管理实践中必须面对的重要问题。

综合上述不足，本文认为，未来研究应焦点放在以下方面：

1．进一步探索传统项目管理方法与敏捷开发方法之间的平衡点；2．深入研究如何构建并维护一个稳定。3.．探索机器学习和人工智能在项目管理中的应用，以提高预测准确性和资源优化的能力；4.研究跨文化团队在敏捷环境下的管理策略，确保团队成员间的有效沟通和协作。这些研究不仅有助于整个领域的发展，还能为实践界提供可行的改进方案和策略。

参考文献  
[1]刘恩泽，谭欣杰．中国软件项目管理现状分析[J].科技资讯,2019,17(16):253-256.D0I:10.16661/j.cnki.1672-3791.2019.16.253.  
[2] Reifer Consultants LLC.Quantitative Analysis of Agile Methods[R].2014(07)  
[3] Hodson N , Woods P, Sobolev M , et al. A Digital Microintervention SupportingEvidence-Based Parenting Skills: Development Study Using the Agile ScrumMethodology[J].JMIR formative research, 2024, 8:e54892.  
[4] Vincent G V , Sigrid D , Orphé M , et al. A self-management psychoeducational eHealthprogram to support and empower people with advanced cancer and their family caregivers:Development using the scrum methodology [J]. Internet Interventions, 2023,33:100659-100659.  
[5] Ballesteros-Pérez P .M-PERT: Manual Project-Duration Estimation Technique for TeachingScheduling Basics[J].Jounal of Construction Engineering and Management, 2017,143(9):04017063  
[6] Leach L P .Critical Chain Project Management Improves Project Performance[J].ProjectManagement Journal, 1999, 32(2):35.DOI:doi:10.1002/9780470172391.ch33.  
[7] 卿启付.房建工程项目经理部如何搞好项目管理?[J].国际经济合作,1992,(02):52-54.  
[8] 赵铁生，刘锋.以总体控制网络系统为主的大型工业建设项目管理[].天津大学学报，1991, (S1): 23-29.  
[9]崔希玲．项目管理在标准化项目中的应用[D]．北京：对外经济贸易大学,2003.  
[10]李丹.H公司机器视觉软件开发项目进度管理研究[D]．浙江：浙江大学，2021.DOI:10.27461/d.cnki.gzjdx.2021.002666.  
[11]ClintonKeith.游戏项目管理与敏捷方法[M]．北京：清华大学出版社，2021.  
[12]王建伟.Q公司A办公平台开发项目进度管理研究[D].北京：北京邮电大学，2023.DOI:10.26969/d.cnki.gbydu.2023.000605.  
[13] 孙科炎．华为项目管理法[M]．北京：机械工业出版社，2014.  
[14]美国项目管理协会．项目管理知识体系指南[M]．北京：电子工业出版社，2018  
[15]段世霞.项目管理[M].南京：南京大学出版社，2020.  
[16]Kenneth SRubin.Scrum精髓：敏捷转型指南[M].姜信宝，等译.北京：清华大学出版社，2014.  
[17]陈建.探析敏捷开发在软件项目进度管理中的应用[J.计算机产品与流通 $, 2 0 2 0 , ( 0 3 ) ; 1 1 + 1 7 1$   
[18]张继忠.利用计划评审技术的项目工期估算方法[J]中小企业管理与科技（上旬

刊），2016,(04):67-69.[19] Hajdu M, Bokor O. Sensitivity analysis in PERT networks: Does activity durationdistribution matter? [J]. Automation in Construction, 2016, 65:1-8.[20]谢雪海.基于修正PERT的关键链进度管理研究D]西安：西安建筑科技大学，2019.DOI:10.27393/d.cnki.gxazu.2019.001022.[21] Kholil M , Alfa N B , Hariadi M .Scheduling of House Development Projects with CPM andPERT Method for Time Efficiency (Case Study: House Type 36)[J].IOP Conference Series:Earth and Environmental Science, 2018, 140(1):012010-012010.[22] Siami-Irdemoosa E , Dindarloo R S , Sharifzadeh M .Work breakdown structure (WBS)development for underground construction[J].Automation in Construction, 2015, 58:85-94.[23]（美）拉尔曼.敏捷迭代开发[M].北京：人民邮电出版社，2013[24]陈汗龙，黄丁琦，孙庆飞.PERT在比测试验项目进度管理中的应用J.中国标准化，2020,(02):184-188.[25]郁延平.X保险公司数据仓库系统集成项目进度管理研究[D].北京：北京邮电大学,2023.DOI:10.26969/d.cnki.gbydu.2023.000468.[26]庞绪瑞.S公司W游戏软件研发顶目的进度管理研究[D]．北京：北京邮电大学,2023.DOI:10.26969/d.cnki.gbydu.2023.000668.[27]王海军E公司非标装置开发项目进度管理研究D]．天津：天津理工大学,2023.DOI:10.27360/d.cnki.gtlgy.2023.000196.[28]郭云贵，薛玉平.京东集团组织结构变革的动因与启示[J]管理工程师,2021,26(01):20-24.DOI:10.19327j.cnki.zuaxb.1007-1199.2021.01.003.[29]刘俊会.Y公司履约线敏捷开发项目需求变更管理的研究[D]．吉林：吉林大学,2024.[30] 赵辉，王静《软件工程》课程中持续集成构建实验探讨[J]产业科技创新，2022,4(05)：47-48.[31] 耿泉峰，李曦，葛维，等.基于DevOps的软件开发管理模式[J]软件,2019,40(01):93-96.

# 附录A项目进度基础问卷

1．您认为本项目目前的进度是否正常？

2.您认为本项目的开发流程是否清晰、有序？

3．在开发过程中，是否遇到过因流程不畅导致的进度延误？请举例说明。

4．您认为是否可以通过优化开发流程以提高项目进度管理效率？

5．需求变更时，团队是否能够及时响应并调整计划？

6．您认为如何更好地管理需求变更，以减少对项目进度的影响？

7．您认为团队在进度管理方面的沟通是否顺畅？

8．是否存在因沟通不畅导致的进度延误或误解？请举例说明。

9.您认为项目团队的人员配置是否满足进度需求？

10.您认为项目在资源管理方面还有哪些可以改进的地方？

11．项目是否重视进度管理相关文档的编制和更新？这些文档包括哪些内容？

12.项目团队是否定期进行进度管理方面的复盘和总结，以识别问题和改进机会？

13．您是否还有本项目在进度管理上的建议？

# 附录B 专家人员访谈问题

1. 您如何定期监控项目的实际进度与计划进度的对比？  
2. 当发现进度偏差时，通常采取哪些措施进行调整？  
3. 有没有遇到过重大的进度风险？是如何应对的？  
4. 项目团队之间的沟通机制是怎样的？如何确保信息畅通？  
5. 有没有遇到过团队协作上的挑战？是如何解决的？  
6. 在项目进度管理过程中，遇到的最大挑战是什么？  
7. 如果可以重来，有哪些进度管理方面的决策您会希望做得更好？  
8. 当项目实际进度落后于计划进度时，团队通常采取哪些具体措施来加快进度或调整计划?  
9．在调整项目进度计划时，团队如何确保变更不会对项目质量或范围产生负面影响？  
10．您是否认为团队在应对进度变更时足够灵活？是否存在一些阻碍快速响应的因素？  
11．在进度监控过程中，团队如何确保所有相关利益者都了解项目的当前状态以及任何必要的调整？

# 附录C执行人员访谈问题

1．你对项目初期的进度计划有何了解？你觉得计划制定得是否合理？  
2. 你是否清楚自己的任务在整体进度计划中的位置和重要性？  
3. 你通常如何跟踪自己的任务进度，并确保按计划完成？  
4. 你觉得团队在共享进度信息方面做得如何？你是否能及时获取所需的信  
息？  
5. 当你的任务进度出现延误时，你通常会采取哪些措施来应对？  
6. 你是否觉得团队在应对进度挑战时足够灵活和协调？  
7. 在进度管理中，你与团队成员之间的沟通频率和方式是怎样的？  
8. 你是否遇到过团队协作上的障碍，特别是在进度管理方面？你是如何解决  
的？  
9.你是否意识到项目中可能存在影响进度的风险？你是否知道如何报告或应  
对这些风险?  
10. 你是否参与过团队的风险讨论或制定过应对措施？  
11．你是否使用了任何技术或工具来帮助你管理自己的任务进度？  
12．你觉得团队在利用技术或工具进行进度管理方面做得如何?