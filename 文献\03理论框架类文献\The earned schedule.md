# The earned schedule

[Conference Paper](https://www.pmi.org/learning/library#sort=&f:ContentType=[Conference%20Paper]) [Scheduling](https://www.pmi.org/learning/library#sort=&f:Topics=[Scheduling]), [Benefits Realization](https://www.pmi.org/learning/library#sort=&f:Topics=[Benefits%20Realization]), [Technical Skills](https://www.pmi.org/learning/library#sort=&f:Topics=[Technical%20Skills]), [Earned Value Management](https://www.pmi.org/learning/library#sort=&f:Topics=[Earned%20Value%20Management]) **18 July 2012**

<PERSON><PERSON>, <PERSON>.

**How to cite this article:**

Anbari, F. T. (2012). The earned schedule. Paper presented at PMI® Research and Education Conference, Limerick, Munster, Ireland. Newtown Square, PA: Project Management Institute.

### **Abstract**

The Earned Value Management (EVM) and the Critical Path Method (CPM) are widely accepted methods and are often used simultaneously to evaluate project performance. The common practice is to use EVM to evaluate the status of project cost and forecast the project's cost at completion while using CPM to evaluate the status of project schedule and forecast the project's completion time. However, EVM and CPM are based on different assumptions about the future and using them simultaneously can lead to the wrong decisions about the project.

The Earned Schedule (ES) allows EVM metrics to be transformed to time or duration metrics to enhance the evaluation of project schedule performance and to forecast the duration needed to complete the project. ES extends the use of EVM data to the assessment of the project's schedule status and the forecast of its completion time. These additional insights regarding the schedule are gained without the need for additional data collection and related cost. ES and EVM use the same underlying assumptions, leading to consistent forecasts about project outcomes. When combined with schedule analysis using CPM, ES enhances the project manager's understanding of project schedule status and forecasts and provides further support for making better, evidence-based decisions about the project's schedule and other parameters.

EVM, ES, and CPM are powerful methods that give executives, project managers, and other stakeholders the ability to visualize project cost and schedule status throughout the project life cycle and consequently manage projects, programs, and portfolios more effectively.

**Keywords:** Earned Schedule (ES); Earned Value Management (EVM); Critical Path Method (CPM); forecasting project outcomes; underlying assumptions.

### **Introduction**

The Earned Value Management (EVM) method helps managers in making evidence-based decisions about project scope, resources, and cost; as a result, it supports effective project cost control and oversight. EVM gives the executive, program manager, project manager, and other stakeholders the ability to visualize project cost status throughout the project life cycle and consequently manage projects, programs, and portfolios more effectively. In its original form, EVM was used to evaluate project performance and forecast the cost of the project at completion. Usually, project control is established at the work package or cost account level. EVM data were generally not used to estimate the time needed to complete an activity, work package, or project, or to forecast their completion date. Project schedule network analysis techniques, such as the Critical Path Method (CPM) method, has been used widely to evaluate project schedule performance and forecasts of completion time. However, EVM and CPM are based on different assumptions about the future and using them simultaneously can lead the project manager to make the wrong decisions about the project. Extensions to EVM have been developed to use EVM data for schedule performance assessment and forecasts. The Earned Schedule (ES) concept allows EVM metrics to be transformed to time or duration metrics to enhance the evaluation of project schedule performance and to forecast the duration needed to complete the project. When combined with appropriate schedule analysis, this approach can enhance the project manager's understanding of the time estimate at completion of the project, and provide further insights for making better decisions about the project schedule and other related parameters. This paper highlights the main elements of EVM; presents the ES concept; compares ES with CPM; and integrates EVM, ES, and CPM.

The use of EVM in private industry and support by popular project management software packages have been rapidly growing in recent years. Details of the method were provided in  *Practice Standard for Earned Value Management* —Second Edition (Project Management Institute, 2011) and in other sources (Anbari, 2003; Association for Project Management, 2006; Humphreys, 2002; Kerzner, 2009; Project Management Institute, 2008; Turner, 2009; Turner, Huemann, Anbari, & Bredillet, 2010). There has been a high degree of acceptance among users of EVM, who tend to agree that EVM can improve the cost, schedule, and technical performance of their projects. Non-believers in EVM claim that the method is hard to use, that it applies primarily to very large projects, and that they do not need it (Fleming & Koppelman, 2010; Kim, Wells, & Duffey, 2003). Anbari, (2003), Kwak and Anbari (2010), Turner (2009), and Turner et al. (2010) show how the method can be simplified and implemented effectively while retaining its essential features. The interest in EVM and its use in differing types of projects are growing globally, particularly in the public sector, with notable progress in Australia, Japan, Sweden, the United Kingdom, and the United States.

ES is a valuable extension to EVM, requires no additional data collection, and provides valuable insights into the project schedule and forecast of its outcome. The use of ES is still very limited in practice.

### **Elements of Earned Value Management**

EVM integrates three critical elements of project management: scope management, cost management, and time management. It requires the periodic monitoring of actual expenditures and the amount of work done (expressed in cost units). To determine cost performance, EVM compares how much we have spent with what we planned to have spent to do the work we have done. To determine time performance, it compares the amount of work done with the amount of work scheduled to be done. To make these comparisons, EVM calculates cost and schedule variances, along with performance indices for project performance management. Based on these results, it forecasts the cost and date of the project at completion and highlights the possible need for corrective action. EVM uses the following project parameters to evaluate project performance (Exhibit 1):

Planned Value (PV): This is the cumulative planned cost for the work planned to be done on the project up to a given point in time. It is the approved budget for completing the work planned so far, and as such it is the cost baseline for the project. It was previously called the budgeted cost of work scheduled (BCWS).

![Components of earned value management. (Adapted from Anbari, 2003; Kwak & Anbari, 2010; Turner et al., 2010)](https://pmi-p-001.sitecorecontenthub.cloud/api/public/content/43c5af2dd2b94d73aeada3299e9a019d?v=2c1aaa1e)

**Exhibit 1: Components of earned value management.
(Adapted from Anbari, 2003; Kwak & Anbari, 2010; Turner et al., 2010)**

Budget at Completion (BAC): This is the total amount of money expected to be spent on the project, and as such, it is the value that PV is planned to reach at completion.

Actual cost (AC): This is the cumulative actual cost spent on the project so far, including all accrued cost on the work done. AC was previously called the actual cost of work performed (ACWP).

Earned value (EV): This represents the cumulative amount of work done up to a point in time, expressed in cost units. It is expressed as the amount that was planned to have been spent on the work that has been completed up to this point. EV was previously called the budgeted cost of work performed (BCWP). To calculate the EV for a given element of work, the planned cost is multiplied by the percentage complete. The EV for the project is the sum of the EV for all the work elements.

BAC, PV, AC, and EV are expressed in cost units. These may be in units of actual money, in any currency, or expressed in hours or days of work. PV, AC, and EV can be calculated for any element of work to determine progress and status of that element of work.

### **Project Performance Measurement**

Cost performance on the project is determined by comparing EV with AC. AC represents what has actually been spent and accrued to do the work so far, and EV represents what was planned to be spent to do the work so far. The difference shows whether the project is over-spent or under-spent. Schedule performance is determined by comparing the EV with the PV. PV shows the amount of work that was planned to have been done and EV represents the amount that has been done. By comparing the two we can determine whether more or less work has been performed than should have been done, and whether the project is ahead or behind schedule. We do these comparisons by calculating the variances and the performance indices.

#### **Variances**

The following formulas are used to calculate the variances:

The cost variance (CV) is a measure of cost performance:

* CV = EV − AC

The schedule variance (SV) is a measure schedule performance:

* SV = EV − PV

#### **Variance Percentages**

The following formulas are used to calculate the variance percentages:

Cost variance percent (CV%):

* CV% = CV ÷ EV

Schedule variance percent (SV%):

* SV% = SV ÷ PV

In the above formulas, 0 indicates that performance is on target. A positive value indicates good performance; a negative value indicates poor performance.

#### **Performance Indices**

The following formulas are used to calculate the performance indices:

Cost performance index (CPI) is another measure of cost performance:

* CPI = EV ÷ AC

The schedule performance index (SPI) is another measure of schedule performance:

* SPI = EV ÷ PV

In the above formulas, 1 indicates that performance is on target; more than 1indicates good performance; and less than 1 indicates poor performance.

### **Forecasting of Project Outcome**

Project management is primarily concerned with decisions affecting the future. Therefore, forecasting is an extremely important aspect of project management. EVM is particularly useful in forecasting the cost and time of the project at completion, based on actual performance to date using the following calculations.

#### **Forecasting of Cost at Completion**

The forecast of the cost of the project at completion is usually called the estimate at completion (EAC) or the cost estimate at completion (CEAC). The forecast of the cost remaining is usually called the estimate to complete (ETC).

Several assumptions can be made about project cost performance in the future (Anbari, 2003). Two major assumptions are:

* The remaining work will be performed according to the original plan, therefore (Turner, 2009):
  * ETC = BAC − EV
  * EAC = AC + BAC − EV = BAC + CV
* The remaining work will be performed while continuing to over-spend or under-spend at the same rate (Anbari, 2003; Project Management Institute, 2011; Turner, 2009):
  * ETC = (BAC − EV) ÷ CPI
  * EAC = AC + (BAC − EV) ÷ CPI = BAC ÷ CPI

#### **Cost Variance at Completion**

The cost Variance at Completion (VAC) provides an indication of the estimated cost under-run or over-run at the completion of the project:

* VAC = BAC − EAC

In this equation, 0 indicates that the project is forecasted to be completed on budget. A positive value indicates a forecasted under-run; a negative value indicates a forecasted over-run.

### **Earned Schedule**

In its basic form, EVM has not been used to estimate the time to completion or to forecast the end date. However, extensions to EVM have been developed to use EVM data for that purpose (Anbari, 2003; Lipke, 2009; Lipke, Zwikael, Henderson & Anbari, 2009; Turner et al., 2010).

The terminology in this area is not as fully agreed on as in EVM, so the following terminology will be used:

Schedule at Completion (SAC): This is the original planned completion duration (date) of the project.

Earned Schedule (ES): This duration from the beginning of the project to the date on which the PV should have been equal to the current value of EV. On the EVM chart (Exhibit 2), it is the date on which the horizontal line through the current value of EV intersects the PV curve.

Actual Time (AT): This is the duration from the beginning of the project to status date.

The average PV per time period can be calculated by dividing the BAC by SAC, and can be called the PV Rate or the Planned Accomplishment Rate (PAR):

* PAR = BAC ÷ SAC

SV can be transformed to time units by dividing SV by PAR. The result is a measure of schedule performance in time units rather than cost units and can be called the Time Variance (TV):

* TV = SV ÷ PAR

ES can be calculated by dividing EV by PAR:

* ES = EV ÷ PAR

Alternately, TV can be calculated by subtracting AT from ES:

* TV = ES − AT

If TV value is negative, the project is behind schedule and if it is positive, it is ahead of schedule. This is sometimes called the schedule variance (time), SV ~(t)~ , but this name can lead to possible confusion with the schedule variance.

![Components of the earned schedule. (Adapted from Anbari, 2003; Lipke, 2009; Lipke et al., 2009; Kwak & Anbari, 2010; Turner et al., 2010)](https://pmi-p-001.sitecorecontenthub.cloud/api/public/content/b8b26e89fc55444ba7b81a1d402f76ec?v=e79577f0)

**Exhibit 2: Components of the earned schedule.
(Adapted from Anbari, 2003; Lipke, 2009; Lipke et al., 2009; Kwak & Anbari, 2010; Turner et al., 2010)**

The time variance percent (TV%) can be calculated as:

* TV% = TV ÷ AT

The result of the above formula is consistent with the result obtained by using the SV% formula provided earlier. However, a more realistic measure of schedule performance may be obtained by calculating TV% by dividing TV by ES.

The time performance index (TPI) can be defined as the ratio of ES to AT and calculated as:

* TPI = ES ÷ AT

This is sometimes called schedule performance index (time), SPI( ~t~ ), but again there is a possible risk of confusion with SPI. As with SPI, if TPI is greater than 1, then the project is ahead of schedule and if it is less than 1, then the project is behind schedule.

### **Forecasting of Completion Time**

As an important extension to EVM, ES is particularly useful in forecasting the time at completion of the project, based on actual performance to date, using the same EVM data and the following calculations.

#### **Forecasting of Time at Completion**

The forecasting of time at completion can be called the time estimate at completion (TEAC). The fact that the estimated cost at completion is generally called the estimate at completion (EAC) and not the cost estimate at completion (CEAC), whereas the estimated time at completion has to be called the time estimate at completion (TEAC), underlines EVM's primary focus on cost control. The forecast of time to complete the remaining work can be called the time estimate to complete (TETC).

Several assumptions can be made about project schedule performance in the future (Anbari, 2003). Two major assumptions are:

* The remaining work will be performed according to the original plan and there will be no further delay to (or speeding of) the project, therefore (Anbari, 2003; Turner et al., 2010):

  * TEAC = SAC + TV
* The remaining work will be performed while maintaining the same rate of doing work for the rest of the project (Anbari, 2003; Lipke, 2009; Lipke et al., 2009; Turner et al., 2010):

  * TEAC = SAC ÷ TPI

  TEAC can also be calculated using the SPI, but the calculations are more complicated. They depend on whether the project has reached the planned completion date and whether it is ahead or behind schedule. If the date is earlier than SAC and the project has not yet finished:

  * TEAC = SAC ÷ SPI

  After the SAC is reached, SV tends toward 0 and concludes at 0 and SPI tends toward 1.0 and concludes at 1when the project is completed. Hence, using SPI for schedule forecasting after the SAC is reached would lead to erroneous results. Therefore, we define the Expected Accomplishment Rate (EAR) as the expected average accomplishment rate per time period from the status date to the completion of the project or the work package. Then:

  * TEAC = AT + (BAC − EV) ÷ EAR

  When past schedule performance is a good predictor of future schedule performance, EAR would be equal to the average Actual Accomplishment Rate (AAR) per time period to date, which can be obtained by dividing EV over AT:

  * EAR = AAR = EV ÷ AT

  Then:

  * TEAC = AT + (BAC − EV) ÷ AAR

#### **Time Variance at Completion**

The Time Variance at Completion (TVAC) gives an indication of the estimated amount of time that the project will be completed ahead or behind schedule:

* TVAC = SAC − TEAC

In this equation, 0 indicates that the project is expected to be completed on schedule, a positive value indicates that the project is expected to be completed ahead of schedule, and a negative value indicates that the project is expected to be completed behind schedule.

#### **Statistical Forecasting**

EVM and ES have been integrated with statistical confidence limits to derive the range of probable outcomes for the project final cost and duration. Results of this work demonstrate that the proposed approach is sufficiently reliable for general application of the forecasting method for both cost and duration, and that the ES approach can be applied effectively, regardless of the type of work or the magnitude of cost and duration of the project (Lipke et al., 2009).

### **Example**

The following example (adapted from Anbari, 2003 and Kwak & Anbari, 2010) illustrates the concepts discussed in this paper. Consider a project that has a baseline Budget at Completion of US$100 (followed by an appropriate number of 0's) and a baseline schedule of 40 weeks. The baseline indicates that by the end of week 20, the project is planned to be 50% complete. At the end of week 20, it is reported that 40% of the project work has been completed at a cost of US$60. The main components of this example are shown in Exhibit 3.

![Main components of the example project. (Adapted from Anbari, 2003 and Kwak & Anbari, 2010)](https://pmi-p-001.sitecorecontenthub.cloud/api/public/content/a2e8d909c98248ee853320964e9e6241?v=7185ac5c)

**Exhibit 3: Main components of the example project.
(Adapted from Anbari, 2003 and Kwak & Anbari, 2010)**

Using the EVM method:

* BAC = US$100
* AT = 20 weeks
* AC = US$60
* PV = 50% × US$100 = US$50
* EV = 40% × US$100 = US$40

Verifying:

* % Complete   = EV ÷ BAC = US$40 ÷ US$100 = 40%
* %Spent    = AC ÷ BAC = US$60 ÷ US$100 = 60%

Cost and Schedule Variances:

* CV = EV − AC = US$40 − US$60 = -US$20
* SV = EV − PV = US$40 − US$50 = -US$10

Performance Indices:

* CPI = EV ÷ AC = US$40 ÷ US$60 = 0.67
* SPI = EV ÷ PV = US$40 ÷ US$50 = 0.80

Estimate at Completion and Variance at Completion:

* EAC = BAC ÷ CPI = US$100 ÷ 0.67 = US$150
* VAC = BAC − EAC = US$100 - US$150 = -US$50

Earned Schedule:

* PAR = US$100 ÷ 40 weeks = US$2.5 per week
* TV = SV ÷ PAR = -US$10 ÷ US$2.5 per week = -4 weeks
* AT = 20 weeks
* ES = 40 ÷ 2.5 = 16 weeks
* TV = AT − ES = 20 − 16 = -4 weeks
* TPI = ES ÷ AT = 16 ÷ 20 = 0.80
* TEAC = SAC ÷ TPI = 40 ÷ 0.80 = 50 weeks
* TVAC = SAC − TEAC = 40 − 50 = -10 weeks

From the above, we can conclude that this project is in serious trouble regarding both cost and schedule performance. Corrective actions should have already been taken. It is critical to conduct an immediate review of this project, evaluate the underlying causes of the problems facing it, and make evidence-based, appropriate decisions promptly.

### **Comparison and Integration of ES and CPM**

Many professionals like to use network analysis techniques, such as CPM, for schedule forecasting and EVM for cost forecasting. However, it is important to note that these methods have different underlying assumptions. CPM is primarily a planning tool, whereas EVM is primarily a monitoring and control tool. CPM generally assumes that future performance will parallel the original plan, unless changes are made to the original plan scope, time, logic, resources, or cost. CPM assumes initially that problems or opportunities that affected performance in the past will not recur in the future and that the original plan is better than past performance on the project as a predictor of future performance on the same project. By contrast, the assumption generally associated with EVM is that past performance on the project is a good predictor of future performance on the same project, that performance to date will continue into the future, and that efficiencies or inefficiencies observed to date will prevail through the completion of the project, unless changes are made. Which of these assumptions and related forecasts will materialize depends greatly on decisions and actions taken by the project manager, the project team, senior executives, and others in the organization (Anbari, 2003; Turner et al., 2010).

Considering the above example, the project is 4 weeks behind schedule as of week 20. Using CPM analysis and assuming the worst case scenario that the 4 weeks are entirely on the critical path of 40 weeks, the project would be forecasted to be completed in 44 weeks, with a delay of 4 weeks. This analysis is demonstrated in Exhibit 4, where the critical path consists of activities C, D, and G, resulting in the planned project duration of 40 weeks:

* Planned Project duration = 14 weeks + 15 weeks + 11 weeks = 40 weeks

![Critical Path Method (CPM) analysis of the example project](https://pmi-p-001.sitecorecontenthub.cloud/api/public/content/ec4181976d81433f8a1e37c71c68e54e?v=c8d859c6)

**Exhibit 4: Critical Path Method (CPM) analysis of the example project.**

If the delay of 4 weeks occurred on the critical path, then the project would be forecasted to be completed in:

* Forecasted Project duration = 40 weeks + 4 weeks = 44 weeks

However, using ES, the project would be forecasted to be completed in 50 weeks, with a delay of 10 weeks. The 4 weeks represent 10% of the original duration of 40 weeks and may be considered a tolerable deviation in some organizations, whereas the 10 weeks represent 25% of the original duration of 40 weeks and would be considered beyond tolerable deviations in most organizations. This important difference affects resource allocation and management decisions greatly. Applying CPM and ES with a clear understanding of their underlying assumptions can enhance the project manager's decisions to achieve various project objectives. A combination of forecasts using CPM and ES can provide better perspectives of possible future outcomes.

It is advisable to ask functional managers and work package managers to review cost and schedule mathematical forecasts and to provide their own subjective forecasts for the work being performed in their areas. Both mathematical forecasts and subjective forecasts could be included in project performance reports. This approach highlights performance deviations for various managers, encourages them to consider appropriate, timely actions, and incorporates their detailed knowledge of performance in their own areas, which may not be evident from the mathematically forecasted values (Anbari, 2003; Turner et al., 2010).

Forecasting in project management may well be a self-defeating prophecy, which, ultimately, may be good for the organization. Large deviations usually attract the attention of senior executives and result in corrective action. Small deviations are usually left alone. By quantifying and highlighting such deviations, bar (Gantt) charts, project network analysis techniques (such as CPM), ES, and EVM help focus management's attention on projects or work packages that need the most concentration. As a result, these tools support effective management of projects and work packages collectively and enhance the management of the enterprise portfolio of projects and programs. Using these techniques consistently to forecast project outcomes provides a uniform approach to project reviews, builds confidence in the project outcome as time progresses, and enhances management's ability to take corrective actions and make evidence-based, appropriate decisions affecting all projects and programs (Anbari, 2003; Turner et al., 2010).

### **Conclusions**

ES is a powerful method that helps the executive, program manager, project manager, and other stakeholders in managing projects, programs, and portfolios more effectively. ES allows EVM metrics to be transformed to time or duration metrics to enhance the evaluation of project schedule performance, forecast the duration needed to complete the project, augment the project manager's understanding of the time estimate at completion of the project, and provide further insights for making better, evidence-based decisions about the project schedule and other parameters.

Anbari, F.T. (2003). Earned value project management method and extensions.  *Project Management Journal, 34(4)* , 12–23.

Association for Project Management (2006). *APM body of knowledge* (5th ed.). Buckinghamshire, UK: APM Publishing.

Fleming, Q.W., & Koppelman, J.M. (2010). *Earned value project management* (4th ed.). Newtown Square, PA: Project Management Institute.

Humphreys, G.C. (2002). *Project management using earned value.* Orange, CA: Humphreys & Associates.

Kerzner, H. (2009). *Project management: A systems approach to planning, scheduling, and controlling* (10th ed.). Hoboken, NJ: Wiley.

Kim, E.H., Wells Jr., W.G., & Duffey, M.R. (2003). A model for effective implementation of earned value management methodology.  *International Journal of Project Management, 21* (5), 375–382.

Kwak, Y.H., & Anbari, F. T. (2010). *Project management in government: An introduction to earned value management (EVM).* Washington, DC: IBM Center for The Business of Government.

Lipke, W.H. (2009). *Earned schedule.* Raleigh, NC: Lulu Publishing.

Project Management Institute (2008).  *A guide to the project management body of knowledge (PMBOK^®^ guide)* —Fourth Edition. Newtown Square, PA: Author.

Project Management Institute (2011).  *Practice standard for earned value management* —Second Edition. Newtown Square, PA: Author.

Turner, J.R. (2009).  *The handbook of project based management* , (3rd ed.). New York, NY: McGraw-Hill.

Turner, J.R., Huemann, M., Anbari, F.T., & Bredillet, C.N. (2010). *Perspectives on projects.* London, and New York: Routledge.
