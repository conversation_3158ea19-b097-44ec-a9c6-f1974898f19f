# 基于关键链理论下项目进度管理的优化路径

马  鑫

（西安石油大学，陕西 西安  710065）

摘要：在现代工程建设量不断增多、建设速度不断加快的大背景下，合理有效的项目进度管理计划制定，是保证整个工程项目顺利建成的重中之重。但是，由于社会经济发展水平的不断提升，建筑领域的新工艺、新技术也展现出一种层出不穷的景象，这种新趋势的产生使得现代项目进度管理工作的开展变得愈发艰难。为了彻底解决这一问题，相关领域和部门的工作人员针对项目进度管理方式的创新进行了积极探索。基于此，本文针对项目进度管理中如何发挥关键链理论的效用进行具体剖析，并对具体的优化路径进行分点阐释，以保证项目进度管理工作的高水平建设。

关键词：关键链理论；项目进度管理；优化路径

# 一、引言

在现代工程项目的管理工作开展中，“项目进度的管理”是其中十分关键的组成部分，能够充分反应工程项目管理水平的高低，因此，采用先进技术开展项目进度管理工作，并注重项目进度计划编制的可行性、合理性，是整个项目总工期目标达成的关键所在。本文所探讨的关键链方法，能够在项目进度管理中，以其自身所具备效用的充分发挥、释放，保证工程项目进度管理的效果、质量。

# 二、关键链理论概述

# （一）关键链理论内涵

以色列Goldratt 博士利用约束理论，开发了一种全面的、具有重要意义的项目管理理论，即“关键链”，在常规情况下，对于关键链的应用需要及时进行工作习惯、人为因素等的层面的全面考量。简单来说，所有的项目工作开展，都需要及时发挥人的主体作用，“人”是项目工作各个环节有效落实的关键，如果没有及时考量人为因素，整个项目建设的目标也很难做到迅速实现，这就是说项目管理水平的提升、项目绩效的提高等，都需要及时考虑“人”的内在因素、工作习惯、自然属性等，同时，需要注重创新管理方式在项目进度管理之中的融入，也就是说关键链在实际应用之中，需要于“人的因素”、管理与跟踪等多方面等进行兼顾考量。

除此之外，关键链是在关键路径基础之上，进行资源间约束关系的考量，并同时提出安全时间减少、缓冲区的设置等多种概念，并以资源冲突解决、安全时间消除等多种举措，保证项目关键路径的完善、优化效果，最终所能够达成的目的，就在于实现项目进度管理优化的目的，而在一般情况下，整个项目进度管理中需要设置的关键链会有多条。将关键链作用于项目进度管理之中，其与传统的项目进度管理方式相比优势是较为显著的，具体表现为，第一，项目进度管理计划的编制，会及时进行人为因素影响的引入，并及时考量，人心理变化的产生对于项目进度计划设计的影响，同时会进行概率分布等的利用；第二，项目进度管理对于关键链方法的应用，会使整个管理工作的开展更具全局性、整体性的特点，项目资源的最大潜能也能够得到最大限度的发挥与展现；第三，关键链的效用，还能体现在能够及时发现对项目进度管理产生影响的制约因素，并且能够对其进行辨别、分析，是项目进度管理人员采取针对性对策的关键支持；第四，关键链这种方式与项目建设的实况是更为接近的，其较为重视项目环节、活动等的逻辑关系，并且能够对各工序、活动的资源约束情况进行及时考量。

# （二）关键链法的提出考量

与传统的项目进度管理模式不同，采用关键链的技术可以有效地解决不同的任务，它可以有效地分析、评估、控制、协同，以解决不同任务与时间的矛盾，从而实现有效的项目资源节约。此外，它还可以根据实际情况，实时地调整和优化项目进度管理结构，以达到更好的效果。总的来说，采用关键链法的项目进度管理，其核心思想是根据可用的资源，明确各个节点并建立相应的缓冲机制，以此来维护整个项目的顺利推行，并最终达到预期的建设效果，实现项目进度管理的高水平建设。同时，通过引入关键链技术，可以有效改善传统的项目进度管理模式，该方式可以有效地捕捉到项目的发展趋势，有效地控制资源的分配，减少可能存在的问题，从而达到有效控制项目进度的目的。随着技术的发展，关键链法已经取得了长足的进展，为了能够保证关键链法的有效、经济、安全、可靠运用，相应管理人员必须做到持续深入的探索与完善，以便能够有效利用这一技术，满足多种需求。

1. 如果一项工作能够做到及时进行，就会存在相应的安全裕量、时间幅度、松弛量等，这就会使该项工作推迟至允许其完成的期限，这就意味着工作效率在该期间之内，没有得到及时的展现与提升，人力、物力等与项目工作相关的资源也就会产生浪费的情况。而如果不考量安全裕量等相关问题，则会给项目工作人员带来一定压力，其选择余地较小，只能够按照既定工期，尽一切努力保证项目建设的如期交付，这就是“关键链法”的重要表现形式。

2. 在进行项目估算的过程中，应该尽力去掉一些潜在的余量。实践证明，人们通常会根据估算结果，将 $100 \%$ 的 所需时间用于估算，但实际上，如果依照仅 $50 \%$ 的可能性来进行估算，那就意味着仅有 $50 \%$ 的可能性，能够完成该项工作，另外 $50 \%$ 面临的就是延期的可能，这样就会将原有工期时间的估算进行缩短。按照平均规律，把项目中所有的任务都按照 $50 \%$ 的概率进行项目的时间估算，结果使项目整个估算时间总体压缩了 $50 \%$ ，如果把它富余的时间压缩出来，作为一个统一的安全备用，作为项目管理的一个公共资源统一调度、统一使用，使备用的资源有效运用到真正需要它的地方，这样就可以大幅缩短原来项目的工期。根据平均数据，将每个任务的预测值减少 $50 \%$ ，从而将预测的总时长减少$5 0 \%$ 。此外，将剩下的部分资金合并，形成一个完善的安全备份，并将其纳入项目的综合管控，从而实现对各个部分的有序利用，从而最终实现最佳的施工周期。

# 三、项目进度管理中关键链的价值意义

# （一）利于找出关键工序及存在问题

关键链在项目进度管理之中，能够及时指出项目周期中的关键链、关键路径关系，二者之间是一种无完全对应的关系，其还能够进行缓冲区的设置，在汇入非时间资源缓冲区、时间缓冲区时，能够将项目存在的问题及关键工序进行及时有效的探查，并且能够进一步降低项目不确定风险的发生率，对于项目风险的把控能力、关键工序中的问题规避能力水平提升来说，也具备重要的作用。

# （二）确保进度计划执行中的稳定性

对于关键链方法的应用，能够对项目管理者的工作方向调整，进行及时有效的指引与启发，这种方式能够在实际进行过程中有效分析项目瓶颈资源，管理者也能够依照最终的分析结果，将项目建设的关键要素进行及时有效的把控，使项目建设中的资源统一协调、调度目标迅速达成，资源的合理使用也就能够得到及时有效的保证，进度计划执行中的稳定性，也能够在管理工作的充分践行之中得到有力支持。简单来说，在实际施工之中资源冲突的问题是难以完全规避的，因此就需要懂得利用相应的方式去有效解决资源冲突，这就意味着管理人员要注重各工序环节的资源平衡，即当多个工序或活动同时进行时，多个工序、活动的需求却恰好相同，这种情况可能会导致资源总量无法满足所有活动和工序的需求，从而使得项目进度受到影响，甚至出现延误。特别是在关键路径和非关键路径上，如果出现资源冲突，将会严重影响项目的整体进度，从而影响项目的完成。如果延误的时间超出了安全限制，那么关键路径将需要进行必要的调整。由此也就能够看出，将关键链方法融入工程项目进度的管理与控制之中，能够对整个项目进度计划执行的稳定性做出强而有力的保证。

# 四、基于关键链理论下项目进度管理的优化路径

而基于关键链理论下的项目进度管理优化路径探讨，笔者将以实际的工程案例为依托，进行详细阐释。在本研究之中的工程位于浦东新区某村镇之中，主要包括1 号、2 号标准养老所及地下汽车库，总建筑面积为 $3 0 4 5 5 . 7 3 \mathrm { m } ^ { 2 }$ ，其中地上总建筑面积为 $2 2 5 7 6 . 7 5 \mathrm { m } ^ { 2 }$ ，地下总建筑面积为 $7 8 7 8 . 9 8 \mathrm { m } ^ { 2 }$ ，1 号、2 号标准养老所为地上 12 层、地下 1 层，地下汽车库为地下 1 层，均为剪力墙结构。在利用关键链理论进行具体项目施工进度管理的过程中，需要重点关注以下四点内容。

# （一）做好准备工作

在前期准备环节需要及时关注到生产图纸的审核、技术交底、生产方案编制、采购方案确定、生产设备维修养护、生产人员培训、生产材料检验与入库、生产准备就绪、质量管理举措践行等多个方面。由此也能够看出，做好工程项目准备工作的重要意义，第一，应当制定一份具有明确目标、针对性较强的施工组织规划。应当采取最新的技术手段、最佳的施工技术、最佳的作业模式以及最佳执行步骤，尤其应当充分考虑到不同职能部门的协同配合，避免出现工种交叉作业矛盾的情况。第二，根据实际情况，科学安排人员和机械，避免由于缺乏人手和缺少必要的设备导致延误。第三，及早沟通物资，并严格遵守物资的发货日期，以便及早完成物资的交付。第四，应该积极开展全面的检测、评估、审核等，以确保物资质量，以便及早发现存在的问题，并采取有效的措施加以解决。第五，应该积极开展全面的项目审核，以确保质量。第六，应该加强与有关部门的沟通，以确保质量控制，以确保质量达到预期的标准。第七，应该加强与客户的沟通，以确保质量达到预期的水平。此外，为了有效地推进项目的完成，采取积极的措施，包括制定有效的工作计划，定期跟踪和检查，定期举办一次专业的内部培训，以便更好地了解和解决可能影响到项目完成的各种因素，从而有效地缩短项目的完成时限。

# （二）确定关键路径

在本研究中所涉及的实际工程项目进度管理，由于整个工程项目的施工时间较为紧张，因此在实际进行工程建设的过程中，也就有具体的工期要求，而多项活动的开展与工序之间本身是一种息息相关的关系。在实际施工的过程中，需要遵循：第一，在项目计划网络图中，紧前工作是指那些比较重要的任务或工作，它们应该优先考虑完成，以确保建设项目进度管理的有效性和高效性；第二，紧后工作则是指在网络图之中，在某项工作任务之后紧排的工作、项目。在这种逻辑顺序的任务践行完成之后，可以根据项目实施过程中，各种活动和工序相互之间的联系、持续时间、分解情况等，来重新构建一张完整的进度计划网络图，以便更好地完成任务，进而最终确定整体施工的关键路径。

# （三）识别出关键链

关于关键链的识别主要需要注意两方面内容：第一，就是作业任务的WBS 的分解，要懂得对本文之中的工程项目进行逐步分解，保证最终所获得的“工作单位”是最小、最易管理状态。运用WBS 将项目工作任务进行了分解，在进行具体活动工作的时候，要对涉及的工作任务做更为具体的分解。第二，需要进行工期的预估，采用三点估算的方式，进行该工程项目的确定，具体的计算公式如下：

$$
t { = } \left( a { + } 4 m { + } b \right) / 6
$$

在该公式之中， $t$ 所代表的是期望工序工期； $a$ 所代表的是乐观时间； $m$ 所代表的是最可能时间； $^ b$ 所代表的是悲观时间。经过专业人士的测算并结合工程实际的建设情况，即可进行各个环节工序所需应用最可能时间的计算，并同时得出项目各工序概算施工的周期。

# （四）设置好缓冲区

通过建立缓冲区，可以及时缩短项目进度的时间，并能够规避工期延迟情况的出现。关键链法的核心也正在于缓冲机制的构建，在一般情况下缓冲区可进行三种类型的划分，其中包括项目缓冲、汇入缓冲和资源缓冲，其中项目缓冲位于末端，不仅可以缩短施工周期，还能抑制或消除关键工序对整个项目进度管理工作开展所造成的影响。汇入缓冲可以被用来减少由于非关键性活动汇入导致的时间损耗，这样就可以确保项目按计划顺利完成，同时也可以防止这些操作给关键链带来负面影响，其存在于关键链与非关键链的交汇位置。而资源缓冲的效用，则展现在能够对关键链的资源供给进行及时有效的提供与支持，并且能够防止资源浪费，并且能够提高整个项目的施工进度、保证资源分配的合理有效。通过建立一套有效的资源缓存机制，不仅有助于维持关键链的正常运行，还有效避免由于资源不平衡导致的项目进度拖慢，使得所有的任务和步骤得到有效的实施。

# 五、结语

综上所述，在项目进度管理工作的开展中，对于关键链理论技术的应用，是保证工程项目工程有效缩短、工程建设质量有效提升的关键。本文从关键链理论概述、项目进度管理中关键链的价值意义、基于关键链理论下项目进度管理的优化路径等三个方面出发，重点阐释了如何更充分地发挥关键链方法的效用，并结合工程实例对关键链方法的具体应用进行了全面剖析，希望能够在具体工程项目建设中为开展项目进度管理工作提供相应思路，并为推进工程项目建设质量、提高项目进度管理水平提供支持与保障。

# 参考文献：

[1]	 徐哲，王黎黎 . 基于关键链技术的项目进度管理研究综述 [J]. 北京航空航天大学学报（社会科学版），2011，24（02）：54-59.  
[2]	 姜海莹 . 关键链项目管理法（CCPM）研究综述与展望 [J].建筑施工，2019，41（09）：1764-1769.  
[3]	 张俊光，季飞 . 基于活动弹性的关键链项目缓冲管理方法研究 [J]. 管理学报，2020，17（06）：924-930.  
[4]	 刘晓娟 . 关键链技术在工程项目进度优化中的应用 [J].科技与创新，2023（05）：170-172+175.  
[5]	 李建国，王涛 . 改进关键链法在某项目群进度管理中的应用 [J]. 项目管理技术，2023，21（05）：153-159.