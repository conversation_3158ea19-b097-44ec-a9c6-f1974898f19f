# 南京郵電士灣硕士学位论文

学 号 1320118211姓 名 蔡丹导 师 魏江茹 教授学科专业 工程管理研究方向 工程管理申请学位类别 工程管理硕士专业学位论文提交日期 2023年6月

# Thesis Submitted to Nanjing University of Posts and Telecommunications for the Degree of Master of Management

Research on Progress Management Optimization of

H Software Service Project of Company A Based on Scrum Model

By

Dan Cai   
Supervisor: Prof. Jiangru Wei   
Jun 2023

# 摘要

随着大数据、云计算等信息技术的不断发展，软件服务产业市场范围不断延伸，企业竞争也愈加激烈，该产业成为了近年增长最快的产业之一。为了促进软件项目按期完成并提高经济效益，各企业对项目过程管理愈加重视。在项目管理中，项目进度管理被认为是非常关键的一个方面，因为它对于保证项目能够在预期时间内完成以及实现项目目标具有直接影响。因此，项目进度管理被视为是项目管理中必不可少的约束条件之一。特别是对于软件服务项目的项目进度管理，已成为软件企业能否实现商业成功的关键要素。

本文聚焦研究 Scrum 敏捷模型在软件服务项目领域的应用。对于A 公司H 软件服务项目的情况和问题进行深入分析，并将 Scrum 敏捷模型与A 公司实际情况、H 软件服务项目实际问题进行深入结合，为H 项目进度管理提出改进措施，并验证 Scrum 敏捷模型在该项目进度管理中的有效性。本文首先对国内外软件项目进度管理相关文献文档进行了汇总及梳理；其次，结合文献研究结果，对A 公司H 项目前期存在的问题进行深入剖析，识别了H项目进度管理中所需关注的 TOP3 问题关注点；然后，通过项目进度管理模型的优劣势分析及 Scrum敏捷模型应用于H项目的可行性分析，可以得出结论:与瀑布模型等传统开发模式相比，Scrum敏捷模型更能够满足H 项目的进度管理要求；最后，基于 Scrum 敏捷模型框架和 H 项目实际情况，建立管理模型并提出优化项目进度管理的方法和技巧。

本研究为A 公司H项目进度管理持续优化改善提供了路径与方法，保障H 项目在既定时间内完成了项目交付。本研究为H 项目实现商业化成功奠定了坚实基础，提升了A 公司的项目管理能力。同时，该研究结果不仅可应用于类似团队或项目的进度管理，也可为其他企业管理工作提供理论指导和实践参考。

关键词：项目进度管理，软件项目进度管理，Scrum 敏捷模型

# ABSTRACT

With the continuous development of information technologies such as big data and cloud computing, the software service industry has become one of the fastest-growing industries in recent years. The market scope is constantly expanding, and competition among software enterprises is becoming increasingly fierce. In order to promote the timely completion of projects and improve economic benefits, software companies are paying more attention to project process management. In project management, project schedule management is considered a crucial aspect as it has a direct impact on ensuring that the project can be completed within the expected time and achieving project goals. Therefore, project schedule management can be regarded as one of the essential constraints in project management. Especially for software service projects, project schedule management has become a key element for software companies to achieve commercial success.

This article focuses on the application of Scrum agile model in the field of software service projects. Conduct in-depth analysis of the situation and problems of Company A's H software service project, and combine the Scrum agile model with the actual situation of Company A and the actual problems of the H software service project, propose improvement measures for the progress management of the H project, and verify the effectiveness of the Scrum agile model in the progress management of the project. This article first summarizes and sorts out literature and documents related to software project progress management both domestically and internationally; Secondly, based on the results of literature research, in-depth analysis was conducted on the problems that existed in the early stage of Company A's H project, and the top 3 issues that need to be paid attention to in H project schedule management were identified; Then, by analyzing the advantages and disadvantages of the project schedule management model and the feasibility of applying Scrum agile model to project H, it can be concluded that Scrum agile model can better meet the schedule management requirements of project H than traditional development models such as waterfall model; Finally, based on the Scrum agile model framework and the actual situation of the H project, establish a management model and propose methods and techniques for optimizing project schedule management.

This study provides a path and method for the continuous optimization and improvement of the company's H project schedule management, ensuring that the $\mathrm { H }$ project completes project delivery within the established time. This study has laid a solid foundation for the successful commercialization of the H project and improved the project management capabilities of Company A. At the same time, the research results can not only be applied to the progress management of similar teams or projects, but also provide theoretical guidance and practical reference for other enterprise management work.

Keywords: Project progress management, Software project progress management,Scrum agile model

# 目录

# 第一章 绪论..

1.1研究背景.  
1.2 研究意义. .2  
1.3 研究内容.. ..3  
1.4 研究方法.. ….4  
第二章研究综述 ….6  
2.1 项目管理的概念与内容. ..6  
2.2项目进度管理概述. ….6  
2.3软件项目进度管理的方法 ..7  
2.4 Scrum 敏捷模型概述， .11  
2.5国内外项目进度管理研究现状. ….13  
2.6 本章小结. ….15  
第三章A公司H软件项目概况及进度管理现状分析 ..16  
$3 . 1 \mathrm { H }$ 项目概况... ..16  
3.1.1 项目背景.. .16  
3.1.2 项目目标.... .17  
3.1.3项目运作情况. ..17  
3.2 项目进度管理现状分析.. ..19  
3.2.1 影响项目进度管理的因素分析. ..19  
$3 . 2 . 2 \mathrm { H }$ 项目进度管理现状.. ..21  
$3 . 2 . 3 \mathrm { H }$ 项目进度管理中存在的问题， .24  
$3 . 2 . 4 \mathrm { H }$ 项目进度管理问题根因分析. ….26  
第四章项目进度管理优化方案， ..31  
4.1项目进度管理模型优劣势分析 ..31  
4.2 Scrum 敏捷模型应用于H 项目进度管理的可行性分析…….33  
4.3基于Scrum 敏捷模型项目进度管理优化方案设计 ….34  
4.3.1项目组织优化方案设计.. ..34  
4.3.2需求管理优化方案设计. ..36  
4.3.3项目执行优化方案设计 ..37  
第五章项目进度管理优化方案的保障措施与效果评价 ….40  
5.1项目进度管理优化方案的保障措施 ….0  
5.1.1需求挖掘与确认. ….0  
5.1.2 有效的沟通.. ….44  
5.1.3变更流程的规范. ….5  
5.1.4项目进度监控. .47  
5.1.5 持续的知识分享与总结. ..49  
5.2 效果评价.. ..50  
第六章 总结与展望 ….53  
6.1 研究结论.. .53  
6.2管理启示.. ..54  
6.3 研究局限与展望. ..54  
6.3.1 研究局限. ..54  
6.3.2 未来展望. ..55  
参考文献.. ..57

# 第一章绪论

# 1.1研究背景

在项目运作中，项目管理一般都面临着时间、成本、质量三大约束条件，如何解决好这三大约束条件之间的冲突，是开展项目管理时必须考虑的问题。项目进度管理作为约束条件之一，直接决定着能否在预期时间内完成项目、实现项目目标。实施规范的项目进度管理，可以有效地保障项目在规定时间、预算和质量范围内顺利完成，从而保证项目的成功；可以帮助确定项目的步骤和任务，并对其进行有效的监控和管理，从而提高项目的效率；可以及时发现和解决项目中的问题，从而降低项目的风险；可以帮助项目团队成员有效地沟通和协调，从而提高团队的合作效率。

随着大数据、云计算等信息技术的不断发展，软件服务市场范围不断扩大，软件服务产业成为了近年来增长最快的产业之一。与此同时，软件企业间的竞争也愈加激烈，为了促进项目按期完成并提高经济效益，各软件企业对项目进度管理愈加重视。但是，尽管相关管理流程和工具在不断的完善，但失败的软件项目数量仍然很多，项目失败的原因主要在于无法在既定的时间和预算内交付满足客户需求的产品和服务。这也与软件项目自身的特点有关系，软件项目通常面临着市场竞争激烈、客户要求高、需求变更频繁等问题。在软件行业，客户总是希望供应商能够以不现实的速度更早地交付特性，有些组织为了实现这个需求而降低了产品质量，减少了测试和其他重要的实践，不能较好地实现进度和质量的双重目标。

将敏捷模型用于软件服务项目进度管理，可以较好地解决上述问题，其核心是：通过持续改进产品和流程，尽早地交付具有商业价值、反映客户需求且经过充分测试的产品。Scrum模型作为多种敏捷方法之一，基于经验型流程控制理论，通过对目标进度进行不断检视和调整，进而实现进度管理的优化。Scrum 模型采用迭代增量式的方法加强可预测性和进行风险管理，可以实现更快地响应市场、更好地适应客户需求的变化，并最终交付更高质量的结果。

A 公司是一家以提供信息服务、技术服务为主的科技公司，实行集团化经营，总部在P市，各地设立分公司负责属地化销售和服务，研发团队依托总部。长期以来，A公司销售产品单一，交付经验较少，主要为标准化产品、软件定制经验及 SaaS 服务（Software as a Service，软件即服务）。近年来A公司业务模式不断调整和创新，逐渐转型并承接知识管理服务相关的软件服务项目。H项目为A 公司在X省的标杆项目，该项目是由X省相关部门重点关注项目。项目具有社会影响力大、上线工期紧、技术方案复杂、产品受众面广、质量要求高等特点。在H项目中，A 公司需要在3 个月内交付一套全新的知识管理 SaaS 服务，客户初验后还需持续交付客户的运营定制化需求，对客户的反馈对不足之处按优先级进行一一优化，直至1 年期满项目终验闭环。但A 公司在从提供标准化产品软件向 SaaS 化软件服务的转型过程中，因自身对软件服务项目不够熟悉，且项目初期需求变更较为频繁，加之需求方在省区分公司、开发团队在总部，存在需求管理混乱、沟通成本高、研发过程效率低等问题，导致A 公司在Η 项目中面临着产品开发速度不够理想、产品交付质量不够符合预期等问题。

本文以A公司H软件服务项目为切入点，从项目立项、需求管理、开发管理、交付初验、运营定制化需求交付、终验结项等过程，分析该软件服务项目管理的现实状况和存在问题，并提供进度管理优化方案，助推 A 公司在日后的软件服务项目中更加规范化和专业化。

# 1.2研究意义

本文基于软件项目进度管理和敏捷 Scrum 敏捷模型的相关理论，并结合A 公司H 项目的具体运作情况，分析该项目进度管理中存在的问题，并提供相应的优化方案。

从理论价值来看，通过对软件项目进度管理和敏捷 Scrum 敏捷模型的研究，可以进一步提高项目效率，减少浪费，提高项目质量；可以更好地掌控项目进度，预防项目延误，确保项目顺利完成；可以帮助团队成员更好地协作，减少冲突，提高团队效率；可以增强项目的透明度，使客户、管理层、团队成员等能更好地了解项目进展情况；可以提高项目经理的项目管理水平，帮助他们更好地管理项目；可以提高项目经理的风险管理能力，以便在项目中及时发现和解决问题；可以增强项目的竞争力，为公司带来更多的商机；可以提高项目沟通效率，减少信息传递的误差；可以确保项目的可持续性，使项目的成果能够长期保存和利用。研究将进一步丰富软件服务项目进度优化管理的相关理论。

从实践价值来看，对于软件项目进度管理和敏捷 Scrum 敏捷模型开展深入研究，可以提高项目的效率，缩短项目周期，减少成本；可以保证项目的质量，确保项目的成果符合预期要求；可以提高团队的协作能力，促进团队成员的配合；可以提高项目沟通的效率，减少信息传递的误差；可以提高项目的透明度，方便决策者了解项目的进展情况；可以增加项目的可持续性，使项目的成果能够长期保存和利用；可以提高项目风险管理的能力，及早发现并应对项目风险，保证项目的顺利进行；可以不断增强项目管理的能力，提升项目管理的水平；可以提高项目的竞争力，在市场竞争中占据优势。

本文以软件服务项目进度管理为研究对象，并结合H项目的实际现状，探讨如何更科学地进行软件服务项目进度管理，解决软件项目进度管理中“因进度管理不确定、效率有待提升等复杂性问题最终导致的项目延期”的问题，基于Scrum 敏捷模型开展研究，并结合实际应用至H项目中，进行有效的软件服务项目进度管理。技术线路图如图1-1所示。

通过查阅近年来项目进度管理、项目进度管理工具和技术相关的著作、期刊和论文等文献资料，比较瀑布模型、极限编程、Scrum 敏捷模型等技术的优劣所在，并重点参阅 Scrum敏捷模型的发展和应用文献，作为论文的理论基础；深入分析A 公司H 软件服务项目的进度管理现状，识别问题根因，在研究分析基础上提出改进方向；基于A公司实际情况及H项目的实际需求，开展 Scrum 敏捷模型在该项目中的适用性分析；将 Scrum 敏捷模型应用至 A 公司H 软件服务项目的进度管理中，并针对项目实际情况做一些适应性调整，使其在H项目的进度管理中更具效果；对 Scrum 敏捷模型在A 公司H 软件服务项目中的应用效果进行评价，验证有效性；最后，对 Scrum 敏捷模型在软件服务项目进度管理中的成果和不足进行归纳总结。

本文通过对A 公司H 项目进度管理进行优化，为A 公司开展同类项目的进度管理提供了方法借鉴，也为A公司优化其公司的项目管理制度提供了成功样例。

# 1.3研究内容

本文属于实践型研究，以A 公司H软件项目为例进行研究。

在H项目早期过程中，A 公司在运作项目时采用瀑布模型，项目组的沟通效率低、开发流程长、需求变更难、重文档输出等问题较为突出。通过深入探究H 项目早期过程中可能会影响项目进度管理的各种因素和存在问题，对影响项目进度控制的风险点进行详细的分析和探讨。

基于H项目早期使用瀑布模型的实践，及客户需求多变、项目工期紧等外部要求，最终识别了H 项目进度管理中所需关注的 TOP3 问题关注点：开发周期短，客户需求多变，减少僵化文档输出、降本增效；除上述三点外，还需提升项目对客户的透明度，进而提升与客户的互动，进而提升客户满意度。

在此基础上，确定选择了Scrum 敏捷开发模型作为H项目的改进提升方向。基于敏捷Scrum 框架的方法和工具，结合实际的项目进度构建管理模型，优化项目进度管理的方式和方法，为H项目进度管理带来持续的优化改善，包括但不限于：开发周期短、文档输出少、对客户透明度高、可以应对H项目需求多变的问题、关键角色明确、项目组决策快捷且可信、流程与关键过程件清晰、沟通效率高等。

本文整体研究框架包括：

第一章：绪论。对本文的研究背景、研究目的、研究内容及研究方法进行说明，并对本文对于项目进度管理的研究结构框架进行简要介绍。

第二章：研究综述。对相关文献进行梳理和总结后，进一步打开分析软件项目进度管理，并梳理相应开发模型，如传统瀑布模型、极限编程、Scrum 等敏捷开发模型。通过理论结合实际，识别当前实践中的不足点，本文进一步展开研究。

第三章：A 公司H 软件项目概况及进度管理现状分析。在H项目中，A 公司采用传统的瀑布模型进行项目进度管理，但在实际应用中遇到了诸多问题。针对这些问题，本章进行了影响因素分析，并对Η 项目进度管理中存在的问题进行了识别，同时对问题原因进行了根因分析，以便更好地解决这些问题。

第四章：项目进度管理优化方案。从项目进度优化角度，结合A 公司在H 项目中遇到的实际问题，设计了基于敏捷 Scrum 模型的项目进度管理优化方案。该方案从项目组织、需求管理和项目执行等多个方面入手，旨在帮助A 公司解决H项目中存在的问题，实现项目进度的优化和管理。

第五章：项目进度管理优化方案的保障措施与效果评价。研究H项目进度管理中的各项关键保障措施，包括需求挖掘与确认、有效的沟通、变更流程的规范、项目进度监控、持续的知识分享与总结等。

第六章：总结与展望。总结 A 公司H 项目实施 Scrum 敏捷模型进行管理变革后的项目进度管理效果，及本对A 公司H 项目进行案例分析，并对其不足之处进行了探讨，同时提出了未来改进的方向和展望。

# 1.4研究方法

本文研究过程中，采用了包括文献参考法、案例研究法等多种研究方案，力图从多维度、多方面深度剖析H项目的问题，提出管理优化方案。

（1）文献参考法

采用图书馆、数据库检索方式搜集论文资料，参阅国内外在项目进度管理及 Scrum 敏捷模型方面的相关文献，并结合软件项目的特点，总结出适用于A 公司软件服务项目的知识理论和方法。

通过文献参考法，能够了解到同一领域的其他研究者的研究成果，减少重复研究；获得大量关于项目进度管理及 Scrum 敏捷模型方面的权威研究信息，从而提高研究的权威性；加深对项目进度管理及 Scrum 敏捷模型的了解，从而为本文的研究提供研究依据。

# （2）案例研究法

案例研究法旨在通过对具体案例的分析和描述来理解某一现象、问题或环境。是一种有价值和有意义的研究方法，在许多学科和领域中都得到了广泛的应用。它的研究价值包括：帮助研究者更深入地理解某一现象或问题，并为进一步研究奠定基础；为相关理论提供实证证据，从而加深对该理论的认识；以生动形象的方式呈现研究结果，使得研究结果更加直观和易于理解；帮助研究者探索未知领域，拓展知识领域。

本文选取A 公司H 软件服务项目作为典型案例，通过分析项目管理中的主要问题，并结合软件项目管理的相关理论、方法和工具进行分析，推动 A公司的项目管理能力得到进一步提升。

# 第二章 研究综述

# 2.1项目管理的概念与内容

项目是组织创造效益的主要方式，是创造独特产品、服务等各类成果的工作任务[]。项目管理是指在有限资源约束条件下，为实现项目目标和质量要求而进行的管理动作[2]。

随着市场竞争日益激烈，传统企业及机构所通常采取的“各层级和各部门基于分工原则在各自领域和专业内开展活动”的管理方式普遍被逐步淘汰，替换成了以项目作为核心的现代管理方式[3Ⅱ4]，其具备技术要求复杂、专业涉及广泛、人员参与众多等特点。

项目管理涉及人力资源管理、风险管理、成本管理、质量管理等众多层面[5]，通过对各管理层次（纵向）和各部门（横向）的资源进行整合，形成一个有机整体，进而保障项目目标的完成。项目管理是对传统管理模式的有益补充，可以较好地实现项目运作。专业化项目管理能够持续提高企业与机构的核心竞争力，助力企业与机构实现其战略目标[6]。

如今，项目管理的应用越来越广泛，不论是交通、水利、国防等国家建设领域，还是企业投资、产品开发等私人经营领域，项目管理已逐渐扩展到各行各业。特别是21 世纪以来，各行业正在通过将大数据、物联网、人工智能和区块链等技术相互融合和应用，实现信息化和智能化的转型和升级[7，软件项目管理作为一项非常重要的工作，为互联网科技和信息化的发展发挥着不可忽视的作用[8]。相关分析表明，是否落实项目管理会直接影响软件开发的成败[9]。以部分企业为例，ABB、IBM、MOTOROLA 等国际著名公司均在公司管理过程中不同程度地应用了项目管理[10]。有研究学者指出，软件项目的管理具备先进性、可靠性、实用性、标准化、可持续性和扩展性等特征[1]。

# 2.2项目进度管理概述

在项目管理领域中，项目进度管理是不可或缺的重要内容之一。项目进度管理具体定义是：通过在时间、资源、人员等方面合理规划项目任务，以达到项目目标的函数最优化[12]。

项目进度管理过程包括定义项目活动、排序活动、估算活动工期、制定进度计划和进行进度控制等多个步骤。其中，项目活动定义是指确定实现项目目标的具体操作活动；项目活动排序是指确定项目活动的逻辑关系，可使用里程碑制度、网络图法及关键路径法等方法进行确定；活动工期估算包括所消耗的工作时间和工作间歇时间，估算方法有历史数据法、专家经验法等；制定进度计划是指通过研究活动顺序、活动历时和资源条件等来制定计划，随着较多数据的获得，对活动程序进行反复改进，实现进度计划的不断更新。特别需要注意的是，软件项目由于具有客户需求变更快且频繁等特点，需要结合软件项目的特点去制定计划；进度控制是指通过采取纠偏措施或预防手段更新进度计划，以实现对项目进度和项目质量的要求。

在项目进行的过程中，通过对各个活动的进展情况以及整体进度进行管理[13]，能够确保项目在预定时间内按照计划完成，并且让项目资源（人员、财务、物资等）得到有效利用。通过有效的项目进度管理，可以提高项目的执行效率，避免浪费时间和资源；确保项目的质量得到保证；提高项目的透明度，使项目成员、客户和管理者能够清晰地了解项目的进展情况；更好地控制项目的风险，确保项目的可控性；增强项目团队之间的沟通，确保项目成员对项目的进展情况有充分的了解；促进项目团队的合作，使项目成员之间能够高效合作，共同完成项目任务；确保项目在预定时间内完成，提高客户对项目的满意度。

对项目进度管理开展研究，可以不断提高项目管理水平，使项目管理更加科学、高效；为项目管理实践提供支持，帮助项目管理者更好地解决项目管理中的实际问题；丰富项目管理理论体系，为未来的研究工作提供基础。同时，该研究对于推进项目管理行业的发展、提高项目管理的成熟度和严谨性具有重要的意义。希望通过对此开展研究，为项目的管理与决策提供有力的依据，帮助项目管理者做出更加明智的决策。

# 2.3软件项目进度管理的方法

“互联网+”时代已经到来[14]，而软件正是构成“互联网+”的重要基础设施。软件是计算机指令和数据按照一系列特定顺序组织的有效结合[15]。当前，软件产业已成为经济发展的数字基础，更是推动经济快速发展的核心动力之一[16]。

项目进度管理是通过运用科学的方法和工具，确定项目的进度目标、制定进度计划以及资源供给计划的过程，通过各类资源的协同与平衡，以达到项目目标[17]。软件项目进度管理，作为项目进度管理的一个子类，被称为“规划软件项目和带领项目团队的科学和艺术”[18]。通过对软件项目进度进行有效管理和规划，能够保障软件项目在满足成本、质量等要求的前提下按照预定的进度计划如期完成。

软件项目进度管理与一般项目进度管理有共通之处，又有一些差异，例如：软件项目进度管理的主要目标是生产高质量的软件，相较于其他项目，软件项目进度管理的目标更加具体，软件项目进度管理过程通常覆盖了软件开发的各个阶段，如需求分析、设计、开发、测试、部署等。一般项目进度管理的过程可能不包括这些步骤。软件项目进度管理需要掌握软件开发的相关技术并且要关注软件产品的质量、安全、可靠性等方面的要求，而一般项目进度管理的技术要求不如软件项目进度管理严格；软件项目进度管理通常需要更多的人力、设备和资金等资源，相比一般项目进度管理，软件项目进度管理需要考虑的风险更多，包括技术、人员、资源等多方面风险，一般项目进度管理的风险因素通常没有这么多。因此，做好软件项目进度管理很有必要性，也面临着很大的挑战。

随着信息技术的快速发展，客户对软件的需求越来越高，软件的功能也越来越复杂，需求规模持续扩大，带来了更多的技术挑战。通过常规方式开展软件研发，已无法满足当前软件研发项目的需求[19][20]。例如，软件项目经常需要跨部门、跨公司合作，带来了人员多样化等问题；软件项目需要大量的资源，如人力、设备、财力等，资源的限制和调度风险也越来越大；软件项目的规模也不断扩大等等。这些因素都使得软件项目管理变得越来越复杂。

由于软件项目越来越呈现复杂化的发展趋势，不可控的风险因素（如需求变动快、返工频繁、项目成员沟通不畅等）也越来越多，对软件项目能否成功产生了很大的影响。特别是互联网企业等以软件为主行业的公司，对软件项目的管理好坏及如何提升项目进度管理水平，成为这些软件企业发展中必将面临的主要问题[21]。

学术界为优化软件项目进度管理提出了一些方法，如：Winston Royce 于1970 年提出瀑布模型，中川威雄于 1979 年提出快速原型模型，Martin Fowler 等 17 位软件专家于 2021 年提出敏捷开发（Agile）概念。

# （一）瀑布模型

瀑布模型，又称顺序式流程或瀑布模型，它是最早出现的软件开发模型，也是人们最容易理解和掌握的开发模型[2]。在瀑布模型中，需求分析、设计、开发、测试、编写文档等是分阶段进行的，一个阶段必须在前一个阶段结束后开始。瀑布模型为软件开发项目提供了基础框架，通过锁定需求并将软件产品一次性交付给客户。

截至目前，瀑布模型仍是大量中小型软件企业所普遍采用的流程。但瀑布模型在实际项目的运作中也会存在一些问题，如分阶段顺序运作在一定程度上会造成不同阶段不同业务职能的人员处于空闲状态，带来时间的浪费。

软件是抽象的、看不见摸不着的、只有逻辑实体而没有物理实体的，客户一般也不具备软件方面的相关知识。因此，在软件服务项目中，尤其是在互联网软件服务项目中，客户提出的需求通常比较琐碎，且经常临时需求变更。在此场景下，软件服务企业较难实现“锁定需求、并将软件产品一次性交付”，因此，瀑布模型对于互联网软件服务开发项目的适配性存在一些不足。

# （二）快速原型模型

快速原型模型是一种软件开发模型。使用该模型时，首先构造一个功能简单的原型系统，再对原型系统逐步优化、持续扩充完善，最终得到终稿的软件系统[23]。

快速原型模型优化了瀑布模型中需求不明确的问题，并加速了需求计划的进程，但快速原型模型没有改变瀑布模型分阶段顺序运作的整体模式，缺乏一定的灵活性。且原型是在很短的时间内构建的，可能出现设计不充分等问题，导致在正式开发迭代过程中可能会出现问题。并且，由于原型的可维护性较差，因此开发人员通常需要花费更多的时间和精力来将原型转换为稳定、可维护的软件系统，也影响了项目效率。

# （三）敏捷模型

随着信息化和IT 行业的发展，对灵活性和适应性有了更高的要求，敏捷模型应运而生。2001 年，17名软件行业专家正式提出《敏捷软件开发宣言》（又称《敏捷宣言》）[24]。宣言中强调了软件开发过程中应该注重人与人之间的交互合作，而不是过于依赖流程和工具；开发出可工作的软件比详细的文档更重要；与客户的紧密合作比合同谈判更为关键；能够应对变化比严格遵循计划更具优势。

敏捷模型通过将整个项目分解成更小的片段（迭代）进行项目运作，可以根据需要调整优先级、控制项目风险、尽早并经常进行测试以实现按时交付满足客户需求的项目产品[25]。敏捷开发在软件开发项目中的应用，具有显著的应用意义[26]，成为了软件行业公司创新项目中应用的重要方法[27]。

敏捷模型的主要特征是快速迭代、小步快跑[28]。它的核心理念是通过迭代以适应客户需求的频繁变更，核心思想可以总结为早期合理规划、小规模迭代开发、尽早交付、与客户沟通并持续改进、在客户需求变更后快速灵活应对与处理等。敏捷开发及项目管理的目标，是希望在最短时间内交付最大的业务价值，以客户为中心、以项目组为基础，高度协作和迭代开发。

在当下日益变化的商业环境中，敏捷开发可以快速响应需求变化并做出调整以赢得商机。敏捷开发项目中，项目组与客户深入互动，并对客户的需求进行快速响应，逐步迭代、渐进明晰，最终找到最合适的解决方案。同时，在敏捷方法中，团队成员互相合作、平等相处，也能够更好地促进项目管理发展[29]。

敏捷产品开发，不仅是执行敏捷方法流程，还包括进行敏捷产品开发，即不仅在研发产品时要应用敏捷思想，产品本身也需要有敏捷性[30[31]。敏捷开发的益处包括但不限于：尽早实现收益、高质量交付产品、项目进度高透明、实施过程风险低、模式便捷灵活、干系人满意度高、团队氛围融洽等。常见的敏捷开发模式包括极限编程（eXtreme Programming，简称XP）、Scrum开发流程[32]等。

极限编程是敏捷开发常用的一种模型方法，由Kent Beck 于1996 年提出。极限编程更加适用于中小型业务团队，通常被应用于开发需求高频变化的或者需求相对比较模糊的软件[33]。极限编程是一种轻量级、灵活的软件开发方法，它将复杂的开发过程分解为一系列小的迭代周期。在这个过程中，开发人员与客户之间积极地进行交流和反馈，通过一系列的实践方法，能够清晰地了解开发进度、变化、问题和潜在困难，并根据实际情况及时调整开发过程。极限编程方法注重团队合作和迭代式开发，旨在提高软件开发效率和质量，以满足客户的需求。它以沟通（Communication）、简单（Simplicity）、反馈（Feedback）、勇气（Courage）、尊重(Respect)为价值标准，要求项目团队遵循团队协作（Whole Team)、规划策略（The PlanningGame)、结对编程（Pair programming）、测试驱动开发（Testing-Driven Development）、重构（Refactoring）、简单设计（Simple Design）、代码集体所有权（Collective Code Ownership）、持续集成（Continuous Integration）、客户测试（Customer Tests）、小型发布（Small Release）、每周 40小时工作制（40-hour Week）、编码规范（Code Standards）、系统隐喻（System Metaphor）等13个核心实践。

实践证明，极限编程作为一种优秀的敏捷开发方法，较瀑布流式等传统开发方式存在明显效率优势。极限编程不是很强调文档在其过程中的重要性，更多是一个由测试驱动且不断滚动向前的迭代流程[34]。但其也存在一定局限性，包括：

（1）重视口头交流，不强依赖正式文档。极限编程理念认为，口头交流效率更高、沟通更直接，因此重视直接交流；甚至鼓励团队处于一个空间内，以便更高效的沟通。当团队处于异地阶段，口头交流不变时，这个会成为一个瓶颈。

（2）集体拥有业务代码。在极限编程模式下，所有团队成员都拥有对代码的修改权限，这种“集体拥有”模式能够鼓励更多的代码共享和团队合作，从而提高开发效率和质量。这个模式整个对团队的整体配合度要求性高，否则极容易出现问题。

（3）文化认同难度高。极限编程需要团队成员共同合作，如果团队中有成员不愿意合作，可能会对项目造成困扰。并且，极限编程要求团队成员具备相应的专业技能，如果团队成员技能不足，可能会影响项目质量。在极限编程中，常常采用结对编程的方式，即由两名程序员一同工作于同一台计算机，进行协同开发，一起设计、开发及测试[35]。有些开发人员习惯于独自完成职责内的部分，可能会出现无法接受此类模式的风险。同时，由于两人同时工作，也会导致两人各自的效率、输出、贡献度难以评价。

Scrum 开发流程，则是另一种敏捷开发常用的模型方法，在本文后续章节中会展开详述。

# 2.4 Scrum 敏捷模型概述

Scrum 敏捷模型软件项目管理中的一种轻量级敏捷软件方法[36]，是敏捷开发最常用的一种模型方法，也是最适用于需求多变的项目的敏捷方法之—[37]，它由 Sutherland 和 Schwaber于 1995 年提出。Scrum 一词源于橄榄球比赛中争球的概念，Scrum 业务流程则将软件开发过程类比为一支橄榄球队伍，整个项目组（即队伍）有着相应的业务目标，并且在开发过程当中使用相应技术法进行规划和开发，通过互相交流合作，使用弹性化的问题解决方式来处理团队遇到的各种问题，确保团队各个环节、各个模块都能达到既定目标。近些年来，敏捷开发正在被越来越多的企业和机构引入和使用，其中，Scrum 开发方法应用最为广泛，成为了大多数敏捷开发项目组和团队的第一选择。

Scrum 由3个角色（即产品负责人、开发团队、Scrum主管）、3个工件（即产品需求列表、Sprint 开发列表、产品增量）以及 5 个事件（即 Sprint、Sprint 计划会议、每日站会、Sprint评审会议以及Sprint 回顾会议）构成。详细说明如下：

（1）Scrum关键角色

产品负责人：通常是指项目中的具体负责人，他们需要从客户和利益相关方的角度出发，对产品进行全面规划，以确保项目产出具有最高的价值。产品负责人需要在原始需求的基础上，将模糊的需求概念转化为具体的产品需求，并编写相应的客户故事，随时更新产品需求列表以满足外部需求的变化。

开发团队：开发团队是软件项目组的核心成员，他们负责根据产品需求和客户故事开发软件代码并将其转化为实际可运行的软件产品。在项目开发的各个阶段，开发团队需要参与并负责开发计划的制定和实施，参加每日站会，及时向团队汇报开发进展和计划，并积极协作解决遇到的问题。在版本迭代结束后，开发团队需要参与评审会议并进行代码审核，确保软件产品的质量和稳定性，同时需演示增量产品，接受会上相关人员的质询，并复盘和总结此次迭代开发过程。

Scrum 主管（Scrum Master）：Scrum 主管负责确保项目按照 Scrum 流程要求规范执行，而他本身不一定是团队的主管。在实际的运作中，为确保整个项目的顺利推进，提升项目效率，Scrum 主管通常也会行使部分团队主管的职责。Scrum 主管是敏捷开发中的关键角色，负责推动 Scrum 流程的实施，以实现高效的项目管理和任务完成。在项目开发过程中，Scrum主管需要根据实际情况对 Scrum 流程进行灵活调整，以确保项目进展顺利。此外，Scrum 主管还需要积极争取团队所需的资源，为团队创造舒适的工作环境，以提高团队的生产力和工作效率。

# （2） Scrum 关键工件

产品需求列表(Product Backlog):待交付的产品内容，需按照优先级进行需求排序。Sprint开发列表（Sprint Backlog）：指在一个版本迭代周期内要实现和完成开发的产品需求。产品增量（Increment）：经由迭代开发后，最小化、可发布的产品版本，即可交付给客户的内容。

（3）Scrum 关键事件

Sprint：Sprint 表示一次软件版本迭代开发周期，通常2-4 周。

Sprint 计划会议：在本会议中，产品负责人讲解已经按优先级排序的产品需求列表，决定将哪些需求排进本次版本计划，并于会上讨论制定详细的迭代计划。

每日站会（Daily Stand-up Meeting）：每日站会是 Scrum 团队在版本迭代开发阶段的重要活动，每天固定时间，通常不超过 15 分钟，全体团队成员参与，会议由Scrum 主管负责召开。会上，所有的团队成员围成一圈，依次向团队成员汇报既定的议题，主要议题包括：昨天的工作成果，今天计划完成的工作任务以及当前遇到的问题和障碍。每日站会通过团队成员之间的相互汇报和同步，增强了团队的凝聚力，同时及时地反馈项目进展与遇到的问题。

Sprint 评审会议：在版本迭代结束后，项目团队会召开一个会议，对本次迭代交付的成果进行展示和评审。

Sprint 回顾会议：Scrum 主管主持会议，总结本次版本迭代开发的成果，分析团队在实践中遇到的挑战，评估团队的整体表现，以便从中学习并提高，在下一次迭代中更好地执行。

（4）Scrum 其他关键概念

燃尽图，又称 Sprint 燃尽图，它是敏捷方法中常用的工具，整个敏捷过程的进度控制可通过 Sprint 燃尽图来进行监控[38，燃尽图是一种可视化的工具，用于显示项目剩余工作量和时间之间的关系。该图通常以时间为Χ轴，以工作量为Y轴。在理想情况下，随着时间的推移，剩余工作量应以相同的速度减少，最终在剩余时间为0 时达到0。这个过程就像一根烛台上的火焰在燃烧，最终耗尽。

Scrum 的基本思想是足够灵活以适应需求、资源的变化。首先团队收集需求并根据需求的优先顺序进行排序后，通过一个较为简短的会议进行规划和设计，将整个项目划分成小的迭代（Sprint），每个Sprint 都有一个Sprint 待办列表，其中包含该Sprint 期间要完成的目标，在迭代结束时交付一小部分软件，侧重于优先交付最高价值的需求，经客户验收测试后若有任何错误或者有任何更改，将其加入产品待办列表并在下一个 Sprint 中予以实施[39]。在每个迭代周期（一般为2-4周）中，Scrum团队各成员互相协作，通力配合，以实现可运行产品的定期交付。

由于 Sprint 将整个项目划分为小的迭代，有助于对正在开发的产品进行进度监控和障碍识别，促进了开发效率的提高[40]。通过运用敏捷 Scrum 框架，可以促进团队成员间的配合并促使成员积极参与到项目全过程，促进有限资源功能的发挥，提高软件开发效率[41]。且通过从客户处获得定期反馈，当客户要求发生更改时，可以针对这些更改进行软件的改进升级，从而实现更加高效、更加快速地进行产品开发，在 Scrum 的帮助下，软件产品的开发可以在相对较短的时间内完成[42]。Scrum 敏捷模型的创立，解决了传统项目管理的困境，为软件开发行业、软件项目行业提供了一个更快速、更高效的开发方法。Scrum 敏捷模型的优势包括但不限于：以人为本，注重团队合作与个体间的互动[43]，提高项目效率；通过迭代开发，快速交付价值，提高交付质量；通过团队成员的日常沟通，提高团队沟通效率；注重灵活性，可以根据客户需求和市场变化，随时调整项目方向；鼓励团队成员的自主性，让团队成员参与到项目决策和计划的过程中；以客户需求为中心，通过快速交付价值，增强客户满意度；通过每日站会和迭代评审，提高项目透明度，让团队成员和客户更好地了解项目进展情况。

除此外，在《2020 Scrum Guide》中，Scrum 敏捷模型定义了5 个价值观：Scrum 敏捷团队致力于达成目标并相互支持，专注于 Sprint 的工作，对工作与挑战持开放态度，团队成员相互尊重，用于做正确的事及处理棘手的问题[44]。

尽管 Scrum 敏捷模型有很多的优势，但不得不承认，其也存在一些不足。例如：Scrum敏捷模型适用于快速变化的环境，对于一些需求稳定、变化少的项目，Scrum 可能不太适用；Scrum 敏捷模型的重点在于交付价值，但未充分强调项目管理中的各项技能，例如预算管理和资源管理等，导致作为 Scrum 团队领导者的 Scrum 主管需要具备高超的项目管理素养，以确保团队能够顺利完成项目。为保障团队的快捷和高效沟通，Scrum 敏捷模型还需要一定的技术支持，如项目管理工具、沟通协作平台等。Scrum 敏捷模型通常的迭代周期较短，对于一些长期项目（例如一年仅发布一个版本的超大型软件开发项目），可能适用性略差。因此，企业需要根据自身公司及业务实际情况，去选择最合适的项目进度管理方法。

# 2.5国内外项目进度管理研究现状

为适应软件项目发展的新趋势，国内外学术界为优化软件项目进度管理提出了一些模型和方法，如瀑布模型、快速原型模型、敏捷方法等，并在各行各业持续实践（荣国平等，2019)。

在瀑布模型中，需求分析、设计、开发、测试、编写文档等是分阶段进行的，一个阶段必须在前一个阶段结束后开始，瀑布模型有利于实现结构化，采用瀑布模型进行开发，对项目组的分工合作极其有利[45]，提升一定的运作效率（郭连明，2016）。瀑布程中需求计划阶段倾向于“深思熟虑”，但由于需求计划阶段通常不完全清楚到底需要什么，较好的解决方案也总是在开发过程中才会涌现出来，故提前计划是较为困难的，且这样做也会花费较多的时间，从而对下一阶段运行产生不利的影响[46]（Ken and Jeff，2014）。通过锁定需求并将软件产品一次性交付给客户，投资和努力的付出是巨大的，同时也面临着较大的失败风险[47](Johnny，2018)。特别是在软件项目中，由于软件本身的不可见性和项目需求的不准确性，难以精确捕获需求，软件项目拖期、交付结果不理想在软件项目中也较为常见（王敬磊，2020)。

快速原型模型提供了一种有效的解决方案，它通过迭代的方式让客户和开发团队快速打磨出一个可行的原型，从而明晰客户需求（张越，2019），优化了瀑布模型中需求不明确的问题，并加速了需求计划的进程，但快速原型模型没有改变瀑布模型分阶段顺序运作的整体模式[4⁸]，缺乏一定的灵活性（张晖，2021）。而敏捷方法的出现可以较好地解决这一问题。

敏捷的提出，最早可追溯到英国文艺复兴时期散文家、哲学家弗朗西斯·培根提出的《论敏捷》，该方法为现代“敏捷”的出现奠定了基础。2001 年，17名软件行业专家正式提出《敏捷软件开发宣言》（又称《敏捷宣言》），“敏捷”的现代化概念从此诞生[49]（HighsmithJ，2005）。宣言中强调了软件开发过程中应该注重人与人之间的交互合作，而不是过于依赖流程和工具；开发出可工作的软件比详细的文档更重要；与客户的紧密合作比合同谈判更为关键；能够应对变化比严格遵循计划更具优势[51】（Leffingwell D，2009）。

敏捷模型可以根据需要调整优先级、控制项目风险、尽早并经常进行测试以实现按时交付满足客户需求的项目产品（Mark，2015），近年来得到了越来越多软件企业的关注和采用。由于敏捷方法将整个项目划分为小的迭代，敏捷开发有助于对正在开发的产品进行进度监控和障碍识别，促进了开发效率的提高（Breno 等，2017）。敏捷软件开发在未来较长一段时间内，仍然会是倍受关注的开发方法（罗昊，2019）。通过运用敏捷Scrum 框架，可以促进团队成员间的配合并促使成员积极参与到项目全过程，促进有限资源功能的发挥，提高软件开发效率（何晶，2021），并且能够通过从客户处获得定期反馈。当客户要求发生更改时，可以针对这些更改进行软件的改进升级，从而实现更加高效、更加快速地进行产品开发，在Scrum 的帮助下，软件产品的开发可以在相对较短的时间内完成（Valpadasu，2020）。

学者们深刻分析了 Scrum 模型在各行各业的应用，并总结了相关实践经验，如 Scrum 模型在嵌入式产品开发中的应用研究（方澜，2015），Scrum 敏捷方法在电子商务研发项目中的应用分析（张彭彪，2019）。同时，也尝试将 Scrum 模型与极限编程混合探索综合敏捷模型的使用，以便更好地开展项目进度管理（Thirugnanam Mythili 等，2022）。

# 2.6本章小结

关于项目进度管理、软件项目进度管理、敏捷 Scrum 敏捷模型优化项目进度管理等领域与模块，通过对相关文献的梳理和总结，发现国内外对软件项目进度管理的相关理论和方法的研究取得了较为丰富的成果。

文献对于 Scrum 敏捷模型的研究中，前人主要是通过践行 Scrum 敏捷模型，对于具体组织的具体实践存在的一定问题进行优化，以适应组织的需求，达到项目进度实施敏捷化管理的目的。通过对于历史文献的研究，可以得出如下几点结论：

（1）项目进度管理是确保项目成功的必要因素之一。在项目实施的早期，实施有效的项目进度管理可以帮助团队更加高效地完成任务，从而提高整体项目的效率。通过项目进度管理，能够及时发现问题并解决，降低项目的成本；清晰地展示项目进展情况，提高项目透明度；帮助团队更好地协作，确保项目顺利完成。

（2）在实施 Scrum 敏捷模型及流程时，不能生搬硬套，而应该根据具体的组织和项目需求进行适当的调整和定制，使得 Scrum 敏捷模型及流程能够更好地满足实际情况下的需求。因此，Scrum 实践的成功关键在于对 Scrum 敏捷模型及流程的灵活应用和适应性调整。

（3）Scrum 敏捷模型实施中需要“全营一杆枪”，每个成员都很重要。各个成员都需要积极参与、积极奉献、力出一孔，才能推动整个项目高效前行。

本文以A 公司H 软件项目为例，梳理H 项目进度管理存在的现实问题，并对问题进行分析。本文还将在历史文献及前人研究的基础上，聚焦研究 Scrum 在软件服务项目领域的应用。将 Scrum 敏捷模型结合A 公司实际情况、H软件服务项目的背景和问题，为H项目的进度管理提出改进措施，并验证 Scrum 敏捷模型在该项目进度管理中的有效性。同时，通过H项目 Scrum 敏捷流程实践过程中发现的问题进一步完善改进措施，持续优化，不断对 A 公司H项目的项目进度、组织制度、项目组成员管理等进行完善，保持组织活力，为H项目实现商业化成功奠定坚实基础，提升A公司的项目管理能力和管理水平。

# 第三章A 公司H软件项目概况及进度管理现状分析

# 3.1H项目概况

H服务数字化提升建设项目旨在满足新农村建设和发展的需求，为农民、管理人员和农技人员提供个性化、专业化和系统化的知识和信息，并提供方便的工具，以促进他们学习和掌握这些知识和信息。该项目的主要目标是通过科技帮助农民致富，同时支持乡村管理和决策，满足不同文化需求。

近年来，随着信息数字化建设水平的不断发展，H服务已在Ⅹ省覆盖多个行政村，在提升农民的科学文化素质、丰富农村文化生活、推动文明乡风建设等方面具有重要作用。X省计划开展新一轮H服务数字化提升建设项目，为农民客户提供更高效、更便捷的H 服务。经过招投标流程，最终由A公司承接X省H项目建设。

# 3.1.1项目背景

（1）H项目背景

H 项目通过现代数字化管理和服务手段，建设一个集成了社会化知识信息服务的系统平台，促进现代信息服务在Ⅹ省农村的融合发展。H 项目作为示范项目，致力于促进科技成果产业化，将科技成果转化为生产力，通过H 项目实现全民知识共享。H 服务数字化建设有助于进一步提高全民人文素质，通过新业态的创新和探索，打造成数字信息进村下乡的新模式。

H项目具有如下五点建设原则：

整体性：在省级层面上，项目采用统一的规划和管理模式，同时建设一个资源中心。在区县层面上，项目开放个性化建设权限；在村级层面上，实现多终端应用。

针对性：通过大数据画像技术，对服务对象进行更精准的分析，以便能够更好地满足客户需求。

扩展性：采用标准化的硬件和软件，各个子系统实施模块化设计，以便更好地满足未来的扩展需求。同时，平台可以与国内同行业平台对接，以提供更多的服务和业务资源。

移动化：支持客户通过手机（包括Android 和 iOS 系统）、微信等移动终端使用H服务，随时随地查看海量内容及使用信息服务。

智能化：注重应用云计算、互联网、大数据、知识图谱和人工智能等新技术。通过大数据和人工智能等高新技术手段，对客户人群画像、客户使用行为等进行统计分析，确保数据南京邮电大学硕士研究生学位论文有据可查。同时，还将通过智能检索、语音等技术，提高客户的使用效率。

（2）A公司业务背景

A公司是一家以提供信息服务、技术服务为主的科技公司，实行集团化经营，员工规模X千人，总部在Ρ市，各省区设立分公司负责属地化销售和服务，研发团队全部依托总部。长期以来，A 公司销售产品单一，主要为标准化产品，软件定制经验及 SaaS 服务交付经验较少。近年来，A公司业务模式不断调整和创新，逐渐转型并承接知识管理服务相关的软件服务项目。

# 3.1.2项目目标

H项目需要A 公司在3 个月内交付一套全新的知识管理SaaS 服务。该服务需要建立省、市、县、镇、村五级架构，通过省级资源中心和活动中心、市一县一镇个性化控制中心、村级多终端应用实现资源精准分发，形成有效、开放、多元的互动模式。通过频繁的良性互动，提高H服务的关注度和认可度，促进业务的长效口碑传播。

该项目重点目标为：推动乡村产业、文化、人才、组织和生态的振兴，并通过数据库海量数字内容服务和专题定制服务，推动乡村建设和规划管理的信息化建设和发展。客户初验后还需持续交付客户的运营定制化需求，结合实际应用场景，开发系列延伸服务，满足农民多元化需求，最大限度地提升H项目服务效能；同时，设计、组织多种形式、趣味性、高质量运营活动，提高Η 服务使用率。同时，需对客户的反馈对不足之处按优先级进行一一优化，直至1年期满项目终验闭环。

# 3.1.3项目运作情况

根据A 公司内部管理规定要求及实践惯例，A 公司为H项目成立了项目组。在项目经理的领导下，开展业务实施。项目组角色包括：领导组、项目经理、专家组、技术总监、产品负责人、开发组（包括架构设计组、开发实施组及系统测试组）、运营经理、项目管理办公室等，整体由项目经理领导和把控项目交付与实施。各业务组/团队间在工作互相协调，在管理上各自独立。项目结构图3-1如下：

![](images/6b8a4cfe722c191d054781ce4296924929cbfc9fd7d0e2f9e89b0c55dc3a042c.jpg)  
图 $3 { - } 1 \ \mathrm { H }$ 项目前期项目组构成

A公司在H项目整个过程中，明确了各个部门、团队及相关人员的分工，详述如下：领导组：领导组作为H项目赞助人，对项目的顶层设计方提供意见，对项目组提供案决策、支持和指导意见；负责客户高层关系维护，保持客户与H公司高层良好互动，确保客户高层对项目的高度支持；协调解决在项目组层次无法解决的问题，提供充分支持。

项目经理：项目经理是H项目的总负责人，他的职责贯穿于H项目的各全流程各阶段，包括项目准备、人员管理、沟通协调、决策与审批等具体工作中。在项目中，项目经理所发挥的领导作用可以影响项目整体的运作。

项目管理办公室：为项目经理和项目组提供支持，如项目各种报表的模板制作、各模块信息搜集与汇总、项目报告的准备与发送等；为项目组成员提供项目管理相关技能的培训，协助各团队快速磨合和融合；整个项目组内的知识信息，将各方的专业信息整理汇总、形成一套方法论，沉淀项目组经验，在公司内传播推广和复制，提升项目组影响力。

技术总监：技术总监是整体研发管理的技术指导者，负责把所有和开发相关的资源都协同管理起来，支持项目经理按时完成项目。同时，技术总监作为技术方面的权威，要对项目下一步的技术发展方向进行一些研究、探讨，做出判断并帮助项目经理做出决策。

专家组：专家组在H项目中发挥着专业指导与顾问的角色。各模块及领域专家，充分发挥其在所属领域的资深经验，对项目中的风险点进行识别、对困难点进行攻关，开展问题研究和撰写建议方案。专家组既是项目组的专业顾问团队，又是项目组的技术攻坚团队，为项目组、项目经理提供充足的信息和参考。

产品负责人：产品负责人在项目中，需要考虑目标客户特征、竞争产品、产品是否符合公司的业务模式等等诸多因素，将项目从抽象的需求转换为具体的产品。在项目中，产品负责人工作内容包括需求收集、需求分析、需求落地、数据跟踪、产品跟踪上线、对运营等业务人员进行培训等工作。

开发组：开发组负责整个项目的研发，开发组内设立了架构设计组、开发实施组及系统测试组等小组。架构设计组主要负责对产品的整体架构进行系统设计，搭建好整体开发框架，将产品需求拆解至各模块开发人员。开发实施组就是对产品进行具体开发实施，从产品设计转化成具体项目，从产品全功能、安全合规等维度开展项目实施，过程中追求极简代码、高效高质；系统测试组负责对开发实施组已完成或基本完成的项目进行测试，验证及测试项目是否能够按计划运行，并给予相关项目优化建议，输出测试报告，并将测试报告反馈给开发实施组进行处理及调整。开发部在进行Η项目开发的过程中，也需要考虑和平衡项目所需功能及预期开发成本，在有限的资源内高效完成项目。

服务经理：服务经理负责硬件在客户侧的进场，及软硬件业务的现场安装调试，安装完成后、对客户实施赋能培训，确保交付的软硬件可使用，并获得客户的验收报告。同时，与客户就使用感受、业务规划等进行现场交流，聆听客户现场声音，识别客户业务痛点，了解客户真实需求，挖掘未来的销售机会点。

运营经理：运营经理需要统筹负责运营工作，协同产品策略，分析挖掘客户场景，通过产品运营、活动运营、数据运营等措施达成业务指标。建立客户运营体系，通过数据监控，分场景分人群的精细化运营，不断优化产品体验，提升客户满意度。需要拥有敏锐的客户需求洞察能力，优秀的数据分析能力，熟悉客户增长模型，客户生命周期模型，思维活跃，有创新能力、组织策划能力，运营规则制定及业务流程管理能力。

# 3.2项目进度管理现状分析

# 3.2.1影响项目进度管理的因素分析

项目过程会受到多种因素的影响，如组织因素、人员因素、资金因素、环境因素等，任一因素控制不好，都会导致项目进度延误。因此，我们有必要对影响项目进度管理的因素进

行分析。

# （1）组织因素

历史研究中，主要的研究都说明组织因素是项目成功的关键因素之一，例如各类项目组织形式的有效性[51][52]、组织文化等。

对于软件服务行业，较多中大型公司均分布多地办公，以A 公司为例：A 公司总部在P市，各省区设立分公司负责属地化销售和服务，研发团队全部依托总部。在H项目中，属地化的项目经理、运营经理在省公司，而其他如产品负责人、开发组、专家组等角色都在P市总部。并且，软件服务项目的各个成员都是脑力工作者，业务输出量化难度较大，团队异地办公，容易导致团队成员的产出不可控。同时，项目中沟通严重依赖于通讯软件、电话等工具，团队内外沟通成本高，沟通效率也存在提升空间。

鉴于上述问题，项目经理需要能够调动项目组成员积极性，制定更明确、更细颗粒度的目标来牵引项目组成员完成业务目标；同时，建立合理的项目体系与项目组织结构，设立完善的项目进度管理制度，避免项目因组织因素影响项目进度。

# （2）人员因素

在项目进度管理中，人是重要资源，是需要重点考虑的[53]。学者 Terry Cooke Davies 明确提出，项目中最重要的是人[54]。

特别是在软件行业，当公司业务开展转型时，人员因素更是项目成功至关重要的影响因子。以A 公司为例，长期以来A 公司销售产品单一，主要为标准化产品，团队人员对于软件定制经验及 SaaS 服务交付经验较少。在A 公司在从卖标准化产品软件向卖 SaaS 化软件服务的转型过程中，因团队成员自身对软件服务项目没有足够熟悉，人员因素对项目的开展与实施有一定影响。

（3）资金因素

在软件项目进度管理中，资金因素非常重要，特别是项目风险管理预算尤其重要，因为项目的进度管理和风险管理中需要消耗相应资金[55]。

H项目作为A公司在X省的标杆项目，项目类型复杂，开发工作量大，技术复杂度高，而且是X省相关部门重点关注的项目，在A 公司内部得到了高度重视，充分给予了项目资金支持。虽然资金池较为充足，但项目组在实施过程中，项目组也需要做好项目的资金计划，严把关每一笔资金支出，严格评估和控制开发成本，做好成本控制，避免资金浪费。

# 3.2.2H项目进度管理现状

如前序章节所述，长期以来A 公司销售产品单一，主要为标准化软件及硬件，A公司在运作项目时通常采用瀑布模型。因此，在H 项目开展初期，H 项目也采用了瀑布模型，需求分析、设计、开发、测试等流程是分阶段进行的，一个阶段必须在前一个阶段结束后开始。A公司H项目早期实施的业务流程图，如图3-2所示。

![](images/939f2eeb958b3ac23b204930e46d28361928d845ae70d44d96a041c68fd24710.jpg)  
图 $3 { - } 2 \mathrm { H }$ 项目早期实施的瀑布式业务流程

从图3-2可以看出，该项目流程为瀑布模型。具体步骤如下：

项目需求分析：在完成招投标流程后，分公司商务侧（客户经理）将客户原始序曲传递给项目组，项目经理与产品负责人分析项目需求，基于商业需求和市场需求，输出量化的产品需求文档（Product Requirement Document，简称“PRD”），PRD 文档包括产品功能定义、产品交互、产品数据需求、规划功能预埋等。同时，完成项目商业计划（Business Plan，简称“BP”）测算，预估项目成本及整体营收情况。BP测算通过后，确定启动项目开发。

产品架构设计:基于产品负责人输出的 PRD,开发组的系统架构设计师（System Architect，简称“SA”），将产品需求进行进一步拆解分析，搭建起开发的框架，输出产品规格书（Product Specification，简称“PS”），产品规格书包括：软件高保真设计、云侧需求规格、端侧需求规格、数据点位及数据上报规则等。产品规格书将作为开发实施的基准文档。

开发：基于系统架构设计师给出的产品规格书，开发人员通过程序设计语言来进行软件系统或者系统中的软件部分的建造。开发人员将产品规格进一步分解成一个个能实现某个功能的数据和程序说明、可执行程序的程序单元，然后实施软件编码，把软件设计转换成计算机可以接受的软件程序。编码完成后，开发人员实施代码的简单自测，自测完成后转入测环节。

测试：基于项目需求，测试人员制作测试方案及编写测试用例，测试方案包括性能测试、功能测试、安全测试、子模块测试、系统联调测试等多种子方案。基于开发人员开发的代码，测试人员进行业务测试，以较小的代价发现尽可能多的错误。常用的测试方法包括白盒法及黑盒法，白盒法测试对象是源程序，根据程序内部的逻辑结构来发现软件的编程错误、结构错误和数据错误，黑盒法主要是检测模块输入接口和输出结果是否满足测试用例要求。

版本发布：产品开发及测试完成后，在确保功能手册、配置说明、升级文档、安装文档、API文档等文档输出后，项目经理组织项目组评审会，评审通过后实施版本发布。

项目交付：拿到版本软件后，服务人员在客户现场实施交付，项目交付包括硬件进场、硬件上电、系统软件安装、业务软件安装、软硬件联调、接入客户网络等步骤。交付完成后，对客户实施赋能培训，确保项目交付完成，客户真正掌握业务。

业务上线运营：项目交付完成、接入现网后，运营经理与客户沟通运营素材，开始客户界面的各项配置。同时，基于客户需求及软件功能，策划活动运营计划，包括日常运营活动、重大节假日运营活动、重要事件保障等。

在本流程中，每步步骤严格依赖于上一步结果，整体看似稳步推进，实则风险极大，一旦有某一步骤发生变化，全部过程即将“推倒重来”。

H项目敏捷化前的项目组制度如下：

（1）项目组制度

在A公司中标H项目、签署完商业合同、明确项目启动后，A 公司就H项目第一时间成立项目组，由项目经理整体牵头负责项目。虽有项目组化运作，但各业务组、团队间在工作间主要是互相协调，而在管理上却各自独立。项目经理对项目组成员没有考评决定权，各项目组成员仍然向各自业务主管实线汇报，对于项目经理仅虚线汇报。

# （2）周会与周报制度

项目经理按周召开H项目的项目组电话会议，项目组核心成员全部参加。周会中评审各模块的进度，通报进度风险，并就项目中遇到的重大问题进行讨论。会议结束后，项目管理办公室发出会议纪要，主送与会人员，抄送全部项目组成员。

（3）业务流程

如前序章节所介绍，H项目早期实施了瀑布式业务流程，按照需求分析、产品架构设计、开发、测试、版本发布、项目交付、业务上线运营的步骤，逐条往前推进。如果任一环节出现问题，都会导致后续环节滞后；且项目初期，客户需求变更较为频繁，由于是瀑布式业务流程，每一次变更带来的影响都极大，因此，每一次变更申请都需要进行决策，一定工作量内可以项目经理决策、较大工作量则需上升到公司高层决策，决策后再确定是否执行。

（4）管理工具

与传统标准化软件产品开发过程类似，H项目早期过程中也主要通过WBS（WorkBreakdown Structure，工作分解结构）、甘特图（Gantt Chart）等常用进度管理工具开展项目进度的辅助管理。

使用WBS 可以有助于有效地规划和管理项目的各个阶段和任务。H 项目组在确定项目的整体目标和范围后，创建了WBS 的层次结构，包括需求分析、产品架构设计、开发、测试、版本发布、项目交付、业务上线运营等，并对每个阶段进一步细分为具体的任务和工作包。同时，对于每个阶段确定了预估时间计划，以监控各阶段的完成情况。

同时，通过甘特图将项目的计划、进度和资源分配情况实现可视化。甘特图可以将项目的任务与时间线对应起来，以条形表示并根据预计的开始和结束日期排列，通过不同的颜色或标记区分不同阶段或任务，通过连接线或箭头表示任务之间的依赖关系。项目组可以在甘特图上标记实际进度，以便监控项目进度。

上述就是A 公司H 项目在实施敏捷化前的实际状况。其采取的瀑布式业务流程是一种有效的管理方式，在数十年间一直被 A 公司广泛使用，对固定版本周期的软件开发更是有着显著的优势。但随着软件行业的发展及 A 公司业务转型，瀑布式业务流程的局限性也逐渐体现出来。该业务流程对客户需求确定性具有极高的要求，在初始阶段，项目组必须对需求进行极其精准的把控，完全确定客户的需求，否则、失败即意味着返工。而实际在H 项目情况中，要客户在需求分析阶段完全、精确、量化地给出需求是十分困难，甚至是不切实际的。客户、项目经理、产品负责人和开发人员关心需求的角度也是完全不同的。客户关心产品的价格、功能及性能，项目经理还需考虑项目可行性、风险、成本、进度等，开发人员则只关心PRD文档分析的结果，这种模式下，常会出现客户需求和实际版本结果不符合的情况。而软件服务是一件非常复杂的逻辑产品，客户很难清楚需要的软件服务到底要做什么，所以从客户侧更希望得到一个可修改的、可逐步完善的系统，直至该系统让客户完全满意。这便导致了需求不断发生变化，而由于客户需求不能最终确定，导致开发工作永远难以进行。这就是瀑布式业务流程面临的最大挑战——需求不确定性。在该流程中，每个步骤间都具有依赖性，且须按照顺序执行，而在需求变动频繁H项目中，一旦发现某一个阶段有错误，极可能要追溯到早期的阶段，回溯成本高昂、版本效率低下。长此以往，产品开发产生恶性循环，极大地影响了项目推进。

# 3.2.3H项目进度管理中存在的问题

在H项目运作一段时间后，A 公司及H 项目组也陆续识别到H 项目早期在项目运作和项目进度管理上都存在一定问题，导致项目进度偏差大。通过WBS、甘特图等工具监测识别出，H 项目如期交付有很大风险。鉴于此，A 公司高层专门安排高层代表、质量与流程 IT人员与项目组核心成员一起，群策群力，就H项目运作效果开展专项研讨，梳理与汇总当前项目运作中的已知问题并开展分析，就“为什么Η项目进度偏差较大？”这一关键问题展开深入探讨，学习项目进度管理理论知识与业界案例，为H项目探索更优的项目进度管理机制。

通过回顾及研讨，识别了H项目早期在项目运作和项目进度管理中存在的主要问题，汇总如下：

（1）项目组沟通效率低

除异地办公、组织架构带来的效率影响外，瀑布式开发流程也对项目组沟通效率产生了负面影响。项目实施过程中，各个阶段严格地按照顺序进行，各阶段人员各司其职，不承担、不了解其他环节的业务。例如，当产品负责人撰写 PRD 文档时，通常没有开发、测试和运营人员的参与。如果在产品设计阶段没有充分考虑到开发、运营等方面的可行性和问题，这些潜在的隐患将不会被及时发现。这些问题可能会在后续过程中被暴露出来，需要重新回到起点进行定位和修改，这将对项目进度和成本产生不利的影响。同样，开发过程中，测试人员也是不会参与其中，而是等开发正式发布版本转测后、测试人员才开始测试，直到测试测出问题后，测试才会与开发沟通。因此，该模式下，整个项目组缺乏过程沟通，都是等到相应环节了或触发问题后才开始沟通，导致各阶段人员彼此之间对需求或文档的理解产生偏差，纠错成本高。

（2）开发流程长

根据 A 公司原先的项目管理模式，项目组在跨部门协作和沟通方面花费了大量时间。当产品需求需要紧急修改时，由于项目管理流程的限制，修改时间被严重延误，导致无法及时响应客户的反馈，进而影响了客户的体验和满意度。

H 项目最初采用传统的瀑布式开发流程，每个阶段都有指定的文档和审核要求，文档是各个阶段工作人员之间沟通的主要工具，也是软件规格和功能维护的重要依据。但是，这种开发模式在实际实施中过于理想化，无法避免出现错误或变更的情况，这导致必须在上一个阶段发现问题后，沿着流程逐步往上追溯错误源头，然后重新进入审核和修改流程，这样会浪费大量的时间和资源。

而且，项目组各成员间的沟通，邮件和 IM 工具沟通频率远大于当面交流，会议讨论的机会更是稀少。这样对沟通效率产生了极大的影响。

# （3）需求难变更

在H项目早期的项目管理流程中，采用了有可预见属性的项目管理流程。整个项目按照固定的流程顺序进行推进，每个阶段都需要编写文档以确认该阶段的质量符合标准。在需求确定且清晰的情况下，这样的传统模型非常合适。该模式下，项目过程划分严格、开发自由度较低、需求变化调整困难。

如果在项目的任何一个阶段出现需求变更，团队很难快速做出反应并将新需求整合到整个项目的流程中，新的需求无法直接被接纳，而是需要产品负责人对变更的需求进行整理，并向项目经理提交版本申请、经各层审批人审核同意后，项目组才能进行需求的变更。同时，需要把之前的全部流程，包括但不限于需求分析及评审等全部再走一遍。因为各个阶段之间的紧密依赖，任何需求变更都需要尽早暴露出来，这样所产生的代价和阻力就会更小。如果在项目开发过半或者接近尾声时才提出变更，那么变更的代价会非常高，很可能会对整个项目造成无法承受的影响。

因此，对于H项目而言，传统的研发模型带来了很大的限制，使得团队无法灵活地应对需求变更和开发中的挑战。这种模型难以满足项目早期客户需求的复杂和多变特性，导致团队内部的压力增加、士气下降，并减少了与客户的交流和互动。

# （4）重文档输出

在瀑布模型流程下，每个阶段都有规定的文档，文档是各阶段工作人员之间沟通的重要依据——产品负责人及项目经理需要先完成产品文档，评审通过后转交架构师输出产品规格，研发再根据详尽的文档再进行执行开发，测试输出详细的测试用例实施验证测试。在这个过程中，有大量的项目管理文档工作需要项目组成员承担。在该项目管理模式下，若出现需求变更或其他问题，团队需要先修改相关文档并进行评审后，才能继续进行后续工作。

在A公司的项目管理流程中，每个阶段的每个角色都需要按照标准模板和详细要求输出相应的文档。由此，在H项目中，团队成员需要花费大量时间来完成文档输出工作，极大地影响了项目进度和效率，延长了项目周期、增大了项目成本。各阶段各角色相关文档所需的工作量如表3-1所示。

表3-1H项目采用瀑布式业务流程时的文档输出所需耗时  

<table><tr><td>角色</td><td>输出材料</td><td>耗时</td></tr><tr><td>产品负责人</td><td>产品需求文档（PRD）</td><td>16H</td></tr><tr><td>系统架构设计</td><td>产品规格书（PS）</td><td>32H</td></tr><tr><td>开发人员</td><td>功能手册、配置说明、升级文档、安装文档、API文档等</td><td>32H</td></tr><tr><td>测试人员</td><td>测试方案、测试用例</td><td>24H</td></tr><tr><td>服务经理</td><td>客户培训资料</td><td>16H</td></tr><tr><td>运营经理</td><td>业务上线素材</td><td>12H</td></tr><tr><td colspan="2">合计</td><td>132H</td></tr></table>

由此可见，在H项目中，每个阶段的工作人员都需要花费大量时间来编写规范的文档，因为这些文档在瀑布式开发模式下是至关重要的。这些文档有着严格的模板和要求，是各个阶段之间交流和沟通的主要工具，而且还是软件功能和规格维护的重要依据。通过文档写作，不仅帮助业务人员梳理了思路，也实现了经验的沉淀和积累，但确实增加了每个阶段每个工作人员的工作任务，为项目效率带来了一定的影响。

# 3.2.4H项目进度管理问题根因分析

鱼骨图分析法是一种广泛使用的问题分析和解决方法，通过该方法，人们可以识别问题的根本原因，而不仅仅是解决问题的表面症状。该方法被称为“鱼骨图”，因为它的形状类似于鱼骨头。在鱼骨图中，主要问题通常放在鱼头上，而问题的影响则放在鱼尾处。图中的“骨头”代表各种潜在的根本原因，这些原因通常归为人员、方法、材料、机器、测量和环境六个类别。通过使用鱼骨图分析法，可以更深入地理解问题，找到问题的本质原因，并进一步探讨更具针对性的解决方案，从而减少问题再次发生的可能性。

鱼骨图分析法的通常步骤如下：

（1）确定问题：明确要解决的问题，并将问题写在鱼头上，用于后续步骤中引导讨论和分析。（2） 确定影响：确定问题的影响，写在鱼尾上，用于理解问题的严重性和紧迫性。（3） 确定骨头：确定问题的可能原因，并将它们写在鱼骨的分支上。（4） 归类骨头：将所有可能的原因分配到相应的分支上。（5）确定关系：确定每个原因与问题之间的关系，并绘制出鱼骨图的完整结构。

（6）分析原因：分析每个原因是否真的是导致问题的根本原因，找到最可能的根因，并制定相应的解决方案，减少问题再次发生的可能性。

5WHY分析法是一种常用的问题分析和解决方法，它可以通过连续反复的“为什么”提问，深入分析问题，并找到问题的根本原因。问题的根本原因往往是隐藏在表面症状之下，需要不断地追根究底才能找到。这个方法需要进行多次反复分析，每次的分析结果都是下一次提问的基础，直到找到最根本的问题所在。通过 5WHY 分析法，可以快速发现问题的本质，为解决问题提供更有针对性的方法。5WHY 分析法所使用的 5WHY 根因分析表如表 3-2。

表3-2 5WHY 根因分析表  

<table><tr><td>5WHY 分析法</td><td>问题</td><td>原因</td></tr><tr><td>WHY1</td><td></td><td></td></tr><tr><td>WHY2</td><td></td><td></td></tr><tr><td>WHY3</td><td></td><td></td></tr><tr><td>WHY4</td><td></td><td></td></tr><tr><td>WHY5</td><td></td><td></td></tr><tr><td>最终根因</td><td></td><td></td></tr></table>

将鱼骨图分析法和 5WHY结合使用可以更有效地解决问题。鱼骨图分析法可用于确定潜在的问题原因并将其分类，而 5WHY 可以深入分析和确认这些原因是否真的是导致问题的根本原因。通过将鱼骨图分析法和 5WHY 结合，可以逐层深入地了解问题，更准确地找到问题的本质，确定问题的根因。

将H 项目前期开展的运作效果开展专项研讨结果，以鱼骨图方式归类如图 3-3 所示。

![](images/c99f05f2caa7a571a636ed89c92ccfc221dceadf2c0ffa1d99d8cf632da18b78.jpg)  
图 $3 { - } 3 \mathrm { ~ H ~ }$ 项目早期运作中影响项目进度管理的因素分析鱼骨图

从鱼骨图中可以发现，H项目前期运作管理中，在流程、协作、人员、管理等方面存在问题。“协作”直接影响了各环节的处理效率；“流程”定义了项目进度管理的具体步骤、顺序和时间节点，是保障项目进度管理的执行效率与成功率至关重要的因素；项目的实施离不开人的参与和劳动，“人员”的因素是项目成功实施的基础；能否对于项目各方（包括但不限于项目组内外人员）实施有效管理，包括客户预期管理、职能划分管理等，都会影响项目的成败。

对于“协作”“流程”“人员”“管理”等核心原因，通过5WHY分析法展开分析，快速识别问题本质，确认这些原因是否真的是导致问题的根本原因，如表3-3 至表3-6。

表3-3“为什么说流程’出问题了？”5WHY分析表  

<table><tr><td>5WHY 分析法</td><td>问题</td><td>回答</td></tr><tr><td>WHY1</td><td>为什么说“流程” 出问题了？</td><td>项目组使用了历史项目常用的开发流程进行推进，但目 前进度很慢。当前，需求分析文档已完成，刚启动产品 设计，开发、测试等还没启动。但时间又太紧张了，只 有3个月，交付风险很大。</td></tr><tr><td>WHY2</td><td>为么到现在计， 还不开发？</td><td>客户需求不够清晰，需通过客户经理与客户澄清，效率 低；客户需求庞大，需求分析文档需详细拆解与撰写， 耗时较多。 产品设计需要给出整体设计方案，多个产品方案细节尚 在澄清中，产品规格书未能定稿，需要等整个产品设计 结束、研发拿到产品规格书后，开发人员才能启动开发。 客户也会不时调整需求，收到调整需求后，需要先刷新 需求分析材料后，才能重新启动产品设计。</td></tr><tr><td>WHY3</td><td>为什么要等设计 结束才能开发?</td><td>开发人员需要拿到产品规格书，且系统架构设计师召集 相关开发人员开展需求串讲后，才能启动开发。当前设 计尚未结束，无法启动开发。而且客户刚提需求变更， 导致产品设计工作暂停，等需求文档刷新后才能重新启 动。</td></tr><tr><td>WHY4</td><td>为什么不一下子 把客户需求都确 定清楚？</td><td>客户也在持续地学习和分析其他同类产品，并在基层开 展调研，如果有新想法就会传递过来。客户也在抱怨开 发慢、为什么一直看不到进展。</td></tr><tr><td>WHY5</td><td>为什么没有做好 进度的监控，让客 户看到进展？</td><td>项目组各成员均为第一次实施此类项目，且客户需求变 更频率高，项目进度表无法拆解，无法做进度监控，因 此，客户侧无法看到进展。</td></tr><tr><td>最终根因</td><td></td><td>交付周期短，客户需求多变，下游工作启动严重依赖于上游输出。</td></tr></table>

表3-4“为什么说协作’出问题了？”5WHY分析表  

<table><tr><td>5WHY</td><td>问题</td><td>回答</td></tr></table>

<table><tr><td>分析法</td><td></td><td></td></tr><tr><td>WHY1</td><td>为什么“协作”出 问题了？</td><td>项目组各岗位人员各司其职，团结性较差。</td></tr><tr><td>WHY2</td><td>为什么组内成员 团结性较差?</td><td>项目组是松散化运作，成员来自各部门，如测试部、开 发部等等，属于临时性项目合作。加上异地办公效率低， 导致组内沟通不便。</td></tr><tr><td>WHY3</td><td>为什么会松散化 运作？</td><td>系统规格设计未完成，开发人员无法启动开发工作。开 发人员认为系统规格设计和他没有关系，需要等拿到系 统规格设计书才能启动开发。因此，在系统规格设计阶 段，开发人员完全不参与项目组运作，协作很松散。</td></tr><tr><td>WHY4</td><td>为什么系统规格 设计不完成，开发 人员无法干活 儿？</td><td>开发需要拿到产品规格书，且系统架构设计师召集相关 开发人员开展需求串讲后，才能启动开发。当前设计尚 未结束，无法启动开发。而且客户刚提需求变更，导致 产品设计工作暂停，等需求文档刷新后才能重新启动。</td></tr><tr><td>WHY5</td><td>为什么不一下子 把客户需求都确 定清楚？</td><td>客户也在持续地学习和分析其他同类产品，并在基层开 展调研，如果有新想法就会传递过来。客户也在抱怨开 发慢、为什么一直看不到进展。</td></tr><tr><td>最终根因</td><td colspan="2">由于下游工作启动严重依赖于上游输出，只有拿到上游的输出件后，下游 人员才启动工作。因此，根因很可能是“流程”问题。</td></tr></table>

表3-5“为什么说管理’出问题了？”5WHY分析表  

<table><tr><td>5WHY 分析法</td><td>问题</td><td>回答</td></tr><tr><td>WHY1</td><td>为什么说“管理” 出问题了？</td><td>项目组只召开了“开工会”，项目例会也为事件性触 发、不定期召开。当前系统规格设计未完成，后续流 程相关人员无法启动下一步工作。</td></tr><tr><td>WHY2</td><td>为什么不定期开 项目例会？</td><td>项目例会基于事件触发，且取决于项目经理本人行程 安排。当前系统规格设计还没完成，项目经理判断此 时无需各成员配合，因此未召开例会。</td></tr><tr><td>WHY3</td><td>项目进展是否有 问题？</td><td>进展过程没问题，但由于单线程运作，项目进展速度 较慢。</td></tr><tr><td>WHY4</td><td>为什么单线程运 作？</td><td>根据当前开发流程，开发需要拿到产品规格书，且系 统架构设计师召集相关开发人员开展需求串讲后，才 能启动开发。当前设计尚未结束，无法启动开发。而 且客户刚变了需求，导致产品设计工作暂停，等需求 文档刷新后才能重新启动。</td></tr><tr><td>WHY5</td><td>为什么不一下子 把客户需求都确 定清楚？</td><td>客户也在持续地学习和分析其他同类产品，并在基层 开展调研，如果有新想法就会传递过来。客户也在抱 怨开发慢、为什么一直看不到进展。</td></tr><tr><td>最终根因</td><td colspan="2">只是表象原因，因是下游工作启动严重依赖于上游输出。因此，</td></tr></table>

表3-6“为什么说人员’出问题了？”5WHY分析表  

<table><tr><td></td><td>问题</td><td>回答</td></tr><tr><td>WHY1</td><td></td><td>为么说“人员”员交付专业素质跟不上。项目工期又紧，员工项</td></tr><tr><td>WHY2</td><td>为什么人员素质 跟不上？</td><td>员工没交付过软件服务项目，按照传统开发策略实施 开发后，当前项目开发中遇到了瓶颈，对项目组员工 也带来了较大的心理压力。</td></tr><tr><td>WHY3</td><td>为什么会遇到瓶 颈？</td><td>项目属于单线程运作，例如：开发人员需要拿到产品 规格书，且系统架构设计师召集相关开发人员开展需 求串讲后，才能启动开发工作。当前设计尚未结束， 无法启动开发。而且客户刚变了需求，导致产品设计 工作暂停，等需求文档刷新后才能重新启动。因此， 就卡在这里了。</td></tr><tr><td>WHY4</td><td>为什么不一下子 把客户需求都确 定清楚？</td><td>客户也在持续地学习和分析其他同类产品，并在基层 开展调研，如果有新想法就会传递过来。客户也在抱 怨开发慢、为什么一直看不到进展。</td></tr><tr><td>WHY5</td><td>为什么没有做好 进度的监控，让客 户看到进展?</td><td>项目组各成员均为第一次实施此类项目，且客户需求 变更频率高，项目进度表无法拆解，无法做进度监控， 因此，客户侧无法看到进展。</td></tr><tr><td>最终根因</td><td colspan="2">人员素质和能力是可以培养的，导致项目出现瓶颈的核心原因并非人员 原因。项目的单线程运作，导致人员能力得不到充分发挥，缺乏一个完 善的流程来指导员工高效交付软件服务项目。根因很可能是“流程”问 题。</td></tr></table>

对于“协作”“人员”“管理”三个原因，通过5WHY分析法展开分析后，最终都指向“流程”原因。项目组前期流程采用了瀑布模型，在瀑布模型中，需求分析、设计、开发、测试、编写文档等是分阶段进行的，一个阶段必须在前一个阶段结束后开始。因此出现了“系统架构设计师未能完成系统设计，开发人员就无法启动开发”的问题，进而由于项目进度慢、遇到瓶颈，也对项目组员工也带来了较大的心理压力，员工也会觉得各成员间协作不佳。作为软件服务项目，H项目也面临着“客户需求多变”这一问题，很难实现“锁定需求、并将软件产品一次性交付”，瀑布模型的业务流程在此场景下的适配性存在一些不足。因此，引发了如“项目例会为事件性触发、不定期召开”等管理问题。

因此可以确定，H项目进度偏差较大的根本原因为“流程”，需要为H项目选择更合适的项目进度管理模型，优化当前瀑布模型的流程问题。

# 第四章 项目进度管理优化方案

# 4.1项目进度管理模型优劣势分析

A 公司为了提高Η软件服务项目的开发效率和质量，更好地满足客户实际需求，提升项目的商业价值，决定对Η项目的进度管理模式进行改进。

基于早期瀑布模型的实践，客户早期需求多变等特性，及项目工期紧等外部要求，项目组确定了本项目进度管理中所需关注的 TOP3 问题关注点：开发周期短，客户需求多变，减少僵化文档输出、降本增效。同时，项目组希望在过程中邀请客户多介入、早介入，对项目对客户的透明度，进而提升与客户的互动，也让客户少提出不切实际的需求。

基于上述需求，项目组根据战略目标，选取了若干潜在可能适合H项目的开发模型作为备选项，分析在各开发模型下的不同情况，希望找到最适合项目的开发模型。

表4-1各典型开发模型对比  

<table><tr><td>模型</td><td>开发 周期</td><td>对 的便捷度</td><td>工作</td><td>对客 明度</td><td>优点</td><td>缺点</td></tr><tr><td>深布有</td><td>较长</td><td>不便捷</td><td>较多</td><td>较低</td><td>各阶段便捷清晰， 输出文档的，大 项目。</td><td>变更困难、调整困难， 可能会不断积累问 骤增。</td></tr><tr><td>快速 原型中等 模型</td><td></td><td>较不便捷</td><td>较少</td><td>较高</td><td>可快速产出原型模 型，让客户确认是 满求，至持 真正满足客戶需求 的产品。</td><td>要花费很长时间才能制 作出能真正满客 终也无法制作成功。</td></tr><tr><td>极限</td><td>较短</td><td>便捷</td><td>较少</td><td>较高</td><td>XP构建一个完整、 重量级、严谨的开 发流程框架，对开 发中多种细节都做 了规定，使适大 版本迭代时间固 定，执行Weekly Cycle和 Quarterly Cycle。</td><td>XP原则上没有规定团 队角色，对于初学项目 个开放的空间中工作， 更适合集中攻坚项目。</td></tr></table>

<table><tr><td>Scru 扭捷敏 型</td><td>较短</td><td>便捷</td><td>较少</td><td>较高</td><td>能够更好地应对客 户需求变化，匹配 早期项目需求多变 的场是，客前 明确关键角色、适 合小团队、异地办 公场景。</td><td>对项目组成员职业素养 与工作技能要求较高， 要求各成员都具备主观 能动性。</td></tr></table>

将表 4-1 中的多个潜在模型，结合A 公司H 项目实际需求进行匹配，分析结果如表 4-2。

表4-2各典型开发模型对H项目的适配性分析  

<table><tr><td rowspan="2">模型</td><td colspan="3">对H项目的核心诉求满足情况</td><td colspan="3">对H项目特殊情况的适配性分析</td></tr><tr><td>发短</td><td>多</td><td></td><td></td><td>团队多地的适配</td><td></td></tr><tr><td>快速</td><td>×</td><td>×</td><td>×</td><td>×</td><td colspan="2">核心诉求已不满足，无需进行适配性分析</td></tr><tr><td>原型 模型</td><td>×</td><td>×</td><td>√</td><td>√</td><td colspan="2">核心诉求已不满足，无需进行适配性分析 极限编程（XP）注重团 队的技术实践和开发方</td></tr><tr><td>极限 编程 （XP )</td><td>√</td><td>√</td><td>√</td><td>√</td><td>高度的协作和沟通， 通常要求团队成员在 同一物理空间内工 作，以便快速解决问 题、提高效率和质量， 在异地办公情况下， 可能会出现团队成员 之间沟通和协作困难 的情况。此外，XP强 调团队成员之间的紧 密合作和面对面交 流，而异地办公可能 会影响这种交流，从 而影响团队效率。</td><td>和持续集成，这些实践 需要团队成员具备高水 平的技术技能和自我管 理能力，以保证实践的 质量和有效性。与其他 敏捷方法相比，XP更加 重视实践的执行和质量 的保障，这对团队成员 的技术水平和实践经验 有较高要对 法的团队来说，可能需 要一定时间逐步熟悉和 掌握这些实践，并需要 更多的技术支持和培训 资源，以便能够顺利应 用XP模型。</td></tr></table>

<table><tr><td>Scru m敏 捷模 型</td><td>√</td><td>√</td><td>√</td><td>√</td><td>Scrum 通过一些明确 的角色和会议，例如 每日站会等，以促进 团队之间的沟通和协 作。当团队成员无法 面对面交流时，团队 协作和沟通的难度会</td><td>项目管理，强调团队的 自治和自我组织能力、 持续集成和反馈、迭代 式开发和快速适应变 化，适合跨部门团队协 作。通过简单易懂的框 架来协调工作、管理进</td></tr></table>

对比之下，我们可以发现，Scrum 敏捷开发模型在各点上都能较好的满足A公司H项目需求，较其他潜在模型有明显的优势。

经过综合评估分析后，A 公司H 项目组发现采用 Scrum 敏捷开发模型具有多项有利点。首先，采用该模型可以缩短开发周期，将业务拆解成多个迭代版本，从而实现快速项目交付；其次，Scrum 模型所需文档较少，可减少文档输出工作；第三，Scrum 模型提供了高度透明度，使客户能够积极参与项目开发过程；第四，Scrum 模型能够灵活应对H 项目需求的变化；第五，Scrum 模型明确了关键角色，使项目组能够快速、清晰、可靠地做出决策；第六，清晰的流程和关键过程使得 Scrum 模型易于执行，尤其适合敏捷初学团队使用；最后，Scrum模型中高效的沟通方式，更适用于中小规模团队和异地办公场景。鉴于这些优势，A 公司决定选择 Scrum 敏捷模型作为H 项目后续的项目进度管理优化方案，并进行可行性分析。

# 4.2 Scrum 敏捷模型应用于 H 项目进度管理的可行性分析

H软件服务项目面临着项目组成员为传统软件开发团队转型、成员异地办公、项目周期短、客户需求多变等问题，在此背景下，对于 Scrum 敏捷模型应用于H项目进度管理的可行性分析如下：

（1）人员能力储备：H项目组成员为传统软件开发团队，拥有软件开发项目经验丰富的项目经理、开发人员、测试人员的专业技术人员储备，但欠缺敏捷开发经验。而 Scrum 模型的基本原则简单易懂，容易被团队成员理解和采用，易于上手和理解。传统开发团队可以快速开始使用 Scrum，而不需要长时间的学习和适应期。而且 Scrum 模型明确了具体的角色和职责，包括 Scrum 主管、产品经理和开发团队等，各团队成员都可以清楚地知道自己的职责和责任，避免产生混淆和不必要的工作冲突。因此，Scrum 敏捷模型适用于H项目组成员使用。

（2）异地办公问题：H项目组成员分散各地，项目经理、运营经理在省公司，而其他如产品负责人、开发组、专家组等角色都在P 市总部。Scrum 模型对于成员分散多地、异地办公的Η 项目组同样适用。团队成员可以通过远程会议和协作工具进行有效的沟通和协作。Scrum 方法中的 Sprint 计划会议、每日站会、Sprint 评审会议、Sprint 回顾会议等会议是团队成员分享进展和协调工作的机制，可以有效地协调异地办公的团队成员，降低异地办公问题对于项目进度的影响。同时，这些会议机制也可以让分散各地的团队成员更好地理解项目的目标和需求，并及时纠正和调整工作方向。

（3）项目周期短：在短时间内快速交付成果，是Scrum 敏捷模型的核心优势之一。通过短期迭代的方式，Scrum 模型可以帮助团队快速交付可用的产品。并通过需求优先级排序、持续评审与回顾等机制来确保工作始终保持在正确的轨道上，避免出现问题发现晚、后期需进行软件重构等情况。

（4）客户需求多变：在软件项目中，客户的需求变更是不可避免的。Scrum 模型可以帮助团队有效地处理和管理变更。在每个迭代结束时，项目组都会进行回顾和总结，以及如何调整项目计划来适应变更。在 Scrum 模型中，变更管理是一个持续的过程，而不是一个单独的任务。这种方法可以让团队及时适应变更，而不是等到问题变得无法控制时再进行调整。

（5）管理层重视：H项目是由X省相关部门关注的重点项目，具有社会影响力大、上线工期紧、技术方案复杂、产品受众面广、质量要求高等特点，项目伊始即得到了A 公司管理层的高度重视。A公司管理层也迫切希望管理好项目交付进度，打造一个软件服务项目的样板点。因此，A 公司高层同意对于H项目组建 Scrum 敏捷开发团队，实施基于 Scrum 模型的软件项目进度管理，并给予H项目组充分的资源支持。

# 4.3基于 Scrum 敏捷模型项目进度管理优化方案设计

# 4.3.1项目组织优化方案设计

项目组织结构的优化是建立 Scrum 敏捷模型的第一要务，同时也是敏捷项目管理的基础，因此，H 项目组织的调整刻不容缓。在确定使用 Scrum 敏捷模型作为H 项目后续的管理方案后，A 公司迅速调整业务队形，优化项目组织，力争尽快形成战斗力强的 Scrum 敏捷项目作战团队。

Scrum 组织关键人员包括三个角色。Scrum 主管，作为敏捷开发的主导实施者，确保整个项目开发过程按规范执行，符合 Scrum 流程要求，为团队争取必要的资源，为团队营造良好的工作环境。产品负责人，需要从客户和利益相关方的视角和立场去规划产品，撰写产品需求，维护产品需求列表，保障项目组产出的成果具有最高价值。开发团队，是项目组中实际的开发者，承接并完成开发任务，将客户需求通过程序语言转化为可正常运行的软件产品。

H项目中，项目组团转换为 Scrum 团队不能简单草率的一刀切、僵化执行，应当符合业务实际需求进行相应微调，以便更好的支撑项目的敏捷交付。在敏捷项目早期，为确保 Scrum敏捷转化的平滑过渡，避免出现模型切换时候的权责真空期、进而影响业务，由 Scrum 主管兼任项目经理职责，发挥原先项目经理的作用。待到Scrum 敏捷实施完成后，再逐步剥离 Scrum主管的项目经理职责。在早期，保留项目管理办公室，支撑 Scrum 主管行使项目经理职责。开发团队整合原先的各模块架构设计、代码开发、系统测试人员，形成开发全功能团队，履行 Scrum 流程所要求的职责，快速敏捷迭代开发。产品团队由过去的产品负责人、技术总监、专家组、运营经理构成。团队以产品负责人为主，产品负责人负责敲定整个产品规划；技术总监、专家组作为支撑力量协助产品负责人做出决策，运营经理也在产品早期设计阶段深度介入，发表运营评审意见，熟悉产品能力和规划、做好后续的运营策划。

综上，H项目实现 Scrum 敏捷化后，与以往A 公司传统项目分为产品、研发、测试等多个模块不同，H 项目设立起一个20 余个人的团队，通过小步快跑方式快速迭代上线产品。其中，Scrum主管1人，产品负责人1人，开发全功能团队（含架构、开发、测试等）22人。

项目组正式建立起独立团队，除人力资源及部分行政工作以外，项目组完全独立，项目组内全角色闭环，以便更好地实施 Scrum 敏捷开发流程。其他非强耦合人员，如专家组、技术总监、运营经理等剥离团队，作为支撑力量支撑产品负责人，产品负责人对相关支撑人员的考虑有建否权。项目组织优化如图4-1，执行Scrum 业务流程如图4-2。

![](images/1433cde9ce83d972d97177817131babf820b77ff8feba0d291eb7c53e008815b.jpg)  
图4-1优化后的H项目组构成图

![](images/fc14a0a1f290eb906dbad27a3ed27d4d404b006bd1ae11444406a76f3b585d08.jpg)  
图 $4 { - } 2 \mathrm { H }$ 项目执行的 Scrum 流程图

# 4.3.2 需求管理优化方案设计

原先H项目执行中执行的瀑布式业务模型，按照需求分析、产品架构设计、开发、测试、版本发布、项目交付、业务上线运营的步骤，逐条往前推进，只有在前一阶段完成后，才能开始下一阶段任务。如果任一阶段出现问题，都会导致后续阶段滞后。

需求澄清阶段，依赖于客户经理与客户的深度交流，获得客户的意见后，产品负责人便基于客户意见输出 PRD 文档，开始需求设计。整个过程客户参与度低，且一下子做整个功能，一旦任一点客户不满意，或客户中间穿插需求、变更需求，每一次变更带来的影响都极大。

因此，计划对H项目的需求管理方案进行优化，以更好地满足项目需求。根据前序章节描述的 Scrum 敏捷模型方法，建立起更符合项目实际情况的需求管理优化方案。

作为 Scrum 流程之源，需要建立起产品需求列表（Product Backlog）。在开始迭代前，产品负责人需要充分拆解项目需求，准备足够多、且描述清晰的产品需求列表。

产品需求列表的来源可能来自客户需求，可能来自产品负责人为提升产品效率的规划，可能来自外部遵从合规要求，也可能来自后续升级功能的预埋需求等等。这些产品需求列表需要充分考虑全，结合各方的意见，形成一个个描述需求。这些需求可以以 PRD 文档、低保真交互设计来说明，文档可以不精美、但一定要清晰，要能让 Scrum 团队人员都能清楚地明白这个需求。进而汇总形成H项目产品需求列表。产品需求列表输出后，产品负责人必须审视所有需求的优先级，通过其商业价值、竞争价值等多维度来评估，确定产品需求优先级。

在此阶段，可以从如下两个点来进行重点优化：

（1）明确产品负责人作为唯一的需求确定人

在产品负责人整理需求清单之时，Scrum主管、开发团队成员及外部支撑力量（专家组、技术总监、运营经理等）都可以参与讨论，给出建议，但相关人员都是作为信息输入的建议人。最终的需求确认人是唯一的，只有产品负责人。

设立此原则，一方面是为了提高沟通效率，因为后续产品负责人需要负责对开发团队、Scrum 主管讲解和澄清需求，产品负责人作为唯一责任人，能够端到端了解问题或需求的始末、详尽问题细节；另一方面，该原则也保障了需求的统一性，避免过度讨论、衍生、解读，导致需求的不一致，进而无法快速进入下一环节，甚至造成后续开发过程中的误解。

（2）需求阶段即与客户深度交流

产品负责人在需求梳理阶段，即与客户开展深度交流。通过该措施，既可以让客户感受到团队的执行力、了解项目计划、增强信心，也可以及时发现客户的需求变化或新的痛点，进而第一时间加到产品需求列表中。

通过与客户的早期、深入、频繁的互动，能够在问题、变更的最早期即介入，项目组也可以对客户深度去讲解项目计划、去影响客户尽量减少偏离原始需求的计划，进而达成 A 公司与客户的双赢。

# 4.3.3项目执行优化方案设计

A 公司H项目组最终选择了Scrum 敏捷开发模式，作为项目新的进度管理方案。项目组打破过去的项目组框架，成立起高效、敏捷的 Scrum 项目组，建立起以 Scrum 主管、产品负责人、开发团队为核心的铁三角团队。

同时，建立起以产品负责人为核心的产品需求管理流程，与客户建立起高频、高效的较量，让项目进度对客户的透明度大幅提高，同时，也尽可能降低了后续客户需求骤变的风险。建立起包括 Sprint 计划会议、每日站会、Sprint 评审会议、Sprint 回顾会议的项目执行优化方案，简洁高效、目的明确的召开每次会议。通过会议节点的把控，确保了 Scrum 流程的透彻执行。同时，也强化了团队间各角色的深度融合，群策群力，通过一个个小的版本迭代循环，进而建起整个H软件服务的“摩天大厦”。

（1）召开Sprint 计划会议

产品需求列表输出完成并确定优先级后，产品负责人召开 Sprint 计划会议，由产品负责人、Scrum 主管，以及开发团队参加。在本次会议上，产品负责人对团队讲解已经按优先级排序的产品需求列表。产品负责人讲解完成后，开发团队根据产品负责人所确定的优先级，从高到低对产品需求进行工作量评估，确定工作量。在研发工作量管道范围内，按照需求优先级从高到低排序，确定哪些需求可以排进本次版本。

在此会议中，产品负责人要解答包括 Scrum 主管、开发团队在内的任何人员提出的关于产品需求的任何问题，澄清并讨论达成一致。纳入版本的需求，由产品负责人和开发团队在会上讨论制定详细的迭代计划，将需求分解成多个子任务，由各个开发团队成员进行任务认领。如有必须完成的需求或子任务无人认领，将由产品负责人进行分配。

在本次会议中，基本确定了一次版本迭代要做什么，什么时候做完。Sprint 计划会议讨论完成后，输出 Sprint 开发列表，即上述的各需求、子任务合集，由开发团队承接并启动版本计划。

# （2）开始 Sprint 迭代

Sprint 表示一次软件版本迭代开发周期，通常 2-4 周。在H项目中，由于客户时间紧张，计划采取第一次 Sprint 周期为4 周、后续 Sprint 周期为 2 周的方式，第一次 Sprint 先实施基础需求，后续 Sprint 持续做小需求并持续优化。

在 Sprint 阶段，由开发团队完成业务设计、需求开发、系统验证测试、端到端联调等全部流程，进而完成产品需求的实现。每次 Sprint 过程中，一旦确认周期时长，将不再接受做任何调整。所有 Scrum 团队成员应该全情投入，尽最大努力确保本次迭代的冲刺目标得以实现。期间，开发团队成员若对任何模块存在疑虑或不确定时，应当第一时间与产品负责人沟通和澄清，确保需求开发高效、正确。在 Sprint 开发阶段，Scrum 主管召开每日站会，每次会议不超过15 分钟，通常所有的团队成员站着、围成一圈，快速把会议开完。会上，所有团队成员快速对齐进展，主要包括：昨天完成了哪些工作、今天计划完成哪些工作、当前遇到了哪些问题，互通信息、共同讨论贡献智慧。

（3）召开 Sprint 评审会议

在本次 Sprint 版本迭代结束后，Scrum 主管、开发团队、产品经理共同召开 Sprint 评审会议。会上，由开发团队展示本次迭代交付的成果，在展示介绍完毕后，项目组全员理解产品功能后并给予反馈和评审意见。相关反馈和意见将作为产品经理产品需求列表的输入，由产品经理确认后续是否纳入；同时，产品经理在评审会议上进行此次 Sprint 的验收。评审会议结束且验收通过后，此次版本迭代产品正式输出，形成产品增量，即可独立交付的版本。

（4）召开Sprint 回顾会议

每次 Sprint 版本迭代发版后，由Scrum 主管主持召开 Sprint 回顾会议，对本次版本迭代开发结果进行总结，分析本次迭代取得的成绩与存在的不足，以便在下次迭代中改进。在每次 Sprint 结束后，立即转入下一次 Sprint 的计划会议，开始新一轮 Sprint，快速迭代。

# 第五章项目进度管理优化方案的保障措施与效果评价

# 5.1项目进度管理优化方案的保障措施

# 5.1.1需求挖掘与确认

在H项目实施Scrum 敏捷开发过程中，项目组全员坚持贯彻“以客户为中心”的理念，以投标文件、双方合同及与客户深度澄清交流后的意见，确定客户真实需求，详细需求描述如下：

# （一）新建H服务数字化客户使用平台

（1）基本功能要求

H服务平台是一个基于互联网服务的数字化服务平台，旨在为X省全省的所有H服务点提供图书、期刊、报纸、音视频资源的阅读、观看和收藏功能。通过构建内容分发管理能力，该平台可以实现内容分发策略的设置，并将内容精准推送到终端进行播放展示。为满足各H服务点的不同个性化定制需求，该平台可以针对每台终端制定独立的交互内容。同时，对于原因已配备远程平台的行政村，需提供持续不断的资源更新服务。

（2）更新客户管理功能

因地域行政区划时有变化，平台需要随时匹配更新地域行政区划，以保证准确性。平台需支持灵活方便的客户信息管理，包括增、删、改、查等功能。上级管理员可以直接修改客户归属信息，且这不会影响后续的统计分析功能。平台可以区分管理社区H 服务和农村H服务，以便后续依据平台使用数据分别开展合适的奖评工作。

该平台需具备客户积分系统，客户可以查询积分明细，定期开设积分兑奖区，兑换实物奖品。手机端和电脑端平台使用数据互通，客户可以使用H 服务资源参与直播讲座、竞赛等活动，可根据参加情况获得个人积分。前端支持自定义统计分析显示模块，可以按照客户积分等排行显示。需设置收集客户和管理员信息反馈的界面及渠道，基于反馈意见，在经甲方报批后，对有建设性的提议进行灵活调整和实施。

（3）新增支持元数据加工及转换

分类标引工具能够对数字对象进行图像识别、排序和格式转换，同时对各种数字对象进行处理，并将其纳入数据库。支持分布式并行加工，可以进行多客户任务分配和流程化管理。支持自定义格式转换、批量转换和自动化转换，并可以与市场主流数据库平台对接。

# （4）新增“智能手机APP $^ +$ 微信”移动端平台

除网页端外，还需支持“智能手机APP $^ +$ 微信”移动端平台，并对接XXXAPP（一款知识型APP）的X省平台内容，共享共用优质资源。智能手机 APP：需开发适配主流智能终端操作系统（包括Android 端及iOS 端）的手机端APP，便于客户接入H 服务实现随时随地阅读。为移动端客户提供了全面的数字阅读内容资源，包括每日更新的阅读资讯、每周更新的专题阅读内容等，帮助客户节省时间和成本，让客户在移动端就可以轻松地获取到高质量的数字阅读资源。微信公众号：开发并持续运营“X省H服务”微信公众号，设计并搭建H5 适配页面，通过微信公众号，让客户便捷地获得优质的数字阅读资源。平台面向客户的界面需采用绿色、安全的设计，并通过统一管控来确保平台的安全性。在设计和开发平台的过程中，需以客户为中心，注重细节和完善功能，平台所有功能都经过优化和测试，需具有高度的客户友好性，保障流畅的使用体验。同时，平台需呈现高水平设计和审美标准，以提供令人愉悦的客户界面。

# （二）改造升级X省旧版省级大数据平台

（1）对旧版省级大数据平台的角色与权限进行优化，将客户角色细化为六类，权限管理划分为五级，实现更加精细化的管理。（2）针对旧版省级大数据平台的检索功能进行升级，支持全网搜索，同时能够智能聚类和排序搜索结果，提升客户检索效率和准确性。（3）将大数据平台开放给省、市、县、镇四级相关管理部门，各级管理部门可根据权限实时监控各H服务点的动态，包括数字出版物的资源管理、分类管理以及客户管理等功能。（4）通过细化客户角色和权限管理，升级检索功能，省级大数据平台需实现上下级权限打通，上级管理员可对下级进行管理、审核和监督。同时，优化使用体验，上级管理员可以直接导出所属H服务网点的基本信息、注册、使用、活动等统计数据情况，并以表格形式展示在数据中心界面上。

（5）省级大数据平台新增线下H服务点活动统计功能。通过足够的云端存储空间，各地上传的H 服务网点活动素材能够集中汇总至平台，同时自动捕获XXX APP 在X 省平台中的联动内容，同步展现在H 服务网页及 APP 中。在数据中心可方便统计各地开展活动的次数，查看举办的活动名称以及开展活动的时间等信息，为后期对 X 省各H 服务点活动效果的管理和评价提供便利。

（6）省级大数据平台需实现数字资源实时更新。基于省内H服务点建设现状，所有已建设服务点中的终端设备连接互联网时，即可直接读取云端的最新资源，确保数据的及时性和准确性。

# （三）配套智能语音学习音箱

基于智能音箱设备，定制制作X省H服务专用智能语音学习音箱，方便客户使用X省H项目平台提供的有声内容，并能访问XXXAPP 平台内容。

（四）一定品类和数量要求的优质内容

项目需为客户提供经过授权且数量充足的高品质正版数字内容作为H服务数字资源，要求包括图书、期刊、报纸、视频节目、有声节目等资源。并且，客户对资源的品类、数量、质量等进行了细化要求。

# （五）软硬件安全要求

高安全：为确保平台和数据的高度安全性，需要在设备和技术方面采取多重防护措施，包括物理隔离、防火墙、反病毒软件、入侵检测系统等技术措施，以及建立安全日志记录、安全事件监测、隔离防护等保护安全策略。需要建立数据备份和恢复机制，以确保数据的完整性和可用性，保护平台和数据免受未经授权的访问、病毒、恶意软件等攻击，确保系统及数据安全。

服务可靠、不中断：系统需禁止非法客户进入，未授权客户不得越权使用，系统要确保全天24小时不间断运行，各服务不中断、不停止。

网络可靠性设计：为确保网络的可靠性，需要在设备、链路、网络和业务等多个层次进行保障。

安全系统：需要建立安全隔离和安全防护系统，以保护系统免受病毒和恶意代码的攻击。同时，需要满足安全保密需求，确保数据不被未经授权的人员访问。

巡检体系：根据定期巡检制度提供全面的巡检服务，重要时期、重大活动期间，需实施额外巡检和维护安排，同时需安排人员值班，以确保业务系统正常工作。

存储及备份系统：需要建立严格的数据备份和故障恢复机制，以应对可能出现的数据丢失、损坏或系统故障等情况，为确保数据安全。

灾备体系：需提供远程数据实时备份及系统恢复服务，以防止数据丢失和保证业务连续性；提供远程集群高可用性服务，以确保系统稳定性和可用性。

（六）期限及交付需求

A公司在合同签订之日起90日内完成优化后的省级大数据平台搭建部署，完成控制平台、移动端平台等开发，经客户验收后交付上线。并同时需完成智能语音学习音箱等硬件设备的安装调试、验收交付使用工作，按照客户建设网点要求配送到各个建设网点，当场培训验收、交付，当地负责人签收确认单。

# （七）持续运营需求

A 公司需按照H服务数字化建设要求，以覆盖更多阅读人群为目标，策划和开展线上、线下活动，搭建线上活动平台，包含特色专题内容活动、知识竞赛、面向农业科学等主题的线上讲座培训直播等，持续以多种形式鼓励群众积极使用H服务。

持续开展内容运营，及时更新H 服务相关政策、资讯、活动等信息，推广平台内优秀的书籍、报纸等内容。持续开展线下活动策划，有效推动H 服务数字化建设，较大比例提高H服务省级大数据平台客户使用率，全年线下专题活动不少于XX 场，并通过各级媒体团队全年开展Η服务的线下推广。

拿到上述需求后，兼职项目经理的 Scrum 主管立即对问题内容进行分类。对于软件产品需求，迅速纳入 Scrum 流程闭环；对于硬件需求，根据 A 公司现有流程提交采购部门处理；对于内容需求，转交内容部门评估和交付；对于运营需求，要求 Scrum 项目组外协人员运营经理提前准备。Scrum 主管与各团队沟通的核心包括：清晰透彻说明需求，就时间点达成一致。详细需求搜集和管理流程如图5-1所示。

![](images/d0cb50c02015faa7107b1b9ccc0494f4683661b382e23a963277da9e7c2af5f6.jpg)  
图5-1优化后的H项目需求管理与分解流程图

# 5.1.2 有效的沟通

在项目过程中，沟通是影响项目进度和效率的决定性因素之一。A 公司总部在Ρ 市，各地设立分公司负责属地化销售和服务，研发团队依托于总部。就H项目本身运作而言，属地化的项目经理、运营经理在省公司，而其他如产品负责人、开发组、专家组等角色都在P市总部。项目中沟通严重依赖于个人通讯软件、电话等工具，团队内外沟通成本高，沟通效率也存在提升空间。

在H项目运作前期，项目经理希望找召开会议，或者项目组成员需要组织小型会议沟通时，主持人需要通过电话、邮件等方式逐个私聊参会人，协调和预约会议时间，经常会遇到日程冲突，会前的沟通和协调非常耗时耗。最终终于约到会议时间，开周会前，各个与会人员在现场讲述自己的相关内容，由于与会的其他人没有提前了解发言者希望讲述的内容，导致会议效率偏低、时间冗长；而主持人在主持讨论的同时，还需要记录会议纪要，导致主持人精力无法 $100 \%$ 与会，整个会议效率低下。因为瀑布流式业务流程时间冗长、沟通较少，这种会议模式的弊端在瀑布流式业务流程下还可接受。H 项目实施 Scrum 敏捷化项目进度管理后，Scrum 流程中的 Sprint 计划会议、每日站会、Sprint 评审会议和 Sprint 回顾会议等能够极大地提升项目的推进效率，让各个角色人员都能快速发现问题、同步进展、高效迭代。但项目组成员处于不同地域，Scrum 主管在省公司，产品负责人、开发团队在Ρ市，如何解决异地团队间的高效沟通、让 Scrum 流程内的快速沟通、高频交流、高效闭环落地，显得至关重要。

高频的业务会议需要快速闭环，特别是每日站会，通常会议时长只有15 分钟，在这个背景下，传统会议运作的低效流程完全跟不上每日更新同步的业务进展。A 公司H项目组也意识到了该问题的重要性。为此，经过慎重的业务选择，H项目组引入了在线协同办公软件“XX”，通过引入先进的业务工具，高效地解决了项目组的沟通问题。

（1）满足 Scrum 业务流程需求，开启高效开会新方式

引入新的在线协同办公软件后，问题得到了解决。策划会议时，召集者可以提前预约会议时间，并将日程同步给所有参会者，届时自动提醒并支持一键发起视频会议。会议开始前，与会人员提早准备议题、发布到会议系统中，大家在会前就已经了解了相关内容，实际开会时候，只挑重要的问题或者尚不清晰的问题进行答疑和讨论即可，大幅提升了开会效率。在会议过程中，常常需要在线协同编辑会议文档，基于新的企业通讯工具，大家可以将迭代文档或思维导图共享出来，真正实现边说边看，还可以共同参与，给与会人员赋予编辑权限，边讨论，边完善。同时，使用云录制功能，方便会后自动存档会议音视频和文字记录。

（2）在线编辑 $^ +$ 团队空间，解决文档效率问题，实现知识沉淀

建立团队空间，组件H项目组团队空间，Scrum 主管、产品负责人及开发团队均作为团队空间成员，在团队空间内，各角色按不同职能汇总项目文档、业务素材、项目进程、数据报告，方便项目组成员实时查看，协作分享。同时，团队的每次会议纪要、周报、月报、阶段性规划等都可以归档到团队空间中，实现知识的沉淀。通过知识沉淀，实现部门成员快速了解业务知识，加快业务决策效率。在项目结束后，H项目组沉淀的知识更可以作为A 公司后续的重要资产和经验，供后续其他项目参考。

（3）可视化项目进展，任务管理更有序

同时，基于新的企业协同办公工具，可以实现线上项目化管理。通过业务看板，呈现着项目的各个关键阶段，随时看见谁在何时要完成什么。例如，H项目的每个 Sprint 迭代流程，每日进度、每个成员的输出都是实时可见。项目组成员不必再在协同工作进展上耗费时间。通过 Scrum 业务流程，将项目拆分为一个个可执行的 Sprint 迭代，整个项目进展井然有序。同时，在企业协同办公工具中，也可以结构化地呈现和体现各个人、各个时间段的职责和输出，帮助每个项目组准确掌握自己在任务中的职责，更好地展现任务进展，高效驱动每一项任务得到落实。

# 5.1.3变更流程的规范

在Scrum 业务流程中，H项目的所有业务需求都由产品负责人这一角色进行集中管理。这种集中管理有助于保持团队对当前 Sprint 版本迭代开发任务的专注，同时也使得内部和外部的需求变更不会影响到团队的开发计划。当临时需求变更出现时，无论变更申请方是客户还是内部其他团队，产品负责人需要与申请方进行及时沟通和协商，以确保团队能够根据新的需求变更及时调整开发计划。

首要的是确定拟变更需求的优先级。如优先级低，纳入产品需求清单，下次 Sprint 时再次审视优先级及排期。如优先级高，产品负责人需要与变更申请方确认这个需求是否需要在当前的 Sprint 版本迭代中处理。如果确实需要在当前 Sprint 版本迭代中变更，产品负责人需要与 Scrum 主管和开发团队进行充分沟通，以便调整当前的开发计划和资源分配，若与会人员都确定当前变更需求能在本次 Sprint 中完成、且不会影响本次 Sprint 计划，则直接进行调整；如果经过评估确认当前变更需求无法直接增加到本次 Sprint 中，超出了当前 Sprint 的研发工作量管道，那么产品负责人、Scrum 主管和开发团队需共同商定对当前需求计划中所有任务优先级情况进行评估和调整。在进行调整时，团队会考虑当前任务计划中所有任务的优先级，将优先级低的任务移交到下次 Sprint 中，以便为本次临时增加的变更需求腾出足够的时间和资源，能够将本次临时变更的需求增加至本次 Sprint 中。若当期 Sprint 的剩余周期的全部工作量都不足以支撑本次增加的需求，则产品负责人、Scrum 主管和开发团队可考虑调整在下次 Sprint 中高优先级处理该问题。不论结果如何，项目组均需与变更申请方（尤其是客户）保持及时沟通和协商，从而灵活应对客户需求变更。H项目需求变更规范流程如图 5-2:

![](images/c0ddd4a9e3b7fd2b963e5e29c9d09b7aa8c5b5be1fd0cb3a25af71ac01b0d285.jpg)  
图5-2 优化后的H项目需求变更流程

# 5.1.4项目进度监控

Scrum 敏捷开发流程是将项目分解成几个 Sprint 进行版本迭代冲刺，每次 Sprint 周期根据Scrum 团队确定的优先级实施版本开发。

Sprint 冲刺周期不宜过长，通常在 2-4 周左右的时间。因省级数据平台数据省级改造是后台后续客户平台调用的基础，经项目组评估及与客户确认，第一个 Sprint 周期的冲刺任务是旧版省级数据平台的交互改造的核心需求，计划基本完成整个改造需求，建立起基础框架，因此第一个Sprint暂定3 周。Spirnt1任务列表 5-1 所示。

表5-1第1次Sprint任务列表  

<table><tr><td>Sprint 任务阶段</td><td>产品需求</td><td>Sprint 周期</td><td>责任人</td></tr><tr><td rowspan="5">Sprint1</td><td>优化客户权限系统及实现上</td><td rowspan="5">3周</td><td rowspan="5">孙XX、高XX、王 XX、张XX等</td></tr><tr><td>下级权限打通</td></tr><tr><td>新增数据开放接口</td></tr><tr><td>平台检索功能</td></tr><tr><td>线下活动统计功能</td></tr><tr><td></td><td>支持实时更新</td><td></td><td></td></tr></table>

在H项目 Sprintl 结束后，项目组进行下一次 Sprint 的需求评审和确认，并再次与客户沟通计划。最终，根据各个需求的优先级，拆解成5 个Sprint，对项目进度实施清晰监控。

特别的，为避免出现业务交付后才转运营、进而产生运营配置问题或运营配置耗时过长、无法满足项目交付的问题，在最后一个 Sprint 阶段，Scrum 主管组织运营经理加入 Sprint5，将开发与运营配置并行。在开发及解决方案联调的同时，运营经理组织运营团队准备运营素材，对于网页、客户页面及客户端进行配置，策划上线营销活动。运营配置中，任何问题随时联系开发定位和指导，并通过每日站会等形式向组织反馈。通过运营纳入 Scrum 团队后的通力合作，确保产品交付时的运营配置均已完成，为客户交付一个“部署即可上线”的完整产品服务。见表5-2。

表5-2第2—5次Sprint任务列表  

<table><tr><td>Sprint任务阶段</td><td>产品需求</td><td>Sprint 周期</td><td>责任人</td></tr><tr><td rowspan="3">Sprint2</td><td>客户使用平台基本功能要求</td><td rowspan="3">2周</td><td rowspan="3">孙XX、高XX、王 XX、张XX等</td></tr><tr><td>客户界面UX开发</td></tr><tr><td>微信端及网页端页面开发</td></tr><tr><td rowspan="3">Sprint3</td><td>平台客户管理功能开发</td><td rowspan="3">2周</td><td rowspan="3"></td></tr><tr><td>Android APP 端侧开发</td></tr><tr><td>XXX APP 对接</td></tr><tr><td rowspan="3">Sprint4</td><td>元数据加工及转换特性</td><td rowspan="3">2周</td><td rowspan="3">孙XX、高XX、王 XX、张XX等</td></tr><tr><td>数据管理特性</td></tr><tr><td>iOS APP 端侧开发</td></tr><tr><td rowspan="5">Sprint5</td><td>APP、平台等全软件联调</td><td rowspan="5">3.5周</td><td rowspan="5">、x</td></tr><tr><td>软件+硬件结合联调</td></tr><tr><td>活动功能及活动模板开发</td></tr><tr><td>全流程体验问题优化</td></tr><tr><td>运营素材准备及配置</td></tr></table>

在每个 Sprint 冲刺过程中，为了实现项目进度的可视化管理，H项目组通过企业通讯软件，Scrum 团队于每天上班后半小时召开每日站会，时长15 分钟，会上记录每个人的工作进展和在工作中遇到的困难。同时，通过业务看板、周报、月报等能力，在项目组团队内，明确各个阶段、各个关键角色的职责和进展；进一步细化任务看板，以便更好地汇总开发进度，让每个团队成员都可以清楚地知道每个Sprint 版本迭代中每个需求的开发进展和进度。

在H项目中，为了保证交付的产品符合客户实际需求，项目组始终与客户保持良好的沟通，并及时根据客户的需求完善和调整需求清单。在每个 Sprint 版本迭代环节，项目组也会与客户同步实时进度，让客户可以实时了解项目的具体进展和每个阶段的产品增量，增强了客户的参与感和获得感，进一步提升了客户关系。通过在项目中不断沟通，项目组与客户之间建立起了信任，消除了客户的疑虑，让双方的关系从过去的甲方乙方关系，转变成全面的合作伙伴关系。这种良好的合作关系为后续的深入合作打下了坚实基础。

另外，H项目组也建立起了对公司高层的有效沟通途径，例行向A 公司高层进行会议汇报，回顾项目进展、汇报后续计划、求助遇到的问题等，使项目对公司高层可视，增强高层团队对业务的信心，也借此机会获得了公司高层对项目的更多支持。

# 5.1.5持续的知识分享与总结

在现代企业快速发展过程中，知识已经成为企业在市场竞争中生存和持续保持竞争优势的主要资源，持续的知识分享和总结是非常重要且不可或缺的，它的益处包括但不限于如下几点：

（1）提高员工专业知识水平：通过不断的知识分享，员工可以不断学习新的技能和知  
识，持续提升员工专业素养；（2）提高团队合作能力：通过知识分享，员工可以互相了解对方的专业领域，扩充知  
识领域，从而提高团队合作效率；（3）提高企业效率：通过知识总结，企业可以识别和解决共性问题和重点问题，并基  
于知识沉淀不断提高业务流程的效率；（4）增强企业的竞争力：通过不断的知识创新，企业可以保持竞争力，更易于在行业  
中脱颖而出；（5）保护企业的知识资产：通过持续的知识分享和总结，企业可以确保知识不会丢失，  
并且可以在员工离职后保护其知识资产；（6）提高员工的工作满意度和积极性：员工参与知识分享和总结，可以激发他们的工  
作热情，增加自豪感和成就感，进而提高工作满意度和工作积极性；（7）增强企业的文化：通过持续的知识分享和总结，企业可以增强其创新文化和学习  
文化，同时也可以提高员工的归属感；（8）提高员工的自我价值感：员工参与知识分享和总结的过程，可以提高他们的自我  
价值感，同时也提高了员工的个人能力。因此，知识资源管理也成为了项目管理的重要组成部分。

H 项目组作为A 公司率先使用 Scrum 敏捷开发流程的团队，将项目运作中产生的知识沉淀下来，在项目组内分享、在公司内分享，更成为了项目组的职责和义务。

H项目组将整个项目知识管理流程分为：沉淀、分享、应用、反馈、更新五个模块，落实到每项工作流程中去，通过“细水长流”“润物无声”的方式，持续改变着组织更好地前进。

（1）沉淀：Sprint 业务流程要求召开每日站会，主要讨论“昨天完成了哪些工作”“今天计划完成哪些工作”“今天工作是否有困难或求助”。在过程中，如有一些业务技巧、经验总结，即可在每日会议中积累下来，同时借助办公软件，沉淀到公司团队空间、知识Wiki库等平台中去。在每次 Sprint 回顾会议中，H 项目组都会详细分析本次 Sprint 中遇到的问题，系统地汇总沉淀，并上传至团队空间作为文档积累。项目人员也可以在不泄密的前提下，总结经验分享至部门知识库、企业内部博客等平台。

（2）分享：Scrum 流程特别重视会议交流，每次会议交流中，项目组成员都可以彼此分享自己的知识和信息；项目组也不定期策划线上、线下分享会，以分享 $^ +$ 讨论的方式，把项目中积累的知识与团队内部及周边团队共享、碰撞，利己利他。同时，项目组在对客户、对公司高层交流时，也可将项目组沉淀的知识对外传播，既能获得客户、公司高层的认可，也能借此听取客户、公司高层的指导意见，更有利于项目的推动。

（3）应用：每次 Sprint 中，项目组都会吸取上次Sprint 的业务经验，避免同一错误犯多次，持续提升业务效率。同时，项目中也不会不定期策划复盘会，复盘和回溯典型问题的根因、规避措施、长期解决方案等。并将重大问题整理成警示案例，持续宣贯，确保 Scrum项目组人员脑海中都绷紧质量弦，切实提升业务质量。对于每次 Sprint 中及 Scrum 整个业务流程中的优秀经验和总结，在知识沉淀后，也会持续的对新加入Scrum 团队的人员进行宣传，将优秀经验沉淀为成员必备素质，进而更好地提升项目组效率。

（4）反馈：在项目组内知识分享和碰撞过程中，项目组充分吸取组内各成员意见，群策群力，提升决策效率，丰富业务知识库；在经验向外部分享后，项目组也需与外部及时跟踪交流，外部应用后有一些进一步意见和反馈时，可及时吸纳有效信息。

（5）更新：知识不是一成不变的，在每次内外部交流、每次 Sprint 过程后，项目组都通过便捷的办公软件和平台，把知识可视化、电子化，分层分级管理，确保知识内容不腐化，持久保持内容竞争力，常用常新、持续迭代。

# 5.2效果评价

对H项目应用 Scrum 敏捷开发流程后，通过 5 次迭代，项目组最终在既定时间内完成了项目交付，项目迭代详情见表5-3。项目交付过程中，项目组与客户持续开展沟通交流，因此该项目的交付结果也得到了客户的高度认可。

表 $5 { - } 3 \ \mathrm { H }$ 项目迭代冲刺详情表  

<table><tr><td>Sprint任务阶段</td><td>实现需求</td><td>Sprint 周期</td></tr><tr><td>Sprint1</td><td>优化客户权限系统及实现上下级权限打通、新增数据开 放接口、平台检索功能、线下活动统计功能、支持实时 更新</td><td>3周</td></tr><tr><td>Sprint2</td><td>容户平基功能要求、客户界面UX开发、微信</td><td>2周</td></tr><tr><td>Sprint3</td><td>平台客户管理功能开发、AndroidAPP端侧开发、XXX</td><td>2周</td></tr><tr><td>Sprint4</td><td>元数发据加工及转换特性、数据管理特性、iOS APP端侧</td><td>2周</td></tr><tr><td>Sprint5</td><td>APP、平台等全软件联调、软件+硬件结合联调、活动功 能及活动模板开发、全流程体验问题优化、运营素材准 备及配置</td><td>3.5周</td></tr></table>

根据以往项目经验，同等情况下，使用瀑布模型可能至少需要6—7个月才能完成交付，如表 5-4 所示。本次项目的如期交付，也为A 公司类软件服务类项目实施敏捷开发奠定了坚实基础和树立了样板。

表5-4本次项目与往期项目对比  

<table><tr><td>项目</td><td>本项目</td><td>往期项目</td></tr><tr><td>项目时长</td><td>3个月</td><td>67个月</td></tr><tr><td>项目延期</td><td>未延期</td><td>延期1—2个月</td></tr><tr><td>需求变更应对度</td><td>需求变更少、且可以灵活应对</td><td>需求变更多、且不能灵活处理</td></tr><tr><td>项目进度透明度</td><td>高</td><td>低</td></tr><tr><td>客户满意度反馈</td><td>高</td><td>一般</td></tr></table>

通过本次项目的实施，在公司内外都得到了不错的反馈，建立了良好的口碑，详细表现如下：

（1）提高了项目内部透明度：在每日站会、Sprint 计划、Sprint 回顾等活动中，团队成员必须共享自己的进展、挑战和计划。这些活动不仅有助于团队成员更好地理解项目目标、进展和困难，也让项目团队成员间实现了高度透明，便于项目组内部更好地协同工作、共同解决问题。

（2）加强了团队内沟通：通过每日站会等活动，鼓励项目组各成员积极交流和分享。

这种高效的沟通，有助于团队成员更好地了解和协调彼此的工作。团队成员也能通过沟通来更好地理解项目需求和客户期望，从而更好地满足客户的需求。

（3）增强了项目灵活性：H项目通过Sprint 计划中灵活地调整任务优先级和分配，以及根据实际进展及时调整计划，团队成员可以更好地应对变化和风险。特别是开创性地在Sprint5 环节引入了运营准备事项，实现了软件发布与运营配置的平滑过渡和无缝衔接，真正为客户实现了“交钥匙式”交付。

（4）突出了价值交付：在H项目的每个Sprint 中，项目组均按照预先确定的目标完成了迭代。为客户及时交付让客户满意度高的产品，是H项目组高度关注客户价值的体现，也是对“实现价值交付”理念的忠实践行。

（5）促进了团队成员自我学习：在H项目中，通过持续 Sprint迭代、Sprint 回顾等实践，促进了团队成员的技能学习与提升，也使得项目组团队更加成熟。这种自我学习与促进的能力，能够帮助项目组更好地适应变化和风险，提高工作效率和质量。

（6）提高了客户满意度：通过 Sprint 计划和 Sprint 回顾等实践，H项目组团队成员可以更好地了解客户需求和期望。对客户满意度的高度关注，能够提高产品质量和客户体验，也是实现Η 项目按时交付的重要因素之一。项目组还邀请客户及时了解项目进度，开展常态化互动，客户满意度得到了明显提升。

（7）促进了持续改进：H项目在应用Scrum模型的过程中，团队成员可以不断反思和总结工作，从而发现改进的机会和问题。这种持续改进促进了H项目组团队的成长和发展，提高了团队成员的满意度和工作积极性，让H项目组从一个传统软件开发型项目组迅速转身成为一个成熟的敏捷开发项目团队。

# 第六章 总结与展望

# 6.1研究结论

本文首先梳理和总结了相关文献，介绍软件项目进度管理的定义，梳理项目进度管理的工具和技术，包括传统瀑布模型，及极限编程、Scrum 等敏捷开发模型。通过理论结合实际，选取A 公司H软件项目作为案例进行研究。

本文对于H项目概况及进度管理现状进行了详细分析。H 项目原先采用传统的瀑布模型进行项目进度管理，过程中遇到了一系列问题，切实影响了项目效率和项目目标的达成。作者通过研究发现，H项目早期在项目运作和项目进度管理上都存在一定问题，包括：项目组沟通效率低：各个阶段严格地按照顺序进行，各阶段人员各司其职，不承担、不了解其他环节的业务，整个项目组缺乏过程沟通，都是等到相应环节了或触发问题后才开始沟通，导致各阶段人员彼此之间对需求或文档的理解产生偏差，纠错成本高；开发流程长：传统的项目管理模式无法满足客户小步快跑、快速迭代发布版本的目标，瀑布式模型这种传统项目管理模式是在假设前一阶段正确的基础上才对下一阶段进行操作，整个过程过于理想化，在实际实施时，难免会出现一些错误或需求变更，一旦出现问题，团队需要追溯错误源头并修正，这个过程需要耗费大量时间和成本；变更需求难：当发生需求变更时，团队可能无法快速响应并将其纳入整个项目流程中，导致需求难以变更；重文档输出：H项目组成员在整个项目过程中，需要投入大量时间用于文档输出，极大地影响了项目进度和效率，延长了项目周期、增大了项目成本。

本文结合H 项目的实际情况，识别了H 项目组最需关注的 TOP3 问题：开发周期短，客户需求多变，减少僵化文档输出、降本增效。基于敏捷 Scrum 敏捷模型，为H 项目实施了项目进度管理优化方案设计，从项目组织优化方案、需求管理优化方案、项目执行优化方案等维度进行研究，并总结、提炼了H项目进度管理中的各项关键保障措施，包括需求挖掘与确认、有效的沟通、变更流程的规范、项目进度监控、持续的知识分享与总结等。特别是在将Scrum 模型落地实施时，H项目组基于客户真实诉求和项目实际情况，分别设计了5 个 Sprint，首先聚焦攻关解决省级大数据平台改造问题，再分别对于客户平台云侧及端侧实施开发，最终实施端到端的解决方案联调。特别的，在最后一个 Sprint 阶段，Scrum 主管组织运营经理加入了 Sprint，将开发与运营配置并行，在开发及解决方案联调的同时，运营经理准备运营素材、对于网页、客户页面及客户端进行配置，并策划营销活动、做好活动上线准备，确保产品交付时的运营配置均已完成。最终，H 项目通过实施 Scrum 敏捷模型进行管理变革，按期为客户交付一个“部署即可上线”的完整产品服务，达成了商业目的，也大幅提升了客户满意度。

通过研究，可以得出结论，与瀑布式等传统开发模式相比，Scrum 敏捷模型更能够满足H项目的进度管理要求。

# 6.2管理启示

通过Η项目进度管理的研究，除上述研究结论外，我们还可以得到 2 点启示：

（1）在对类似 A公司这样的传统企业，在软件服务项目交付过程中使用Scrum 敏捷开发时，可以在标准的 Scrum 敏捷模型下由 Scrum 主管兼职项目经理，以实现传统项目团队往Scrum 敏捷团队的平滑过渡，在提升迭代效率的同时，也能让成员尽快熟悉和掌握 Scrum 敏捷模型流程。

（2）在面向客户交付需持续运营的软件时，软件开发与运营都是不可分割的一部分。在 Scrum 业务流程中，特别是最后一个 Sprint 阶段，要特别考虑纳入运营环节，确保给客户实现“交钥匙”式交付，真正实现“上线即可用”。

但 Scrum 敏捷模型在企业的应用，也存在一些局限性，如：难以适用于大型项目或跨越多个团队的项目；对于需要高度专业技能的项目，Scrum 模式可能会难以适用；Scrum 所严格要求的迭代周期可能不适用于某些类型的项目，如具有长期依赖性的项目；长期的 Scrum敏捷模型可能会带来频繁的冲刺，造成团队成员的过度压力和疲劳，不利于团队和企业的长期发展。

尽管Scrum 敏捷开发模式在H项目中取得了良好的效果，但每个项目的情况都是独特的，当应用本文所研究的方法于其他类似软件服务项目的进度管理中时，需要充分考虑该团队和项目本身的特性和情况，如项目团队规模、项目需求范围、需求变更频率等。

# 6.3研究局限与展望

# 6.3.1研究局限

在H项目的进度管理实践的过程中，通过 Scrum 敏捷模型，解决了H 项目早期遇到的各类问题，提升了效率，实现了业务按期交付。

但整个 Scrum 敏捷过程中，主要是在于软件业务实施了敏捷，只是在最后一个 Sprint 将运营及内容相关人员卷入流程，保障了业务交付。对于内容准备、运营策划、硬件采购，在本项目中前期并未进行 Scrum 管理，实际存在交付风险及客户满意度风险。如负责硬件、内容、运营的团队没有参与客户的交流，不了解过程中客户真实意图的变更，不能确保为客户提供高满意度的交付件。

整体来看，本次对于 Scrum 的研究还是存在一定局限性，包括但不限于：

（1）缺乏系统性研究：虽然 Scrum 敏捷模型在很多实践中得到了广泛应用，但对 Scrum敏捷模型的系统性研究目前还是偏少的；（2）缺乏标准的评估指标：当前对于Scrum 效果的评估，主要是基于项目实际来评价，缺乏标准化评估指标，导致研究结果不够准确，可能会因为行业原因而出现一定偏差；（3）长期效果评估缺失：对 Scrum 敏捷模型，更多是项目型研究，缺少长期效果的观察与评估，需要更多的长期研究，以观察 Scrum 敏捷模型在长期模式下的效果；（4）更侧重实践，理论研究待进一步增强：当前，Scrum 敏捷模型的研究目前偏重于项目实践，足够的理论支持尚有待继续填充和完善；（5）研究方向偏特定行业：Scrum 敏捷模型的研究大多集中在软件开发领域，对于其他行业的应用研究较少，有待进一步加强，以验证 Scrum 敏捷模型在更多行业的普适性。

# 6.3.2 未来展望

对于软件服务行业，软件 $^ +$ 硬件 $^ +$ 内容 $^ +$ 持续运营的交付场景后续会越来越普遍。Scrum 敏捷业务流程也需要随之持续优化，扩大软件、硬件、内容、运营等全业务场景，成立全场景、全流程的 Scrum 业务模型，打造一个全功能的 Scrum 作战团队，各个环节、各个部件、各个团队对客户高度透明、高频交流，真正做到“以客户为中心”，交付出真正让客户满意的产品和服务。

项目应用 Scrum 敏捷模型的过程中，需要通过 Scrum 敏捷模型对项目进度管理过程进行不断地检视、不断地纠偏、不断地优化，将 Scrum 敏捷模型切实融入到项目中去，才能真正实现“敏捷交付”。

Scrum 敏捷模型在软件服务项目中的变革是个持续的过程，但唯有变革、才能适应未来的更多的整合。未来，Scrum 敏捷模型也有望在更多行业得到应用，如金融、服务、医疗、制造等。随着人工智能、大数据等高科技的支持发展，Scrum 敏捷模型也可能得到更多的技术支持，效率和效果也有望得到大幅提高。在更多行业使用 Scrum 敏捷模型的基础上，Scrum敏捷模型的研究将更加注重理论支持，以加强社会和科研界对 Scrum 敏捷模型的认识和应用，

Scrum 敏捷模型的实践方法也将得到改进，进一步优化和增效。总体而言，Scrum 敏捷模型未来的发展将会更高效、普及和多样化。

项目进度管理优化过程是“进无止境”的，逆水行舟，不进则退。只有通过Scrum 敏捷模型等工具持续改善，才能保持项目进度足够透明、足够快、足够敏捷。通过有效的项目进度管理，能够加速项目的进展，提高项目的效率，保证项目质量，减少项目的风险，提高项目管理的水平，从而更快地助力社会进步和发展。

参考文献[1]张凡,张岚.展览项目管理如何定义[J].中国会展,2019,437(05):56-58.[2]陈志辉.浅析项目管理理念在建筑工程管理中的应用[J].房地产世界,2022,375(19):83-85.[3]李虎雄,李虎俊.Web 系统软件开发项目的成本估算模型[J].计算机工程,2007(16):274-276.[4]李月娥.定制软件项目的成本估算与成本控制探讨:以医疗信息化行业软件建设为例[J].财会学习,2019(26):229-230.[5]李天宇.数字化在企业项目管理中的运用探讨[J].商讯,2022,275(13):107-110.[6]黄超.基于战略背景下企业项目管理举措探究[J].商场现代化,2022,974(17):73-75.[7]黄敏珍,王璐璐,林晓蕾,等.面向软件项目管理的数据生命周期管理研究[J].项目管理技术,2023,21(02):124-129.[8]王成伟.浅谈计算机软件项目管理[C]/天津市电子学会.第三十六届中国（天津）2022IT、网络、信息技术、电子、仪器仪表创新学术会议论文集.[出版者不详],2022:3.[9]张小雨.PERT 技术在软件项目管理中的应用[J].电子技术,2023,52(01):298-300.[10]John D Strain,David A Preece. Project management and the integration of human factors inmilitary system procurement[J]. International Journal of Project Management, 1999,17(5).[11]冯卫.浅谈软件项目管理中的成本效益和收益管理[J].商业观察,2023,9(01):77-80.[12]龚侠义.数据中心建设项目进度管理优化策略[J].产业创新研究,2022(22):29-31.[13]李彩红.国家出版基金项目进度管理的路径优化策略——以广西教育出版社为例[J].传播与版权,2023(04):58-60.[14]冯果.网络技术发展与公司法律制度创新[N].检察日报,2021-04-17(003).[15]夏俊杰.软件项目管理的计划和控制方法研究[J].石河子科技,2021(01):46-47+57.[16]赛迪智库软件和信息技术服务业形势分析课题组.2021 年中国软件和信息技术服务业发展形势展望[N].中国计算机报,2021-04-26(014).[17]王坤琦.项目进度管理的动态调整研究与应用[D].北京交通大学,2021.[18]Andrew S,Jennifer G.Applied Software Project Management[M].O'Reilly Media,2015.[19]尹婷.软件企业开发项目全成本管理分析[J].财经界,2022(16):71-73.[20]曹泽军.软件企业开发项目全成本管理研究[J].中国乡镇企业会计,2022(3):66-68.[21]Sigal Kordova,Moti Zwilling,Omer Rozen. The impact of management method on IT projectssuccess[J]. International Journal of Innovation and Learning,2021,29(1).

[22]张越.瀑布模型、快速原型模型和增量模型的对比[J].电子技术与软件工程,2019(03):32.  
[23]肖建芳.快速原型模型在学生选课系统中的应用[J].现代计算机,2020,697(25):97-100.  
[24]中国敏捷软件开发联盟 ADKOB 编写组.敏捷开发知识体系[M].清华大学出版社,2013.  
[25]Mark C L.敏捷项目管理：从入门到精通实战指南[M].人民邮电出版社,2015.[26]Brandl Felix J,Roider Nina,Hehl Martin,Reinhart Gunther. Selecting practices in complextechnical planning projects: A pathway for tailoring agile project management into themanufacturing industry[J]. CIRP Journal of Manufacturing Science and Technology,2021,33.[27]Julia-Anne Scholz,Felix Sieckmann,Holger Kohl. Implementation with agile projectmanagement approaches: Case Study of an Industrie 4.0 Learning Factory in China[J]. ProcediaManufacturing,2020,45(C).  
[28]李凤梅.基于 Scrum 敏捷开发的 D 公司软件项目管理优化研究[D].浙江工商大学,2022.[29]Alam MP, Toppur B.Hybrid Agile Project Management Model for New Product Development inAerospace[J].International journal of operations and quantitative management,2019,25(1):59-73.[30]Mahdi MN,Mohamed Zabil MH, Ahmad A R. Software Project Management Using MachineLearning Technique-A Review[J]. Applied Sciences,2021,11(11):5183.  
[31]Ken Schwaber,Mike Beedle.Agile Software Development with Scrum[M].Prentice Hall,2001.[32]王成飞.Scrum 方法在万维公司软件开发过程管控中的应用研究[D].兰州理工大学,2018.[33]高铭，张慧玲.基于CMMI的软件敏捷开发过程管理模型研究[J].现代管理科学,2017(09):12-14.  
[34]王璐,赵凯.极限编程初探[J].网友世界,2014,274(05):12.  
[35]董冰,薛蕾.极限编程优势与局限性探讨[J].创新科技,2013(12):60-61.  
[36]张东红，刘丹，王振.基于敏捷开发的 Scrum模型的改进[J].信息技术与信息化,2021(11):84-86.  
[37]丁慧,余亚萍,陈杰.敏捷思想在软件研发中的研究与实践[J].航天工业管理,2021(08):18-22.[38]王一然.机场休息室管理系统软件项目进度管理研究[D].浙江大学,2021.  
[39]Gaurav R,Komal Y,Arunima J.Emphasis on Testing Assimilation Using Cloud Computing forImprovised Agile Scrum Framework[C].International Conference on Futuristic Trends onComputational Analysis and Knowledge Management,2015.  
[40]Breno Gontijo Tavares,Carlos Eduardo Sanches da Silva,Adler Diniz de Souza.Riskmanagement analysis in Scrum software projects[J].International Transactions in OperationalResearch,2019,26(5).

[41]何晶.Scrum 敏捷方法在软件项目管理中的应用[J].数字技术与应用,2021,39(3):87-89.

[42]Valpadasu Hema,Thota Sravanthi,Naresh Kumar S,Padmaja Ch,Rama Krishna CBala,Mahender K.Scrum: An Effective Software Development Agile Tool[J].IOP Conference Series:Materials Science and Engineering,2020,981(2).  
[43]胡新梅.敏捷软件开发在央视新闻客户端开发中的实践[J].现代电视技术,2021(07):82-84.[44]李斐斐,丁艺.敏捷思维助力航空公司信息化“起飞”[J].国际公关,2022,148(16):119-121.[45]郭连明.谈瀑布模型及其局限性[J].科技展望,2016,26(06):172.  
[46]Ken S,Jeff S.30 天软件开发：告别瀑布拥抱敏捷[M].人民邮电出版社,2014.  
[47]Johnny D M.Applying 1970 Waterfall Lessons Learned Within Today's Agile DevelopmentProcess[J].PM World Journal,2018.  
[48]张晖.基于敏捷开发的D 公司软件项目管理过程优化研究[D].山东大学,2021.  
[49]Highsmith J.敏捷项目管理第 1 版[M].清华大学出版社,2005.  
[50]Leffingwell D.可伸缩敏捷开发：企业级最佳实践[M].电子工业出版社,2009.  
[51]Adams,J.R.,Barndt,S.E.Behavioral Implications of the Project Life Cycle,Project ManagementHandbook[M].eds D.I.Clelend and W.R.Kings,NewYork, 1983.  
[52]Mccollum J,Sherman D.The Matrix Structure:Bane or Benefit to High TechOrganizations[J].Pro-ject Management Journal, 1993,14(2):23-26.  
[53]边秀武,吴金希,张德.项目管理中的人和组织因素研究现状综述[J].清华大学学报（哲学社会科学版）,2006(S1):89-97.  
[54]Terry Cooke-Davies.The Real Success Factors on Projects[J]. International Journal of ProjectManagement,2002,(20):185-190.  
[55]刘晓庆.J 公司客户中心系统项目风险管理研究[D].南京邮电大学,2022.