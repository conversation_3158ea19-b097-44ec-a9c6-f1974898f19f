# 硕士学位论文 THESIS FOR MASTER'S DEGREE

论文题目 H 镁矿智慧能源集中监控系统集成项目进度管理研究

作 者 杨 琦学 号 2080702学 院(部) 工商管理学院专 业 工程管理指导教师 戢守峰 教授分类号 密级UDC

# 学 位 论 文

# H 镁矿智慧能源集中监控系统集成项目进度管理研究

作 者 姓 名 ： 杨 琦  
作 者 学 号 ： 2080702  
指 导 教 师 ： 戢守峰 教授东北大学工商管理学院  
申请学位级别： 硕士 学科类别：工程管理硕士专业学位  
学科专业名称： 工程管理  
论文提交日期： 2022 年 12 月 论文答辩日期： 2022 年 12 月  
学位授予日期： 2023 年 1 月 答辩委员会席： 张翠华 教授  
评 阅 人 ： 关志民 教授、张广胜 教授

# A Thesis for the Degree of Master in Engineering Management

# Research on Integration Project Schedule Management of H Magnesium Mine Smart Energy Centralized Monitoring System

By Yang Qi

Supervisor : Ji Shoufeng Professor

# Northeastern University December 2022

# 独创性声明

本人声明，所呈交的学位论文是在导师的指导下完成的。论文中取得的研究成果除加以标注和致谢的地方外，不包含其他人己经发表或撰写过的研究成果，也不包括本人为获得其他学位而使用过的材料。与我一同工作的同志对本研究所做的任何贡献均己在论文中作了明确的说明并表示谢意。

学位论文作者签名：期：2022 年 12 月

# 学位论文版权使用授权书

本学位论文作者和指导教师完全了解东北大学有关保留、使用学位论文的规定：即学校有权保留并向国家有关部门或机构送交论文的复印件和磁盘，允许论文被查阅和借阅。本人同意东北大学可以将学位论文的全部或部分内容编入有关数据库进行检索、交流。

作者和导师同意网上交流的时间为作者获得学位后：

半年 □ 一年□ 一年半□ 两年学位论文作者签名： 导师签名：签字日期：2022 年 12 月 12 日 签字日期：2022 年 12 月 12 日

# H 镁矿智慧能源集中监控系统集成项目进度管理研究

# 摘 要

镁在移动通信、汽车、电子、电器、航空航天、国防军工、交通运输等领域，具有重要的应用价值和广阔的应用前景。在许多传统金属矿物趋于枯竭的今天，中国社会可持续发展的重要举措之一就是加速镁的开发，镁及镁合金的应用正受到前所未有的关注。H镁集团作为老牌资源型高耗能国有企业，所在地区的镁矿储量全国领先。本文以 H 镁矿智慧能源集中监控系统集成项目为研究对象，针对项目施工过程中的进度计划和控制进行设计、优化和保障。基于项目进度计划与控制等理论和方法，结合案例研究方法、文献研究方法、定性分析与定量分析结合研究法和实证调查与分析方法等，对 H 镁矿智慧能源集中监控系统集成项目进度计划制定和控制进行研究。本文的主要研究内容包括如下 3 个方面：

（1）H 镁矿智慧能源集中监控系统集成项目概况分析。这部分主要通过对项目的基本背景、建设内容和项目现状及问题进行分析，明确项目进度计划目标，确定后续项目进度计划保障措施的侧重点，已达到项目顺利完成的目标。

（2）制定H镁矿智慧能源集中监控系统集成项目进度计划。将项目进度计划制定的理论与方法应用到 H 镁矿智慧能源集中监控系统集成项目进度计划制定的研究中。充分运用 WBS 方法、项目时间估算方法和关键路径法等理论和方法制定出该项目的进度计划并对其优化，使之成为合理、有效和具有可实施性的项目计划。

（3）对H镁矿智慧能源集中监控系统集成项目进度计划的控制研究。主要针对项目控制的关键因素采用挣得值分析法对项目进度进行综合评价，并针对项目实施过程中可能出现的问题，提出了有针对性的保障措施。

本文通过项目进度管理的相关理论与方法应用于 H 镁矿智慧能源集中监控系统集成项目中，不仅使 H 镁集团能在预算范围内按时完成项目进度，完成预期目标，同时，还能有效提高资产管理效率。此外，本文也为其他相关企业的项目进度管理提供了一定的指导意义。

关键词：能源监控系统；项目进度管理；网络计划技术；进度控制

# Research on Integration Project Schedule Management of H Magnesium Mine Smart Energy Centralized Monitoring System

# Abstract

Magnesium has important application value and broad application prospects in the fields of mobile communications, automobiles, electronics, electrical appliances, aerospace, national defense, transportation, and other fields. Today, when many traditional metal minerals tend to be exhausted, one of the important measures for the sustainable development of Chinese society is to accelerate the development of magnesium. The application of magnesium and magnesium alloys is receiving unprecedented attention. As an old resource-based and high-energy-consuming stateowned enterprise, H magnesium company is a national leader in magnesium ore reserves in the local region. This paper took the integration project of $\mathrm { H }$ magnesium mine smart energy centralized monitoring system as the research object, and designed, optimized, and guaranteed the schedule plan and control during the project construction process. Based on the theories and methods of project schedule and control, case study methods, literature review methods, qualitative analysis and quantitative analysis combined research methods, and empirical investigation and analysis methods, etc., the research on integrated centralized monitoring system for $\mathrm { H }$ magnesium mine smart energy was conduct on project schedule formulation and control. The main research contents of this paper include the following three parts.

(1) An overview of H magnesium mine smart energy centralized monitoring system integration project was introduced. This part mainly analyzed the basic background, construction content, current situation and problems of the project, clarified the goals of the project schedule, determined the focus of the follow-up project schedule guarantee measures, and has achieved the goal of successful completion of the project.

(2) The schedule plan of the H magnesium mine smart energy centralized monitoring system integration project was formulated. The theory and method of project schedule formulation were applied to the research on H magnesium mine smart energy centralized monitoring system integration project. Theories and methods such as WBS method, project time estimation method and critical path method to formulate and optimize the schedule of the project were used to make it a reasonable, effective and implementable project plan.

(3) Research on the schedule plan control of H magnesium mine smart energy centralized monitoring system integration project was conducted. The earned value analysis method was adopted to comprehensively evaluate the project progress according to the key factors of project control, and some targeted safeguard measures for the problems that may occur in the process of project implementation were provided.

In this paper, the relevant theories and methods of project schedule management were used to the integration project of H magnesium mine smart energy centralized monitoring system, which not only enabled H magnesium company to complete the project progress on time within the budget and achieve the expected goals, but also effectively improved the assets management efficiency. In addition, this paper also provided certain guidelines for the project progress management of other related enterprises.

Key words: Energy monitoring system; Project schedule management; Network planning technology; Schedule control

# 目 录

# 独创性声明..

# 第 1 章 绪 论..

# 1.3 国内外研究现状分析..

# 1.4 研究内容与研究方法. 6

# 1.5 论文结构.. 8

# 第 2 章 相关研究理论概述. . 10

# 2.1 项目进度管理.. 10

# 2.2 项目进度计划..

# 2.3 项目进度控制.. 16

# 2.3.1 项目进度控制定义. .16

2.3.2 项目进度控制原理. 16  
2.3.3 项目进度控制方法.  
2.3.4 项目进度控制过程. 18

# 第 3 章 H 镁矿智慧能源集中监控系统集成项目概况.. ... 20

# 3.2 项目组织结构及设计原则.. .24

# 3.3 项目实施要点.. . 26

# 第 4 章 H 镁矿智慧能源集中监控系统集成项目进度计划的制定

... 33  
4.1 项目工作分解与责任分配. 33  
4.1.1 项目工作分解.. . 33  
4.1.2 项目责任分配情况.. . 35  
4.2 项目活动的持续时间与逻辑关系.. . 35  
4.2.1 项目活动的持续时间估计方法.. . 35  
4.2.2 项目活动持续时间与逻辑关系确定. . 36  
4.3 网络计划图的编制.. 37  
4.3.1 网络时间参数计算及关键路径确定. 37

# 4.3.2 项目网络计划图.. .40

# 4.4 项目进度计划的优化. . 41

# 4.5 本章小结. . 46

# 第 5 章 H 镁矿智慧能源集中监控系统集成项目进度计划的控制

# .... 47

# 5.3 项目进度控制过程. .49

# 5.4 项目进度计划纠偏与保障措施. .53

# 第 6 章 结论与展望.. . 56

参考文献... . 58   
致谢.... .61

# 第 1 章 绪 论

# 1.1 研究背景

镁作为一种轻型工程材料，其潜力还没有完全挖掘出来，远没有像铁、铜、铝那样成熟地得到开发利用。镁在移动通信、手提计算机等壳体结构件上，以及在汽车、电子、电器、航空航天、国防军工、交通运输等领域，具有密度低、比强度和比刚度高、减震性好、电磁屏蔽性能优异、切削加工性能和热成形性好等特殊性能，具有重要的应用价值和广阔的应用前景。在许多传统金属矿物趋于枯竭的今天，中国社会可持续发展的重要举措之一就是加速镁的开发。

镁及镁合金的应用正受到前所未有的关注，因为人们越来越关注能源和环境。镁是过去十几年来中国镁矿产业由弱变强的罕见优势金属资源之一，如今已成为全球绝对的原镁生产大国。根据美国地质调查局公布的数据表明，我国镁矿资源总储量占全球 $2 2 . 5 \%$ ，位居世界第一，世界镁矿产量的 $70 \%$ 以上都是由我国提供。H镁集团作为老牌资源型高耗能国有企业，所在地区的镁矿储量占全国的 $8 5 . 6 \%$ ，在镁矿的储备以及地区分布上具有巨大优势。然而，在生产过程中的能耗监测、设备能耗转化率上，跟世界上其他发达国家相比，还处于相对落后的状态。

重点用能单位能耗在线监测系统建设工作是党中央、国务院加快推进生态文明建设的一项重要任务，能耗在线监测系统建设是根据国家发展改革委《关于印发“十三五”节能减排综合工作方案的通知》(国发〔2016〕74 号)、《关于印发〈重点用能单位能耗在线监测系统推广建设工作方案〉的通知》(发改能源〔2017〕1711 号)等国家政策性文件要求，根据《国家发展改革委关于印发“十三五”节能减排综合工作方案的通知》(国发〔2016〕74号)，将各重点用能单位能耗在线监测系统建设与接入情况纳入市政府 2021 年度能耗“双控”。

通过开展能源消耗大数据分析应用，为政府部门做好能源宏观分析和战略规划，开展能源消费总量和强度“双控”形势分析，实施节能监察，加强能源计量管理，制定节能标准，加强能源日常监控管理，开展能源审计，能效对标，能源计量审查，节能改造等提供及时、准确的数据支持，为重点用能单位提供支撑服务，有效促进企业提质增效降本，促进企业加快发展，实现能源消费总量和强度“双控”，促进能源资源节约利用。

为积极响应国家号召，同时增强企业的能耗监测能力，更加合理地提升生产设备的能耗转化效率，H镁集团在全集团范围内布局智慧能源集中监控管理系统。智慧能源集中监控管理系统是利用分层分布式结构，借助云服务数据中心，智能处理各种能源的生产、输送、消费等各种信息，监控和管理整个能源系统的大型开放式能源监控管理服务平台，由大数据、物联网、移动互联网技术等支撑。该系统的设计理念是通过计算机信息技术和现代化的管理软件，最大限度地降低成本，提高效率，降低排放，实现跨领域、跨地域、跨专业、跨时限、跨层级的管理。

同时，智慧能源集中监控系统的建设是 H镁集团向着国际化、智慧化、集约化、低碳化、智能化迈出的重要一步，也是集团公司领导积极响应国家“双碳”战略和省级能耗双控目标的重要举措。一方面，整合企业资源，提升集中管控能力、一体化管控能力和调度指挥能力，提升企业信息化应用水平，实施 H 镁集团智慧能源集中监控管理系统项目；另一方面，通过完善能源管理制度和手段，发现能源消耗问题，挖掘节能潜力，提高企业能管水平，促进企业节能降耗、降本增效，使公司现代化管理的整体水平和国际形象得到相当程度的提升，为集团公司今后做强做大，兼并重组，走向国际化打下坚实的基础，公司的现代化管理工作也将取得新的进展。

# 1.2 研究目的与研究意义

# 1.2.1 研究目的

本文依据 H 镁集团的智慧能源集中监控系统集成项目的实际情况，结合项目进度管理的相关知识，主要研究如何运用项目进度计划和项目进度控制等方法，解决智慧能源集中监控系统集成项目中所涉及到的项目进度管理问题，本文的主要研究目的如下：

（1）结合项目的具体特点，在初始阶段制定 H镁集团的智慧能源集中监控系统集成项目的进度计划，并针对项目的进度进行质量和成本控制管理，从而解决项目活动过程中可能出现的一些问题，保证项目按照预期目标顺利进行，从根本上实现缩短项目周期，提高质量，降低成本，增加企业经济效益的目的。

（2）以本文的研究为例，将项目进度管理的相关理论和方法引入到智慧能源集中监控系统集成项目中，提升智慧能源集中监控系统项目企业的管理理念。

另外，对于后期类似的项目活动，通过对项目过程中经验的不断总结，提供宝贵的参考意见。

# 1.2.2 研究意义

本文以 H 镁矿智慧能源集中监控系统集成项目为研究对象，将项目进度计划和控制的相关理论知识与方法融入到本文的研究中，从而确定与 H 镁矿智慧能源集中监控系统集成项目相匹配的进度计划和控制流程，最终实现 H 镁矿智慧能源集中监控系统集成项目的根本管理目标。本文的研究具有以下意义：

（1）理论意义。H 镁集团作为老牌国企，恰逢新机遇，势必会不断地改进和创新。集团每年都会有大量的新项目，项目管理工作是决定公司长远发展的核心业务之一。根据总体规划，H 镁集团智慧能源集中监控管理系统主要是通过煤、电、水、油、气等能源介质能耗的线监测、耗量平衡、统计分析、操作指导、优化运行等生产过程的集中监控管理，通过能耗管理为抓手，融入现代化的管理理念和信息技术手段，实现可视化、数字化、智能化的管理。一方面，实现集约化、智慧化、现代化管理，通过集中监控管理、设备资产管理、物资管理、能源中心管理、办公管理等精细化管理软件；另一方面，通过新技术、新工艺、新装备的引进，实现技术上的升级换代，技术上的升级换代，装备上的升级换代。通过管理、技术、装备，多头并举，达到降低成本、提高效率、减少排放、增加效益的目的。

（2）实际应用意义。因此，本文将项目进度管理相关的理论和方法应用到现代化的智慧能源集中监控系统的建设中，将会从一定程度上改善 H 镁集团在项目进度管理方面的现状，使公司在项目计划安排，时间节点监督，整体进度控制方面得到有效的管理和提高，从而促进增产增效、节能减排及可持续发展方面提高竞争力，并且为东北地区经济的发展以及国民经济的发展继续贡献一份力量。

# 1.3 国内外研究现状分析

# 1.3.1 国外研究现状

国外对于项目管理的研究起步非常早，从第二次世界大战开始就产生了关于项目管理的概念。20世纪初，美国人甘特发明了甘特图（Ganttchart），并在工程项目中加以应用。该方法将具体的工程项目环节按照年、月、日分成不同的时间节点，方便人们清晰地了解整个项目的进度计划。迄今为止，甘特图仍被广泛应用于各个领域。但甘特图的不足之处在于，其逻辑关系和资源分配关系在不同项目活动中无法得到体现。因此，甘特图的使用通常适用于一些比较小的基础项目[1]。

网络计划技术是项目进度管理中使用频率最高的技术，通常能够在提升项目管理水平和项目进度效率等方面表现出高效的特点。20世纪50年代到60年代，美国企业和军方相继在网络计划技术基础上研究出计划评审技术（PERT，Program Evaluation and Review Technique）和关键路径法（CPM，Critical PathMethod）两种不同的方法[2,3]。PERT 最开始在美国的军事研究当中使用，目的就是为了使项目更加高效。CPM 是在 PERT 的基础上进一步发展而来的。从此，PERT 和 CPM 两种主要的网络计划技术被全世界的项目管理人员广泛应用。二者的区别在于PERT技术对于项目时间的消耗采用概率的方式进行预测，而CPM方法则是基于具体的项目工期来进行进度安排和计算[4]。CPM 关注于整个项目的成本以及工期的计划和控制，PERT 则更加关注于项目工期的推进[5]。

以色列 Goldratt 博士于 1996 年在约束理论(TOC)的基础上引入了关键链法(CCPM)，这一新技术被认为是进度计划管理(Progressive Plan Management)的又一次实质性进展[6]。CCPM也属于一种可以利用有限资源对整个工程进度进行调整的网络计划分析方法。关键链法的特点是将非工作进度时间考虑进去，作为应对某些不确定事件发生的缓冲时间。在此基础上，另有学者进行了课题进展方案的研究。Roghanian 等（2018）借助于模糊数学的方法来改进 CCPM 方法对于不确定性事件的处理[7]。Rezaie等（2009）提出了一种新的持续时间计算方法，找出了与不确定性相关的安全时间[8]。

项目进度管理主要包括对整个项目的计划和控制，目前有很多学者在各个领域都对项目进度管理进行了相关的研究。Sanchez 等（2017）使用 CTPM（Costand Time Project Management）工具对某一家首席银行的项目进行分析时发现，项目规模、持续时间、延迟情况以及项目经理的权力在决定项目时间和费用是否能够管理成功的诸多因素中与项目成功的可能性成正比关系，但团队规模、团队配置的分散程度却与成功的可能性成反比[9]。Andrade 等（2019）通过引入一种结合项目进度绩效和进度遵守情况的挣得值管理（EVM，Earned Value

Management）和挣得值工期管理（EDM，Earned Duration Management）的预测方法来提高项目工期预测的准确性，最后采用建筑行业的数据进行了方法有效性的验证[10]。Muhammad 等（2020）通过文献和原始数据研究了关键的项目延误因素及其补救建筑信息模型（BIM，Building Information Modelling）特征，并开发了一个因素-特征矩阵。为了衡量所研究方面的实际有效性，进行了一个案例研究。发现 BIM 对施工工程进度管理影响重大[11]。Azeem 等（2014）开发三个模型来预测完工时的预计工期。其中两个模型是确定性的；挣值（EV，Earned Value）和挣得进度（ES，Earned Schedule）模型。第三个模型是基于 Kalman Filter 开发算法和获取进度管理的概率模型。因此，将评估 EV、ES 和卡尔曼滤波器预测模型（KFFM，Kalman Filter Forecasting Model）在不同项目期间的准确性，并与其他预测方法（如关键路径法进行比较），结果发现 KFFM的预测误差最小，精度最高[12]。。Rahman 等（2019）研究了工作分解结构方法与马来西亚造船项目工时之间的联系，发现产品工作分解结构是确定船体建造项目工时的最佳方法[13]。Ricardo 等（2019）展示了 MS project 软件的一个插件，该插件基于资源有限的项目进度管理模型（RCPSP-FRM），提供了一些灵活的图表和表格格式，使项目经理轻松和客观的找出那些可以延长或缩短时间的活动，大大的改善了项目的执行时间[14]。

# 1.3.2 国内研究现状

我国在项目管理方面的研究起步比较晚，最早是由数学家华罗庚于 1961 年对西方的 CPM和PERT 进行学习和改良，形成了适合中国国情的“统筹法”[15]。

《中国项目管理知识体系与国际项目管理专业资格认证标准》一书由中国项目管理委员会成立于 1991 年，发行于 2001 年，标志着我国项目管理工作取得了实实在在的进步。然后在在上世纪 80 年代到 90 年代年之间开始将项目管理首先应用于地质勘探、核电站建设、煤田油田建设、水土治理以及建筑工程上，随即从 90 年代至今广泛应用于道路交通建设、水利水电、农业高新技术研究、航空、机电、环保、企业并购、产品开发甚至扶贫开发等各个领域[16]。黄德才等(2000)提出具体工程中每项作业的活动时间用 $\mathsf { a } + \mathsf { b } \mathrm { i } { + } \mathsf { c } \mathrm { j }$ 型的联系数表示，实现了对某些工程周期波动等现象的定量化表述，从而为网络计划的实施提供了数据[17]。马国丰等（2002）认为项目优化应该遵循整体大于局部的思想，要考虑影响到项目的制约因素并将其加入到项目管理的范畴中[18]。李建平等（2008）对项目进度管理需要完成的一些任务进行了概括性的总结，然后引用一些案例进行了分析[19]。赵彬等（2011）将 BIM 技术引入到建筑工程行业领域的项目管理当中，从而为项目管理提供了一种新的技术管理手段[20]。王佳敏等（2014）针对工程项目的具体案例，采用模糊收入值分析的方法监控整个工程的造价情况，并结合 Montcalo 模型对工程造价进行预测[21]。袁家永（2018）将工作分解（WBS，Work BreakdownStructure）模式应用于部分城市公园工程进度管理中，并验证了工程进度管理在公园工程中的必要性[22]。薛建英等（2019）借助于 BIM 技术、挣得值分析以及WBS 方法，对工程项目中产生的成本进行了分析，结果发现 BIM法在对项目成本以及进度管理控制过程中有较好的效果[23]。田旻等（2019）对 CCPM 方法的核心进行了拓展和升级，构建了具有鲁棒的多模式资源调度模型，并对结果的有效性进行了验证和分析[24]。谭泽涛（2019）对一些影响工程进度计划的因素从不同方面进行了 CPM 和动态管理的方法分析，并给出了一些切实可行的指导建议[25]。赵宏（2019）将挣得值法应用于建筑施工，并验证其能够有效控制成本、增加企业的经济效益[26]。张凯钧等（2020）将 CCPM 引入到项目管理中，验证了CCPM 比 PERT 和 CPM 的方法在手机开发技术领域的项目管理中更具有优越性[27]。

# 1.3.3 研究现状评述

针对以上国内外学者的相关研究可以发现，项目管理的研究和相关应用已经渗透到各行各业当中，并且研究的方向和方法也更加的具体化。网络计划技术、关键路径法、关键链法、WBS 工作分解结构、挣得值分析法等仍然是学者们在进行项目研究中采用的重点方法。这些研究虽然开辟了一些更先进的方法和技术，以研究项目进度管理的相关理论基础，但通过大量的案例和分析，可以比较出在项目管理的相应领域中哪些方法更具有自己的独特优势，从而为下一步的研究提供了新的思路，同时也是一种优化和发展原有方法的研究方法。

# 1.4 研究内容与研究方法

# 1.4.1 研究内容

本文首先对国内外工程项目进度管理相关研究进行梳理、分类，重点对系统建设工程进度管理进行深度学习，奠定进度计划及进度控制的理论基础，同时，运用 WBS 工作分解技术、网络计划技术以及挣得值分析技术等项目进度管理方法整合 H 镁矿智慧能源集中监控系统集成项目中的任务、时间和资源。其次结合 H 镁矿智慧能源集中监控系统集成项目实际情况，对项目计划进行编写，并合理运用项目控制管理理论着重对项目计划中的任务、时间和资源等重要因素进行优化，对可能产生的偏差进行分析，给出切实可行的控制与保障对策，提升项目实施效率，从而对工程项目进度管理进行具体、全面的研究。按照此研究思路，本文的主要研究内容如下：

（1）通过对相关国内外文献资料的查阅以及学习掌握一些基本的理论和方法，根据国家政府部门的相关规章制度以及 H 镁矿智慧能源集中监控系统集成项目的相关背景，结合整个项目的具体情况，对 H 镁矿智慧能源进度管理过程中出现的一系列问题和相关影响因素进行重点分析。

（2）分析项目任务和条件，结合企业资源和项目要求，编制项目进度计划，确定进度总目标和分目标。同时，构建项目组织结构，应用工作分解结构（WBS）分解任务，并根据项目活动的持续时间以及相互逻辑关系，给项目活动排序并制作网络计划图，应用关键路径法确定关键工作。

（3）合理应用各种控制机制及控制原理对项目进度进行有效控制，选择挣得值分析法对项目进度进行有效控制，根据 H 镁矿智慧能源集中监控系统集成项目情况采取一些有针对性的措施来调整优化计划，为实现项目进度计划的顺利进行提供保障，提高经济效益和项目管理水平。

# 1.4.2 研究方法

（1）文献分析法

《文献研究法》是根据研究目的，通过网络等途径，查阅国内外相关的书籍、期刊、会议、学位论文等文献资料，从而对所要研究的问题有一个全面的了解和掌握的方法。通过查阅国内外相关文献资料，学习项目进度管理的理论知识，吸收和借鉴国内外相关项目研究的经验，为本文接下来的研究提供理论依据和指导方法。

# （2）案例分析法

案例分析法（case analysis）是一种基于企业情况，围绕企业管理问题对特定管理情景进行客观描述，并加以分析的方法。本文将对 H 镁矿的内外部环境进行分析，根据项目目标和企业的实际情况，应用项目管理相关知识对 H 镁矿智慧能源集中监控系统集成项目进度做出合理的计划与控制。

（3）实证调查与分析法

通过对 H 镁矿智慧能源集中监控系统的实际调查，对 H 镁矿智慧能源公司内部负责项目管理的相关专家和员工进行访谈，然后结合对专家的咨询以及收集到的资料进行分类整合并分析，发现 H 镁矿智慧能源集中监控系统集成项目在实施过程中可能会存在的相关问题以及解决这些问题的侧重点，从而为接下来研究的 H 镁矿智慧能源集中监控系统集成项目进度计划的制定和控制管理提供了有效的基础保障。

# 1.5 论文结构

本文共由 6章构成，具体章节安排如图 1.1所示。下面对本文章节结构进行简要地说明：

第 1 章为绪论。除介绍国内外相关研究现状外，主要介绍了 H 镁矿智能能源集中监控系统集成项目的相关背景、研究目的及意义，并对主要研究内容及研究方法进行了阐述。

第2章为相关研究理论概述。主要介绍工程进度管理的相关概念，工程进度计划，工程进度控制等内容。

第 3 章为 H 镁矿智慧能源集中监控系统集成项目概况。首先对 H 镁矿智慧能源集中监控系统集成项目的背景和基本内容进行了简要介绍，并对工程目标、工程组织结构和设计原理、工程重点、工程进度管理问题等进行了阐述，最后进行了分析。

第 4 章为 H 镁矿智慧能源集中监控系统集成项目进度计划的制定。主要是将工程进度表从工程工作分解和资源分配，确定工程活动持续时间和相互逻辑关系等方面进行编制，再从优化工程进度表的目标、方法、结果等方面进行优化。

第 5 章为 H 镁矿智慧能源集中监控系统集成项目进度计划的控制。主要介绍工程进度控制体系的构建、工程进度控制的关键因素，然后对工程进度计划进行综合评价，依据挣得值分析法对工程进度计划进行分析，对工程进度计划中存在的问题进行分析，最后对工程进度计划的落实提出保障措施。

第 6 章为结论与展望。总结本文的主要研究工作与结论，并对未来相关研究

的开展进行展望。

![](images/8292d522eba3f3f25ae92031f3e246e1ea15945cbb749571b8e617560a96cf40.jpg)  
图 1.1 论文结构  
Figure 1.1 The structure of the thesis

# 第 2 章 相关研究理论概述

# 2.1 项目进度管理

# 2.1.1 项目进度管理定义

项目进度管理（Project Schedule Management），是指在工程实施过程中，为实现工期目标所进行的一系列管理活动，在与质量和费用目标协调的基础上，采用科学和有效的方式来制定进度计划目标，编制项目进度计划以及资源供应计划，从而对进度进行控制[28]。工程造价管理、质量管理、范围管理与工程进度管理并称为工程管理中相互制约、相互影响的四个最重要的要素，对工程能否按时完成质量的满足发挥着重要的作用。工程进度管理首先需要根据工程的具体情况，对每项工作任务所需的时间进行估算，并通过计算确定工期，从而制定出工程进度管理的第一要素[29]。在项目进度计划制定后，通过对进度实施的监控来保证项目的顺利完成，如果遇到问题还需要及时纠偏。工程进度计划与工程进度控制一起构成工程进度管理。

依据项目管理知识体系指南的内容，项目进度管理主要包括以下几个部分：

（1）对项目整体规划进度的管理，提前制定相关制度，确保项目计划和进度管理工作的有序进行。（2）定义工程中的活动，把总体分割成若干小目标，并记载有关具体行动，以完成工程可交付的成果。包括活动清单，活动属性，里程碑清单，变更请求，更新项目管理计划。（3）确定每项活动的先后顺序及与各项目之间的逻辑关系，最终形成项目网络计划图。（4）估计项目活动的持续时间，结合以往的类似项目经验以及现有的资源情况进行估算，确定每一项活动所需要的时间。（5）编写项目进度计划，分析对应每个项目的影响因素。（6）项目控制与监督，对每个阶段的项目运行状况进行实时监管，确保项目正常运行。

# 2.1.2 项目进度管理作用

项目进度管理的主要目标是要在规定的时间内制定出合理、节约的进度计划，然后检查实际进度与计划进度是否相符，确保工程在实施过程中按时完成。项目管理受到时间、质量和成本三个方面的约束限制，一个项目能否保质保量的按时完成，不仅与组织的效益挂钩，还能极大程度上体现管理水平，进而影响组织形象。项目进度管理可以产生如下几方面作用：

（1）项目进度管理可以有效避免投入资源未得到预想收入的不良后果。项目是否能够在保证质量的情况下按时完成，在很大程度上投资方对项目的投资能否带来成效，也会相应对施工方的组织形象产生影响。因此一个科学的项目进度管理可以预防资源投入得不到预想回报这一后果的发生。

（2）项目进度管理可以协调各方资源。在制定项目进度计划的过程中势必要同参与项目的各方进行沟通，从而得到各项工作的所需时间，继而将统合与预测到的物质、人力、财务资源作为约束条件来制定符合本次项目要求的进度计划。在此过程中，可以对工程进度管理在不同时间所需时间、所需资源的等级进行预测，并赋予其有利于各方资源调配的不同优先级，使各方资源在不产生倾轧等情况下，在计划下能够很好地协同工作。

（3）项目进度管理可以满足严格的工期要求，在组织时间协调方面提供极大的支撑。在项目进度计划确立后，将针对各阶段工作落实的具体时间，在工程进度控制环节中采取监控调整，对整个进度方案进行梳理，对进度方案中的各种资源进行调控，发现偏差及时采取措施予以纠正，有利于严格有序地按方案实施工程进度。

（4）项目进度管理可以帮助组织提高效益。组织的生产效率在项目进度与工程进度的合理控制下得以提高，而人工费用、设施租用费用等组织的生产成本也将随着建设时间的缩短而下降，从而提高了组织效益。

# 2.2 项目进度计划

# 2.2.1 项目进度计划定义

进度是指项目在开展过程中每项活动的时间安排，是组织在实施项目时需要重点关注的一个因素。项目进度计划是指由项目的里程碑、项目活动、可交付成果和预计开始和结束时间的一系列总和。工程进度需要考虑工程的规模，工程的复杂程度，工程的工期，细节的把握，工程的工艺水平，资源和造价等各方面的情况。制定项目进度计划是进行项目进度管理的一个关键步骤，是组织在规定的工期时间内按照要求的质量标准完成整个项目目标的指导性文件。制定项目进度计划主要是为了确定项目中各阶段工作的合理完成时间以及对应的逻辑顺序，协调项目执行过程中的各种人员调动、资源分配、时间限制等。

# 2.2.2 项目进度计划方法

（1）甘特图

甘特图也称为条形图，通过在横轴上绘制项目的活动时间来直观地表达项目的执行情况。线条的起点和终点代表着项目的开始和结束时间，线条的长度表示各项活动的持续时间，如图 2.1 所示。甘特图的优点是容易编制和理解，缺点是仅仅反映了完成各项工作所需时间及其先后顺序，因此只适用于一些简单的项目，无法满足复杂项目的要求。各项工作之间关系复杂，很难通过线图清晰地描述出来，而且难以进行定量分析。

![](images/8f7a7f0cf109b066ed2988e6f783fbc83b920af589998389db7b79251c03dbd9.jpg)  
图 2.1 甘特图  
Figure 2.1 Gantt chart

（2）里程碑计划法（Milestone Planning Approach）

里程碑是指标志着项目取得重大进展的项目中的关键事件，如某一阶段的活动在项目中完成或刚刚开始等。里程碑计划包含了项目中所有里程碑的逻辑关系和时间顺序，如表 2.1所示，以总体目标为导向，以目标分解结构为基础。

里程碑计划的步骤是倒推法（RevolutionAct），先确定项目的最终里程碑，然后根据其反推其他里程碑，绘制里程碑计划图或表格，通过制定目标分解结构，并评估和修正里程碑计划。值得注意的是，里程碑计划中里程碑数目过多或过少也会对工作人员产生不良影响，因此需要根据行业成熟度、历史经验和项目信息来对里程碑计划进行调整[30]。

表 2.1 里程碑计划  
Table 2.1 Milestone planning   

<table><tr><td>工作时间</td><td rowspan="3">2021年</td><td rowspan="3">2022年</td></tr><tr><td>工作任务</td><td></td></tr><tr><td>9月</td><td></td></tr><tr><td>开工 任务1完成</td><td>△11月</td><td></td></tr><tr><td>任务2完成</td><td>12月</td><td></td></tr><tr><td>任务3完成</td><td></td><td>2月</td></tr><tr><td></td><td></td><td></td></tr><tr><td>任务4完成</td><td></td><td>5月</td></tr></table>

（3）网络计划技术

网络计划技术（Network Plan Technology）是指利用网络图制定项目进度计划并对其进行控制的方法[31]。网络计划技术依据建立合理科学的网络图、估算出的网络参数、找寻的关键路径对项目进度进行控制，指引目标顺利完成。网络规划图包含能反映各作业间逻辑流程的箭线、节点和网状图形；对项目进展中的人、事、物的时间要素，网络参数都有明确的指示；关键路径是在进度管理中需要重点关注的项目过程中的关键作业流程。同时，网络计划的建立也伴随着不断的迭代更新与优化。利用网络图编制项目中各项工作之间的关系，根据时间约束找出关键路径，项目管理中比较常用的两种网络计划技术是计划评审技术（PERT）和关键路径法（CPM）。PERT 在新项目的研究和开发中应用较多，而 CPM 主要应用在有一定经验的承揽项目中。

计划评审技术是指在无法确定项目部分或全部工作持续时间的情况下，利用统计学的相关方法，以项目工作期望的平均持续时间代替网路图中的相关工作持续时间，用估算和分析的方法消除不确定性的一种技术[32]。PERT 可以通过网络图清晰体现各作业间逻辑关系，比其关键路径法更注重不确定因素的影响。PERT用三点法来估计工作持续时间：在假设其服从 $\beta$ 分布的情况下，对每个工作期望的平均持续时间进行求得，即乐观时间（a）、最可能时间（m）、悲观时间（b）。乐观时间指可能完成作业的最短时间，最有可能的时间指最有可能完成作业的时间，悲观时间指最长可能完成作业的时间。某个特定工作的持续时间可表达为公式（2.1）：

$$
T = \frac { \mathbf { a } + 4 m + b } { 6 }
$$

方差可表达为公式（2.2）

$$
\sigma ^ { 2 } = ( \frac { b - a } { 6 } ) ^ { 2 }
$$

PERT 技术也存在自身的不足，其本质依旧是估计值而非精确的预估值，因此，如果项目进展时出现一些重大的影响因素，各个作业长期处于乐观或者悲伤的时间段，实际作业期望就会与预估作业期望相差巨大，进行估计的项目工期造成影响。

关键路径法从杜邦公司最初在化学工业使用后至今，依旧是被普遍运用的管理技术之一，其通过计算项目网络中各活动时间资源占用和时序安排，根据作业间相关逻辑关系建立网络模型，从而得到初步的网络图，在这个基础上，以顺推法估算最早开始和结束时间，以逆推法估算最晚开始和结束时间，以此来计算作业总时差和自由时差，工作进度安排最严格即总时差最小的作业组成的路径即为关键路径[33]。需要说明的是，这种方法存在一定的局限性，即在项目的实施过程中，各项工作的持续时间未必完全与预期一致，引起该问题的主要原因可能是资源受限的情况可能在项目实际运行过程中发生，或者关键路径上的各个任务信息通常不够全面。

在选择项目计划的方法时，通常需要考虑一下几个方面的因素：

（1）项目规模的大小。整个工程所需资源的投入，要看工程的总体规模如何。（2）项目的复杂程度。一般而言，项目的复杂程度与项目的规模大小呈正比，但是也可能有例外。（3）项目周期长短。在整个项目中，时间是最关键的制约。越复杂的工程往往会耗费更多的时间来制定工程计划。通常采用里程碑计划或者甘特图来制定项目的前期计划。（4）项目成熟度。越是成熟的项目，对于项目所需要的时间、成本、资源等方面的因素会有历史经验辅助决策，因此，在制定项目的进度计划时可以很好的控制。

（5）团队的专业水平以及技术的先进性。团队的专业水平决定了是否能够操作使用网络计划图及相应计算机软件来管理项目，技术先进性主要体现在现代化的项目管理设备、工具、以及软件的应用范围。

（6）关键工作的确定。重点工作的确定，直接决定着工程的周期时间，而要完成各项重点工作的时间总和，则决定着工程进度计划的制定。

（7）资源分配和客户需求。各个项目的完成需要处理好客户的需求以及不同资源的分配关系。

# 2.2.3 项目进度计划优化

制定好的工程进度表在实施过程中可能会受到多种因素的影响，因此有时需要优化工程进度表。工程进度表优化是工程管理中十分重要的一环，是评估、诊断、调整和完善工程进度表的过程，也是协调三者均衡分配关系的重要手段，即时间、成本和资源。通常情况下，优化工程进度计划主要是优化时间、优化造价、优化资源。

（1）时间优化

首先，针对工程中的一些突发事件，需要压缩工期，对工程进度计划进行调整。其次，项目工期是由关键路径的整个持续时间所决定，如果关键路径的整体时间比规定的工期时间段，说明指定的时间比较充裕，若关键路径耗时超过规定工期，则需要压缩关键路径的持续时间。一是按照网点规划图确定重点路线和工程工期；二是对照规定工期确定压缩时间的关键路径时长；最后，要考虑压缩后对工程质量、安全问题和工程造价的影响，还要考虑资源是否充足，才能确定可压缩关键路径上的关键工作。

# （2）成本优化

通过调整项目中各项工作的具体安排使整个项目成本达到最小化的过程，该过程必须满足项目时间与资源方面的限制。此外，诸多项目都会存在一些间接成本，包括管理成本、固定资产的折旧以及相关财务费用，通常情况下持续时间较长的项目其间接成本也较高。在进行成本优化时，首先要确定关键路径和关键工作，需要计算正常和最短的持续时间和成本，以及工程进度计划中每项工作的直接成本变化率；再在直接费用变化率中选取最小的重点岗位；其次，有些项目可能会出现拥有多条关键路径和多项关键工作的情况，这就需要从被压缩的工作时间中选择最小的时间来完成各项工作所能缩短的工作；最后，制定相应的时间表网络图，以便优化进度。

# （3）资源优化

由于资源分配不合理可能会导致一些资源浪费，因此，在项目实施过程中有必要对资源进行优化。资源优化的关键在于协调时间与资源间的关系，合理分配资源，减少供需不匹配的情形出现，从而达到优化资源的最终目的。资源优化主要分为两种不同的情形：资源有限条件下的任务工期最短的优化，此时，工作计划量与供给量不匹配，需要调整进度计划以减少甚至杜绝拖延时间。资源均衡优化在工期固定的情况下，此时，由于总工期是不可改变的，资源需求的变化是一条动态曲线，首先，在资源强度最大的时候，要对非关键工作的起始时间进行调整，使资源强度降低，资源需求的变化会随着时间的变化而逐步趋于平缓。

# 2.3 项目进度控制

# 2.3.1 项目进度控制定义

项目进度控制就是在根据已制定的工程进度计划，检查在工期规定时间内的实际进度与要求是否相符，如有偏差应及时查明原因，以便改正。由于项目的进度计划存在风险性，因此需要进度控制对其进行管理，使不确定因素对进度计划实施造成的影响降到最低。项目进度控制包括事前控制、过程控制及事后控制。事前控制指在项目实施前评估影响项目进度计划的潜在因素，并做好预案，防患于未然；过程控制是进度控制中最为重要的部分，需要及时掌握实际进度的相关信息与数据，并与项目进度进行比对，如若出现偏差则要及时纠偏，保证项目按计划进行，必要时可调整计划；事后控制即为出现问题后，及时纠正，把损失降到最低。

# 2.3.2 项目进度控制原理

（1）动态控制原理

由于工程本身是按照事先预定的进度计划实施的动态过程，因此，在工程推进过程中，要发现实际进度与计划的偏差，及时采取有效措施进行纠偏时，就需要不断地对进度进行检查、监测和调整。这是一个动态调整的过程，项目进度控制不断地在发现偏差、采取措施、计划调整的过程中循环往复，并以此推进项目顺利完成。

# （2）系统控制原理

在实际项目工作中，可能会存在很多影响进度计划的因素，必须从整体到局部、层层工程、层层任务层层分解进度控制目标，才能有效控制工程进度，形成工程计划控制体系。项目的负责人，都要按照进度计划规定的要求，明确分工、严格管理，形成一个完整有序的项目控制组织系统。

（3）封闭循环原理

项目进度的控制需要多次的进行监测、检查、发现并解决问题，不是一次性的工作，是一个循环的过程，同时总项目与各个子项目也有一定的范围限制，因此，形成一个循环封闭的系统。

# （4）信息反馈原理

在项目进度计划制定后，通过纵向控制网络从上到下传到给项目的工作人员，而工作人员再将实施计划过程中发现的问题从下至上反馈至项目负责人，再由项目负责人对进度计划进行调整修改，并再将修改后的进度计划传达到项目实施人员，不断循环这个信息反馈的过程，使项目向预定计划目标推进。项目控制管理中如果缺少信息反馈，就没有调整计划的信息基础，也就谈不上对进度计划进行控制。

（5）弹性原理

在项目实施的过程中，由于各种影响因素较多，难免会发生一些突发的情况，存在一定的风险，因此，在项目进度的编制过程中要依据经验尽可能地预估潜在的风险及其影响程度，并预留出一定时间应对未考虑到的风险，使计划具有一定的弹性。此外，弹性的计划也给后期时间优化及改变各项工作间衔接关系提供了空间，能够有效保证项目如期完成。

# 2.3.3 项目进度控制方法

项目进度控制主要包括以下几种常见的方法：

（1）横图比较法

横图比较法是指将工程实施过程中收集到的资料，利用横图线整理出时间，对照进度计划进行对照的方法。该方法简单直接，清楚直观，能够很好地反映项目进度偏差。

（2）项目进度跟踪报告法

项目进度跟踪报告法主要是为了及时监测并掌握整个项目进度的具体情况以及对发生偏离项目进度的问题进行及时地管控。具体通过文本的形式，将项目内容、项目实际进度、未完成原因、纠正措施与方法记录下来，有利于项目进度的审查。

# （3）挣得值分析法

挣得值分析法又称偏差分析法，主要是对工程进度和成本进行综合评价的一种方法，它是通过几个工程的重要参数（工程活动的预计费用，实际已完成的活动费用，预计已完成的活动费用）。

（4）S 型曲线比较法

S 型曲线方法中的横坐标为进度时，纵坐标为累计完成任务量，在相同时间内完成的工作与计划完成的工作进行比较，根据收集到的信息绘制出相应的 S 曲线。若实际进展点位于计划曲线左侧，说明工作内容完成快于计划估计；若位于右侧，则说明实际工作有些拖沓；若恰好与曲线重合，则证明实际与计划相符，目前尚无可调整之处。

（5）“香蕉”曲线法

“香蕉”曲线由两条曲线组成，一条是以每项工作最早开始时间为基准计算所绘制的进度曲线，另一条是以每项工作最晚开始时间为基准计算所绘制的进度曲线。两条曲线在项目的最终结束时间处汇合。由于两条曲线呈闭合状，形如“香蕉”，故称为“香蕉”曲线[34]。

# （6）甘特图比较法

在甘特图中用不同的颜色或者不同的线条，可以将已经完成的项目活动和还未开始的项目活动进行区分，从而可以直观地掌握项目的进度。

# （7）因果分析图比较法

因果分析图，也称为鱼骨图，是指借助于头脑风暴的方法对项目进度中出现的问题进行深度剖析，然后对每一个问题产生的原因再进行归类或总结，从而继续挖掘内在的原因，直到找到最为关键的影响因素[35]。因果分析图法一般在质量管理中比较常用，当项目在推进过程中发生了进度偏差，可以借助于因果分析法来寻找问题的根源。

# 2.3.4 项目进度控制过程

由于工程进度计划的制定并不能预测到工程实施过程中可能出现的所有风险因素，因此，确保一旦出现偏差能立即进行纠偏，从而确保工程计划与实际进度相一致，最终达到工程规定的目标，就需要在工程推进过程中不断地督促检查。此外，还应重点注意项目进度的控制应该与成本、质量等相匹配。通常情况下，工程进度控制过程首先从工程进度计划入手，然后实施工程，并在实施过程中动态监控，再对照进度计划，以判断两者是否相符。如果不一致，进一步分析产生差别的原因并考虑采取纠偏措施，最后进一步分析执行纠偏措施是否会影响原计划。具体的项目控制过程如图 2.2 所示。

![](images/fd3354df7e9737919b9e65f3a1517d7573a3d3bf9cac045ea59aaf8c04181c99.jpg)  
图 2.2 进度过程控制流程  
Figure 2.2 The process of schedule control

# 第 3 章 H 镁矿智慧能源集中监控系统集成项目概况

# $3 . 1 \mathrm { H }$ 镁矿智慧能源集中监控系统集成项目简介

# 3.1.1 企业概况

2021 年 3 月，为贯彻落实省发改委《关于进一步加快推进重点用能单位能耗在线监测系统推广建设的通知》，根据省政府关于节约能源相关工作的部门和质量管理技术服务监督等部门的要求，加强能源计量基础能力建设，结合现有能源管理信息化平台，开展为期 30 天的调研工作，秉承发现能耗问题、挖掘节能潜力、促进节能降耗、降本增效的宗旨，结合国家政策要求和企业内控需要，制定了《H 镁集团智慧能源集中监测管理系统总体设计规划方案》，简称 HM156 工程。根据，该项目定义为 H镁156 工程，简称 HM156 工程。其中，HM 是集团名称的手写字母，代表 H 镁集团。“1”代表了包括：信息网络系统、监控大厅和机房、应用系统开发平台等在内的涵盖 H 镁集团的信息系统集成平台；“5”代表，包括：能源集中监控系统、能源智慧集中监控管理系统、视频监控分析系统、企业门户网站管理等五大业务应用管理系统；“6”代表，包括：标准规范制度，安全保护制度，人才保障制度，管理调控制度，考核评价制度，技改制度等六大保障制度。

H镁集团作为一家国有企业，在资源整合、产业升级、企业兼并重组方面具有得天独厚的先天条件，将会迎来重要的发展机遇。常言道：“不谋全局者，谋不足而谋全局；”“万世者不足谋一时”，为此，镁集团需要高瞻远瞩，未雨绸缪，求真务实，在现有的决策、资金、管理条件下，提前布局，真抓实干，在企业管理、人才培养、“软件”升级等方面做出全面部署，力求在未来的产业整合中保持长久的活力和优势。

根据总体规划，H镁集团智慧能源集中监控管理系统工程是能源集中监控系统，通过能耗管理为抓手，融入现代化的管理理念和信息技术手段，一方面通过对煤、电、水、油、气等能源介质的能耗进行线监测、耗量平衡、统计分析、操作指导、优化运行等生产过程的集中监控管理，实现可视化、数字化、智能化管理；另一方面，通过能源集中监控系统，一方面，实现集约化、智慧化、现代化管理，通过集中监控管理、设备资产管理、物资管理、能源中心管理、办公管理等精细化管理软件；另一方面，通过新技术、新工艺、新装备的引进，实现技术上的升级换代，技术上的升级换代，装备上的升级换代。通过管理、技术、装备，多头并举，达到降低成本、提高效率、减少排放、增加效益的目的。

# 3.1.2 项目基本内容

由于受到外部环境、历史条件和管理所属等因素的限制，与国内外先进的冶金企业相比，H镁集团在“硬件”和“软件”上都存在较大差距，设备工艺上比较传统、企业管理上略显粗放、人才储备上有些后劲不足、信息应用上明显落后，主要表现在以下几个方面：

（1）“硬件”条件技术需要升级。

窑炉技术工艺比较传统、混配上料方式仍然手工作业、很多企业已经淘汰的电动机仍然在服役、污染物排放等仍需要深度治理。

（2）“软件”条件需要加强。

$\textcircled{1}$ 信息技术应用在很多行业“创一流”评比中是必备条件，同时也是现代化企业管理的必备工具和手段。H 镁集团在信息技术应用上相对落后，没有相关软件系统作为支撑，管理略显粗放。

$\textcircled{2}$ 由于过去多年没有引进高校人才，造成人才结构偏“老化”，使得人才储备后劲略显不足。

$\textcircled{3}$ 管理机制偏向“传统国企”，缺乏“赛马机制”，员工的积极性、主动性、创造性未得到有效发挥，管理提升潜力巨大。

结合前期的调研工作，参考国内外镁业发展现状以及H镁集团的实际情况，项目主要设计了以下几个方面的内容：

$\textcircled{1}$ 信息网络系统。信息网络系统是企业信息化建设的基础，包括：企业内部局域网络，涵盖办公信息网，生产信息网，监控中心和机房，大屏幕系统，外网等。

$\textcircled{2}$ 能耗集中监控系统。主要包括：能源消耗(电、煤、气、油等)在线监测，电力供应在线监测，能耗消耗平衡，能耗数据分析等模块和多功能系统，如统计报表系统。

$\textcircled{3}$ 能源智慧管理系统。包括：交接班、运行日志、运行记录、两票、运行报表定时存储、资料分析等管理模块，实现能耗监控中心运行的无纸化办公。

$\textcircled{4}$ 高效办公平台。主要有：办事须知，办事流程审批，办事查询，信息发布等内容。$\textcircled{5}$ 供电无人值守监控系统。包括：六十六千伏变电站监控系统，运管系统，视频监控等内容。$\textcircled{6}$ 企业门户管理系统。其中包括：外网资讯发布，内网资讯发布，高效办公平台等内容。$\textcircled{7}$ 能源管理中心运行管理。能源管理中心的管理机构设置、人员配置、相关职责、管理方式、管理制度、运行规程等建议。$\textcircled{8}$ 保障体系搭建的建议。标准规范体系、安全防护体系、人才保障体系、管理调控体系、评价考核体系、技术改造体系建议。

# 3.1.3 项目工程目标

H 镁集团智慧能源管理系统工程——能源集中监控管理系统的建设目标主要分为管理目标和系统建设目标。管理目标是以能源管理为抓手，开发集团的智慧能源集中监控(中心)系统，将能源管理中心打造成为： $\textcircled{1}$ 能耗集中监控中心、$\textcircled{2}$ 节能管理中心、 $\textcircled{3}$ 生产调度中心、 $\textcircled{4}$ 生产辅助决策中心、 $\textcircled{5}$ 生产数据管理中心、$\textcircled{6}$ 发改委数据传输中心。

系统建设目标是建设一个覆盖全H镁集团的能源实时监控、集中调度管理、运行管理、供电监控系统、能源数据分析、能耗统计报表、高效办公系统、辅助决策等信息系统的一体化平台系统。整个项目从 2022 年5 月25日开始，计划于2023 年 1 月 7 日完成。因此，本集成项目的工程目标主要包含以下几点。

（1）工程质量目标

针对该项目的特点，在整个项目实施过程中，每一阶段的任务都要保证质量，以确保 H 镁智慧能源集中监控系统集成项目能够高标准完成任务。由于升级改造，可能部分电路会存在一些隐患，因此需要提前做好相应的准备，避免因为临时突发事件给项目进度计划造成偏差。因此，对每一项工程活动的质量过程都需要进行有效的监督，以保证最终可以高效地完成工程质量目标，顺利通过验收的基础上，确保可以实现工程质量目标，达到过硬的标准。因此，在项目活动中应遵循以下原则：

“以人为核心”：把人作为工作的关键，提高员工的素质，调动员工的积极性和创造性，强化员工的责任意识，在员工之间树立“质量第一”的价值观，保障工作质量也就是保障过程质量，通过提高工作质量来促进工程上的质量，从而减少一些人为的失误。

“以防为主”：即改变结果控制为全程控制，即由事后把关质量向事前控制质量、事中控制转变；由检验产品质量向检验工作质量、检验工序质量、检验中间产品转变。

（2）环境和职业健康安全目标

根据《项目施工合同条例》要求，严格执行《建设工程安全生产管理条例》（中华人民共和国国务院令第 393 号）等有关规定，避免在项目实施过程中可能出现的任何死亡事件以及减少重伤事故的发生。此外还需要根据公司《环境与职业卫生安全管理年度工作方案》的要求，做到目标、指标三落实：

$\textcircled{1}$ 全年杜绝了因机械设备、火灾、交通、施工造成的人员死亡事故和中暑、中毒事故的发生；$\textcircled{2}$ 杜绝因工重伤、死亡事故发生，全年工伤事故发生频次控制在千分之一点零以内；$\textcircled{3}$ 建筑工地安全施工合格率要求达到 $100 \%$ ，严格避免工地不合格情况的发生，此外还要避免出现被上级批评通报的现象。

（3）文明施工目标

$\textcircled{1}$ 有整套的施工方案;  
$\textcircled{2}$ 有完善的建设指挥体系和岗位职责体系;  
$\textcircled{3}$ 程序衔接交叉合理，明确交接职责;  
$\textcircled{4}$ 临时设施和各种材料摆放整齐;  
$\textcircled{5}$ 为施工人员配备安全帽，使其具备持证上岗的条件;  
$\textcircled{6}$ 机具设备完好、使用合理、符合消防及安全要求的施工作业。

（4）成本目标

H镁矿智慧能源集中监控系统集成项目在制定进度计划的过程中，通过提前对所需要的资源进行预估可以避免后期施工过程中的资源浪费问题。项目在推进过程中也可以根据实际需要对资源进行协调，严格控制各项成本开支，达到项目

部与公司签订的内部承包合同要求。

（5）服务目标

在维修保障时限内，实行 24 小时全天候服务，随到随办。过了质保期后，依然可以随叫随到，让业主放心使用，及时提供必要的服务。

下表 3.1 是整个工程的概况。

表 3.1 工程概况  
Table 3.1 Project Overview   

<table><tr><td>序号</td><td>项目</td><td>内容</td></tr><tr><td>1</td><td>工程名称</td><td>H镁集团智慧能源管理平台项目</td></tr><tr><td>2</td><td>建设地点</td><td>H市</td></tr><tr><td>3</td><td>工程范围</td><td>配电室、天然气站、重油站、戊烷站</td></tr><tr><td>4</td><td>质量标准</td><td>符合验收规定，确保工程达到优良标准</td></tr></table>

# 3.2 项目组织结构及设计原则

# 3.2.1 项目组织结构

根据工程实际情况，施工项目部必须面对施工现场，组织机构层次要少，人员要精干，一岗多职，并在下图 3.1 所示的施工组织机构框架内，建立各部门的岗位责任制。

项目经理职责：主持项目管理实施方案的编制和项目目标的系统化管理，项目管理目标责任书规定的职责；动态管理资源；建立与组织实施相适应的各类专业管理制度；利益分配在授权范围内进行；做好项目资料的收集和结算资料的准备工作，参加项目的竣工验收工作。

驻厂项目经理职责：对一些专业技术知识进行把握；处理在施工中可能会产生的一些疑难问题；负责协调各部门之间的关系；严格控制项目的施工进度。

![](images/de9d0f50848d109a3812296b1368ca6a83802f85fb9bf796ce1d2c3fea008880.jpg)  
图 3.1 项目组织结构图  
Figure 3.1 Organizational chart of project

软件工程师职责：软件工程师一共有 5 人，完成软件系统全部功能，负责对接软件全部问题。

硬件工程师职责：硬件工程师一共有 5 人，完成硬件系统全部功能，负责对接硬件全部问题。

项目协调经理职责：负责项目的事宜协调工作。

# 3.2.2 项目设计原则

系统设计决定了系统的整体架构及性能，设计遵循的原则决定了设计成果的技术、性能、功能的实用性，H镁集团智慧能源集中监控管理系统的设计需要遵循以下基本原则进行：

（1）实用性与先进性

在企业网络的设计中，应该要重点关注实用性和易操作性，系统需要有效管理和易于维修，从而有助于用户理解，方便学习和和日常使用，这一点在网络设计在对企业现有的相关设备和可以利用的资源，并且确保已有的相关投资的同时，利用一些技术成熟的网络技术、设备和通信技术。

当前网络技术发展较快，新设备不断涌现并趋于成熟，应该尽量使用那些能够适应企业未来计划以及发展需要的一些先进并且可以实现的网络技术和基本的通信设施，这样可以有助于提高计算机网络应用的技术水平，以满足实用性为基础，起点要高。

（2）开放性与标准化

在整体设计上，要让网络扩展起来非常便捷，针对一些比较独立的分系统要容易组合以及调整，选择开放式的网络架构。拥有一定的能力可以适应外界环境的动态变化，如果外部出现一定的环境变化时，搭建的系统可以在新的环境下运行，而不需要修改或只需要很少的修改。

网络选用的通信协议和设备应与国际标准或工业标准接轨，有机地结合不同的应用环境和网络的不同优点。即把网络的硬件环境，通信环境，软件环境，运营平台之间的相互依存关系降低到最小程度，把各自的长处发挥到最小程度；同时，要确保网络互联互通，创造有利条件，使信息互通，应用互通。

（3）可靠性与安全性

H 镁矿智慧能源集中监控系统建设应该主要保证系统的安全可靠运行为根本。对于一些特殊的能源调度系统需要确保高的可靠性，并且每个级别的网络都要具备一定的管理和监督能力，此外还需要考虑一些关键的设备和线路是否冗余，在线修复、替代、扩容等都是可以的。要确保系统正确，确保数据传输正确，确保防护设施必须到位，防止出现异常情况。

为确保网络安全运行，拒绝非授权访问，根据具体情况采用网络隔离、防火墙、虚拟网(VLAN)等安全控制措施。

（4）经济性与可扩展性

制度建设也要在完成制度目标的基础上，着眼于经济性，力求投入最少、成效最大；同一张网的建设，要能随着企业的成长而不断壮大。应用系统的开发要根据现场管理的需要和人员应用水平不断扩展，以保证系统的发挥积极的作用。

（5）可操作性与易维护性

系统设计需要考虑界面、功能、操作等方面灵活、简洁，便于操作，确保使用人员能够快速学会并且熟练操作，以 WEB方式及图形方式为主。另外系统要尽量模块化建设，便于系统维护，以最大可能的满足生产实际的需要和功能拓展的需要。

# 3.3 项目实施要点

H 镁矿智慧能源集中监控系统集成项目实质是对已经运行的镁矿产品生产加工运营活动进行升级改造，因此这与新建的项目相比，对进度计划、控制管理的要求更高。当进行工程改造时，可能会涉及到对一些覆盖范围的区域停电，因此需要与用户沟通协调停电时间和范围。此外，智慧能源集中监控系统集成项目的质量直接影响到整个监控集成系统的可靠性，如果质量无法得到有效的保证，可能会对整个系统造成安全隐患。

H镁集团在软件上的升级改造也是项目的关键，这些软件问题的解决对于未来行业整合和企业兼并重组所做出的战略布局具有重要意义。

# 3.3.1 搭建智慧能源集中监控系统一体化平台

以能源管理信息系统建设，搭建集团信息技术一体化平台，为企业智慧化、精细化、现代化管理提供工具及手段的支撑。H 镁矿智慧能源集中监控系统建设的总体目标是：系统并充分考虑先进成熟的技术，遵循优良的布局设计，合理的设备应用，友好简便的界面，有序实用的功能，良好的升级扩展性，构建一套集中监控系统设计，以满足对整个智慧能源项目进行有效监控的需求，并充分考虑先进、稳定、实用、集成、扩展性和经济性的原则。“假舆马者，非利足也，而致千里；假舟楫者，非能水也，而绝江河”，企业应对激烈的市场竞争和发展壮大的必经之路，就是要充分借助现代化信息化手段，以信息化带动企业现代化，实现跨越式发展。

# 3.3.2 引入先进的管理理念和方法

以应用软件系统为主线，引入先进的管理理念，并加强培养具有现代化企业管理视角的人才梯队。应用软件的过程，是先进管理思想引进消化的过程，同时也是企业自身管理流程梳理再造和提升改良的过程。所以设计，开发，引进先进，高效，实用的管理软件是当务之急。通过引入先进的管理方法，对巡检计划管理、巡检周期管理、巡检路线管理、巡检项目管理、巡检工器具管理、巡检标准化管理、巡检人员管理等制定标准的管理方法。巡检计划是预先制定好系统中巡检任务、时间安排、巡检人员、巡检路线等工作任务分配，预先配置好巡检设备和系统到后台系统中，并能实现工作流审批或自动生成巡检计划。巡检周期管理是对设备及系统分类列表维护，按照相关运行规程配置巡检周期，人员要求及巡检要求，并可以设置系统提示周期及提示条件等。巡检路线是巡检过程的路线图，是根据设置巡检计划后自动生成的路线图，通过模拟流程图或位置分布线路图的形式展现出来的，具体的巡检内容、巡检方案、巡检任务、巡检人员等相关信息，都可以点击路线图相关的巡检设备组进行查看。巡检项目管理是对设备及系统需要巡检的部位、内容、方法、参数等进行分类，列出相关明细。巡检工器具管理，主要包括查询工器具的详细情况、工器具的要求、工器具的使用方法、特殊需要借用的工器具的借用工作流、巡检工器具一般与巡检策略相关联使用等，是对巡检时需要佩戴的工器具进行配置的管理。巡检记录主要包括运行参数、维护检查记录、设备系统照片、试验数据、采集数据等，对需生成缺陷单的超标或按规程设定的异常记录、触发缺陷管理系统启动工作流审批模块的缺陷单、缺陷单或需审批的缺陷记录，进行相关记录的维护、查询、统计等管理。巡检标准化是对常规巡检内容、巡检方式、巡检方法、巡检流程、巡检工器具、巡检记录、巡检术语等按照有关规程进行规范化、规范化的维护管理，并能不断地对系统数据进行维护和补充，便于巡检人员实时查看和对照，同时为新员工提供培训课件和案例。巡检员管理是根据配置好的周期提示相关人员进行相关设备的巡检，主要包括：人员基本信息、岗位、权限、巡检内容等维护、查询、提示等，根据人员的岗位职能授权相关巡检内容、巡检项目、巡检周期配置等。

# 3.3.3 完善企业生产经营管理制度

完善集团生产经营管理制度、细化流程管控、重塑考核评价机制、优化管理流程等，以建设 H 镁矿智慧能源集中监控系统项目为契机，为全面提升企业现代化管理水平发挥“示范窗口”作用。这也为企业未来的产业升级和兼并重组奠定基础和增加筹码。在企业管理上、人才培养上、“软件”升级上提前布局、真抓实干、全面部署，力争在未来的产业融合中继续保持着长久的生命力和优势。一方面，实现集约化、智慧化、现代化管理，通过集中监控管理、设备资产管理、物资管理、能源中心管理、办公管理等精细化管理软件；另一方面，通过一些新技术、新工艺、新装备的引进，实现技术的更新换代，技术的更新换代，装备的更新换代。通过管理、技术、装备，多头并举，从而达到降低成本、提高效率、减少排放、增加效益的目的。

# 3.4 项目特点及可能存在的问题分析

# 3.4.1 H 镁矿智慧能源集中监控系统集成项目特点分析

H 镁集团的智慧能源集中监控系统集成项目与普通项目存在着非常明显的差异，这就给工程进度管理带来了一定的难度和挑战，使得工程进度管理在实施进度管理过程中存在着诸多不确定性。

首先从项目自身情况来看，存在如下的特点：

（1）H 镁矿智慧能源工作环境特殊，涉及到不同的工作种类和大量的工作人员，地理位置较为分散，横向距离路线长，需要升级改造的计量表分布范围广，对应的监控系统覆盖面大，监控设计方案比普通的项目要复杂很多，给项目的实施带来了一定的挑战。

（2）整个系统需要与其他系统进行集成（包括计量数据智能采集系统，能耗（电能、煤、天然气、石油）在线监测系统、能耗数据分析系统以及统计报表系统等），测试内容特别复杂，和其他系统之间的交互性较强，容易产生相互干扰导致监控数据被延迟的结果。

其次，从 H 镁集团的项目条件来看，存在以下特点：

（1）项目团队现状。项目经理刚调到团队来不久，团队管理经验不够丰富，对团队成员的情况了解不够全面，在一定程度上没有默契度，在项目过程中管理沟通上存在一些挑战。此外，对于项目管理上的一些分工合作可能缺乏针对性和有效性。

（2）能源计量现状。现有电力抄表系统二级监测点位布局较为分散，基于供电线路所统计的几处电量是几个不相干生产部门的用电总和，而几个不相干部门各自的用电量无法拆分。现有电力抄表系统数据提供及时度不高，不能达到秒级的数据采集的频度。天然气表计共 1 台，具备通讯接口，数据可上传辽宁省能耗在线监测平台，可直接取数据。重油、戊烷、柴油、矿石、原材料及产品共有计量地磅共 8 台，分别计量不同的燃料和产品，重油罐、戊烷罐等无计量仪表，柴油有加油站，3 只加油枪，可以计量。

（3）监控系统现状。现有的监控系统都是各个部门或厂房独立配备的，没有进行一个统一的集成，且无法实现远端无线传输功能。此外，由于使用时间较长，一直没有维护，经常出现监控信息中断或者没有及时反馈等问题。而且，监控设分辨率低，只能连续记录一周的时间，历史数据存储空间较小。实时数据监控系统是一个监控全集团实时信息的系统(包括将各种形式的生产实时数据信息集中在一个强大的数据库中)，该系统可以在适当的时候向相关人员发送正确的信息，一方面可以支持能耗平衡、价值核算、效能分析、优化运行等，此外，它承担着控制与管理之间的过度和连接的作用，是整个能源控制中心应用系统的核心基础系统，同时也是整个能源管理与控制中心应用系统。

# 3.4.2 项目可能存在的问题分析

项目的进度管理具有几个典型的特征，首先是进度管理的动态性。相应的进度管理也是动态调整的，因为项目在推进中是一个动态变化的过程。其次，工程进度管理是有阶段性的，可以把整个工程进度管理分成若干不同的阶段。进度计划存在一定的不均衡，同时也具有较大的风险。

参考工程进度管理的特点，分析 H 镁集团智慧能源集中监控系统集成工程的实际情况，可以归纳出：有关人员因素、组织管理因素、资源因素、现场条件因素、资金因素、环境因素等可能影响工程实施进度的有关因素，具体如图 3.2所示。

![](images/7e0697d1dce008a0bfb15c2f486325a9550f4858cff5f8b0e3cd481fea09d608.jpg)  
图 3.2 项目进度影响因素分析  
Figure 3.2 Analysis of factors affecting project progress

（1）相关人员因素：由于项目经理来到团队的时间不是很长，缺少相关项目的历练，因此存在经验欠缺的问题，此外团队配合默契度不够，不同成员在一起的工作时间不到 1 年，经历的项目次数较少，员工对于一些硬件的安装和调试过程不够熟练，因此可能会导致项目进度的不确定性，在计量设备的安装过程中

可能会存在一些挑战。

（2）组织管理因素：由于整个项目非常复杂，涉及到的各种活动很多，因此不同部门在进行沟通的时候可能存在交流不畅的情况，从而给后续工作的衔接带来不便之处。各部门之间配合不积极，拖延任务完成时间。一些工人的自我约束不够，工作态度较为散漫，影响活动的进程。此外，团队成员之前未组织对项目方案的可行性进行有效论证，这可能为接下来的项目任务的实施造成一定的隐患。

（3）资源因素：整个项目划分的具体任务非常多，存在工作人手不够的情况，而且在施工过程中发现有些材料质量不合格（例如，计量表外部有损坏），耽误硬件更换升级的完成时间。此外，有些配件供货不及时，造成个别部门出现等待的现象。由于现在使用的计量设备是很久之前安装的，因此智能化程度难以满足现在的需求，需要大量的替换安装和调试。在智能化程度不够的情况下，采用高度的人工辅助方法进行计量，这也导致了无法对三级单位的计量数据进行有效统计。

（4）现场条件因素：由于计量设备非常分散，硬件的更换升级涉及到的地点特别多，覆盖范围比较广，因此对技术人员比较辛苦，需要去往不同的地方工作，而且需要更换的设备特别多，人手不太够，对技术人员的要求比较高。此外，由于整个监控系统布局非常复杂，目前也没有形成一个有效的监控网络，无法实时对整个系统的能耗进行有效监控。

（5）资金因素：项目初期公司对项目建设资金预算不到位，导致临时筹措资金比较麻烦，而且需要经过不同部门的审批，流程速度慢效率低。项目建设周期比较长，中间可能存在一些突发状况需要应急资金解决。此外，也没有对软件和硬件进行相关的功能检测。

（6）环境因素：目前的基本情况是项目现场的 4G网络信号不太稳定，经常出现中断的现象，这就直接导致了无法进行有效的数据采集和传输，并且不能实现同步的功能。因此，最终也很难实现对收集的稳定数据进行实时的检测、分析以及统计。

# 3.5 本章小结

本章主要从 H 镁矿集团的概况、H 镁矿智慧能源集中监控系统集成项目的基本内容、项目工程目标这三个方面来介绍 H 镁矿智慧能源集中监控系统集成项目。然后对项目组织结构和设计原则进行了介绍，并阐述了项目关键点即项目实施过程中的一些关键要素。与此同时，通过对类似的技术改造项目进度管理进行调研后，分析出 H 镁矿智慧能源集中监控系统集成项目的特点以及项目进度管理中存在的问题，为接下来的项目进度管理优化奠定基础。

# 第 4 章 H 镁矿智慧能源集中监控系统集成项目进度计划的制定

# 4.1 项目工作分解与责任分配

# 4.1.1 项目工作分解

WBS 是一种在项目管理阶段对项目任务进行分解的技术，主要是为了后期项目推进过程中便于对具体任务进行高效的管理。通过将整个项目的所有任务逐一分解为各个阶段的工作，为项目的实施和进度计划奠定基础。对 H 镁矿智慧能源集中监控系统集成项目内容进行分析后，按照项目的生命周期将其划分为五个阶段。

（1）项目准备阶段

在项目准备阶段，项目经理需要对整个项目要求进行再次确认和仔细审查，然后从技术需求和功能需求的角度对整个项目方案的可行性进行分析。技术和功能的实现直接影响到项目的质量和最终的结果，因此在签订协议前需要再次确认清楚。一旦确认无误签署协议之后就可以正式开始实施项目任务。这一阶段主要分为项目确认和签订协议两个任务。

（2）计量表改造阶段

计量表的改造属于整个智慧能源集中监控系统集成项目的基础部分，现有电力远传抄表系统基本实现了集团内二级用电单位和转供电量的结算。然而，电力抄表系统在每月统计电量的时候智能化程度达不到预期目的，需要高度的人工干预。电力抄表系统只适用于二级单位电量的统计，针对精细化能管需要采集的电力数据三级深度监测是不能胜任的，监测的点位明显不全。

（3）云平台搭建阶段

对于智能化升级过的计量表，需要搭建一个可以采集数据和深度监测的云平台。计量数据采集传输网络均采用无线4G/5G网络，将数据传输到云平台，并实现相关监测、分析、统计等功能。云平台搭建阶段主要完成系统设计、平台搭建、无线调试三个任务。

（4）监控系统设计阶段

监控系统设计是对整个能源系统及变电站系统进行集中监控，采用WEB方式进行软件系统信息发布，各客户端(各级领导、管理人员及有权限访问的用户)均可通过浏览器查看各能源系统的实时数据，能耗均衡的实时数据，统计报表信息的实时数据信息等。该阶段主要完成结构设计、光学设计、电路设计、零配件设计、面板设计、实用性设计六大任务，完成能源系统和变电站系统的监控平台搭建和运行工作。

（5）测试阶段

测试阶段主要针对前面搭建好的平台和系统进行测试，确保可以正常持续运行。因此，在这个阶段的任务主要包括硬件功能测试、硬件连接测试、软件功能测试、软件连接测试、集成数据测试三部分。

（6）项目完成阶段

整个智慧能源集中监控系统集成项目完成后，最后一步是对项目的评审和验收，确认无误后就可以进行交付了，最终整个项目到此结束。这一阶段主要包括评审验收、完成交付两个任务。

对该项目的内容进行工作分解结构，最终结果得到如表4.1所示。

表 4.1 工作分解结构表  
Table 4.1 Work breakdown Structure   

<table><tr><td>一级子任务</td><td>二级子任务</td><td>工作要点</td></tr><tr><td rowspan="2">1.项目准备</td><td>1.1 项目确认</td><td>仔细检查项目内容</td></tr><tr><td>1.2 签订协议</td><td>确认无误后签订合同</td></tr><tr><td rowspan="3">2.计量表改造</td><td>2.1 硬件替换</td><td>智能化升级</td></tr><tr><td>2.2 安装调试</td><td>安装完成后调试软件</td></tr><tr><td>3.1系统设计</td><td>确定使用的系统</td></tr><tr><td rowspan="4">3.云平台搭建</td><td>3.2 平台搭建</td><td>确定服务器、通讯协议、采集传输</td></tr><tr><td>3.3无线调试</td><td>对搭建好的平台进行调试</td></tr><tr><td>4.1 结构设计</td><td>对监控系统的结构进行设计</td></tr><tr><td>4.2光学设计</td><td>对监控系统的光学进行设计</td></tr><tr><td rowspan="3">4．监控系统设计</td><td>4.3 电路设计</td><td>对监控系统的电路进行设计</td></tr><tr><td>4.4 零配件设计</td><td>对监控系统的零配件进行设计</td></tr><tr><td></td><td></td></tr></table>

# 续表 4.1

Continued Table 4.1   

<table><tr><td rowspan="6">5．系统测试</td><td rowspan="5"></td><td>4.5面板设计</td><td>对监控系统的面板进行设计</td></tr><tr><td>4.6 实用性设计</td><td>对监控系统的实用性进行设计</td></tr><tr><td>5.1 硬件功能测试</td><td>对整个系统的硬件功能进行测试</td></tr><tr><td>5.2 硬件连接测试</td><td>对整个系统的硬件连接进行测试</td></tr><tr><td>5.3 软件功能测试</td><td>对整个系统的软件功能进行测试</td></tr><tr><td rowspan="3"></td><td>5.4 软件连接测试</td><td>对整个系统的软件连接进行测试</td></tr><tr><td>5.5集成数据测试</td><td>对集成系统进行数据测试</td></tr><tr><td>6.1评审验收</td><td>对整个项目进行评审和验收检查</td></tr><tr><td rowspan="2">6.项目完成</td><td></td><td></td></tr><tr><td>6.2完成交付</td><td>交付整个项目</td></tr></table>

# 4.1.2 项目责任分配情况

由于该项目涉及到的任务比较多，属于任务密集型项目，项目过程中需要对人力资源进行合理的分配，才能有助于提高项目的效率。该项目人力资源分配情况如表4.2所示。

表 4.2 人员分配列表  
Table 4.2 Human resources assignment list   

<table><tr><td>人员编号</td><td>职位名称</td><td>主要任务</td></tr><tr><td>1</td><td>项目经理</td><td>项目的任务安排、管理</td></tr><tr><td>2</td><td>驻厂项目经理</td><td>现场事物的负责和监督</td></tr><tr><td>3</td><td>硬件工程师</td><td>硬件设计、安装、测试</td></tr><tr><td>4</td><td>软件工程师</td><td>软件设计、安装、测试</td></tr><tr><td>5</td><td>项目协调经理</td><td>协调项目相关事宜</td></tr></table>

# 4.2 项目活动的持续时间与逻辑关系

# 4.2.1 项目活动的持续时间估计方法

项目活动持续时间是指项目任务分解后，完成每项活动所需的时间。项目活动时长的估算是在每一阶段确定所有活动时长后，才能计算出整个项目周期所需时间的基础上，制定项目进度计划的关键依据。项目活动的持续时间不宜过长或过短。如果估算的时间过长，会导致整体项目的完成时间变长，不仅可能导致无法及时完成项目进度计划的要求，也会增加整个项目的成本。如果估算的时间过短，会使项目活动在进行过程中比较紧张，从而会影响一些任务的完成质量，也可能导致任务无法完成。因此，确定项目的持续时间需要按照科学的计算方法，确保估计的任务时间都尽可能准确无误。常用的项目活动估算时间方法主要有以下几种：

（1）类比估计法

类比估计法是指通过类比的方法来确定当前项目的各项活动所需的时间，参考以往同类项目活动的持续时间。如果能在过去的项目中找到类似的活动，那么采用这种方法就比较可靠。

（2）专家评估法

专家评估法是指通过邀请项目管理方面的专家，结合他们的经验和专业知识，对当前的项目活动进行综合评价。专家评估法主要依据专家的个人经验以及大量的历史数据，因此存在一定的主观性，得出的结果也就具有不确定性。

# （3）三点估算法

三点估算法是指对项目活动的乐观时间（a）、最可能时间（m）、悲观时间（b）以一定的假设条件为前提进行估计，然后用公式2.1计算出每项活动的持续时间。不过值得说明的是，该方法得出的任务持续时间并不是活动的实际完成时间。

# 4.2.2 项目活动持续时间与逻辑关系确定

根据调查研究，对H镁矿智慧能源集中监控系统集成项目的活动时间采用类比法和专家评估法结合的方式进行。除了估算项目活动时间之外，不同活动之间的逻辑关系也需要确认和梳理，经过分析之后，该项目的活动持续时间和逻辑关系如表4.3所示。

表 4.3项目活动任务工期和逻辑关系  
Table 4.3 Task duration and logical relationships of project activities   

<table><tr><td>序号</td><td>任务名称</td><td>活动名称</td><td>持续时间 (天)</td><td>前置任务</td></tr><tr><td>1</td><td>项目确认</td><td>A莊</td><td>5</td><td></td></tr><tr><td>2</td><td>签订协议</td><td>B</td><td>5號</td><td>A莊</td></tr><tr><td>3</td><td>硬件替换</td><td>C</td><td>20</td><td>B</td></tr><tr><td>4</td><td>安装调试</td><td>D</td><td>10</td><td>C</td></tr><tr><td>5</td><td>系统设计</td><td>E</td><td>10</td><td>D</td></tr></table>

# 续表 4.3

Continued Table 4.3

<table><tr><td>6</td><td>平台搭建</td><td>F</td><td>8</td><td>D</td></tr><tr><td>7</td><td>无线调试</td><td>G</td><td>5</td><td>E、F</td></tr><tr><td>8號</td><td>结构设计</td><td>H</td><td>15</td><td>G</td></tr><tr><td>9號</td><td>光学设计</td><td>I</td><td>15</td><td>G</td></tr><tr><td>10</td><td>电路设计</td><td>J</td><td>20</td><td>G</td></tr><tr><td>11</td><td>零配件设计</td><td>K</td><td>15</td><td>G</td></tr><tr><td>12</td><td>面板设计</td><td>L</td><td>10</td><td>G</td></tr><tr><td>13</td><td>实用性设计</td><td>M</td><td>20</td><td>H、I、J、K、L</td></tr><tr><td>14</td><td>硬件功能测试</td><td>Nä</td><td>5</td><td>M</td></tr><tr><td>15</td><td>硬件连接测试</td><td></td><td>5</td><td>M</td></tr><tr><td>16</td><td>软件功能测试</td><td>Pä</td><td>5</td><td>N、O</td></tr><tr><td>17</td><td>软件连接测试</td><td>Q•</td><td>5</td><td>N、0</td></tr><tr><td>18</td><td>集成数据测试</td><td>R蓮</td><td>10</td><td>P、Q</td></tr><tr><td>19</td><td>评审验收</td><td>S</td><td>5號</td><td>R</td></tr><tr><td>20</td><td>完成交付</td><td>T</td><td>3</td><td>S</td></tr></table>

# 4.3 网络计划图的编制

# 4.3.1 网络时间参数计算及关键路径确定

当整个项目活动的逻辑顺序和持续时间确定以后，为了发现关键路径，可以对项目活动网络图中各个任务的时间参数进行计算，具体计算方式如下：

（1）最早开始时间ES和最早结束时间EF，计算方式如下：  
$\mathrm { E S } = \mathrm { m a x } .$ {前置任务的EF}  
$\mathrm { E F } = \mathrm { E S } +$ 任务持续时间 T  
（2）最晚开始时间LS和最晚结束时间LF，计算方式如下：  
$\mathrm { L F } = \mathrm { m i n }$ {后置任务的LS}  
$\mathrm { L S } = \mathrm { L F } \ .$ - 任务持续时间 T

（3）任务总时间差TF，是指在不影响项目工期的前提下，某个任务可以推迟的时间，计算方式如下：

（4）自由时间差FF是指某项任务可以自由推迟的时间，前提是不影响最早开始的后置工作，计算方式如下：

$$
\mathrm { F F } = \operatorname* { m i n } \{ \mathrm { E S } ~ ( \sqrt { \right. } \frac { \left. } { \forall } \langle \pm \rangle \langle \frac { \varkappa } { \varkappa } \rangle ) ~ \} - \mathrm { E F }
$$

（5）关键路径的确定。任务总的时间差最小的任务称之为关键任务，由一系列重点任务组成的线路叫做重点路径。整个项目所需时间也直接由关键路径决定。通过确定关键路径，可以有效寻找项目中的关键任务，从而更有针对性的分配资源，保证项目的健康实施。

针对H镁矿智慧能源集中监控系统集成项目，计算ES、EF、LS、LF、TF以及各个项目活动的FF，找出关键路径，可以根据图4.1看出，任务总时差TF均为0的活动构成了该项目的关键路径。其中在O（N）和P（Q）两处活动的任务总时间差TF均为0，因此该项目的关键路径在图中如红色所示：A-B-C-D-E-G-J-M-N(O)-P(Q)-R-S-T，项目持续时间为123天。

图例  

<table><tr><td>ES</td><td>LS</td><td>TF</td></tr><tr><td>D</td><td>活动名称</td><td></td></tr><tr><td>EF</td><td>LF</td><td>FF</td></tr></table>

<table><tr><td>0</td><td>0</td><td>0</td></tr><tr><td>5</td><td>A</td><td></td></tr><tr><td>5</td><td>5</td><td>0</td></tr></table>

<table><tr><td>5 ↑ 5</td><td>5</td></tr><tr><td></td><td>B</td></tr><tr><td>10</td><td>10</td></tr></table>

<table><tr><td>40</td><td>40</td><td>0</td></tr><tr><td>10</td><td>E</td><td></td></tr><tr><td>50</td><td>50</td><td>0</td></tr></table>

<table><tr><td>50 ?</td><td>50</td><td>0</td></tr><tr><td>5</td><td>G</td><td></td></tr><tr><td>55</td><td>55</td><td>0</td></tr></table>

<table><tr><td>55</td><td>60</td><td>5</td></tr><tr><td>15</td><td>H</td><td></td></tr><tr><td>70</td><td>75</td><td>5</td></tr></table>

<table><tr><td>55</td><td>60</td><td>5</td></tr><tr><td>15</td><td>I</td><td></td></tr><tr><td>70</td><td>75</td><td>5</td></tr></table>

<table><tr><td>55</td><td>55</td><td>0</td></tr><tr><td>20</td><td colspan="2">J</td></tr><tr><td>75</td><td>75</td><td>0</td></tr></table>

<table><tr><td>55</td><td>60</td><td>5</td></tr><tr><td>15</td><td>K</td><td></td></tr><tr><td>70</td><td>75</td><td>5</td></tr></table>

<table><tr><td>75</td><td>75</td><td>0</td></tr><tr><td>20</td><td colspan="2">M</td></tr><tr><td>95</td><td>95</td><td>0</td></tr></table>

<table><tr><td>55</td><td>65</td><td>10</td></tr><tr><td>10</td><td>L</td><td></td></tr><tr><td>65</td><td>75</td><td>10</td></tr></table>

![](images/59378efb1140ebaa25528c0e4367b50dc7a3674e917ca2a2b88b79f2267e4f34.jpg)  
图 4.1 网络计划图  
Figure 4.1 Network planning diagram

# 4.3.2 项目网络计划图

结合 H 镁矿智慧能源集中监控系统集成项目的网络计划图，可以确定整个项目的完整进度时间表。每项活动实施的起始时间、实施完毕时间，都在表格中列明。完成时间是在活动开始时间的基础上参考任务持续时间以及节假日确定的。工程进度计划的制定，一旦工程进度出现偏差，能够及时采取有效措施予以纠正，可以为工程实施过程中的监督管理提供参考。该项目的进度计划如表 4.4 所示。

表 4.4 项目进度计划  
Table 4.4 Project schedule   

<table><tr><td>序号</td><td>任务名称</td><td>活动名称</td><td>持续时间 (天)</td><td>开始时间</td><td>完成时间</td></tr><tr><td>1</td><td>项目确认</td><td>A</td><td>5</td><td>2022/5/25</td><td>2022/5/30</td></tr><tr><td>2個</td><td>签订协议</td><td>B</td><td>5</td><td>2022/5/31</td><td>2022/6/5</td></tr><tr><td>3個</td><td>硬件替换</td><td>c</td><td>20</td><td>2022/6/6</td><td>2022/6/26</td></tr><tr><td>4</td><td>安装调试</td><td>D</td><td>10</td><td>2022/6/27</td><td>2022/7/7</td></tr><tr><td>5號</td><td>系统设计</td><td>E</td><td>10</td><td>2022/7/8</td><td>2022/7/18</td></tr><tr><td>6個</td><td>平台搭建</td><td>F</td><td>8</td><td>2022/7/19</td><td>2022/7/27</td></tr><tr><td>7</td><td>无线调试</td><td>G</td><td>5號</td><td>2022/7/28</td><td>2022/8/2</td></tr><tr><td>8號</td><td>结构设计</td><td>H</td><td>15</td><td>2022/8/3</td><td>2022/8/19</td></tr><tr><td>9號</td><td>光学设计</td><td>I</td><td>15</td><td>2022/8/20</td><td>2022/9/5</td></tr><tr><td>10</td><td>电路设计</td><td>J</td><td>20</td><td>2022/9/6</td><td>2022/9/26</td></tr><tr><td>11</td><td>零配件设计</td><td>K</td><td>15</td><td>2022/9/27</td><td>2022/10/18</td></tr><tr><td>12</td><td>面板设计</td><td>L</td><td>10</td><td>2022/10/19</td><td>2022/10/29</td></tr><tr><td>13</td><td>实用性设计</td><td>M</td><td>20</td><td>2022/10/30</td><td>2022/11/19</td></tr><tr><td>14</td><td>硬件功能测试</td><td>N</td><td>5號</td><td>2022/11/20</td><td>2022/11/25</td></tr><tr><td>15</td><td>硬件连接测试</td><td></td><td>5號</td><td>2022/11/26</td><td>2022/12/1</td></tr><tr><td>16</td><td>软件功能测试</td><td>P</td><td>5</td><td>2022/12/2</td><td>2022/12/7</td></tr><tr><td>17</td><td>软件连接测试</td><td>Q•</td><td>5號</td><td>2022/12/8</td><td>2022/12/13</td></tr><tr><td>18</td><td>集成数据测试</td><td>R</td><td>10</td><td>2022/12/14</td><td>2022/12/24</td></tr><tr><td>19</td><td>评审验收</td><td>S</td><td>5號</td><td>2022/12/25</td><td>2022/12/30</td></tr><tr><td>20</td><td>完成交付</td><td>T</td><td>3</td><td>2023/1/4</td><td>2023/1/7</td></tr></table>

# 4.4 项目进度计划的优化

# 4.4.1 项目进度计划优化的目标

从项目的进度计划表可以发现，该项目从 2022 年 5 月 25 日开始，计划于2023 年 1月 7日完成。但是客户追加 8万元人民币的费用，希望能够提前 10 天交付整个项目。项目组了解到情况后，及时组织项目小组开会讨论，为了满足客户的需求而且费用不能超出预算，对项目中的一些活动持续时间进行压缩和优化。具体而言就是，保证项目能在 2022 年 12 月 27 日前完成，并且优化的成本不超过 8万元。

# 4.4.2 项目进度计划优化的原则

项工程进度计划优化不是简单地压缩任务工期，而是在不影响工程整体质量的前提下，有选择地科学优化不必要的工期，实现工程进度的优化。H镁集团的智慧能源集中监控系统集成项目的优化目标是在满足客户提前 10 天完成交付项目的基础上，成本费用不超过 8 万元。通常而言，在保障整个项目的成本和质量的前提下，优先考虑对项目关键路径上的活动进行进度优化，可以采用的方法主要有：串联活动优化为并行任务、增加活动的资源、压缩关键任务等。结合 H镁集团的智慧能源集中监控系统集成项目的实际情况，采用压缩关键路径上的任务周期比较合理。

需要遵循以下几个原则进行关键赛事的持续时间压缩：

（1）时间缩短对工程质量影响不大；（2）较少的资源需要增加，以压缩活动的持续时间；（3）需要增加的成本较低，以压缩活动的持续时间；（4）有足够备用资源的项目活动；（5）注意压缩时重点线路与非重点线路的转换，避免重点线路向非重点线路转换。

# 4.4.3 项目进度计划优化的内容

根据 H 镁矿智慧能源集中监控系统集成项目的实际情况以及项目小组讨论出来的优化目标，最终确定采用时间优化法对整个项目的进度进行优化管理，具体优化的步骤为：

（1）弄清项目的重点路线和每项重点活动的时长，从而确定整个项目的时长总和。

（2）计算每项关键活动的可压缩时间。

（3）计算压缩各项关键活动所需要的成本。

（4）压缩各关键活动在关键路线上的持续时间，如果出现多个关键路线，可能导致优化组合策略的多种不同。

（5）关键路径是否在压缩时间判断后发生改变。

（6）对各优化组合方案进行比较分析，选出费用最低的一种方案作为最后的优化方案。

# 4.4.4 项目进度计划优化过程与结果

第一步：根据网络计划图及关键路径的计算，可以确定 H 镁矿智慧能源集中监控系统集成项目的关键路径为 A-B-C-D-E-G-J-M-N(O)-P(Q)-R-S-T。为了便于整体的观察，将图 4.1项目网络计划图转化为简单的项目活动流程图，如图 4.2所示。

![](images/f816ce9ae70e5f780320eed5181f8f496dfb63283da23a72e055677ed49b4198.jpg)  
图 4.2 活动流程图  
Figure 4.2 Activity flow chart

第二步：结合前面确定的项目进度表，如下表 4.5所示的网络计划时间参数及压缩费用参数表，依据类比法和专家评估法确定各关键活动可压缩的持续时间及压缩后产生的成本。其中，D 为活动计划持续时间， $\Delta \mathrm { d }$ 为可压缩的最大工期，C为压缩后所产生的成本，单位为万元/天。

表 4.5 中，由于项目的确认、协议的签订、评审验收和完成交付对整个项目的完成至关重要，因此项目中 A、B、S、T 活动的工期均不可压缩，其相应的费用 C 为 $+ \infty$ ， $\Delta \mathrm { d }$ 为 0。

# 表 4.5 网络计划时间参数及成本参数表

Table 4.5 Table of network planning parameters of the time and cost   

<table><tr><td>活动名称</td><td>D</td><td>△d</td><td>ES</td><td>EF</td><td>LS</td><td>LF</td><td>TF</td><td>FF</td><td>C</td></tr><tr><td>A</td><td>5</td><td>0</td><td>0</td><td>5</td><td>0</td><td>5</td><td>0</td><td>0</td><td>+8</td></tr><tr><td>B</td><td>5</td><td>0</td><td>5</td><td>10</td><td>5</td><td>10</td><td>0</td><td>0</td><td>+8</td></tr><tr><td>C</td><td>20</td><td>3</td><td>10</td><td>30</td><td>10</td><td>30</td><td>0</td><td></td><td>0.5</td></tr><tr><td>D</td><td>10</td><td>1</td><td>30</td><td>40</td><td>30</td><td>40</td><td>0</td><td></td><td>0.7</td></tr><tr><td>E</td><td>10</td><td>1</td><td>40</td><td>50</td><td>40</td><td>50</td><td>0</td><td></td><td>0.7</td></tr><tr><td>F</td><td>8</td><td>1</td><td>40</td><td>48</td><td>42</td><td>50</td><td>2</td><td></td><td>0.6</td></tr><tr><td>G</td><td>5</td><td>1</td><td>50</td><td>5</td><td>50</td><td>55</td><td>0</td><td></td><td>1.5</td></tr><tr><td>H</td><td>15</td><td>2</td><td>55</td><td>70</td><td>60</td><td>75</td><td>5</td><td>5</td><td>0.6</td></tr><tr><td>I</td><td>15</td><td>2</td><td>55</td><td>70</td><td>60</td><td>75</td><td>5</td><td>5</td><td>0.6</td></tr><tr><td>J</td><td>20</td><td>3</td><td>55</td><td>75</td><td>55</td><td>75</td><td>0</td><td></td><td>0.5</td></tr><tr><td>K</td><td>15</td><td>2</td><td>55</td><td>70</td><td>60</td><td>75</td><td>5</td><td>5</td><td>0.6</td></tr><tr><td>L</td><td>10</td><td>1</td><td>55</td><td>65</td><td>65</td><td>75</td><td>10</td><td>10</td><td>1.2</td></tr><tr><td>M</td><td>20</td><td>3</td><td>75</td><td>95</td><td>75</td><td>95</td><td>0</td><td></td><td>0.5</td></tr><tr><td>N</td><td>5</td><td>1</td><td>95</td><td>100</td><td>95</td><td>100</td><td>0</td><td></td><td>1.5</td></tr><tr><td>0</td><td>5</td><td>1</td><td>95</td><td>100</td><td>95</td><td>100</td><td>0</td><td>0</td><td>1.5</td></tr><tr><td>P</td><td>5</td><td>1</td><td>100</td><td>105</td><td>100</td><td>105</td><td>0</td><td></td><td>1.5</td></tr><tr><td>Q</td><td>5</td><td>1</td><td>100</td><td>105</td><td>100</td><td>105</td><td>0</td><td></td><td>1.5</td></tr><tr><td>R</td><td>10</td><td>1</td><td>105</td><td>115</td><td>105</td><td>115</td><td>0</td><td>0</td><td>1.2</td></tr><tr><td>S</td><td>5</td><td>0</td><td>115</td><td>120</td><td>115</td><td>120</td><td>0</td><td>0</td><td>+8</td></tr><tr><td>T</td><td>3</td><td>0</td><td>120</td><td>123</td><td>120</td><td>123</td><td>0</td><td>0</td><td>+8</td></tr></table>

第三步，确定关键路径上可进行压缩的关键活动，即 C、D、E、F、G、H、I、J、K、L、M、N、O、P、Q、R、S、T。上述活动中同时满足成本最低且为关键活动的有 C、D、E、J、M。将这些活动压缩后，可计算其总压缩时长为 $\Delta \mathrm { T } =$ $3 + 1 + 1 + 3 + 3 = 1 1$ ，因此，压缩后的关键路径不发生变化。由于只需要压缩 10 天即可满足任务要求，按照费用最低原则，需要对可压缩的关键活动所产生的成本进行排序，从高到低依次为：D（0.7/万元每天）、E（0.7/万元每天）、C（0.5/万元每天）、J（0.5/万元每天）、M（0.5/万元每天）。为了使压缩后的成本最低，优先安排活动 C、J、M，压缩时间为 $3 + 3 + 3 = 9$ 天，然后对活动 D进行压缩 1 天，正好完成 10天的项目压缩时间。产生的压缩费用为 9天 $\times 0 . 5$ 万元/天 $+ 1$ 天 $\times 0 . 7$ 万元/天 ${ : = } 5 . 2$ 万元。优化后的网络计划时间参数及压缩费用参数如表 4.6所示。

可以发现，该优化结果不仅能够满足提前 10 天完成任务，而且压缩任务所花费的成本也没有超过预算，因此整体上完成了预期的目标。优化后的 H 镁矿智慧能源集中监控系统集成项目网络计划图，如图 4.2所示。

表 4.6 优化后的网络计划时间参数及成本参数表  
Table 4.6 Table of network planning parameters of the time and cost after optimization   

<table><tr><td>活动名称</td><td>D</td><td>△d</td><td>ES</td><td>EF</td><td>LS</td><td>LF</td><td>TF</td><td>FF</td><td>C</td></tr><tr><td>A</td><td>5</td><td>0</td><td>0</td><td>5</td><td>0</td><td>5</td><td>0</td><td>0</td><td>+8</td></tr><tr><td>B</td><td>5</td><td>0</td><td>5</td><td>10</td><td>5</td><td>10</td><td>0</td><td>0</td><td>+8</td></tr><tr><td>C</td><td>17</td><td>0</td><td>10</td><td>27</td><td>10</td><td>27</td><td>0</td><td>0</td><td>0.5</td></tr><tr><td>D</td><td>9</td><td>0</td><td>27</td><td>36</td><td>27</td><td>36</td><td>0</td><td>0</td><td>0.7</td></tr><tr><td>E</td><td>10</td><td>1</td><td>36</td><td>46</td><td>36</td><td>46</td><td>0</td><td>0</td><td>0.7</td></tr><tr><td>F</td><td>8</td><td>1</td><td>36</td><td>44</td><td>38</td><td>46</td><td>2</td><td>2</td><td>0.6</td></tr><tr><td>G</td><td>5</td><td>1</td><td>46</td><td>51</td><td>46</td><td>51</td><td>0</td><td>0</td><td>1.5</td></tr><tr><td>H</td><td>15</td><td>2</td><td>51</td><td>66</td><td>53</td><td>68</td><td>2</td><td>2</td><td>0.6</td></tr><tr><td>I</td><td>15</td><td>2</td><td>51</td><td>66</td><td>53</td><td>68</td><td>2</td><td>2</td><td>0.6</td></tr><tr><td>J</td><td>17</td><td>0</td><td>51</td><td>68</td><td>51</td><td>68</td><td>0</td><td>0</td><td>0.5</td></tr><tr><td>K</td><td>15</td><td>2</td><td>51</td><td>66</td><td>53</td><td>68</td><td>2</td><td>2</td><td>0.6</td></tr><tr><td>L</td><td>10</td><td>1</td><td>51</td><td>61</td><td>58</td><td>68</td><td>7</td><td>7</td><td>1.2</td></tr><tr><td>M</td><td>17</td><td></td><td>68</td><td>85</td><td>68</td><td>85</td><td>0</td><td>0</td><td>0.5</td></tr><tr><td>N</td><td>5</td><td>1</td><td>85</td><td>90</td><td>85</td><td>90</td><td>0</td><td>0</td><td>1.5</td></tr><tr><td>0</td><td>5</td><td>1</td><td>85</td><td>90</td><td>85</td><td>90</td><td>0</td><td>0</td><td>1.5</td></tr><tr><td>P</td><td>5</td><td>1</td><td>90</td><td>95</td><td>90</td><td>95</td><td>0</td><td>0</td><td>1.5</td></tr><tr><td>Q</td><td>5</td><td>1</td><td>90</td><td>95</td><td>90</td><td>95</td><td>0</td><td>0</td><td>1.5</td></tr><tr><td>R</td><td>10</td><td>1</td><td>95</td><td>105</td><td>95</td><td>105</td><td>0</td><td>0</td><td>1.2</td></tr><tr><td>S</td><td>5</td><td>0</td><td>105</td><td>110</td><td>105</td><td>110</td><td>0</td><td>0</td><td>+8</td></tr><tr><td>T</td><td>3</td><td>0</td><td>110</td><td>113</td><td>110</td><td>113</td><td>0</td><td>0</td><td>+8</td></tr></table>

图例  

<table><tr><td>ES</td><td>LS</td><td>TF</td></tr><tr><td>D</td><td>活动名称</td><td></td></tr><tr><td>EF</td><td>LF</td><td>FF</td></tr></table>

<table><tr><td>51</td><td>53</td><td>2</td></tr><tr><td>15</td><td>H</td><td></td></tr><tr><td>66</td><td>68</td><td>2</td></tr></table>

<table><tr><td>51</td><td>53</td><td>2</td></tr><tr><td>15</td><td>I</td><td></td></tr><tr><td>66</td><td>68</td><td>2</td></tr></table>

<table><tr><td>0</td><td>0</td><td>0</td></tr><tr><td>5</td><td colspan="2">A</td></tr><tr><td>5</td><td>5</td><td>0</td></tr></table>

<table><tr><td>5</td><td>5</td><td>0</td></tr><tr><td>5</td><td colspan="2">B</td></tr><tr><td>10</td><td>10</td><td>0</td></tr></table>

<table><tr><td>10</td><td>10</td><td>0</td></tr><tr><td>+ 17</td><td colspan="2">C</td></tr><tr><td>27</td><td>27</td><td>0</td></tr></table>

<table><tr><td>46</td><td>46</td><td>0</td></tr><tr><td>5</td><td>G</td><td></td></tr><tr><td>51</td><td>51</td><td>0</td></tr></table>

<table><tr><td>51</td><td>51</td><td>0</td></tr><tr><td>17</td><td colspan="2">J</td></tr><tr><td>68</td><td>68</td><td>0</td></tr></table>

<table><tr><td>51</td><td>53</td><td>2</td></tr><tr><td>15</td><td colspan="2">K</td></tr><tr><td>66</td><td>68</td><td>2</td></tr></table>

<table><tr><td>51</td><td>58</td><td>7</td></tr><tr><td>10</td><td>L</td><td></td></tr><tr><td>61</td><td>68</td><td>7</td></tr></table>

<table><tr><td>68</td><td>68</td><td>0</td></tr><tr><td>17</td><td colspan="2">M</td></tr><tr><td>85</td><td>85</td><td>0</td></tr></table>

![](images/9557b33aff0c9d17f8861d1550484caf6a6e9db0a13d26ff4c4aa610b5c2da0b.jpg)  
图 4.2 优化后的网络计划图  
Figure 4.2 Network planning diagram after optimization

# 4.5 本章小结

本章首先对 H 镁矿智慧能源集中监控系统集成项目进行 WBS 工作结构分解，并对各项项目活动的持续时间和对应的逻辑关系进行了初步的确认，然后对项目活动网络图中各个任务的时间参数进行计算，从而确定了 H 镁矿智慧能源集中监控系统集成项目的网络计划图和关键路径。最终采用时间优化法对整个项目的进度进行优化管理，得到了优化后的网络计划图，并满足了预期的优化目标。

# 第 5 章 H 镁矿智慧能源集中监控系统集成项目进度计划的控制

项目进度计划的准确性对整个项目按照预定目标进行实施非常重要。经过优化之后，H 镁矿智慧能源集中监控系统集成项目的进度计划最终得到确定。因此，为了保证 H 镁矿智慧能源项目能够按提前制定好的计划执行，有必要采取一些针对性的措施来监管和控制整个项目的实施。如工程在推进过程中出现一定偏差，为确保工程能够按照预定的进度计划实施，需要第一时间采取相应的措施对有问题的工程进行对应的调整。此外，还要详细分析项目在执行过程中出现偏差的根源，这样才能保证类似的情况在后期不再发生。

# 5.1 项目进度控制体系构建

项目进度控制是指在工程的各个关键节点上，对制定好的进度计划进行控制。因此，首先要建立一个完整的工程进度控制体系，项目推进过程中收集到的工程数据，可以通过部门会议或项目组讨论的方式，由项目小组各个部门通过对工程进度的监测报告、数据报表或其他相关形式进行分析汇总、沟通交流，从而实现对工程进度的监控和控制。在资料汇总的过程中，需要对应的部门负责人对工程进度的实际情况进行分析，提出有针对性的解决问题的办法，并将其整理成相应的管控报表，供后续工程总结时参考。监控报告的形式可参考表 5.1。

表 5.1 项目监控报告  
Table 5.1 Monitor report of project   

<table><tr><td colspan="2">工作名称： 工作代码：</td></tr><tr><td>负责人：</td><td>监控日期：</td></tr><tr><td colspan="2">监控类别：工期口 质量□ 成本□</td></tr><tr><td colspan="2">对比进度计划与执行程度：</td></tr><tr><td colspan="2">与进度计划是否发生偏离：是口否□ 偏差程度：</td></tr><tr><td colspan="2">偏差原因：</td></tr><tr><td colspan="2">负责人意见： 执行人： 时间：</td></tr></table>

# 5.2 项目进度控制的关键因素

依据H镁矿智慧能源集中监控系统集成项目自身的特点，需要从项目质量、技术人员、项目进度以及整体成本四个主要因素对整个项目的实施进行有针对性的控制。

# 5.2.1 项目质量

项目的质量控制主要是指质量监督管理工作在工程实施过程中所发生的问题。通过质量验收是整个工程圆满完成的标志，也是一项工程顺利与否的依据。作为对生产过程的集中监控管理，如煤、电、水、油、气等能源介质能源消耗的耗量平衡、统计分析、操作指导、优化操作等复杂工程，以及目视管理等，都涉及到质量问题。H 镁矿集团要求所有项目有关的成员必须严格按照项目进度计划进行工作，把质量放在第一位。如果有发现不符合规范要求的作业，应该在第一时间采取针对性的整改措施。定期加强项目组成员的质量意识，要求相关负责人和质检员在工程的每一个关键节点，对工程阶段性进展情况进行检查验收，以高质量、高标准的要求，确保完成工程任务。如在开展质检过程中出现不符合要求的问题，为确保 H 镁矿智慧能源集中监控系统集成工程质量可靠，需要及时提出有针对性的修改方案，并指定相应责任人。

# 5.2.2 技术人员

技术人员是整个项目相关活动的参与者，其专业知识和工作经验对整个项目的开展和实施有非常关键的作用。因此，H 镁矿集团中的项目技术人员都严格要求具有丰富的项目工作经验，同时项目组人员还需要定期进行业务上的培训，从而不断提高工作人员的技术水平。

# 5.2.3 项目进度

项目进度需要严格按照预定的进度计划执行，项目中的各个相关部门要按计划完成任务，项目经理要整体上监督和管控项目的进度情况，定时比对项目计划进度与实际进度之间是否有偏差。如果出现项目进度延后现象，需要及时进行总结分析，并采取有针对性的措施来纠正偏差，从而保证整体的项目进度按计划有序地进行。

# 5.2.4 整体成本

H 镁矿智慧能源集中监控系统集成项目组采用成本偏差控制法，来对预算成

本与实际成本之差进行分析，总结成本发生偏差的原因，采取合理的措施来进行调整，使整体的项目成本在预定的目标范围内。

# 5.3 项目进度控制过程

项目进度的控制主要包括三个方面：第一，首先要制定一个符合项目自身特色的项目控制目标；其次，要围绕该目标确定一个合理的考核绩效的方式；第三，对项目进度进行实时和动态监控，当发生项目的实际进度偏离预定的计划时，应该要及时分析原因并制定针对性的整个方案进行纠偏。本文主要采用进度跟踪报告法和挣得值分析法对项目进度进行控制。

# 5.3.1 进度跟踪报告法

项目进度跟踪报告法主要是为了及时监测项目进度的具体情况以及是否发生偏离项目进度的问题，实现对项目进度的有效控制。项目进度报告模板如表5.2所示。

表5.2 项目进度跟踪报告  
Table 5.2 Report of project progress review   

<table><tr><td>项目名称</td><td></td><td>项目负责人</td><td></td></tr><tr><td>检查日期</td><td colspan="3">报告份数</td></tr><tr><td>项目内容</td><td colspan="3"></td></tr><tr><td>目标描述</td><td colspan="3"></td></tr><tr><td>计划完成日期</td><td colspan="3"></td></tr><tr><td>实际进度</td><td colspan="3"></td></tr><tr><td>未完成原因</td><td colspan="3"></td></tr><tr><td>措施与方法</td><td colspan="3"></td></tr><tr><td>纠错完成日期</td><td colspan="3"></td></tr><tr><td>项目组意见</td><td></td><td>签名</td><td>日期</td></tr></table>

项目团队可以组织定期工作巡回检查，根据定期巡回检查内容、定期轮换内容、定期试验测试的内容分别维护定期工作的项目内容，然后根据专业对应授权到各个运行日志中，并根据各个内容维护定期工作的周期和提前工作提示的时限周期。定期工作提示时限周期的设置与定期工作周期及管理要求相对应，与运行岗位、运行日志相关联，可以在当班提示或者其它某个时间在相应的项目进度报告中提示，根据工作提示可以点击查询需要工作的内容列表，可以查看工作方法，并在完成定期工作后，直接填写定期工作记录，在记录中决定是否添加到项目进度报告中，并选择添加的内容，并在工作完成提示消除或者工作推迟在未完成提示，原因填写后，自动把相关内容加载到项目进度报告中。同时还可以查询某个班或某人在某段时间内完成的、未完成的定期工作类别、项目，并可以查看定期工作记录；可以查询在未来某个时间内该做的定期工作项目；可以查询到期该做而没有完成的定期工作项目，并可以查看未完成原因等。对于该本班进行的定期工作，但由于某种原因无法进行的工作，需要推迟到下一个班或者某个时间的工作，在填写定期工作记录中未执行原因及推迟时间后，需要重新设置提示周期。对于定期工作查询可以根据不同查询条件的设定，查询得到不同的结果；查询条件主要有：定期工作的类别、工作项目名称、工作周期、工作状态、工作结果、岗位、运行日志种类等信息进行单一或者组合条件查询，并可进行相关统计。同时可以进行相关指标的统计，例如：定期工作完成率，定期试验的合格率、定期进度完成率等，与相关考核相挂钩。此外，项目团队经理应该定期组织专题会议来讨论和管控项目进度计划的执行情况，包括出现的一些问题和提出对应的整改措施。

# 5.3.2 挣得值分析法

好的成本控制方法能够对成本效率的提升起到关键性的作用，本文采用挣得值分析法对项目进度和成本进行综合评价。挣得值分析法主要有以下三个基本参数[36]：

（1）项目活动的预计费用（BCWS 或者 PV）；（2）已完成活动的实际费用（ACWP 或者 AC）；（3）已完成活动的预计费用（BCWP 或者 EV）。挣得值分析法的具体计算公式如下：  
（1） $\mathrm { E V } =$ 给定时间的完成比例 $\times$ 预计成本（2）成本偏差 $\mathrm { C V } = \mathrm { E V } - \mathrm { A C }$   
（3）进度偏差 $\mathrm { S V } = \mathrm { E V } - \mathrm { P V }$

（4）成本绩效指数 $\mathrm { C P I } = \mathrm { E V } / \mathrm { A C }$ （5）进度绩效指数 $\mathrm { S P I } = \mathrm { E V } / \mathrm { P V }$

根据挣得值分析法，H镁矿智慧能源集中监控系统集成项目的所有成本指标和绩效指标具体情况如表 5.3 所示，其中 PV、EV、AC、CV 以及 SV 的单位均为万元。

表 5.3 项目成本偏差指标和绩效指标表  
Table 5.3 Table of the project cost and performance deviation indicators   

<table><tr><td>序</td><td>任务名称</td><td>活动 名称</td><td>工期 （天）</td><td>完成率 (%)</td><td>PV</td><td>EV</td><td>AC</td><td>CV</td><td>sV</td><td>CPI</td><td>SPI</td></tr><tr><td>号 1</td><td>项目确认</td><td>A</td><td>5</td><td>100</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>2</td><td>签订协议</td><td>B</td><td>5</td><td>100</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>3</td><td>硬件替换</td><td>C</td><td>17</td><td>100</td><td>20</td><td>20</td><td>26</td><td>-6</td><td>0</td><td>0.77</td><td>1</td></tr><tr><td>4</td><td>安装调试</td><td>D</td><td>9</td><td>100</td><td>8</td><td>8</td><td>10</td><td>-2</td><td>0</td><td>0.80</td><td>1</td></tr><tr><td>5</td><td>系统设计</td><td>E</td><td>10</td><td>100</td><td>6</td><td>6</td><td>6</td><td>0</td><td>0</td><td>1</td><td>1</td></tr><tr><td>6</td><td>平台搭建</td><td>F</td><td>8</td><td>100</td><td>5</td><td>5</td><td>5</td><td>0</td><td>0</td><td>1</td><td>1</td></tr><tr><td>7</td><td>无线调试</td><td>G</td><td>5</td><td>100</td><td>2</td><td>2</td><td>2</td><td></td><td></td><td>1</td><td>1</td></tr><tr><td>8</td><td>结构设计</td><td>H</td><td>15</td><td>100</td><td>9</td><td>9</td><td>12</td><td>-3</td><td>0</td><td>0.75</td><td>1</td></tr><tr><td>9</td><td>光学设计</td><td>I</td><td>15</td><td>100</td><td>10</td><td>10</td><td>12</td><td>-2</td><td>0</td><td>0.83</td><td>1</td></tr><tr><td>10</td><td>电路设计</td><td>J</td><td>17</td><td>100</td><td>12</td><td>12</td><td>15</td><td>-3</td><td>0</td><td>0.80</td><td>1</td></tr><tr><td>11</td><td>零配件设计</td><td>K</td><td>15</td><td>100</td><td>7</td><td>7</td><td>9</td><td>-2</td><td>0</td><td>0.78</td><td>1</td></tr><tr><td>12</td><td>面板设计</td><td>L</td><td>10</td><td>100</td><td>5</td><td>5</td><td>6</td><td>-1</td><td>0</td><td>0.83</td><td>1</td></tr><tr><td>13</td><td>实用性设计</td><td>M</td><td>17</td><td>100</td><td>10</td><td>10</td><td>12</td><td>-2</td><td>-2</td><td>0.80</td><td>1</td></tr><tr><td>14</td><td>硬件功能测试</td><td>N</td><td>5</td><td>80</td><td>4</td><td>3.2</td><td>4</td><td>-0.8</td><td>-0.8</td><td>0.80</td><td>0.8</td></tr><tr><td>15</td><td>硬件连接测试</td><td>0</td><td>5</td><td>80</td><td>4</td><td>3.2</td><td>4</td><td>-0.8</td><td>-0.8</td><td>0.80</td><td>0.8</td></tr><tr><td>16</td><td>软件功能测试</td><td>P</td><td>5</td><td>80</td><td>4</td><td>3.2</td><td>4</td><td>-0.8</td><td>-0.8</td><td>0.80</td><td>0.8</td></tr><tr><td>17</td><td>软件连接测试</td><td>Q</td><td>5</td><td>60</td><td>3</td><td>1.8</td><td>2</td><td>-0.2</td><td>-1.2</td><td>0.9</td><td>0.6</td></tr><tr><td>18</td><td>集成数据测试</td><td>R</td><td>10</td><td>/</td><td>2</td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>19</td><td>评审验收</td><td>S</td><td>5</td><td>/</td><td>1</td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>20</td><td>完成交付</td><td>T</td><td>3</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td colspan="2">累计</td><td></td><td></td><td>112</td><td>105.4</td><td>129</td><td>-23.6-5.6</td><td></td><td>0.82</td><td>0.94</td></tr></table>

根据计算可以发现：$\mathrm { C V } = \mathrm { E V } - \mathrm { A C } = - 2 3 . 6$ $\mathrm { S V } = \mathrm { E V } - \mathrm { P V } = - 6 . 6$ $\mathrm { C P I } = \mathrm { E V } / \mathrm { A C } = 0 . 8 2$ $\mathrm { S P I } = \mathrm { E V } / \mathrm { P V } = 0 . 9 4$

$\mathrm { C V } { < } 0$ ， $\mathrm { C P I } { < } 1$ ，表示该项目已超出预算；

$\mathrm { S V } { < } 0$ ， $\mathrm { S P I } { < } 1$ ，表示该项目进度延误。

经过对各项活动的成本偏差指标和进度偏差指标进行分析可以发现，H镁矿智慧能源集中监控系统集成项目出现成本超过预算以及任务延期的活动分别为硬件替换、安装调试、结构设计、光学设计、电路设计等。

# 5.3.3 项目施工进度偏差分析

根据挣得值分析法的结果，对项目施工进度出现偏差的原因进行分析发现，上述问题主要是由于下面几个原因导致：

（1）硬件工程师经验不足，在对硬件进行替换和安装调试的过程中，由于缺少相关的经验导致一些硬件在使用过程中出现损坏的情况，临时安排采购人员进行补货，又重新联系供应商发货，导致重新采购的小批量硬件产品价格比之前大批量采购时的价格高。此外，额外采购的产品同时也增加了交通运输、人工搬运、仓储等相关费用，最终造成已完成活动的实际费用大于项目活动的预计费用，直接增加了项目的成本。

（2）软件工程师经验不足，由于该项目涉及到结构设计、光学设计、电路设计、零配件设计、面板设计等，涉及到的范围非常广，而且设计过程非常复杂，同时还要求相当高的精度。尽管软件工程师都具备非常专业的技术能力，但是由于是第一次接触到这种复杂的项目时，在实践过程中还是出现了经验不足的问题，导致了一些设计任务中途出现反复修改的情况，最终未能按时完成项目进度计划的要求。尽管在成本上没有超出整个项目规定的预算，但是仍然耽误了项目进度的工期。

（3）项目本身的因素，H 镁集团的各个生产单位地域位置比较分散，单位之间的距离比较远，因此给硬件替换和安装调试的工作人员带来了巨大的挑战。此外，H镁集团智慧能源管理应用系统重点分为能源，能源集中监控系统、能源智慧管理系统、数据统计分析系统、视频监控分析系统、企业门户管理系统。而能源集中监控系统主要包括：电能、水、天然气、燃油、煤炭等能源介质计量实时监控、耗量平衡、数据分析、统计报表、事件管理等模块，涉及范围非常广，项目结构复杂。因此，给结构设计、光学设计、电路设计、零配件设计、面板设计等工作设计人员带来了巨大的挑战。

（4）材料质量不合格，部分采购的零部件产品在使用过程中才发现质量有问题，因此不得不替换掉，然后导致合格的原材料不足需要加急重新采购，从而增加了项目的成本，也会影响项目的进度计划。

（5）项目正处于推进阶段，由于整个项目正在进行，因此有一部分活动还未开始，有个别活动已经开始实施但是还未结束，因此造成整个项目进度比较缓慢，部分项目延期完成。

# 5.4 项目进度计划纠偏与保障措施

在 H 镁矿智慧能源集中监控系统集成工程实施过程中，可能会有许多不确定因素对项目进度造成影响，原因是工程复杂程度的原因。此外，由于项目进度控制属于动态过程，为确保工程按期完成，根据活动完成的实际情况，需要有效调整工程进度计划。针对以上出现的问题，项目团队将采取以下纠偏措施来保障项目按时保质完成。

# 5.4.1 项目管理人员配备组织保障

项目团队定期培训，要求参与项目具体活动的工作人员都要参加，并且培训结束后还要组织考核，避免个别工作人员出现应付、不积极参与的情况。提高工作人员的专业意识和能力，做到有问题提前发现、提前解决，避免项目实施过程中造成损失。

积极开展岗位技能培训，通过培训不断提升各级人员的岗位技能和、节能意识、管理水平，并结合岗位技能竞赛等活动，建立良好的“赛马机制”，激发员工的积极性、主动性、创造性，也为人才的培养与选拔奠定基础。建立行之有效的后备干部培养机制，有计划的组织开展后备干部现代化企业管理培训，开拓视野、强化认识、提升高度、丰富手段、激发活力。

# 5.4.2 项目施工机具及材料保障

对采购的零部件进行严格的质量检查，及时把有问题的残次品甄别出来，保障在项目活动进行过程中有充足的正常零部件可供使用，避免因为原材料的问题耽误工期，同时也降低因此导致的成本。此外，开发物资采购询问比价平台，使

采购工作更加透明、高效率、降低成本，同时可以对供应商实行更加深入的管理，提高备件的质量和服务的质量。系统可以跟踪产品的价格动态趋势，不断降低采购成本。

# 5.4.3 项目施工技术保障

项目实施前对每位技术工人进行合理的任务布置，确保每位技术工人明确自己的任务。对可能遇到的一些技术难题，要制定符合项目要求的技术工艺标准，对技术人员提前进行技术培训，组建攻关小组，确保各项技术方案措施最终能取得实效。

# 5.4.4 项目施工质量保障

实施全员素质管理，要求参与工程的职工人人自觉素质。建立有效的质量保证制度，对工程过程中可能遇到的质量问题，认真执行各项质量管理规定，对工程质量提前制定有针对性的防范措施，准备好相应的解决办法，杜绝因返工造成工程延期的一切质量事故的发生。由于现有的工艺流程，很多窑炉的填料及原料的混配、产品加工等作业主要是靠人工，因此对人的经验、技术、以及责任心等的依赖性非常大，不同人员操作的方式对成本效率的降低、能源资源的消耗、产品质量的提升、污染物排放的降低，差距巨大，因此积极开发节能竞赛等活动，配套合理的评价、奖励、考核机制，可以实现大大降本、提效、减排的效果，这也是投入产出比最合理、最直接、最高效的方法之一，可以达到“事半功倍”的效果。

# 5.4.5 项目施工沟通协调保障

项目现场协调人员要提前做好各部门的协调沟通工作，项目开工前明确各部门的职责。在项目实施过程中，协调人员要进行现场监督，对遇到的任务突发问题进行有效协调。此外，还要求一线工作人员对发现问题最早的工作人员，能够最早发现问题并给予奖励，鼓励全员参与到项目活动督导过程中，对发现的问题要建立问题报告制度，做到第一时间报告，项目小组再及时采取措施予以解决，力争最小限度地减少对项目的影响。

# 5.5 本章小结

本章主要针对上一章提出的 H 镁矿智慧能源集中监控系统集成项目进度计划制定相应的控制保障措施，确保整个项目计划能够按照预期完成。首先介绍了工程进度控制体系的构建，接着对工程进度控制的关键因素从工程质量、技术人员、工程进度及总体费用等方面进行了阐述，最后综合评价了工程进度计划，并对工程进度进行了偏差分析，给出了有针对性的工程进度保障措施。

# 第 6 章 结论与展望

# 6.1 本文的主要工作与结论

本文立足于 H 镁矿智慧能源集中监控系统集成项目进度管理的实际情况，首先对 H 镁矿集团的基本背景、项目基本内容以及工程目标进行了详细的分析研究。然后有针对性地制定工程进度表，并根据实际情况加以优化，运用工程进度控制的有关理论，在工程实施过程中，对工程实际进展情况进行严格监控。本研究证明了项目进度管理理论与方法在解决实际项目领域的有效性和可行性，为H镁矿集团的智慧能源监控系统升级做出了巨大的贡献。本文的主要结论总结如下：

（1）项目内容的清晰界定、项目工程目标的合理性以及项目组织结构的有效设定是整个项目取得成功的第一步。为了明确项目目标，避免因为项目内容不清楚导致不必要的返工情况发生，项目团队在一开始进行了充分的沟通和交流，明确了项目的成本、工期以及质量的要求。目标明确后，建立有针对性的项目组织结构是对项目进行高效管理的关键因素。根据实际情况确立责任分配矩阵，使各个部门都能清楚本部门的具体工作以及对应的职责，从而促进团队间各部门的高效分工与合作。

（2）制定合理的项目进度计划是整个项目取得成功的关键。通过对项目的范围进行 WBS 工作分解，然后根据工作的先后顺序和逻辑关系编制网络计划图，并对每一个网络时间参数进行计算，从而对整个项目的关键路径进行确定。基于上述结果和项目的实际情况，对项目进度计划进行了有针对性的优化，最终实现了项目在进度计划和成本预算方面的要求，同时也保证了项目的质量。

（3）由于项目工期、成本等是项目管理过程中的重要影响因素，因此需要对其进行有效的控制，这也是项目能否取得成功的重要保障。对工程进度计划中监测到的工期延迟、费用超出预算等问题，借助盈得值分析法建立了有效的工程控制体系，提出了有针对性的保障措施，使工程按预期的质量完成有了重要保障。

# 6.2 未来展望

由于本人学术水平和研究时间的限制，本文也存在一定的局限性。

首先，本文主要针对 H 镁矿智慧能源集中监控系统集成项目的进度管理进行了相关研究。该项目属于大型且复杂的设计改造施工项目，在项目实施过程中包含众多的研究领域，本文只针对其中的进度管理进行了研究，还未考虑到其他研究领域，因此，未来的研究可以针对该项目进行其他方向的研究，例如风险管理等。

其次，本文在对项目进度管理进行研究过程中，只关注了时间和成本资源的约束。一个项目的实施可能会涉及到多种资源的相互影响，因此仅考虑较少的因素可能无法涵盖整个项目进度管理的内容，未来的研究可以尝试在多种资源并行考虑的前提下，对项目进度管理的相关研究进行研究，如果考虑到较少的因素可能会影响。

最后，本文在对项目进度进行控制的时候，主要采用了挣得值分析法来进行综合评价，未来的研究可以考虑采用多种方法进行对比，包括 S 型曲线、香蕉曲线等等，多种方法对比有助于得到更为精确的项目进度综合评价结果。

# 参考文献

[1] Shibuya M, Chen X. Production Planning and Management Using Gantt Charts [J]. Journal of Mechanics Engineering and Automation, 2021, 11(3): 68-76.   
[2] Cottrell W D. Simplified program evaluation and review technique (PERT) [J]. Journal of construction Engineering and Management, 1999, 125(1): 16-22.   
[3] Hofmann P A. Critical path method: an important tool for coordinating clinical care [J]. The Joint Commission journal on quality improvement, 1993, 19(7): 235-246.   
[4] Ba'Its H A, Puspita I A, Bay A F. Combination of program evaluation and review technique (PERT) and critical path method (CPM) for project schedule development [J]. International Journal of Integrated Engineering, 2020, 12(3): 68- 75.   
[5] Huynh Q T, Nguyen N T. Probabilistic method for managing common risks in software project scheduling based on program evaluation review technique [J]. International Journal of Information Technology Project Management, 2020, 11(3): 77-94.   
[6] Goldratt E M. Critical chain [M]. Routledge, 2017.   
[7] Roghanian E, Alipour M, Rezaei M. An improved fuzzy critical chain approach in order to face uncertainty in project scheduling [J]. International Journal of Construction Management, 2018, 18(1): 1-13.   
[8] Rezaie K, Manouchehrabadi B, Shirkouhi S N. Duration estimation, a new approach in critical chain scheduling [C]. 2009 Third Asia International Conference on Modelling & Simulation, 2009: 481-484.   
[9] Sanchez O P, Terlizzi M A. Cost and time project management success factors for information systems development projects [J]. International Journal of Project Management, 2017, 35(8): 1608-1626.   
[10] De Andrade P A, Martens A, Vanhoucke M. Using real project schedule data to compare earned schedule and earned duration management project time forecasting capabilities [J]. Automation in Construction, 2019, 99: 68-78.   
[11] Sami Ur Rehman M, Thaheem M J, Nasir A R, et al. Project schedule risk management through building information modelling [J]. International Journal of Construction Management, 2020: 1-11.   
[12] Azeem S A, Hosny H E, Ibrahim A H. Forecasting project schedule performance using probabilistic and deterministic models [J]. HBRC journal, 2014, 10(1): 35-42.   
[13] Wan Abd Rahman W, Mohd Zaki N, Abu Husain M. Work breakdown structure application for man-hours calculation in hull construction shipbuilding in Malaysia [J]. Cogent Engineering, 2019, 6(1): 1599524.   
[14] Lima R, Tereso A, Faria J. Project management under uncertainty: resource flexibility visualization in the schedule [J]. Procedia Computer Science, 2019, 164: 381-388.   
[15] 徐伟宣. 华罗庚与优选法统筹法 [J]. 高等数学研究, 2006, (06): 63-64.   
[16] 詹伟, 王兆红, 邱菀华. 企业项目管理理论及其框架研究[J]. 现代管理科学, 2008, (05): 108-110.   
[17] 黄德才, 赵克勤, 陆耀忠. 联系数 $\mathtt { a } + \mathtt { b } \mathrm { i }$ 的运算及在网络计划中的应用 [J]. 浙江工业大学学报, 2000, (03): 9-13.   
[18] 马国丰, 屠梅曾. 制约因素在项目进度管理的应用 [J]. 管理工程学报, 2002, (04): 72-75.   
[19] 李建平, 王书平, 宋娟. 现代项目进度管理 [M]. 机械工业出版社, 2008.   
[20] 赵彬, 王友群, 牛博生. 基于 BIM 的 4D 虚拟建造技术在工程项目进度管理 中的应用 [J]. 建筑经济, 2011, (09): 93-95.   
[21] 王佳敏, 陈永洲, 王敏. 工程建设项目成本进度集成控制的模糊挣值法研究 [J]. 项目管理技术, 2014, (1): 7.   
[22] 袁家永. 基于 WBS 理论的 JLW公园项目进度计划管理研究 [D]. 哈尔滨工 业大学，2018.   
[23] 薛建英, 谭萍, 孟繁敏. BIM 与挣值法在施工进度及成本控制中的应用研究 [J]. 建筑经济, 2019, 40(06): 115-119.   
[24] 田旻, 张光军, 刘人境. 基于改进关键链方法的 MRCPSP 的鲁棒性优化 [J]. 系统工程学报, 2019, 34(02): 277-288.   
[25] 谭泽涛. 基于关键路径法的项目进度管理研究 [J]. 建筑经济, 2019, 40(9): 5.

[26] 赵宏. 基于挣值法的某工程建设项目成本控制研究 [J]. 建材与装饰, 2019,(27): 2.

[27] 张凯钧, 夏叶津, 孙嵘. 探究关键链在手机研发项目进度管理中的应用 [J].数字通信世界, 2020, (07): $1 8 3 + 1 9 1$ .

[28] Lotfi R, Yadegari Z, Hosseini S, et al. A robust time-cost-quality-energyenvironment trade-off with resource-constrained in project management: A case study for a bridge construction project [J]. Journal of Industrial and Management Optimization, 2022, 18(1)：375-396.   
[29] Sami Ur Rehman M, Thaheem M J, Nasir A R, et al. Project schedule risk management through building information modelling [J]. International Journal of Construction Management, 2022, 22(8): 1489-1499.   
[30] Ibrahim H, Jones Jr M D, Andolsek K M. Use and potential misuse of milestones [J]. Journal of Graduate Medical Education, 2021, 13(2): 283-284.   
[31] Meng K, Zhang W, Qiu J, et al. Offshore transmission network planning for wind integration considering AC and DC transmission options [J]. IEEE Transactions on Power Systems, 2019, 34(6): 4258-4268.   
[32] Kartini I A, Aspiranti T, Rani A M. Implementation Of Program Evaluation And Review Technique (Pert) To Optimize Shophouse Development Projects [J]. Journal of Management and Energy Business, 2021, 1(1): 117-127.   
[33] Li Y, Li X, Gao L, et al. An efficient critical path-based method for permutation flow shop scheduling problem [J]. Journal of Manufacturing Systems, 2022, 63: 344-353.   
[34] 黄朝合. 谈香蕉曲线在工程施工进度与成本控制方面的应用 [J]. 工程建设 与设计, 2009, (10): 3.   
[35] 李萌, 郭大为, 王慧宇等. 基于鱼骨图分析法的呼吸机质控检测影响因素探 讨 [J]. 中国医疗设备, 2020, 35(6): 4.   
[36] Song J, Martens A, Vanhoucke M. Using Earned Value Management and Schedule Risk Analysis with resource constraints for project control [J]. European Journal of Operational Research, 2022, 297(2): 451-466.

# 致谢

在东北大学工商管理学院的研究生学习生活即将结束，非常感谢东北大学严谨务实的学风熏陶，感谢学校各位老师的教导，感谢同学们对于我的帮助。在东北大学的学习生活使我变得更加成熟，更加自信。对于工程管理有了更加深刻的领悟与理解，使我能够在工作中更加游刃有余。

我特别要感谢我的导师-戢守峰老师。戢老师渊博的学识，严谨的治学态度都对我的学习和思想产生了深刻的影响。此论文能够顺利完成更是有幸得到了戢老师精心指导，从选题、立意、写作、修改直至最终定稿都给予我悉心指导，通过不断打磨，论文不断完善。正是因为戢老师不断指导与帮助，才使我日益进步，我为我能有这么一位可敬的良师感到骄傲。

同时，我还要感谢工程管理专业所有的老师和同学，感谢各位老师的指导与教学，使我受益匪浅。正是在东北大学的求学，使我能够认识这么多志同道合的同学们，在这两年中，大家共同学习，共同进步，我为能够在这么优秀的集体中学习而自豪。

最后，感谢我的父母及家人对我的鼓励与支持，来自家人的关爱与支持是我前进的动力，让我有足够的信心去实现自己的梦想。

图：8  
表：11  
页数：72  
参考文献：36