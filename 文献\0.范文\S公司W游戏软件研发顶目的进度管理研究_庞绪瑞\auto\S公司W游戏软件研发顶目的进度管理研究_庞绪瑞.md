密级：公开

# 硕士学位论文

![](images/030055ba1a0e6f6282c5046e47433d4484f617c1964804f72db8838fa363f6b2.jpg)

题目：_S公司W游戏软件研发项目的进度管理研究

学号：_2021180442

姓名：_庞绪瑞

学科专业：_工程管理

[养方式：_全日制]

导师：_柏琳

学院：经济与管理学院

密级：公开

# 北京郵電大學

硕士学位论文（专业学位）

![](images/87e875cbb9afd58452a99e419ca31a019d0a9086ad2bc344525d21b20856b520.jpg)

题目：S公司W游戏软件研发项目的进度管理研究学 号： 2021180442姓 名： 庞绪瑞学科专业： 工程管理培养方式： 非全日制导 师： 柏琳学 院： 经济与管理学院

# BEIJING UNIVERSITY OF POSTS AND TELECOMMUNICATIONS

Thesis for Master Degree (Professional degree)

Title: Research on Progress management of Company S's W game software development project

Student ID: 2021180442

Candidate: PangXuRui

Major: Engineering Management

Form: Part Time

Supervisor: BaiLin

Institute: School of Economics and Management

# 答辩委员会名单

<table><tr><td>职务</td><td>姓名</td><td>职称</td><td>工作单位</td></tr><tr><td>主席</td><td>司亚清</td><td>副教授</td><td>北京邮电大学</td></tr><tr><td>委员</td><td>杨旭</td><td>副教授</td><td>北京邮电大学</td></tr><tr><td>委员</td><td>苏静</td><td>副教授</td><td>北京邮电大学</td></tr><tr><td>委员</td><td></td><td></td><td></td></tr><tr><td>委员</td><td></td><td></td><td></td></tr><tr><td>秘书</td><td>李雅文</td><td>副教授</td><td>北京邮电大学</td></tr><tr><td>答辩日期</td><td colspan="3">2023-9-12</td></tr></table>

# S 公司W游戏软件研发项目的进度管理研究

# 摘要

随着信息技术的发展和游戏娱乐产业在此基础上的蓬勃应用，网络游戏行业成了非常热门的文化娱乐创意产业，满足了人民进一步的娱乐需求，然而网络游戏的迅猛发展迫切需要合理的项目管理方法进行指导改进，加强对网络游戏研发项目的进度管理，包括以科学的方法制定项目进度计划和积极正确的组织措施，使用专业合理的手段及工具，使项目确保项目按计划运行及动态监控及纠正执行过程的偏差，达到项目过程的进度适应性、项目产品高质量要求和成本预算可控性等特点。

网络游戏项目不同于传统IT项目，网络游戏中创新设计尤为重要，而且技术更新迭代快及专业分工的精细化，软件开发项目中各模块分工明确和充分协作，项目管理在其中发挥着很多作用。相较于传统软件行业，游戏项目需要更多领域合作，对项目管理的要求更高。研发项目具备逻辑复杂程度高，创新程度多，职业工种多样，人员沟通复杂，需求变更频繁及版本任务的时间紧迫等特点，诸多因素容易导致项目研发的进度失控，进而导致时间延后的成本超出和急迫的更改带来影响产品质量下降等情况发生，因此为了能保证项目进度按时按质完成，对游戏项目进度管理的研究尤为重要。同时这也是目前笔者所在游戏研发公司的网络研发项目需要解决优化的问题。

本文以S公司W游戏项目为研究对象，分析了项目的研发背景、研发方法流程、进度管理等情况，结合进度管理理论、问卷访谈数据对公司内实际项目的进展情况研讨问题原因与进行原因分析。在此基础上提出一些问题的解决方法，制定S公司W游戏项目进度管理策略，重整项目计划，实时地根据项目中的偏差进行实时调整。最后在力图工期达标的同时配合缓冲区设置，为项目计划的顺利进行提供保障措施，达到最终的项目进度优化效果。本文提出的一些解决问题方法也可以为同行业同类别项目提供相应的参考。

关键词：项目管理进度管理游戏项目

# Research on Progress management of Company S's W game software development project

# ABSTRACT

With the development of information technology and the vigorous application of the game entertainment industry on this basis, the online game industry has become a very popular cultural and entertainment creative industry, meeting the people's further entertainment needs. However, the rapid development of online games urgently needs reasonable project management methods to guide and improve, and strengthen the progress management of online game research and development projects, It includes developing project schedule and positive and correct organizational measures in a scientific way, using professional and reasonable means and tools to ensure that the project runs as planned, dynamically monitors and corrects deviations in the implementation process, and achieves the characteristics of schedule adaptability of the project process, high quality requirements of project products, and controllable cost budget.

Online game projects are different from traditional IT projects. Innovative design is particularly important in online games, and technology updates and iterations are fast and professional division of labor is refined. In software development projects, each module has a clear division of labor and full cooperation. Project management plays a lot of roles in it. Compared with the traditional software industry, game projects need more field cooperation and have higher requirements for project management. R&D projects are characterized by high logic complexity, many innovations, diverse types of work, complex personnel communication, frequent demand changes, and tight time for version tasks. Many factors are likely to lead to the uncontrolled progress of project R&D, which will lead to the cost of time delay and the decline of product quality caused by urgent changes. Therefore, in order to ensure that the project progress can be completed on time and quality, The research on game project schedule management is particularly important. At the same time, this is also the problem that the network R&D project of the game R&D company where the author works needs to be optimized.

This paper takes the W game project of S Company as the research object, analyzes the research and development background, research and development methods and processes, progress management and other situations of the project, and combines the progress management theory and questionnaire interview data to discuss the progress of the actual project in the company and analyze the reasons. On this basis, it proposes solutions to some problems, formulates the schedule management strategy of S Company's W game project, reorganizes the project plan, and makes real-time adjustments according to the deviation in the project. Finally, while trying to reach the construction period, cooperate with the buffer zone setting to provide guarantee measures for the smooth progress of the project plan and achieve the final project schedule optimization effect. Some solutions proposed in this paper can also provide corresponding reference for similar projects in the same industry.

KEY WORDS: project management,schedule management,game project

# 目录

第一章绪论  
1.1 研究背景 ........................................................ 1
1.2 研究目的和意义 .................................................. 1
  1.2.1 研究目的 ................................................... 1
  1.2.2 研究意义 ................................................... 1
1.3 国内外研究现状 ................................................. 3
  1.3.1 国外研究现状 ............................................... 3
  1.3.2 国内研究现状 ............................................... 4
1.4 研究方法 ....................................................... 5
  1.4.1 文献理论研究 ............................................... 5
  1.4.2 案例研究法 ................................................. 5
  1.4.3 专家判断法 ................................................. 6
  1.4.4 问卷调查法 ................................................. 6
  1.4.5 项目进度控制法 ............................................. 6
1.5 研究内容和框架 ................................................. 7
1.6 本章小结 ....................................................... 8

第二章 相关理论综述
2.1 项目进度管理的理论和方法 ........................................ 9
  2.1.1 项目进度管理的理论体系 ...................................... 9
  2.1.2 项目进度控制原理方法 ....................................... 10
2.2 项目进度控制管理工具 ........................................... 11
  2.2.1 甘特图 .................................................... 11
  2.2.2 关键路线法(CPM) ........................................... 11
  2.2.3 PERT计划评审技术 .......................................... 11
  2.2.4 里程碑图 .................................................. 12
  2.2.5 工作分解结构WBS ........................................... 12
2.3 网络游戏项目进度管理 ........................................... 13
  2.3.1 网络游戏项目特点 ........................................... 13
  2.3.2 网络游戏项目进度管理 ....................................... 14
  2.3.3 影响网络游戏项目进度因素 ................................... 15
  2.3.4 网络游戏研发项目的管理现状 ................................. 16
2.4 本章小结 ...................................................... 17

第三章 W游研发项目的进度管理现状与问题 .............................. 18
3.1 S游戏公司概述和项目情况 ........................................ 18
  3.1.1 S游戏公司简介 ............................................. 18
  3.1.2 S游戏公司组织架构 ......................................... 18
  3.1.3 W网络游戏项目情况 ......................................... 18
  3.1.4 现有进度管理情况 ........................................... 19
3.2 W网游研发项目进度管理现状及存在问题调研 ......................... 20
  3.2.1 资料与文献理论研究 ......................................... 20
  3.2.2 问卷调查法 ................................................ 21
  3.2.3 专家判断访谈 .............................................. 23
3.3 现有进度问题的成因分析 ......................................... 25
  3.3.1 开发生命周期模型适应性分析 ................................. 25
  3.3.2 活动工期与任务分解不合理分析 ............................... 25
  3.3.3 组织机构问题的成因分析 ..................................... 25
  3.3.4 计划编制问题的成因分析 ..................................... 26
  3.3.5 管理制度与流程问题的成因分析 ............................... 26
  3.3.6 进度控制问题的成因分析 ..................................... 26
3.4 本章小结 ...................................................... 27
第四章 W游研发项目进度管理改进方案设计 ............................ 28
  4.1 优化WBS任务分解 ............................................ 28
  4.2 调整项目进度计划 ........................................... 30
  4.3 任务活动历时估算修改 ....................................... 31
  4.4 增强人员的能力素质 ......................................... 33
  4.5 需求变更控制优化 ........................................... 35
  4.6 进度的绩效管理优化 ......................................... 35
  4.7 敏捷开发模式推进 ........................................... 37
  4.8 建立风险预警机制 ........................................... 38
  4.9 新增任务缓冲区的设置 ....................................... 40
  4.10 本章小结 .................................................. 42

第五章 W网游研发项目进度管理实施及效果 ........................... 43
  5.1 具体实施保障 ............................................... 43
  5.2 实施效果评价 ............................................... 44
  5.3 本章小结 ................................................... 46

第六章 结论与展望 ................................................ 47
  6.1 研究结论 ................................................... 47
  6.2 研究不足与展望 ............................................. 49
参考文献 ......................................................... 50

# 第一章绪论

# 1.1 研究背景

随着互联网的发展，21世纪进入了移动互联网的时代，信息发展带动智能终端设备和网络宽带技术的更新升级换代，移动互联网进入迅猛发展期，互联网软件已经成为人们密不可分的一部分，网络游戏也成了很多人的日常消遣活动，网络游戏产业规模高速增长，用户规模屡创新高，截至2021年国内游戏市场营销总额达到2965多亿元[，游戏软件项目也经历了从小型单机到中型游戏、大型网络游戏几个阶段。到如今的网络游戏时代的欣欣向荣，游戏软件项目竞争日益激烈，国内游戏产业格局出现产业巨头占据市场规模多半，前五大游戏企业市占率由2015年的 $5 5 \%$ 左右增至2020年的 $8 4 \%$ ，其中腾讯与网易市占率长期占据高位及其研发项目中大部分游戏多元化，游戏品类全，创新能力强。对中小游戏厂商带来一定影响，中小游戏厂商的游戏产品之间的竞争也是更显加剧[2²。中小游戏厂商的游戏研发项目跟传统软件研发项目的区别在于一款产品对于整个项目组甚至于公司至关重要，重视程度很高，研发周期通常耗费时间长。网络游戏研发项目的开发版本迭代周期紧、开发内容量多等特点，同时开发过程中的需求变动、进度延后影响产品的上线，同时赶工影响产品质量。因此对网络游戏研发项目的合理管理要求日益增高，进度管理的把控对项目起到很大作用，良好的进度管理是保证游戏项目按照良好路线进行研发的基础。

当前游戏软件项目成功率并不高，很多项目在未上线就死在研发期，还有一部分上线运营期的营收不佳和项目管理不合理导致营收少，甚至损失严重，进而影响游戏项目[³。传统游戏软件项目的管理模式，迫切地需要选用合适的方法模式用在游戏软件项目管理中，通过针对游戏软件项目的特点，深入研究，以现有管理理论知识为基础，使用进度管理理论方法和工具优化项目管理，对如今的大中小游戏软件项目来说至关重要。

本文研究以S公司W项目为研究对象，研究W网络游戏研发项目的进度管理现状和分析现有的问题及提出相应的解决方案。希望能够提升W项目的进度管理水平，也为类似项目提供借鉴意义，进而提升S公司的项目管理能力。

# 1.2 研究目的和意义

# 1.2.1研究目的

中国的网络游戏产业的由高速发展期已经调整进入了稳定期。市场也由原来的蓝海变成了红海市场。主要原因在于技术壁垒的增加以及成本的不断增加、开发难度增加和产品同质化严重、项目管理越发困难等。目前的网络游戏的市场份额占据总游戏市场份额的 $70 \%$ 以上，但仅几家游戏研发大厂占据大部分营收。由于早期游戏项目规模相对较小、复杂度不高，项目研发过程中没有一套完整的进度控制和管理措施或者没有实时更新换代的管理措施。然而，随着社会发展技术进步和游戏项目规模的不断扩大，网络游戏项目研发的进度管理的更新显得尤为重要。根据网络游戏的市场份额，网络游戏份额占游戏的绝大部分，复杂度也最高，管理也最难，因此这类游戏项目的研发进度管理也更具代表性。

本课题研究的目的主要是，结合S公司的W项目的进度管理现状，主要分析项目管理进度中的问题，结合现有科学理论，利用一些技术方法，对项目进度进行研究设计，解决项目中应对变化频繁、进度滞后、延期上线等问题。也通过研究的过程能够避免同类问题，可以为给S公司在项目进度管理方面提供一定的借鉴作用和参考价值。也为同类型游戏项目在管理中提出可参考的可行性的解决方案。

# 1.2.2 研究意义

本课题的研究主要具有实践意义。在市场经济中，网络游戏研发公司能否获得预期的利润很大程度上取决于其项目管理效率和能力。网络游戏开发追求快速版本迭代开发，周期上线运营等特点，需要一套科学合理的项目进度管理对企业项目进行指导。在需求调研，需求确认开发，测试部署上线整个流程的进度的管理，关系游戏软件项目产品及时上线，保证运营质量及产品生命周期。通过研究S公司W游戏软件研发项目的进度管理实践中，探索进度管理存在问题的及制定相应解决方案，为S公司在软件项目中进度计划制定、进度控制等方面积累实践经验和提供参考案例。从而提高项目团队对进度的控制能力。这有助于确保项目按时交付，减少项目延期风险，可以找到资源分配中的瓶颈和优化机会，提高资源利用效率，提高管理效率，降低成本和风险，扩大项目的成功率，从而获得更高的经济效益。

本文针对网络游戏项目的开发进度进行了分析和研究，旨在为业内人员和游戏项目经理提供更好的项目进度管理方法和一些问题解决方案的参考，以确保游戏项目能够按照预定的时间保质保量顺利完成，并规避项目风险。通过对网游项目的特点进行梳理，结合传统软件项目管理的方式方法，通过实践，制定适用于网络游戏项目的可以参考的开发流程和进度管理方面的方案，旨在提高项目管理进度的效率和质量，降低项目风险。其中，包括制定详细的项目计划和时间表、建立有效的沟通机制和团队合作、及时发现潜在的问题与风险和制定解决措施。通过本文的研究和分析，提供基于W项目实际开发经验进行归纳总结的解决方案，以更好地推进和管理网络游戏项目，从而获得更高的经济效益和用户满意度，具有相应的实践意义。

# 1.3国内外研究现状

# 1.3.1国外研究现状

项目进度管理是项目管理中的一个重要方面，其历史发展起源于20世纪初期，项目进度管理最早起源于工业革命时期。此时公司开始注意到通过工程管理来提高生产效率的重要性，从而开始进行生产项目进度的协调和管理。

到了20世纪90年代至今，在互联网和信息技术的大力推动下，项目进度管理的工具和方法得到了巨大的进步，如BPM、敏捷开发、Scrum和看板管理等。同时项目进度管理已成为不同行业和领域在其项目管理中的重要部分，并成为使企业获得项目成功的重要技能。项目进度管理是项目管理中的一项重要内容，其目的是确保项目活动与任务在规定的时间内完成，以实现项目目标。国际上针对项目进度管理的理论现状可归纳如下几种。

资源或任务的分配理论：工作分解结构（WBS）的历史可以追溯到1960年代，起源于美国国防部，当时美国国防部采用了一种叫做Pert Chart（程序评审技术图）的项目管理方法，而WBS作为PertChart的重要组成部分之一出现。美国国防部率先在导弹项目上应用工作分解结构WBS参与研究，以探索如何更好地管理复杂的航空航天项目。为了解决这个问题，研究人员应用WBS理论，并将其应用于实际项目中进行测试和优化。WBS帮助确定项目范围和任务，将任务分为可管理的工作单元；RBS是一种资源分配的层次结构，类似于WBS，最早由美国工程师和项目管理专家PMBOK联合开发。但不同之处在于RBS根据资源类型对项目可用资源进行了分解。它将项目中所需的所有资源划分为特定类别，例如人力资源、物资资源、设备资源和财务资源等，并将这些资源进一步分解为更小的组成部分。RBS则帮助管理和调配各种资源，以便更好地完成任务。资源依赖理论认为项目进度取决于工作流程中资源之间的依赖关系。由于不同的工具和技能之间存在不同的依赖关系，因此理解和管理这些依赖关系对项目进度管理至关重要。

时间管理理论：项目的时间是极其重要的资源，时间管理可以通过制定明确的时间表，获得合理的项目进展，并减少时间的浪费，从而提高工作效率。时间的管理包括计划编制、时间估算、进度控制等方面的知识和技能。常用工具包括甘特图、网络图等。1917年，HenryGantt发明了甘特图。甘特图是一种常用的流程图，用于显示任务在时间轴上的子进程和工期。这种方法是管理复杂项目的一种有效方法，可以帮助管理人员快速了解进度，并在需要重新安排时进行任务分配。能够在视觉上呈现活动/任务的时间表。任务在执行过程中，也能分析任务是否正常按时运行，检查偏差，若出现偏差采用纠正偏差相关方法，包含方法有组织类措施，技术类措施等。旨在控制项目进度任务表中变动部分，加快进度任务执行。

开发模式的理论：包括使用瀑布模型、敏捷方法、迭代式开发等方式。瀑布模型是由W.W.Royce 在1970年最初提出的软件开发模型。复杂的大型项目采用瀑布模型已经没有竞争力，采用合理的开发模式能够促进项目的进度推进。迭代式开发最初是在20世纪70年代由BarryBoehm提出的。迭代式开发游戏项目通常需要从策划、设计、开发、测试到运营等多个阶段，并且每个阶段都会存在诸多风险和挑战。采用迭代式开发的方法可以更好地掌控每个阶段的进度，确保各个阶段的顺利推进和质量保障。1988年，Barry Boehm正式发表了软件系统开发的“螺旋模型”，它将瀑布模型和快速原型模型结合起来，强调了其他模型所忽视的风险分析，特别适合于大型复杂的系统。2001年，KentBeck,MikeBeedle等软件开发领域人物以《敏捷宣言》整理出的软件开发方法。敏捷开发强调快速反应变化实现小范围的迭代式演进，强调反复迭代、持续交付、灵活适应变化等特点。常用工具包括看板、燃尽图等。

国际上许多大中型的游戏公司，比如微软、索尼等都已经普遍采用敏捷开发模式进行管理项目。逐步适应游戏需求多变及创意特性突出等特点，并且其公司在21世纪以来产出了很多3A品质的游戏产品，从中验证了敏捷开发在游戏项目管理中的有效性。总之，以上这些理论、方法和工具是项目进度管理的重要实践，并可以帮助管理人员更好地掌握项目进度情况，从而实现项目目标。可以看出国外的游戏项目管理理念的成熟与全面，积极探索开发模式的实际应用，解决实际项目中的进度控制的系统问题，风险管理和质量控制问题。已经注重敏捷开发和自组织式管理，引导了员工的创新和自我激励。在项目上取得很多成果。

# 1.3.2国内研究现状

我国的项目进度管理起步较晚，由20世纪早期，我国的软件项目进度管理主要依靠管理人员的个人经验和技能，没有系统的管理方法和工具。到21世纪初期，我国开始引进国际先进的软件项目管理理论和方法，如PMBOK指南、CMMI模型等。同时一些国内企业也开始逐步探索和建立自己的软件项目管理模式和体系。其中PMRC于2001年在其成立10周年之际也正式推出了《中国项目管理知识体系》（C一PMBOK）。

而近些年以来，国内大量的软件项目的执行开展以来，也逐步形成了一套相似的项目进度管理知识体系[4]。我国的软件项目进度管理逐渐成熟和规范化。一些企业建立了完整的项目管理体系，采用敏捷方法、迭代式开发等先进的软件开发方法，能够更好地适应不断变化的市场需求和技术趋势。其中华为集团发展出独特的IPD管理体系当属中国先进项目管理系统[5]。

当前，中国的软件项目进度管理已经成为软件行业中的一项重要管理实践。在政府和企业的支持下，一些开发团队和管理人员通过不断学习和实践，不断优化和改进软件项目进度管理模式和方法，推动了软件项目管理的规范化和专业化发展。我国的研究学者也做出很多突出的研究。举例以下学者的研究包括：

余建民在项目进度管理中的活动作业的进度的关键点采取动态管理，并利用Project软件对项目进度计划进行编写以及变更，以达到控制进度，完成既定目标的作用[6。刘晓东在项目进度管理中提出知识体系管理在项目进度管理中的作用和方法。方觉晓的论文研究游戏软件项目的范围与进度管理研究与实践。谢添敏发表的论文《项目管理理论在游戏项目开发中的应用》。

以上简要说明了我国学者在进度管理方面的研究。除此之外还有很多研究学者在进度管理的不同方面做出过重要贡献，具有很多学习参考价值。国内的项目管理注重发展路径从注重管理层的指导和控制发展到目前的注重员工的实施力和执行力与创新力。比如国内互联网大厂的普遍采用的扁平化管理，注重员工自我的效率提升。信息化应用发展应用到全行业的各个方面，应用IT技术、数字技术、自动化工具和协作平台，大幅度提高了进度管理的精度和效能[8]。

综管以上国内的研究现状，很多学者研究注重理论方面的探索研究及传统软件项目的管理研究，在某些特定类型的项目中较少有真正实际的落地经验，对于软件项目管理领域中的游戏项目管理领域，研究文献相对还是偏少的。怎么在需求多变的游戏研发项目管理中规避风险，把控进度，合理落地。还是具有挑战性的。因为在项目具体落地实施过程中有很多实际的特定项目的问题需要解决。本文的研究主要是以实际项目为基础，进行相关理论的应用实践及研究。

# 1.4研究方法

本文采用的主要理论研究方法：文献查阅、案例分析、问卷调查、专家判断法、关键链路法。用到的关键技术包括甘特图、三点估算、群体决策、进度网络图、关键路径法、项目管理软件等。

# 1.4.1 文献理论研究

通过查阅理论书籍，搜索引擎搜寻资料，知网查阅论文期刊等途径，搜集整理与论文研究相关资料。明白该领域的前沿技术及业界应用情况。在研究资料和应用理论知识的基础上，结合笔者参与开发的十多年项目经验，对S公司W项目进行理论分析。

# 1.4.2案例研究法

本文通过S公司W项目的管理过程，重点关注进度管理情况，进行数据收集与整理，做定量定性的分析。结合项目进度管理方法工具。对W项目进行实际分析问题，探索原因，并想出若干解决问题措施进行说明。

# 1.4.3专家判断法

本文通过S公司W项目组中专家进行评估总结，与项目中重要人员进行沟通判断，从不同角度，不同岗位人员，不同的过程，记录相关信息数据，为后续分析问题与总结问题及解决问题提供参考。

# 1.4.4 问卷调查法

问卷调查法是一种数据收集方法，在调查中使用广泛。是指为统计和调查为目的、以设立问卷的方式制作给受访者分发问卷并让他们回答问题来收集信息和意见。研究者用针对性设立问卷的问题，一定程度上对所研究的问题进行度量，进而搜集到可靠资料的一种方法。其主要优点有可以快速经济的获取数据、获得相对广泛的反馈，多种形式的问卷有侧重性规范化数据。可以拿数据直接分析变量之间的关系和趋势，为进一步研究提供参考和线索。

# 1.4.5项目进度控制法

通过项目进度管理软件的监控、计划及执行情况的数据。分析项目实际运行中的任务活动执行情况，在项目工期、缓冲区的设置等方面通过制定措施改善项目进度管理。

# 1.5 研究内容和框架

本文针对网络游戏项目类型，以S公司W项目为案例，分析S公司的W项目的管理现状，然后对W项目在进行管理中是否存在问题进行研究和分析，使用管理工具和方法改进W项目进度管理进行改进优化，跟踪W项目进度执行流程，纠正进度偏差，提出实施保障措施，依托W项目进度管理优化方案，进而探索网络游戏项目的进度管理理论方法。本文分六章进行讨论。

本论文的研究内容结构框架如下图1-1所示：

第一章主要介绍了题目的网络游戏项目研发中进度管理的重要性。首先介绍了题目的研究背景及研究目的和意义，课题的国内外发展的理论现状，进一步挖掘了网络游戏项目进度管理的价值。

第二章主要介绍项目进度控制的理论和方法。本章分析了项目进度计划的项目进度计划、控制、项目进度约束、风险评估和相关管理工具，阐述网络游戏项目类型的进度管理的相关内容和管理特点及影响进度的影响因素。

![](images/b948f85e3a4346e2f19fea6a25f4be80ff037cde5fa3c872b7d8660b35b1f4db.jpg)  
图1-1研究内容框架图

第三章首先介绍S公司的背景，并全面分析了网络游戏W项目开发中的管理现状及存在问题调查研究分析，从项目规划、项目分解任务包方法、项目实施执行与控制四个方面分析网络游戏项目进度管理，然后对网络游戏项目的组织机制等因素分析，探索网络游戏项目进度控制失灵的原因。并进行原因分析总结。

第四章主要讲述将总结的项目研发进度控制经验和方法应用于大型网游W提出从项目管理、项目开发过程、团队组成结构等方面的解决方案的改进。提出相应的补救措施，优化方案。然后综合总结进度控制相关的实践经验。

第五章论述要实行方案的保障。总结进度控制相关原则在实践中在网络游戏项目开发中的具体应用。在项目实行方案过程的探索应用方案，补充了进度控制原则在网络游戏项目开发中的局限性。

第六章最后为网络游戏项目进度控制研究提供了本文研究结论回顾和方向展望。

# 1.6本章小结

本章论述了论文题目的研究背景和题目的研究意义，阐述了相关研究的国内外研究现状和使用的研究方法、给出了本篇文章的主要框架结构。

# 第二章相关理论综述

# 2.1 项目进度管理的理论和方法

# 2.1.1项目进度管理的理论体系

项目进度受多种因素影响，有成本、风险、质量、时间等多种因素，并且有相互作用的影响。进度管理中主要使用的主要原理有：动态控制理论，系统理论，信息反馈原理，弹性变化原理，网络计划技术原理，时间管理理论[9]。进度管理是项目管理中非常重要的部分，它涉及计划、监督和控制项目进度，以确保项目按时完成。其理论体系包括以下几个方面：

工期的管理：主要包括工作分解结构（WBS）、网络图、里程碑计划等。这些工具和技术用于确定项目活动、任务、持续时间和关键路径等因素，并建立规划框架。

资源的管理：资源管理包括人力、资金、物力等各种资源的规划、分配和优化。该理论侧重于如何用有限的资源实现利益最大化。

进度风险的管理：风险管理旨在识别潜在风险，评估其可能性和影响，并采取适当措施加以解决。在项目进度管理中，风险管理可以帮助识别可能导致延迟的风险并制定相应的响应策略。

进度偏差分析及纠正理论：偏差分析是一种比较实际进度和计划进度之间差异的方法。该理论为评估项目绩效提供了定量指标。常见的偏差分析方法为挣值分析。采用分析之后进而对偏差进行纠正，采取偏差值进行相应资源调度，使进度能够纠正回原本计划的措施。

进度阶段性的管理：阶段管理是指将整个项目划分为多个可管理的阶段，以更有效地控制进度、质量和成本。该理论强调在每个阶段结束时进行评估，并根据结果进行必要的调整和改进。

# 2.1.2 项目进度控制原理方法

网络游戏软件项目也是属于软件工程项目，整个开发生命周期内，合理把控时间、进度、成本三个要素合理规划，使整个项目在合理的开发大周期内完成产品，进而按周期进行产品的版本更新上线。目前已有大量的项目进度管理的方法工具有：甘特图、关键链、设立缓冲区、关键路径法、工作分解结构等方法。

项目进度管理是项目管理10大领域中非常重要的一个模块，是保证项目顺利完成的先决条件。项目进度管理过程有7个模块组成有：计划进度管理、定义活动、制定活动顺序、估算活动资源、估算活动持续时间、制定进度计划和控制进度。在网络游戏项目过程中，这些过程都非常重要，然而其复杂度和影响因素也因项目而异[10]。

计划进度管理：这个过程是整个项目进度管理的基础，它提供了政策、流程和标准化文档，确保项目进度管理的一致性和规范性。游戏开发通常有多个部门协同工作，需要一个良好的计划进度管理过程来统一规划和管理项目进度。同时网络游戏项目类型的特点也会对计划进度管理有其独特的影响。

定义活动：这个过程通过工作分解结构（WBS）将项目工作包细化为更小的部分，以定义需要执行的活动，这是项目执行的基本组成部分。针对游戏项目，定义活动需要考虑到游戏玩法、图形资产、音频资产、程序开发等方面，需要考虑游戏设计师、程序员、美术设计师和音效设计师等多种团队成员的需求。

制定活动顺序：这个过程将项目内部活动之间的关系进行整理，综合考虑各种约束和限制条件，获取项目执行的正确顺序。在游戏项目中，制定活动顺序需要考虑游戏玩法和逻辑，例如制作游戏关卡时需要的美术产出、程序制作、玩法定义三种任务活动之间的顺序。

估算活动资源：这个过程通过识别每个活动所需的资源类型和数量，对项目成本进行有效管理。在游戏项目中，资源包括人力、硬件和软件等多种类型。需要考虑到游戏开发过程中需要使用的引擎和工具，以及不同团队之间的协调。

估算活动持续时间：这个过程利用技术和经验数据，估算每个活动所需的时间来完成，以便确定项目完成日期和可行性。在游戏项目中，不同类型的游戏可能有不同的开发周期，需要考虑到游戏类型、开发平台、游戏功能等因素。

制定进度计划：这个过程将所有先前的信息整合起来，建立一个完整的项目进度计划，以确保项目按时开发与交付。在游戏项目中，制定进度计划需要考虑到各团队之间的相互依赖关系，特别是在开发复杂的游戏时。

控制进度：这个过程监控项目的实际进度与计划进度的差异，并采取相应的措施以确保项目进度可控。在游戏项目中，可能会出现许多不可预测的因素，如技术难题、创意变化等，需要及时调整进度计划以保证项目按时完成。项目执行进度也会与计划发生偏差，控制进度就是要监督项目进度情况，有日常观测监督和定期观测监督等周期的方式检查，然后处理纠正偏差。

# 2.2 项目进度控制管理工具

# 2.2.1 甘特图

甘特图是一名美国科学家亨利·甘特在1917年提出的一种项目进度管理工具，表现为一种流程图，用于显示项目计划和任务进度。在项目研发中，甘特图通常用于跟踪各个任务的完成情况，以及整体进度和预算的变化情况。它可以帮助项目团队更好地了解项目的整体进度和预算的情况，并根据需要进行调整。例如，开发团队可以使用甘特图来跟踪每个任务的进度，并确保按照预定时间表推进游戏开发。如果有任务延迟或出现其他问题，团队可以及时采取措施来避免影响整个项目的进度。但甘特图也有一定的缺点，项目各活动间的逻辑关系不能在甘特图上直观的展现出来，任务活动的定量分析也不直观。

# 2.2.2 关键路线法 (CPM)

关键路线法（CPM）是通过网络图方法展现项目任务路径的方法，不考虑资源限制的估算项目的工期及任务活动的工期，它使用网络图示的分析技术使用顺推与逆推两种方式，计算出每一项活动的最早开始、最早结束、最晚开始和最晚完成时间。通过计算活动线路图的工期找出一条工期时间最长的路径作为关键路径。关键链技术是一种基于理论的项目管理方法，用于识别项目中的瓶颈和制约因素，并提供解决方案，以确保项目能够按时成功完成。在研发项目中，关键链技术可以用于确定项目开发过程中的瓶颈，并找到解决方案来优化资源分配和管理。例如，开发团队可以使用关键链技术来确定哪些任务和关键活动可能会影响项目进度，并采取措施来缓解这些影响。而非关键路径上的任务节点不变动的情况下不影响工期，若出现延时会动态更新关键链路。项目进度管理的焦点集中到关键路径上进行合理管理。通过定位分析项目中的瓶颈任务活动，来去定位专项解决，进而提高项目的成功率。

# 2.2.3PERT计划评审技术

计划评审技术（PERT）起源于美国的一项导弹项目，在当时该技术是使这一项目研发时间比计划的时间节省了 $20 \%$ ，由此引发了项目管理者的广泛学习。计划评审技术是一种项目管理方法，利用专家团队对项目计划进行审查和反馈。在进度管理方面，计划评审技术可用于检查项目开发计划的合理性和可行性，以帮助项目团队优化资源分配和管理。计划评审技术还可用于检查项目开发计划的合理性和可行性，以帮助项目团队优化资源分配和管理。例如，项目团队可以组织一次计划评审会议，邀请相关专家对项目开发计划进行审查，并根据反馈进行调整和优化。项目管理中主要应用技术在以下要点。

1.估算项目时间：计划评审技术利用三个估算值来计算项目或活动的时间：乐观时间、悲观时间和最可能时间。这些时间值结合在一起，可以计算出一个预测时间，并且给出概率范围，使项目经理可以更好地规划时间和资源。

2.确定资源需求：计划评审技术可以帮助项目经理了解每项活动需要的资源，包括人员、物资和设备等。项目经理可以根据这些信息来制定资源计划，以确保项目在预算内按时完成。

3.监控项目进度：计划评审技术可以帮助项目团队监控项目进度。项目经理可以跟踪每项活动。

# 2.2.4里程碑图

里程碑图是一种项目管理工具，用于展示项目中重要的时间点和阶段，以及与之相关联的任务和交付物。通常包括主要任务、时间点和关键里程碑等信息。在研发项目中，里程碑图可以用于追踪项目开发的不同阶段和关键交付物的完成情况。它可以帮助项目团队确定项目进度，并确保按计划推进项目的开发。通过使用项目的里程碑图，团队成员可以更好地了解项目的整体进展情况，以及每个人的角色和责任。每一个里程碑标志着一个实施阶段的终止。它使项目的进度计划及执行安排比较清晰，而且执行后有了积极进取的执行力，参与者能够知悉有若干阶段及当前阶段所需投入的工作任务及其工作产出，有利于项目管理者把控项目的进度，方便进行任务工作包的布置分配。

# 2.2.5 工作分解结构 WBS

工作分解结构（WBS）是项目管理中的一种层次化结构，用于将项目分解为可分配管理的任务包。WBS对于有效地规划、执行和监控项目非常重要，因为它提供了一个清晰、透明的视图，可以帮助项目组成员理解项目目标和任务，并跟踪进展情况。

WBS通常由以下几个层次组成：项目、阶段、交付物、工作包。

每个层次都有特定的任务和目标，这些目标在不同级别上进行细化并得到具体实施。WBS可以使项目经理和其他相关人员更加了解项目目标和所需资源的数量，从而更好地控制项目进度情况和安排成本预算。

在游戏项目管理中，WBS可以帮助项目团队确定项目目标和任务，识别需要完成的各项任务，并确保每项工作都能够顺利地实现。例如，在开发游戏项目时，WBS可以帮助团队定义阶段，如概念设计、原型制作、开发和测试，然后将其进一步分解为交付物和工作包，如游戏剧情设计、用户界面开发、音效制作等。通过对WBS的使用，团队可以更好地规划项目时间表、资源和成本，并确保所有任务都能及时完成。

# 2.3网络游戏项目进度管理

# 2.3.1网络游戏项目特点

网络游戏研发项目首先属于软件项目，是属于软件项目的一类,同时又是通过互联网作为传输媒介的游戏软件。传统软件项目管理认为软件的项目管理是为了使软件项目能够按照预定的成本、进度、质量顺利完成。进而对项目的成本、人员、进度、质量、风险等进行分析和管理。其管理的根本目的是让软件产品从开始到最终结束包括初始计划、分析设计、执行编码测试、上线预维护全过程都能够在管理者的控制之下以预定成本按期按质地完成软件直到交付软件用户手上使用。然而对于网络游戏研发项目不止具有普通软件项目的特点还有其独特类型特色的特点。下图2-1图示网络游戏项目的生命周期。

![](images/096294ef64625eda2342db85f860f5125a16a5a83f6e0dba244e9be79330406f.jpg)  
图2-1网络游戏项目生命周期

（1）网络游戏软件项目具有以下特点：

1.创意性内容开发。游戏开发是创造虚拟性世界，软件项目中需求变化较频繁，需要不断对产品进行调整和改进，通常注重创意、美感、平衡、趣味等方面。传统软件开发相对是满足已有的需求，围绕既定业务逻辑进行开发[1]。

2.技术复杂性高。软件项目技术复杂性高，需要高度专业化的人员进行开发。游戏编程语言的要求通常是技术栈丰富，需要掌握的开发技能多。需要有图形图像编程、服务器编程技术、游戏引擎架构等相关领域知识。其中游戏引擎是支撑网络游戏运行效果的核心技术[28]，开发难度极高。

3.开发成本高，要求效率产出。相对于其他项目，软件项目的开发成本通常较高。涉及内容量大，制作周期长，需要资本投入多。投资方要求回报率的情况下就需要高效率地完成开发既定工作以保证商业运转。

4.工种多样，沟通人员多。由于技术复杂性高，需要有多样性内容产出，由多个部门分别负责内容制作，并且多个部门团队之间协作沟通。其中主要部门包括美术、技术、策划三个大类，及其大类下相关细分领域的子类内容制作。就如美术部门里面就包含场景美术、原画师、地编美术、技术美术、模型美术、动画师等相关岗位人员。两个岗位之间的协作流程和标准，不是自上而下的客观“科学标准”，需要双方参与共同制定的。一款好的游戏最终评价来自于玩家游玩体验，其体验就出自相关岗位涉及领域内容[27]。

5.迭代式开发。游戏项目处在不同的开发周期或者阶段，在项目管理方式上会存在较大的差异性。软件项目通常采用迭代式开发过程，使得在开发周期内可以及时提供满足用户需求的版本，并避免一次性开发出大量功能未被用户认可的游戏软件。因此针对不同的周期采用迭代式版本内容开发。

6.质量控制重要性高。软件项目的质量控制非常重要，因为软件的质量问题可能导致严重后果，如安全漏洞或业务失误。那么就会引起运营事故，进而影响游戏产品的生命周期。

（2）游戏研发项目中的管理还具有以下特点：

1.在需求管理方面，需要关注用户需求的多样性和变化性，要及时调整开发重点；比如游戏开发前期注重新手用户容易上手，注重新颖性及符合玩家口味，后期注重留存使已经使用的玩家用户能够持续创造可玩性，调和已有玩家用户的矛盾等。

2.在范围管理方面，需考虑具体游戏类型和玩法等因素，确保开发出符合市场需求的产品。网络游戏类型具体还可分为更多种类，比如SLG,MMO,MOBA等类型。针对不同的类型开发任务内容的倾向性有着根本不同。

3.在进度管理方面，需要注意业界竞争压力和玩家期望值的提高，要保证项目按时完成。比如网络游戏线上运营期时既定时间的任务要上线，已经公布给玩家，如果不能按时上线，导致多次延期，会对玩家造成影响。即使已经上线的产品如果周期的更新版本未能上线，也会对留存玩家影响，进而对项目盈利影响。

4.效率衡量方面，创意性的工作成果不好衡量，比如美术原画的产出，工期内应该固定产出多少个人无法完全量化。比如游戏程序开放，写出的代码行数越多并不能说距离任务完成越接近。创业性的工作没有科学标准，一款游戏好玩与不好玩，无法用科学形式给出标准答案。

此外，在质量管理方面，需要特别关注游戏稳定性和流畅度等方面的问题，以及保障安全性和防作弊等方面的需求。

# 2.3.2网络游戏项目进度管理

网络游戏项目的进度管理按照PMBOK项目管理知识体系指南的标准分为五大过程组，分别是：启动、规划、执行、监控与控制、关闭。以下是每个过程组的详细内容：

启动过程组。启动过程组是确定项目目标和启动项目的过程。在网络游戏项目中，这包括识别利益相关者、创建项目章程、制定项目管理计划等活动。在游戏项目中，相关方可以是游戏发行商、玩家、开发人员等。项目研发团队中包括游戏策划师、程序员、美术人员、测试人员。在网络游戏项目中笔者参与的项目并设置有时间安排表，项目目标包括开发一款优秀的游戏、获得更多玩家、提高盈利等。项目章程需要与所有相关方进行沟通和协商，以确保他们对项目有共同的理解和期望。

规划过程组。规划过程组是为了明确项目目标并制定实现这些目标的具体计划。在网络游戏项目中，这包括定义工作范围、制定进度计划、资源计划、成本计划、质量计划和沟通计划等方面的活动。项目经理需要开始制定项目计划，包括详细的时间表、资源分配、成本评估和风险评估等方面。这些计划将成为项目执行的基础，并有助于确保项目能够按时、按质量要求完成。这包括各个部门的产出内容计划安排，涉及比如范围规划、成本规划。游戏内容规划又包括：游戏玩法设计、人物建模、场景创建等等内容。

执行过程组。执行过程组是实施项目计划并完成项目目标的过程。在网络游戏项目中，这包括直接进行开发、测试、集成和发布等方面的活动。项目组各部门包括程序员、美术人员、测试人员、运维人员、策划人员，各自执行相关内容进行协调内容产出，共同为最终产品服务。

监控与控制过程组。监控与控制过程组是为了跟踪项目的进展情况并采取必要的纠正措施。在网络游戏项目中，这包括监视进度、成本和质量，评估风险和采取应对措施等活动。

收尾过程组。收尾过程组是为了完成项目并确认其完成情况的过程。在网络游戏项目中，这包括整理和归档项目文档、向利益相关者汇报项目成果、评估项目绩效等方面的活动。

总之，网络游戏开发过程中各个过程中有相当多的输出产物与虚拟物体，需要投入大量时间和精力来完成。通过合理规划和有效执行，可以确保游戏项目能够按时、按质量要求完成，最终取得成功。

# 2.3.3影响网络游戏项目进度因素

软件项目管理一种特殊类别的项目管理，具有普适性的项目管理特点，如按需求确定任务需求的范围、按迭代目标制定迭代版本计划、按计划执行落实任务分配，按变化进行调控任务进度时间。软件开发项目各阶段项目管理的目的，是为增强对软件产品项目管理的控制能力，提升软件产品的质量，达到软件产品的开发完成。软件项目进度管理中的影响因素有很多包括时间、计划、资源等因素，而且还要结合项目的具体特殊情况，通常网络游戏项目的影响因素主要有以下几点：

计划方面：编制复杂计划需要基于具有良好技能和相关执业经验的工作或管理人员，然而游戏固有的频繁的需求变化和不精确的项目范围是游戏软件项目的最主要特征。原始的计划很难详细清楚，面面俱到。

技术方面：包括技术难度、技术选型、代码质量等都影响研发产品的进度。游戏项目的复杂度高需要涉及多个技术领域，还有技术选型不当与代码质量低下都会导致开发进度延迟。

人员方面：包括团队规模、团队成员素质、沟通协作能力等。如果人员不足、素质不够、沟通协作能力差，则很难保证有效的内部协作和团队合作，从而影响项目的进度。软件项目多依赖于个人能力，尤其是技术人员的研发实力、策划人员的专业素养等因素会对项目有重大影响。

设计方面：包括需求变更、原型设计、UI设计等。如果游戏需求频繁变更，原型设计不够清晰，UI设计不够精美，都会导致开发进度延迟。

测试方面：包括测试用例设计、测试环境搭建、测试人员素质等。如果测试不充分、测试用例不够全面、测试环境不够稳定，以及测试人员素质不够高，则会导致上线日期的延迟。

资源方面：包括资金、时间、设备等。如果缺乏资金、时间或者缺乏必要的设备，都会导致开发进度延迟。

规模复杂程度方面：项目的规模大小及复杂程度直接影响项目的进度，规模越大越复杂耗时越久。项目的时间计划是否合理，任务的工作量，需求是否明确等都会影响复杂度。当前的网络游戏项目在中前期开发阶段是经常发生需求变更的，由于初创阶段，策划人员对自己的玩法制作的需求不明确，玩法变更很频繁，在任务执行交付之后，策划和其他人员会提出很多修改的需求。进而修改整理增加进度耗时。 ;

项目的规模及复杂程度。根据项目规模确定项目进度计划办法，小项目用简单的方式，大项目用复杂的应对方法。然而网络游戏项目的频繁的需求变化和不精确的项目范围是游戏软件项目的最主要特征。

# 2.3.4网络游戏研发项目的管理现状

经过调研资料发现网络游戏项目目前的管理现状主要体现在以下几个方面：

1.项目规划不够充分：有些游戏软件研发项目在规划阶段没有充分考虑到关键因素，如项目风险、资源分配、时间管理等，导致在后续的开发过程中出现了一些问题，进度滞后或无法按时交付。而且网络游戏项目上线后经常会根据用户的游戏数据，相对频繁地在核心玩法、附加内容和盈利模式等方面进行一系列升级改造[31]。

2.研发周期不太受控：游戏软件研发项目往往需要花费数月甚至数年的时间进行研发，研发周期较长，需要持续的投入和管理。这对企业的经济实力和管理能力提出了挑战。

3.研发过程中缺乏有效的沟通和协作：游戏软件研发项目通常需要涉及多个团队之间的协作，包括策划、美术、程序、测试等多个部门。如果缺乏有效的沟通和协作，会导致项目进度滞后，版本质量下降，甚至引发部门之间纷争。

4.项目管理经验不足：目前的网络游戏软件研发项目管理人员的水平参差不齐，有些管理者缺乏实践经验，很难应对研发过程中出现的各种挑战。同时一些企业对项目管理的重视程度也有所不足。只重视游戏产品。

5.难以控制人员流动：游戏软件研发项目通常需要耗费大量的人力资源，但由于网络游戏研发行业的特点，员工流动性较大，企业难以控制员工的流动和留存，这可能会导致项目进度和质量的不稳定。员工的考核情况不足，不能严谨判断员工的实际产出及作用。

# 2.4本章小结

本章介绍项目管理和项目进度管理的理论知识，其中包括项目管理知识体系理论介绍，然后对控制原理的方法阐述，进而对进度控制用到的管理理论工具进行说明，然后又阐述了网络游戏在项目管理中的独特的项目特点，列出在网络游戏项目管理中在实际项目管理方法时遇到的特点、难点及管理现状。后续针对网络游戏项目的特点阐述影响进度管理的影响因素。

# 第三章W网游研发项目的进度管理现状与问题

# 3.1S游戏公司概述和项目情况

# 3.1.1S游戏公司简介

S游戏公司成立于2003年，主要从事游戏软件的开发，秉持做好原创中国特点的精品游戏理念，公司的理念提出用打造精品网络游戏创建中国的文化娱乐游戏产业高端品牌。公司已经成功挂牌新三板市场。目前从事游戏软件的开发，致力于大型网络游戏研发领域，公司开发过多款玩家认可的PC游戏项目，而在手机端游戏项目，中是相对大公司缺乏成功项目的经验。公司在职人员400余人。游戏产品主要面向国内市场，海外市场主要面对东南亚市场。项目组分4个项目组，本文选取的是作者参与研发的W网络游戏项目，进行其项目管理内容进行深度剖析，影响进度管理的各个方面调查研究，发现其中的问题，分析问题原因，提出问题的解决方案设计[12]。

# 3.1.2S 游戏公司组织架构

S公司目前采用项目型组织架构，根据项目划分几个项目工作室，项目工作室根据职能划分程序、美术、产品、策划四个职能部门。每个职能部由该部门分配的主管领导带领负责，整个项目组由项目经理统管。形成的扁平化管理，项目经理可以直接与职员业务沟通。安排工作决策。以项目为导向的组织有一个总体目标，并且在安排工作时完全以项目为中心。决策速度加快，能够及时响应项目相关方需求。项目团队的团队能力得到充分发挥，有利于项目的顺利推进。

# 3.1.3W网络游戏项目情况

# 3.1.3.1W网络游戏项目背景

W项目是公司重点研发的手机端网络游戏项目。项目组150人左右，由美术部、策划部、产品部、程序部、引擎部门组成。研发项目是一款大型多人在线网络游戏项目。项目在2017年预研立项，在2018年—2020年主要研发期，2021年初一2023年上线运营期。游戏内容背景是以山海经为题材，地图设计的世界无限大，精美地貌各种各样，自由探索的空间足够，美术量大，地图上可以自由建造房屋，可以交易物品道具，各种精美坐骑NPC可以任意抓，角色种族多种多样，可以自由捏脸。武器多种多样并附有华丽的战斗效果。非常具有独特的原创风格。在国内有大量忠实玩家，最初上线阶段吸引了大量玩家好评，但是运营1年多的时间内时候玩家用户量有一定程度下滑，玩家吐槽产品某些方面也比较多，主要集中在策划设计与程序BUG方面居多。游戏客户端引擎采用自研引擎。

熟悉引擎的开发人员极少，而且引擎相关的工具链不是很完善，这就让整个项目技术上的开发环境面临诸多的挑战性，提高了项目进度合理管理的迫切合理性。

# 3.1.4现有进度管理情况

在规划总体进度方面项目采用瀑布模型与迭代开发综合的模型，是一种迭代式、增量式的软件开发方法，强调团队的协作、玩家与用户的满意和快速响应变化。网络游戏项目在预研期中的开发DEMO的过程中，需求变化非常频繁，瀑布型不足以应对这种情况，迭代开发可以更好地适应需求变化的开发与版本迭代。项目组采用分业务模块，具体模块分配具体人员开发的模式，当模块具体任务时间线太长的时候，拆分任务的模块细节。版本周期为3个月产出一个大版本，每月一次中版本，每两周迭代小版本。

项目的活动分解方面。游戏项目的任务活动繁多，专业化程度高，有经验的人员适合有经验的模块，所以项目组针对这种情况把游戏项目根据任务模块划分不同。示例项目中的WBS项目分解结构图一张在下图3-1所示。

![](images/7067d536e95b10388fa59416943e2ba6f08d63e3111b01cbaae232c0cd3d18c5.jpg)  
图3-1WBS项目分解结构图

项目的排列活动顺序方面，项目组根据人员经验的不同拆分活动分解给不同的人员负责。版本任务拆分不同模块共同组成，由活动负责人共同执行制作版本任务活动。项目经理与部门业务进行估算活动资源，然后调配各种资源，与各个部门组长进行估算活动持续时间，然后与各部门组织讨论确定制定进度计划。

控制任务进度方面，首先项目组制定根据情况定下版本内容、制定版本计划，划分版本内容的活动分解的时间估算清单。人员执行版本内容，按照排期完成版本任务活动。中间会有进度压缩、加大资源投入调整时间安排，尽量保证版本内容的完整性及时性。

常用工具，项目计划制定与实施的时候，采用制作甘特图，清晰制定大版本的任务时间安排，从甘特图上可以看到活动名称、活动历时、活动资源、依赖情况、完成百分比。实施过程中，根据每周版本情况跟踪调节活动资源。在资源缺少的时候投入资源，在时间不合理时，裁减活动，安排到下个版本周期。使用的项目管理工具报表总览如下图3-2与迭代需求个数图3-3所示：

![](images/6119602595327c9093201b60b56497bf7f7f2163f8775868e9f3037321d47be7.jpg)  
图3-2项目管理工具系统的报表面板

![](images/b45b23b8d681b39f55948cb44f2bacb16f33e053f7fe30d5f5ec62c08c6d2f26.jpg)  
图3-3迭代需求个数累计图

# 3.2W网游研发项目进度管理现状及存在问题调研

# 3.2.1 资料与文献理论研究

W项目采用迭代的瀑布模型开发项目，开发计划依据以前的项目经验划分若干开发周期，三个月一次版本迭代周期，迭代版本为一个里程碑目标开发。把每一次周期划分若干子任务，子任务划分给到相关人员。里程碑交付物包括需求说明书、设计代码实现、测试完成报告、美术资源产出、游戏软件程序。本研究对相关进度管理的文献资料进行深入分析，选取可供参考的研究作为本文的理论基础。

# 3.2.2问卷调查法

对若干问题，采用匿名问卷的方式，探索若干管理过程中的问题，让W项目组中人员匿名填写进行数据收集，整理统计相关数据，供问题分析。主要采用问卷形式进行调查，作者制作调查问卷，请项目组内各组人填写问卷。

# 1.问卷对象

从不同的岗位的普通工作人员进行比较全面的问卷调查。不同的岗位的视角是不一样的。普通的工作人员占据总项目人数的 $8 0 \%$ 。本研究因此采取对四大主要的部门人员进行问卷调查。主要是对四个部门的职员，包括程序、美术、策划、测试人员。总体问卷调查对象统计如下表3-1：

表3-1总体问卷调查对象汇总表  

<table><tr><td>职务</td><td>程序</td><td>美术</td><td>策划</td><td>测试</td></tr><tr><td>人数</td><td>25</td><td>80</td><td>24</td><td>10</td></tr></table>

# 2.问卷内容设计

问卷调查根据问题大类别，设置每个问题的点，供用户选择同意与不同意区间的选项。对问卷进行网页系统形式匿名提交。如下表3-2所示。问卷内容共21道题目，围绕影响项目进度管理中因素点及现象点，供答卷人进行回答。进而根据答卷人的回答，统计报表进行分析。

表3-2问卷调查内容表  

<table><tr><td>1．您在完成任务时，是否会出现拖延、延误的情况？</td></tr><tr><td>2.如果出现进度延误，您会及时向上级或相关人员汇报吗？</td></tr><tr><td>3.您的上级是否及时关注您的工作进度？</td></tr><tr><td>4.您认为您的工作进度是否能够满足公司的需求和要求？</td></tr><tr><td>5.您是否有任何关于进度管理方面的建议或意见？</td></tr><tr><td>6．您认为当前的版本规划设计是否合理？</td></tr><tr><td>7.您认为您在项目里沟通是否顺畅？</td></tr><tr><td>8.您认为项目里是否有影响质量的因素？</td></tr><tr><td>9．项目里资源是否充足，影响项目的资源是？</td></tr><tr><td>10.项目版本里有哪些容易考虑不到的风险？</td></tr><tr><td>11．您认为您的上级是否会根据您的工作进度，给您合理的工作量和时间分配？</td></tr><tr><td>12．您是否能够准确地估算完成一项任务所需要的时间以及进度？</td></tr><tr><td>13．您与上级、同事沟通并确定进度时，是否考虑到各种可能的问题和不确定因素？</td></tr><tr><td>14.企业里是否有员工培训？是否需要？哪方面？</td></tr><tr><td>15.员工动力是否充足？</td></tr><tr><td>16．研发的需求是否经常变动？</td></tr></table>

<table><tr><td>17．工作量是否过多？</td></tr><tr><td>18.某些员工是否能力可以提高？</td></tr><tr><td>19.绩效考核是否有效？</td></tr><tr><td>20．任务估时是否准确？是否有效？</td></tr><tr><td>21．需求理解是否清晰？是否返工过？</td></tr></table>

# 3.问卷调查结果

通过对问卷中的问题答案进行分类汇总，结果如下表3-3所示：

表3-3调查问卷的收集数据分类汇总  

<table><tr><td>问題分类</td><td>具体问题</td><td>同意数</td></tr><tr><td rowspan="2">人员激励</td><td>绩效考核不足</td><td>81</td></tr><tr><td>研发绩效奖励性不足</td><td>92</td></tr><tr><td rowspan="4">人员自身</td><td>工作能力不足</td><td>45</td></tr><tr><td>时间估计或时间安排不合理</td><td>95</td></tr><tr><td>工作量过多加班多</td><td>56</td></tr><tr><td>需求理解不清楚</td><td>40</td></tr><tr><td rowspan="3">研发流程</td><td>需求频繁变动</td><td>112</td></tr><tr><td>临时插入新需求照成进度影响</td><td>96</td></tr><tr><td>研发的需求不统一影响开发</td><td>50</td></tr><tr><td rowspan="3">进度控制</td><td>进度变动没有采取措施</td><td>40</td></tr><tr><td>进度计划不合理</td><td>93</td></tr><tr><td>进度跟踪不及时</td><td>64</td></tr><tr><td rowspan="2">企业管理</td><td>企业中没有技能培训</td><td>84</td></tr><tr><td>企业氛围没有动力</td><td>95</td></tr></table>

通过调查问卷的结果汇总分析可以得出，对项目进度影响大的概况以下几个方面。

（1）需求频繁变动，进度调控不足。  
（2）临时插入新需求，开发时间紧张。  
（3）时间估计安排不合理，有些员工任务多。  
（4）有些软件系统模块出现返工重做现象。  
（5）美术资源产出总是滞后。

# 3.2.3专家判断访谈

本研究采用了定性访谈的方法，与W项目组的层级管理人员进行了深入交流，以探讨项目进度管理方面的问题。以下表格提供了受访人员的相关信息。名单如下表3-4所示：

表3-4W项目相关开发人员采用单人访谈表  

<table><tr><td>序号</td><td>姓名</td><td>项目岗位</td><td>年龄</td><td>学历</td><td>专业</td><td>工龄</td></tr><tr><td>1</td><td>李**</td><td>项目经理</td><td>40</td><td>本科</td><td>软件工程</td><td>18</td></tr><tr><td>2</td><td>张**</td><td>后端主程</td><td>35</td><td>本科</td><td>网络工程</td><td>13</td></tr><tr><td>3</td><td>刘*</td><td>前端主程</td><td>36</td><td>本科</td><td>计算机</td><td>14</td></tr><tr><td>4</td><td>王**</td><td>主美术</td><td>38</td><td>本科</td><td>艺术设计</td><td>16</td></tr><tr><td>5</td><td>赵**</td><td>主策划</td><td>35</td><td>本科</td><td>计算机</td><td>13</td></tr><tr><td>6</td><td>柳**</td><td>制作人</td><td>37</td><td>本科</td><td>计算机</td><td>15</td></tr></table>

对W项目组中相关的专家展开访谈，访谈问题的设计思路是依据项目现状影响进度的因素点收集专家对此相关的访谈看法，访谈问题包含如下问题：

（1）您认为项目是采取什么样的进度管控办法？优缺点是？（2）您认为目前项目的进度管控如何？哪些因素影响？（3）您负责业务模块按期完工吗，超时完工或赶工的情况是哪些为什么？（4）您发现有哪些任务或阶段需要额外的支持或资源？（5）您负责游戏项目任务中，我们面临了哪些影响项目进度的挑战？（6）任务包的历时估算和项目工期调整是怎样完成的？（7）您在项目中如何评价团队成员的进度绩效？是否有效（8）当前开发项目中，哪些工作是可以优化和简化的？您有任何建议吗（9）您认为最大的时间浪费是什么？您有什么建议来解决这个问题吗？

对W项目相关开发人员采用单人访谈的方法，了解项目进度管理情况。人员名单如下图3-6所示。

![](images/1201cfed8b9117344900f09d15d64467dcae57920ad5894229cbcff6a8b7748b.jpg)  
图3-6W项目相关开发人员采用单人访谈

座谈的问题围绕如下方面：

（1）您认为项目制定项目的计划是否合理，是否参与并考虑任务的不确定性？（2）您参与项目中的任务包技术难度如何？耗时时长？估算时长是否匹配？（3）您负责开发任务包的时间是否充足？是否受其他前置任务影响？（4）您负责的开发模块是否遇到沟通不畅，难以开展的情况？（5）您认为软件项目产生延期的原因有哪些？有哪些建议？

通过上述访谈进行详细的对W项目组的进度管理现状、流程、方案有比较全面地认识和了解。对访谈结果分析总结，通过鱼刺图问题主要如下图3-7所示：

![](images/fc0074fc3f6a89958680809ed006c66484654f3af38a13b74da9ce2612a05f81.jpg)  
图3-7访谈结果分析鱼池图

（1）开发流程问题，进度管理评估机制。流程上研发分配的任务研发人员知细节不知整体，无法对整体成果有清晰认识，导致频繁返工去修改，逐步认识了解最终效果，最终验收整个最终效果迟缓。

（2）工作模块划分粗糙，尤其在研发Demo和常规版本迭代中制作大的活动包的阶段，动辄30工作日及以上的长期评估工时，粗略的任务活动估时，工作量量化的不确定导致难以控制计划的合理有效执行。工作划分到具体员工，有些任务活动繁琐性及员工兴趣程度不高，导致积极性不足，很多员工是在非常充足与有些员工在极度匮乏的时间内完成给定任务活动，也相应的影响产品质量。

（3）技术选型问题，服务器架构不稳定。组员开发业务严重影响？门槛比较高。客户端引擎是自研引擎，引擎接口api没有文档，需要人员沟通增加耗时。（4）策划需求变更频繁，某些大的任务系统变更多次，影响进度。（5）美术产出内容时间常常靠近deadline时间线，导致无法正常测试，影响策划人员的工作流程，也影响产品的封版完成。

（6）员工能力参差不齐，比如有些策划提出的需求点，没有需求说明文档，程序及测试人员的需要多次沟通才能确定真实需求。

# 3.3现有进度问题的成因分析

# 3.3.1 开发生命周期模型适应性分析

W项目采用的是瀑布模型及迭代开发模型的综合开发。瀑布模型开发要求项目需求明确，项目前期能够对项目的整体实施进行有效规划，项目整体实施过程中变更内容较少。项目是不是适用瀑布模型，要根据项目的特点和需求方对整体需求的明确程度进行定义[13]。

W网络游戏开发项目的需求不是一成不变的，随着项目的进展，开发方和用户方对于项目的理解会不断加深，带来想法的改变，一些功能的需求和实现方式发生了多次变化，而团队对于应对这种变化措施不足，导致有些系统模块反复修改多次大的改进，拖延了整体开发进度，开发人员也多有怨言。

# 3.3.2活动工期与任务分解不合理分析

由于工作时间和经验的差异，项目团队中的开发人员可能需要不同的工作时间来实现相同的功能。在确定活动持续时间的过程中，有必要充分考虑项目团队的技术和学习能力。每项活动的持续时间应根据个体差异确定。重要的是不要使用一刀切的方法来统一估计持续时间。统一工期估算的问题在于，能力强的人快速完成工作，留下大量的工作时间剩余，能力差的人保持长期加班的状态，给团队的稳定和发展埋下隐患。

同时对于功能能力不同的人，在分配工作时也要综合考虑。对于工作经验丰富、学习能力强的人员，应多安排框架工作和技术研究工作。新人和能力弱的成员就多安排技术需求较低的内容、重复性比较多的工作，让每个人都能体会到在工作中实现自己能力的价值机会，增强每个人的成就感与价值感。

# 3.3.3 组织机构问题的成因分析

管理制度与流程方面的问题。员工组织层面，没有合理规划，分配的两个小组长仅仅是在公司时间比较长，但是年龄偏小，也没有话语权，没有具体的管理能力。仅仅是对项目内的磨合比较好而已。研发流程可以更优化，首先对于活动执行情况没有很好的进度监控，是策划层层上报的方法没有自动化的监听方法。如果有实时监听的情况，很好地可视化疑难问题延误时间及人员的工作量情况。其次开发版本计划的内容，现在的分配方式是按功能模块划分，而不是按量分配，会导致分配不均衡的现象出现，然后有些员工的活动任务过多，时间卡点的情况。每个人一定时间里只能完成一定的工作量，出现这种情况会使进度滞后。第三，沟通和协作不畅的问题，比如引擎部门没有接口文档，浪费人的沟通时间。

# 3.3.4计划编制问题的成因分析

计划编制问题。进度计划方面采用不透明的进度计划[6，员工的分配的进度中任务急迫程度并不明显，团队开发人员没有参与计划制定，团队讨论对于项目计划制定来说，是有利于通过集体思考和努力来保证计划的合理性，从而尽可能避免单纯的追逐工期时间而造成的不利返工后果。还可以帮助相关项目成员通过讨论达成共识，促进项目任务的顺利进行，促进成员的积极性和责任感，在完成自身任务的过程中更加注意避免失误[12]。

计划编制的不透明，员工没有参与度，仅仅任务自已执行。DEMO研发期，没有采用合理的开发模式，所有员工分配各自模块开发，但是对于老板迫切希望的demo演示迟迟不能观看。具体制作Demo的人员仅仅有几名人员，而且都是新手程序员。采用原型模式迭代开发能够紧凑开发。

网络游戏开发技术的快速更新、美术风格市场的变化及项目内部的切换不统一、需求的多重变化、多重约束、效果的主观评价，都预示着游戏软件开发的过程不会一帆风顺，而是必然是一个涉及多方、多碰撞、最终一致性的复杂过程。项目进度过于理想化，随之而来就是无法承受一些工作延误造成的进度压力，最终导致项目延迟交付。

# 3.3.5管理制度与流程问题的成因分析

控制范围变更的流程问题。游戏项目产品跟传统软件项目不同的地方有一点是根据体验玩法的效果的不同，需要频繁调整改动需求或者增加新需求，这种改动会严重影响活动的进度，所以需要想办法制定一套合理的机制解决这种问题。对于需求不确定的东西要不要纳入版本计划里，对于效果不一定到预期的要不要纳入版本计划里。否则带来的进度延期风险变化，若未采取有效措施进行管理，将会导致进度延长。

没有老员工带新员工的模式。网络游戏的研发人员及制作内容具有有创造力和创新性，尤其策划人员制定的内容玩法很多时候需要突破原有的既定程序规则实现创意玩法，也就是不受约束，但是不加以合理有效的约束，就有可能导致游戏设计内容的无限增长，而且需要开发额外不在任务清单里面的功能实现，而且需求的反复确认，策划开发人员的提的需求文案，心血来潮比较多。

# 3.3.6进度控制问题的成因分析

进度控制问题。进度计划安排活动分为大版本，小版本。进度计划在操作过程中脱离实际生产，程序员的计划跟实际版本活动紧密性不强，是被策划安排，而不是跟随整个项目组的计划，在实际探讨制定计划的编制过程中没有引入相应组员或组长讨论，只是项目经理跟主策、主程、主美这些相应部门一级领导推进制定。当有程序员进度脱离计划的时候，只能策划人员上报项目经理，在调整计划或活动安排调度中，程序员的参与度很低。要么是延迟到下个版本计划，要么是督促加班或增加资源继续推进活动[14]。

需求频繁的变更时，没有严格把控，随意变更延长任务的时长，对于低延时的任务随意延长的不加以控制，进度缓慢，对于延时长的任务没有进一步的切分，分配不合理。领导对于延期任务的并不知情，具体执行的策划与程序直接延长任务的时间，而对于上级领导仅仅是策划领导的知情权，程序领导没有知情，没有进一步的引导，仅凭初中级程序员的自我控制。

# 3.4本章小结

本章首先介绍S公司的基本情况及其项目的组织结构。通过对公司的介绍，公司发展游戏的多年积累情况。然后解释了要研究的项目的背景，项目的管理流程，研发模式，人员组成等情况。其次，介绍了研究项目的主要内容和项目的基本发展情况。通过不同角度的分析，在公司项目进度管理现状中发现了项目进度管理中的主要问题以及问题带来的后果。主要内容是对游戏W项目的进度管理进行研究，梳理现有项目进度、WBS工作结构分解、责任认定等内容的直接关系影响，搜寻资料理论分析制作问卷调查探寻问题所在，采用专家访谈与组员访问方式调查项目进度管理的问题。最好基于以上总结原因具体分析造成这些原因的过程，为后续制定解决方法提供资料。

# 第四章W网游研发项目进度管理改进方案设计

对于W网络游戏项目而言，其作为公司自主研发的一款新项目，相关研究范围主要涉及到五大领域。而策划部分主要是制定游戏玩法与产出系统功能文档，这是整个游戏的基础，同时策划所设计的玩法也是一款游戏最核心的部分，决定了是否能够吸引到玩家，并让他们在游玩中获得快乐。美术设计则是以能够给玩家带来新的视觉体验为基础，并愿意为了新的视觉效果而产生付费。而对于前台、后台开发而言，则主要是配合策划和美术完成相应的美术和玩法功能效果的同时兼顾后台程序的稳定。测试主要是对游戏功能玩法的确认验收及系统检测稳定性的人员，从每次打出的体验版本中，运行和游玩游戏，从中发现问题和设计缺陷，反馈给相应人员进行修正和调优。该游戏项目的里程碑版本大致的阶段是：需求阶段一〉构建阶段一〉优化阶段一〉运营阶段[15]。其中阶段交付物包括：策划需求案，美术原画图，技术方案架构，3D模型资源，游戏DEMO程序，游戏版本交付程序，上线性能优化意见表，内测反馈报告，阶段结项报告等。

根据以上分析得出的影响项目进度的几点，现提出以下几点方案设计，本着既要能够满足研发项目的周期要求，还要保证研发内容质量正常产出。设计更科学合理和能够执行的进度管理方案，最终实现项目进度管理的方面的提高。

# 4.1优化 WBS 任务分解

W项目任务分解与项目特点不符与不合理，资源分配不均。造成后果是有些任务大都集中在一部分的工作人员，导致进度缓慢。针对这种情况，采用以下方法：（1）首先，针对软件项目构建WBS工作分解结构图，将项目按照流程划分为若干阶段任务，每个阶段任务根据内容体系拆分为分子任务，子任务的大模块可以继续拆分三级子任务活动。（2）在最近时间所需的版本中选择计划任务作为版本内容计划[16。此计划任务包含三个级别的子任务。最下层级别的工作包任务分配给具体工作人员，任务与人员展示面板上会展示具体每个人对应的工作包。跟进每个人的工作任务包的情况，任务繁多的人，可以优化这部分员工，将任务拆解分配其他人，分配任务较少的人可以继续分配领取其他任务包繁多人的工作，这样在人力资源分配任务与展示中多次调配达到合理初始调配，最后利用资源管理软件来管理资源。（3）游戏项目虽然与传统项目不同，但有一些相似之处，比如美术资源可以作为原材料。有些功能是在原材料到位的前提下开发的，而高质量的游戏项目需要大量的美术资源，往往占据了游戏开发的大部分时间。好在同类型任务，虽然美术资源生产的开发周期与以前大致相同，但开发的边际成本却很小[17]。从这个角度来看，将美术任务的需求细分为单个任务可以启动下一个任务，只是最终的项目时间还需加入美术推量阶段。此阶段将有总实际需求加上标准美术产物生成时间。下图图示4-1说明改进之前的任务结构图，图示4-2说明改进之后的任务结构图。

<table><tr><td>1D</td><td>任务名称</td><td>开发人员</td><td>开始期</td><td>结来日期</td><td>持续时间</td><td>完成</td><td>01290220</td></tr><tr><td>1</td><td>战斗逻辑</td><td></td><td>2021-04-20</td><td>2021-06-01</td><td>31.0日</td><td>16.2%</td><td></td></tr><tr><td>2</td><td>技能策划</td><td>张x</td><td>2021-04-30</td><td>2021-05-20</td><td>15.0日</td><td>10.7%</td><td></td></tr><tr><td>3</td><td>技能程序</td><td>王X</td><td>2021-05-03</td><td>2021-05.06</td><td>4.0日</td><td>45.0%</td><td></td></tr><tr><td>4</td><td>技能测试</td><td>李x</td><td>2021-05-14</td><td>2021-06-01</td><td>13.0日</td><td>0.0%</td><td></td></tr><tr><td>5</td><td>技能关术</td><td>xx</td><td>2021-04-20</td><td>2021-05-21</td><td>24.0日</td><td>23.7%</td><td></td></tr><tr><td>6</td><td>任务模块</td><td></td><td>2021-04-30</td><td>2021-05-20</td><td>15.0日</td><td>4.8%</td><td></td></tr><tr><td>7</td><td>任务策划</td><td></td><td>2021-04-30</td><td>2021-05-06</td><td>5.0日</td><td>20.0%</td><td></td></tr><tr><td>8</td><td>任务程序</td><td></td><td>2021-04-30</td><td>2021-05-03</td><td>2.0日</td><td>0.0%</td><td></td></tr><tr><td>9</td><td>任华美术</td><td></td><td>2021-04.30</td><td>2021-05-03</td><td>2.0日</td><td>0.0%</td><td></td></tr><tr><td>10</td><td>任务剧情策划</td><td></td><td>2021-05-07</td><td>2021-05-13</td><td>5.0日</td><td>0.0%</td><td></td></tr><tr><td>11</td><td>任务测试</td><td>周X</td><td>2021-05-12</td><td>2021-05-20</td><td>7.0日</td><td>0.0%</td><td></td></tr><tr><td>12</td><td>数值模块</td><td></td><td>2021-05-07</td><td>2021-05-25</td><td>13.0日</td><td>0.0%</td><td></td></tr><tr><td>13</td><td>数债策划</td><td>吴X</td><td>2021-05-07</td><td>2021-05-13</td><td>5.0日</td><td>0.0%</td><td></td></tr><tr><td>14</td><td>数值程序</td><td>徐x</td><td>2021-05-10</td><td>2021-05-14</td><td>5.0日</td><td>0.0%</td><td></td></tr><tr><td>15</td><td>数值测试</td><td></td><td>2021-05-20</td><td>2021-05-25</td><td>4.0E</td><td>0.0%</td><td></td></tr><tr><td>16</td><td>商业系统</td><td></td><td>2021-05-14</td><td>2021-05-25</td><td>80日</td><td>0.0%</td><td></td></tr><tr><td>17</td><td>商业系统策划</td><td>朱X</td><td>2021-05-14</td><td>2021-05-21</td><td>6.0日</td><td>0.0%</td><td></td></tr><tr><td>18</td><td>商业系统程序</td><td></td><td>2021-05-14</td><td>2021-05-21</td><td>6.0E</td><td>0.0%</td><td></td></tr><tr><td>19</td><td>商业系统测试</td><td></td><td>2021-05-20</td><td>2021-05-25</td><td>4.0日</td><td>0.0%</td><td></td></tr><tr><td>20</td><td>商业系统英术</td><td></td><td>2021-05-14</td><td>2021-05-20</td><td>5.0日</td><td>0.0%</td><td></td></tr></table>

<table><tr><td>1D</td><td>任务名称</td><td>开发人员</td><td>开始日期</td><td>结束日期</td><td>持续时间</td><td></td><td></td></tr><tr><td>1</td><td>三版本选代1</td><td></td><td>2022-04-20</td><td>2022-05-31</td><td>30.0日</td><td>0.0%</td><td></td></tr><tr><td>2</td><td>技能策划</td><td>3x</td><td>2022-04-29</td><td>2022-05-19</td><td>15.0日</td><td>0.0%</td><td></td></tr><tr><td>3</td><td>任务策划</td><td></td><td>2022-04-29</td><td>2022-05-19</td><td>15.0E</td><td>0.0%</td><td></td></tr><tr><td>4</td><td>任务程序</td><td></td><td>2022-04.29</td><td>2022-05-19</td><td>15.0日</td><td>0.0%</td><td></td></tr><tr><td>5</td><td>任务程序1</td><td></td><td>2022-04-29</td><td>2022-05-19</td><td>15.0日</td><td>0.0%</td><td></td></tr><tr><td>6</td><td>任务程字2</td><td></td><td>2022-04-29</td><td>2022-05-19</td><td>15.0日</td><td>0.0%</td><td></td></tr><tr><td>?</td><td>技能程序</td><td></td><td>2022-05-03</td><td>2022-05-06</td><td>4.0日</td><td>0.0%</td><td></td></tr><tr><td>8</td><td>技能子程序1</td><td></td><td>2022-05-03</td><td>2022-05-06</td><td>4.0日</td><td>0.0%</td><td></td></tr><tr><td>9</td><td>技能子程序2</td><td></td><td>2022-05-03</td><td>2022-05-06</td><td>4.0日</td><td>0.0%</td><td></td></tr><tr><td>10</td><td>技能美术</td><td></td><td>2022-05-13</td><td>2022-05-31</td><td>13.0日</td><td>0.0%</td><td></td></tr><tr><td>11</td><td>任务美术</td><td></td><td>2022-04-20</td><td>2022-05-23</td><td>24.0日</td><td>0.0%</td><td></td></tr><tr><td>12</td><td>选代测试</td><td></td><td>2022-05-13</td><td>2022-05-31</td><td>13.0□</td><td>0.0%</td><td></td></tr><tr><td>13</td><td>技能式1</td><td>李X</td><td>2022-05-13</td><td>2022-05-31</td><td>13.0日</td><td>0.0%</td><td></td></tr><tr><td>14</td><td>技能到式2</td><td></td><td>2022-05-13</td><td>2022-05-31</td><td>13.0日</td><td>0.0%</td><td></td></tr><tr><td>15</td><td>任务测试3</td><td>同X</td><td>2022-05-13</td><td>2022-05-31</td><td>13.0日</td><td>0.0%</td><td></td></tr></table>

上图4-2可以看出，一次版本迭代里把具体任务模块按任务栏分配不同人员，不再是原来的具体模块划分不同的人员，任务推进不再受任务量多的人员所困扰。也会调动起原来工作任务量少的人员参与进这次版本迭代里面来。

# 4.2 调整项目进度计划

进度计划这里主要通过编制里程碑与迭代版本计划。游戏开发阶段的版本，是游戏内容开发到一定阶段可体验的游戏内容的版本。游戏研发划分为几个主要阶段，每个阶段都有比较重要的节点，重要节点称之为里程碑。而每个阶段都有相应的版本产出，每次版本产出以验证重要内容为目标，持续迭代游戏版本演进，通过每次达成游戏项目版本目标，最终通过里程碑。里程碑包含了版本中将有哪些功能点、完成多少资源以及确定分别由主要开发人员跟进每个功能模块。里程碑计划和版本内容一般是合并在一起提及的，在有条不紊的开发过程中，一个大版本的完成时间通常就是这个版本的里程碑时间。W项目经过这些迭代阶段周期如下表4-1所示：

表4-1版本迭代里程碑表  

<table><tr><td>序号</td><td>时期</td><td>游戏版本</td><td>目标</td><td>里程碑</td></tr><tr><td>1</td><td>调研期</td><td></td><td>分析</td><td></td></tr><tr><td>2</td><td>预研期</td><td>DEMO原型</td><td>立项</td><td>需要</td></tr><tr><td>3</td><td>开发前期</td><td>版本</td><td>核心功能</td><td>需要</td></tr><tr><td>4</td><td>开发期</td><td>开发版本1</td><td>功能切分</td><td>需要</td></tr><tr><td>5</td><td>开发期</td><td>开发版本2</td><td>功能完善</td><td>需要</td></tr><tr><td>6</td><td>开发期</td><td>开发版本X</td><td>体验丰富</td><td>需要</td></tr><tr><td>7</td><td>测试开发</td><td>封测版本</td><td>体验优化</td><td>需要</td></tr><tr><td>8</td><td>测试期</td><td>内测版本</td><td>反馈修正</td><td>需要</td></tr><tr><td>9</td><td>测试期</td><td>公测版本</td><td>启动上线版</td><td>需要</td></tr><tr><td>10</td><td>运营期</td><td>运营版本</td><td>两周版内容</td><td>需要</td></tr></table>

里程碑的制定以版本目标为核心验证达成设立，是游戏内容玩法、技术实现、美术表达实现和产品测试达成为过程，输入汇总到产品中。所以项目计划以这几类切分任务计划，不同的工作人员执行这些计划内容。用张弛有道的方法控制开发的节奏，并在必要的时间里投入大量的人力和时间突击难点。通过下图4-3所示W项目的阶段版本的内容设定。版本里程碑计划的可以使开发团队在过程中就预见到产品完成时将呈现的样子，这对于调动团队热情有十分重要的意义。为团队人员坚持执行、效率执行奠定了基础。

![](images/65e3be9c07bc0aa4e4c3574f8198b40367b51d81e032ef01860cce27744a3dc0.jpg)  
图4-3版本内容迭代图总览

# 4.3 任务活动历时估算修改

活动持续时间估算也是一项非常重要的工作，其准确性与项目版本的制定和控制有关。影响项目开发进度时间。活动估计方法包括模拟估计、专家估计、德尔菲估计、三点估计等。W项目采用项目经理和开发人员的经验估计，其中包含许多主观因素，达不到精确估计。因此修改采用加入使用三点估计（PERT技术）来估计持续时间可以避免过多的主观因素，使估计更准确。

三点估算技术属于项目进度管理范畴。它是规划审查技术的工具技术，也就是规划评审技术（PERT），PERT根据概率学统计计算出任务及项目的完成时间。

PERT估计项目活动的完成时间分为三种不同：乐观时间（OT）：一切顺利时完成工作的时间；最有可能的时间（MT）：通常完成任务的时间；悲观时间（PT）：在最坏的情况下完成工作的时间。假设上述三个估计值服从正态分布，则每个活动的预期值由此计算：该算法的公式为：活动持续时间均值（或估计值）$=$ （乐观估计值 $+ 4$ $\times$ 最有可能估计值 $^ +$ 悲观估计值）/6，活动持续时间标准差 （悲观估计值一乐观估计值）/6。然后为活动任务的持续时间创建一个工作分配表。资源输入建立资源缓冲区，时间缓冲区。使用PERT技术估计持续时间，从此计算每个活动估计日期的时间。

项目管理人员就是项目经理接入之后，会组织会议在需求评审阶段进行需求评审。通过评审的需求会综合项目各模块负责的部门组长，各小组组长以及具体制作人员运用三时间估算法综合评估出三个时间。即工作最顺利地完成时间（to）来表示乐观时间。整个任务最可能完成的时间（tm）在不遇到突发问题的通常情况下一项工作完成的时间。以及（tp）表示受到不在计划内的突发事件的影响，在不顺利的情况下完成任务的悲观时间。

有了部门领导及组长的共同评估，预计的任务使用时间会更为准确，也预防了某些任务因为夸大其难度而导致的周期过长。同时在许多需要美术跟开发协作的部分，引入技术美术的加入。能兼顾美术制作流程及开发实现顺序，让评估更为准确，也在某些未知或有较大风险的地方。提前规划好合理的悲观时间tp以应对可能出现的进度延期问题。提出关键路径进行优化改进。

改进之前的任务估算示例表下表4-2：

表4-2改进之前的任务估时  

<table><tr><td>ID</td><td>任务名称</td><td>开发人员</td></tr><tr><td>1</td><td>技能策划</td><td>张X</td></tr><tr><td>2</td><td>技能程序</td><td>15 王X 5</td></tr><tr><td>3</td><td>技能测试</td><td>李X 4</td></tr><tr><td>4</td><td>技能美术</td><td>刘X 8</td></tr><tr><td>5</td><td>任务策划</td><td>陈X 14</td></tr><tr><td>6</td><td>任务程序</td><td>杨X 7</td></tr><tr><td>7</td><td>任务美术</td><td>黄X 7</td></tr><tr><td>8</td><td>任务测试</td><td>周X 4</td></tr></table>

表4-3改进之后的任务估算表  

<table><tr><td>ID</td><td>任务名称</td><td></td><td>开发人员乐观估时</td><td>最可能估时</td><td>悲观估时</td><td>应用估时/天</td></tr><tr><td>1</td><td>技能策划</td><td>张X</td><td>10</td><td>15</td><td>18</td><td>14.7</td></tr><tr><td>2</td><td>技能程序</td><td>王X</td><td>5</td><td>5</td><td>8</td><td>5.5</td></tr><tr><td>3</td><td>技能测试</td><td>李X</td><td>3</td><td>4</td><td>5</td><td>4</td></tr><tr><td>4</td><td>技能美术</td><td>刘X</td><td>7</td><td>8</td><td>12</td><td>8.5</td></tr><tr><td>5</td><td>任务策划</td><td>陈X</td><td>12</td><td>14</td><td>20</td><td>14.7</td></tr><tr><td>6</td><td>任务程序</td><td>杨X</td><td>6</td><td>7</td><td>9</td><td>7.2</td></tr><tr><td>7</td><td>任务美术</td><td>黄X</td><td>7</td><td>7</td><td>14</td><td>8.2</td></tr><tr><td>8</td><td>任务测试</td><td>周X</td><td>4</td><td>4</td><td>7</td><td>4.5</td></tr></table>

改进之后的任务估算示例表上表4-3所示。通过计算每一项的应用估时 $\Longrightarrow$ （乐观估时 $+ 4 \times$ 最可能估时 $^ +$ 悲观估计)6，将此估时真正应用到项目时间里的任务估时。确保能够尽可能精确估算任务工时。

# 4.4增强人员的能力素质

虽然项目管理中要控制很多因素，但项目管理的核心之一是对人力资源的管理，项目最终是由工作人员进行实施执行的，人员对项目进度的素养和关心程度，关系任务包的执行时间情况。人是具体执行任务包的资源。通过解析任务活动利益相关者矩阵或责任矩阵都有望实现人的管理，因此如何提高人员在进度管理实施中的素养将是辅助完成项目进度管理的一个重要方面[7。作者将再次提出关于人员管理的几点改进：

（1）制定培训机制，分为专项技能培训、进度流程培训和凝聚力培训。游戏研发项目组人员复杂，工种多样有时候沟通不畅的很多原因不是技能强弱的问题，而是因为每个人在一件事上的观点不同，立场也就不同，那么这个项目就是把人们聚集在一个组织中，朝着一个统一和基线范围的目标努力。其次，过程不被理解。需要考虑实施执行人员与管理人员视角的差异。结合这些观点，W项目实施一套培训机制，建立起专项技能培训内容，通过每月实施一次专项技能培训，培训讲师采用公司内部专家级别的工作人员，培训内容是专家制定的专项技能的学习。这样加强公司的自主研发力量，鼓励员工创新[29]。进度流程的培训为部门组长培训组员按照流程工具方式执行的习惯措施，改正某些偏见的观点，让组员的立场观点不至于偏颇。凝聚力的培训是请公司人力资源部门采用团建结合的方式建立。

（2）建立员工关怀，每个人都有自已的特点，因此要充分发挥管理者的主观能动性，用温情的管理方法进行合适引导刚加入的公司的员工，这部分员工快速熟悉公司业务标准。具备提供人文关怀的能力和精力，当项目团队稳定时，这种方法最有效。项目管理人员带头作用关怀员工。W项目的采用的措施有节日送礼品的方法。举例如中秋节或春节时机，项目采购礼品，赠予员工。

（3）项目经理及相关管理者自身的定位。在我国项目组织结构中，除工程项目外，大多数项目经理都存在于强矩阵组织结构中，项目资源受到项目绩效的制约。在这种情况下，项目经理需要将自己定义为协调员、助理，而不是经理，以便项目成员信任他们。他们还可以组织成员主动完成任务。其他项目相关管理者也是同理。

# 4.5 需求变更控製优化

W项目属于游戏软件项目，项目特点本身就包括制作过程中需求有很多变化，都是为了达到极致的效果。但是当工作负载频繁且繁重时，仍然需要控制需求变化，因为它们会影响整体进度，或者因为他们可能会影响任务的进度延迟。建立需求变更机制。W项目在改进之前的需求变流程如下图4-4所示。能够看出W项目执行期间，任务需求参与者或部门负责人都可以提出变更，需求的开发者执行项目变更申请及实施。

![](images/86ddef4391b53d2f4e72a0dbd5ae5e3e56808e8d9089925f2cc20ca439ac499d.jpg)  
图4-4改进之前需求变更流程图

通过分析W项目需采用更加合理的需求变更措施，首先需要确定项目执行期间可能的项目更改，部门负责人和任务需求参与者成员可以识别并提交的项目变更申请。识别是否可被更改内容可以基于：（1）当项目范围发生变化超过项目定义的问题阈值时。（2）当项目工期和工作量超过项目定义的问题阈值时。（3）项目投入资源及工时超出时，严格管控。（4）项目参与人员发生变更时。（5）有严重bug、重大效率提升、可玩性提高等。

其次优化项目变更的流程管理，当可以在阈值范围内进行调整时，超出阈值范围，则进入项目变更流程。当项目触发需求变更时，变更发起者向变更申请人发起申请，变更申请人向上一级管理者提交申请并附带详细说明的变更原因，变更内容，影响范围情况，一级管理者进行初步的审核判断，再进行上报项目经理，项目经理进行最终的裁决。最终反馈给变更发起者与申请人，进行变更内容的制作。而且此项变更记录在案，项目经理会根据此项变动，会相应修改相关影响任务调度的更改。改进之后需求变更流程图4-5所示：

![](images/e3d18debd7bf6442820b36062fbc1eae028acbef0588a94fba25223745f14611.jpg)  
图4-5改进后需求变更流程图

# 4.6 进度的绩效管理优化

在确定了项目的基本任务活动包后，我们可以从任务工期的总期望中看到，这个项目的任务周期更长。随着时间的推移，在同一项目的开发中，它也会使员工感到疲劳。个别员工也只关注他们当前的任务，对项目组织没有很好的了解。项目本身将在此过程中受到质疑甚至动摇。为了控制这种情况蔓延，项目会定期选择里程碑节点，与所有开发者同步项目更新，让他们看到自己辛勤工作的成果。最终使相信该项目成功上线，取得了巨大的经济效益[18]。项目在改进之前的绩效采取的方式是：员工自评 $+$ 组长评分 $^ +$ 平时加班时长统计 $+$ 任务完成数统计四种结合的评估方式。员工自评分ABCD四种，A非常优秀，B优秀，C及格，D不及格。组长评分也是ABCD这四种。然后附加评述说明文字。然后汇报项目经理进行统计结算。综合来说绩效评估较简单。因此针对绩效提出一些改进措辞。提出以下增加两种绩效统计点方式：

1.增加任务完成度量绩效点

遵循SMART原理和XY理论，设计员工任务活动按时完成和超时完成差异化处理的学分制度。对高累积信用给予适当的奖励，可以是金钱或荣誉奖励[19]。

对于低分的适当提醒，考核应按月为单位。在项目管理中，主要的数据输出环节是项目进度管理。要实现对项目的真正控制，有必要提高项目绩效，提高项目成员的响应能力和积极性。项目绩效体系可加入到公司绩效管理中，统一管理，增强人员可控性。提高团队绩效团队负责人负责关系得分。

2.绩效点系数参与里程碑分红绩效

首先，项目的初版演示Demo阶段达成和中期研发项目的推量阶段达成是重要的里程碑事件。其次，当第一个角色的原始绘图完成时，模型也将标志着设计的初步完成，这也将作为一个里程碑。通用角色的3C基本功能的完成，预示着游戏可以在真机上操作和玩，也是一款游戏产品落地的一个特别重要的里程碑，从简单的审美效果到与玩家互动的产品。结合完成每个重要功能模块。这样每个里程碑的完成，项目管理人员都会记录相关人员的绩效点。累计绩效点为后续项目的分红收益与年终奖励提供参考价值。在一些关键节点可见，展示项目目前的成果，不仅可以增强团队士气，增加开发者和项目相关人员的信心，还可以让项目经理和公司高管知道项目目前在做什么。因此，里程碑不仅仅是需要了解当前项目进度的项目管理，它们对整合团队和建立共同目标具有积极影响。里程碑的节点设置也是喝季度时间节点一致性。季度考核时依据绩效点平均进行奖金的激励[20]。

通过统计团队成员的个人表现、考核指标：WBS完成时间比、任务难度给出团队人员的绩效点得分。累计任务完成绩效点也将为后续项目的分红收益与年终奖励提供参考价值。以下图4-6所示绩效考核积分方式：

<table><tr><td colspan="8">考核项目 项目权重 考核明细 明细权重自我考核 20%自动考核20%组长考核 50N部门领导考核30%</td></tr><tr><td rowspan="5">个人素</td><td rowspan="5">40%</td><td>有强烈的工作责任心</td><td>20%</td><td>5</td><td>0</td><td>4</td><td></td><td>考核周分 0.31</td></tr><tr><td>发现题以及决的的态度</td><td>20%</td><td>4</td><td></td><td>m</td><td></td><td>0.26</td></tr><tr><td>良好的组织能力与协调能力</td><td>20</td><td></td><td>0</td><td></td><td></td><td>0.24</td></tr><tr><td>有团队查识，集体利益为重</td><td>20</td><td>5</td><td></td><td></td><td></td><td>0.35</td></tr><tr><td>职业操守，过守职业道德</td><td>20</td><td>10</td><td></td><td></td><td></td><td>0.31</td></tr><tr><td rowspan="5">工作考校</td><td rowspan="5">60%</td><td>考勤状态统计表</td><td>20%</td><td>0</td><td>10</td><td></td><td>?</td><td>0.37</td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>无成工作的致平与质器</td><td></td><td></td><td></td><td>m</td><td></td><td></td></tr><tr><td>完成任务难度系数</td><td>20</td><td>0</td><td></td><td></td><td></td><td>0.50</td></tr><tr><td>加班时长</td><td>20%</td><td>0</td><td>5</td><td></td><td>5</td><td>0.30</td></tr></table>

考核得分在0-1之间的评级为差D，得分在1-3之间评级为及格C，得分在3-4之间评级为良好B，评分在4-5之间为优秀A。所以从上图4-6各项考核得分汇总得到最终得分为3.26，考核评价为良好。然后到达季度绩效奖金分红时依据评级进行分配奖金。示例如下表4-4：表中奖金数额仅做参考，实际数额也是依据这个原则进行分配。

表4-4奖金分红示意表  

<table><tr><td>评分</td><td>A</td><td>&#x27;B</td><td>C</td><td>D</td></tr><tr><td>奖金</td><td>10000</td><td>5000</td><td>3000</td><td>1000</td></tr></table>

# 4.7 敏捷开发模式推进

# 1．采用敏捷开发方式

将项目按照小型迭代方式开展，快速响应变更和用户反馈。对于W网络游戏项目确定迭代的周期分为大周期与小周期，制定周期计划，小周期为2周，大周期为1个月与6个月两种。分配周期计划任务，跟进迭代情况分配目标任务，部分人员在小周期内，而一部分在执行中长期的迭代任务里。实施迭代发布，跟进周期迭代情况进行评估。在每个迭代结束后，对工作成果进行总结和评估，并将所学到的经验和教训运用到下一个迭代中。通过合理地设置迭代周期、制定计划、分配任务、实施迭代管理和监控评估等环节，可以提高项目效率，实现项目目标[21]。以下图4-7表示项目的敏捷开发图。

![](images/68f857157585cc3aee27d910e0fb6316ac32eef32ec907e1d072693e6741dd85.jpg)  
图4-7敏捷开发

2.利用自动化工具优化流程线

效率优化方面是使用自动化工具，包括版本的管理工具化，程序的自动化构建，自动的单元测试，自动化部署与上线。利用DevOps理念加速软件开发过程，减少人工干预，提高测试覆盖率和质量，减少发布上线时间线。从而缩短项目开发周期。具体采用办法包括：（1）项目中产品人员使用的版本工具采用新开发的内容下载器，利用下载器比版本工具SVN的下载更迅速，产品人员也不需要SVN 的提交功能，这样缩短下载时间，增加产品人员的工作时间。(2)使用 Jenkins自动集成工具，每日自动构建软件版本。因此减少了专门人员去构建版本[22]。(3)自动化测试的应用，引入开发类型的测试人员，采用测试框架，编写自动测试软件模块，对软件产品进行自动化测试。这样减少了程序的Bug数量。（4）自动化运维引入。比如每日版本的自动构建与服务器重启机制。减少运维人员的手动干预。下图4-8示意图生产流程线路。

![](images/839c71e09b533246209c1d392f52c7c00befb60491474e75ba6a5e0738961540.jpg)  
图4-8流程线示意图

# 4.8建立风险预警机制

对于项目控制的主要负责人，需要及时识别项目风险，以便以后对项目的控制。而进度管理中的风险因素有很多，诸如技术难题，时间短，沟通占用时间等等[25]。项目经理制作项目风险表，识别相应的风险，并进行制作相应的风险应对措施，避免风险转移成实际问题，当将要成为问题之前及时出现问题警告，项目经理及时加以引导修复。W项目起初只是应对技术风险比较多，项目经理出身于技术，对技术风险加以了合理控制管理，但是遇到非技术问题时也应该合理管控。项目中除了技术，还有美术部门，策划部门等等都要管理者对部门管理人员提出风险列表，加以汇总管理控制。以下图示4-9说明改进之前的风险控制策略：

![](images/74320b6bf38e2007f0ab9f2a8d6eab5545404e0fe22975d8ceae7a1dd2115a70.jpg)  
图4-9改进之前的风险控制

现对W项目改进方法提出以下日常的管理风险的制度如下[3]：

# 1.日常监控和检查

对于一个游戏项目本身来说，它有着很强的复杂性和不确定性，这会导致项目进度计划执行实施过程当中，会受到很多因素影响，项目人员、设备、技术、环境等因素也会影响工作的实际进度。为此，在项目W的实际过程中，一定要密切关注项目的实际进度情况，不然做的进度工作等于白费，而且也要及时确定项目的实际任务的交付情况。目前是有周相关任务的周报，总结相关工作内容，以保证信息齐全，同时将相关信息提交给项目部。每周的例会相关模块人员都会自行拉起会议，对上周完成的内容和本周要处理的工作进行简要介绍和整理。

对于日常的项目监控，主要是将项目工作任务分解成小任务，并根据相关人员确定各自的职责，每个任务都会有自已独立的文档，并与其父任务相关。在W项目进度的日常监控过程中，公司提供项目管理相关软件的技术。所有项目参与者都可以看到他们日常任务的显示。可以看到每个任务何时处于该阶段，是否延迟，并快速预览甘特图以查看任务的整体情况，为后续进度偏差分析提供适当的保障。对于上线项目还要有实时监测网络游戏质量，根据线上用户反馈优化游戏业务质量[26]。

# 2.定期监测和检查

通过监控项目W的周期，项目经理可以更好地了解项目的进度。每个部门都可以按照规定的工期适当确定部门任务的进度。在日常督导中，主要是根据各自的职能进行详细的统计和分析，并根据进度督导确定相关项目的进度。在帮助项目经理的前提下，项目经理进行相关的统计和总结，从而达到对它们的及时分析。由于项目例行监控更多是基于项目的完成程度，因此项目例行监控只能判断每个项目的进度。在例行监督项目时，还必须将各种工作转换为整个项目计划。为此，有必要判断各种工作任务的完成情况，项目进度状态等。使项目部更好地了解项目的实际运转，以便将资源合理地调配调度及相关任务比例的安排。图示如以下图4-10所示。

通过对项目W执行情况分析，检测周期可以确定为“月”，然后项目经理可以汇总出项目的进度，并结合工作进度，确定相应的总结报告。通过总结项目不同工作的实际进度，确定项目实施情况，对相关进度内容做出合理的预测和评价。

![](images/253b2b3b452e1671407d88d7003b072a444a5bb495246a03282474314e0bda04.jpg)  
图4-10改进之后的风险控制

# 4.9 新增任务缓冲区的设置

为软件项目设立进度管理方面的缓冲区时间通常是为了应对不可预见的风险和延迟，以确保项目能按时交付。在本文中，我将介绍如何为软件项目设置进度管理的缓冲区时间并进行一一罗列说明。

1.确定关键路径，需要确定项目的关键路径，即决定了项目最短完成时间的一系列任务和活动。这些任务必须按期完成，否则整个项目的完成时间都会受到影响。

2.估算延迟系数：根据经验和历史数据，对项目中可能出现的风险和延迟进行估算。考虑到各种不可预见的情况，最好对风险和延迟进行保守估计。其中部门组长根据复杂程度给出一个系数，任务具体的实施人给出一个评估难度系数。

3.计算缓冲区时间：根据关键路径和风险延迟估算结果，计算出所需的缓冲区时间。缓冲区时间应该足够长，以确保在遇到风险和延迟时仍能按时交付项目。

4.分配缓冲区时间：将缓冲区时间分配给关键路径上的任务活动。

评估结果：在项目完成后，评估缓冲区时间的使用效果，并总结经验教训。这将有助于提高为下一个项目设置缓冲区时间的准确性和有效性。

以下表4-5所示采用弹性系数的缓冲区设置：

to:乐观估时; te:悲观估时; s:是否关键路径;B:缓冲量;i:任务ID;p:工序位置，指的该任务在关键链中的工序顺序位置号；  
$\mathbf { k }$ 部门组长给出的复杂程度评估系数（区间1-10）；  
h:任务实施人员评估难度系数（区间1-10）；  
关键路径任务：C;TB:缓冲区设置总量；  
B(i):代表i任务的缓冲量；  
公式计算为： $\mathbf { B } _ { \mathrm { i } } = ( \mathrm { t e _ { i } } \mathrm { - t o _ { i } } ) \mathbf { s } _ { \mathrm { i } } ( \mathbf { k } _ { \mathrm { i } } / 1 0 \mathrm { + } \mathbf { h } _ { \mathrm { i } } / 1 0 ) ;$ []  
缓冲总量计算为： $\mathrm { T B } = \sum _ { \mathrm { i } \in C } \mathrm { ( t e _ { i } - t o _ { i } ) p _ { i } s _ { i } ( k _ { i } / 1 0 + h _ { i } / 1 0 ) } ;$ []

表4-5任务缓冲区的设置表  

<table><tr><td>ID</td><td>任务名称</td><td>to</td><td>te</td><td>S</td><td>P</td><td>k</td><td>h</td><td>B</td></tr><tr><td>1</td><td>技能策划</td><td>10</td><td>18</td><td>1</td><td>1</td><td>5</td><td>6</td><td>2.4</td></tr><tr><td>2</td><td>技能程序</td><td>5</td><td>8</td><td>1</td><td>2</td><td>4</td><td>4</td><td>0.96</td></tr><tr><td>3</td><td>技能测试</td><td>3</td><td>5</td><td>1</td><td>4</td><td>3</td><td>4</td><td>0.96</td></tr><tr><td>4</td><td>技能美术</td><td>7</td><td>12</td><td>1</td><td>3</td><td>4</td><td>2</td><td>1.2</td></tr><tr><td>5</td><td>任务策划</td><td>12</td><td>20</td><td>0</td><td>1</td><td>3</td><td>4</td><td>0</td></tr><tr><td>6</td><td>任务程序</td><td>6</td><td>9</td><td>0</td><td>2</td><td>4</td><td>5</td><td>0</td></tr><tr><td>7</td><td>任务美术</td><td>7</td><td>14</td><td>0</td><td>3</td><td>3</td><td>2</td><td>0</td></tr><tr><td>8</td><td>任务测试</td><td>4</td><td>7</td><td>0</td><td>4</td><td>2</td><td>4</td><td>0</td></tr><tr><td>9</td><td>缓冲总量</td><td></td><td></td><td></td><td></td><td></td><td></td><td>5.52</td></tr></table>

上表通过计算关键路径上的活动从1到4的每一项的缓冲值，进而通过累加每一项任务的缓冲量计算得出缓冲总量。然后在计算版本迭代总工期时加上缓冲总量，就是迭代版本的时间计划。

# 4.10本章小结

本章是进行网游研发项目进度优化改进方案提出的方法机制，提出的方法机制主要是对计划、流程、需求变更、开发模式等提出的一些改进措施。管理项目建立合理的流程机制，再结合合理的项目开发模式才是实现对项目进度管理的加快推进，还有要对进度绩效的优劣情况进行奖惩，加强实际执行的落地，提高管理的可控性。然后在项目工期加上自动风险预警机制，将版本的关键链任务提前识别，跟踪监控，合理推进改进，达到游戏开发进度控制，进而达成项目目标。

# 第五章W网游研发项目进度管理实施及效果

上一章提出了一些针对性的进度管理改进方案，本章提出一些针对于具体实施环节上的保障措施。进一步阐述方案的落地执行的实用性。然后对于实施结果进行说明。

# 5.1 具体实施保障

# 1.进度跟踪保障

设置项目计划进度追踪时间跨度单位，即项目花了多久时间跨度就一定要做进度绩效报告及考察。若绩效追踪时间跨度太长，就会使计划存在较大概率失控；但若太短，又极易提高管理成本与队伍负担。因此应按项目特征科学设置其时间跨度。

根据项目特点，追踪时间跨度设置为一星期一次。周四晚会对本周合入工作内容，周五下午组织会议。在例会上，采集所需的信息。按照项目特性，项目组把重点问题定为：工作范畴有变动吗，超出目标时间了吗，估测有问题吗等，用这些信息收集进度绩效。在每周会议，项目组人员按自已需求任务单的分工并围绕“内部进度报告”表收集信息。

# 2.组织保障

# （1）实施人员分配

现在会有专门的项目管理人员进行进度管理。在本项目中，还为开发和美术两条路线分别配备了不同的项目辅助管理员。对各自的路径进行监督。同时经过一段时间的磨合，项目管理员也对开发和美术各自专业领域有了更多了解，为之后更好协调工作安排打下基础。

# （2）做好培训任务

在项目的初期，部分美术任务需要确立项目标准，反复调试导致部分任务延期。导致美术经理与项目管理者对工作任务重点产生分歧。如果双方更多地了解对方专业领域的知识。

培训不仅是项目管理者要加强学习项目进度管理知识，专业技术员也需要。在企业内进行相关知识培训，开展在线课程，无偿提供给职员，支持他们学习。尽量让技术员在技术力强的同时也可以掌握管理知识，培育“技术管理复合型”专业人才。

# （3）实施绩效考评

要解决管理低效，最好的方法是把全部职责一次分配到岗。优化管理程序及各种管理机制，会对原本的施行流程产生冲击，并提高任务量，若缺少对应的嘉奖制度，会引起施行者的抵触。把工作、责任落实到人，对其开展考评奖励，是推动新程序和机制的最佳办法。快速设计、执行岗位责任制，推广绩效考评管理法，为进度管理优化方案的顺畅施行奠定基础。

# 3.资金保障

（1）人工成本的保障与培训费用保障。加强成员配置，就提高了人事投资，会加大项目人事成本。新计划也加大了项目组员的平日任务量，相应的绩效考评和嘉奖机制，也会提高人工支出。新计划的施行应确保这些新增人工开支的落实，完成项目早期预算。

（2）新计划中，应全面增强项目经理管理知识能力，提高专业技术者的进度管理知识储备，应设计合理、有用的培训方案。落实人员培训，适用新的任务活动政策执行流程方法。落实绩效考核，让进度优先执行好的员工有相应金钱回报。设定共同愿景，共同努力为项目组进度稳扎稳打。

（3）团队建设开支保障。游戏项目特别依赖团队协作，策划、美术、程序开发只有共同合作才能产出满意的产品。任何一方出现问题都会导致项目成果不够理想。在团队内部，各自任务也有相互依赖关系。良好的团队关系与氛围，加强各方协作效率。每个阶段性里程碑任务达成之后，也会组织开展团队建设活动。

在本次项目过程中，人工成本方面公司提供了稳定的保障。尤其是在项目中期任务繁多时，从其他项目抽调了部分同事过来支持，大大节省了整体开发时间。同时抽调的同事也对其专业领域有着很深厚的研究与丰富的实践经验，降低了开发延期风险。团建方面，在取得阶段性展示成果之后，各部门也组织人员进行团建活动。提升整体团队士气，让项目参与者更了解项目实际效果，对未来目标与方向更有信心。并且团队氛围与协作效率也得到了很好的保障。

# 5.2实施效果评价

方案可行性实施：方案实施到项目中可以跟踪观察，活动任务的延期情况，进行统计观察，跟之前的活动延期情况比例进行比较。进度关联绩效，员工工作积极度应该跟之前不同。变更控制之后预期版本质量会逐步提高。版本内容开发进度由于有奖惩制度及缓冲区的设定得到一定程度的控制。对实施结果进行了以下汇总：

1．WBS任务分解经过优化之后，项目团队人员的工作量逐渐趋于饱和，相比之前的某些人员工作量大及某些人员的工作量少的现象很少发生。每次版本迭代的内容量相比之前更加清晰，对控制任务扩散起到了意义。

2.进度计划的调整之后，团队成员为里程碑执行版本内容，更有热情，积极性更充足，员工非常积极为自已达成任务目标及为达成项目里程碑版本目标。绩效管理在为里程碑版本方面的驱动也启到了相应的作用。

3．通过任务优化估时、需求变更控制的修改之后，项目总体任务按时完成率比之前提高。根据某一次迭代的版本依据改进之后的方案施行之后，项目依据原先模式的计划工期与改进优化之后的工期对比在下表5-1所示。

表5-1任务计划工期与改进工期对比表  

<table><tr><td>工作编号</td><td>任务名称</td><td>计划工期</td><td>改进工期</td></tr><tr><td>1001</td><td>新手任务</td><td>11</td><td>10</td></tr><tr><td>1002</td><td>元素玩法</td><td>14</td><td>10</td></tr><tr><td>1003</td><td>生活技能</td><td>6</td><td>6</td></tr><tr><td>1004</td><td>战斗技能</td><td>8</td><td>7</td></tr><tr><td>2005</td><td>商城系统</td><td>5</td><td>5</td></tr><tr><td>2006</td><td>风景点</td><td>12</td><td>12</td></tr><tr><td>2007</td><td>巨兽美术</td><td>9</td><td>10</td></tr><tr><td>2008</td><td>宠物美术</td><td>17</td><td>17</td></tr><tr><td>3002</td><td>界面美术</td><td>16</td><td>17</td></tr><tr><td>3003</td><td>战斗玩法</td><td>9</td><td>9</td></tr><tr><td>3004</td><td>新手玩法</td><td>6</td><td>5</td></tr><tr><td>3005</td><td>成长玩法</td><td>8</td><td>7</td></tr></table>

从此次版本来看改进之后的实际执行工期比原计划的工期整体相比减少了。起到了比较好的进度优化的作用。

4.通过引入任务缓冲区的设立，项目版本迭代的任务达成更有了调节性，对关键链任务的重视，使其对版本迭代的影响变的有限。迭代版本趋于稳定。

5.通过DevOps方法，进入自动化工具后，版本持续迭代节省了大量时间，团队开发人员关注版本周边工具更少，转移重点到自身的开发内容时间更多，进而间接推动自身任务的进度。通过敏捷开发模式的推进，项目持续根据反馈参与迭代，团队习惯趋于稳定，产生有效的开发节奏。

6.项目风险预警机制的引入和人员能力素质提升机制的引入，项目的版本稳定程度比之前好，以一次两周版本迭代为例，其bug相比改进之前要少。如下表5-2所示。

表5-2版本迭代bug数量与改进之后对比表  

<table><tr><td>版本类型</td><td>bug 总数</td><td>严重bug数</td><td>轻微bug数</td></tr><tr><td>原来的两周版</td><td>34</td><td>11</td><td>23</td></tr><tr><td>改进的两周版</td><td>25</td><td>7</td><td>18</td></tr></table>

通过版本相比，改进之后的bug数量相对更少，严重 bug更少。改进方式具有控制项目版本质量的作用。

# 5.3本章小结

在本章主要是设计S公司W游戏项目进度改进方案的实施方式，从进度绩效报告、组织保障、资金保障三方面提出了各自的保障措施。是让方案能够顺利实施的保证。然后进行方案实施后，得出实施结果，对方案设计措施进行得以验证。

# 第六章结论与展望

# 6.1研究结论

本研究应用了项目管理理论知识，通过项目进度的研究和制定，发现W项目研发工作项目进度管理中的不足之处。本文以网络游戏W项目为例，应用项目管理理论知识及工具对其项目进度管理进行调研分析与实验，研究项目的工作计划，改进项目迭代方式，优化项目流程，优化进度控制，优化工作分解结构，改进关键任务工作包，并使用PERT技术估算活动时间，制定合理适用的进度计划，然后对游戏W项目进行监控，从而改进W项目的项目进度管理方法和流程，最终达到控制工期和工作量、平衡资源、控制成本、加快进度的作用。理论联系实际，力求切实有效地控制项目进度，最终达到理论指导实践、实践补充理论的研究。

本研究针对于W网络游戏项目的研究结论可以概况如下：

1.W项目的进度和计划的控制是项目成功实施的关键。因此，项目管理人员应该确保对整个项目的基本任务包进行合理的安排和监控，同时掌握项目的范围和主要方案设计。建立起项目实施开发流程体系，以确保项目按计划进行。

2.在W项目中，工作结构分解和活动时间估计是至关重要的环节。首先，采用WBS方法将工作分解为基本单元的任务工作包，并将资源分配到各个WBS包中。其次，利用PERT技术对每个工作包进行三点估计，计算项目所需的活动时间，并与相关人员一起确定最终的项目周期。然后，根据项目活动的直接关系对工作包进行排序，形成项目的关键路径和里程碑事件。通过使用管理进度软件来控制变更流程，使项目控制阶段更加直观和明确。

3.W项目进度的实施需要与沟通计划和基线控制相结合，及时识别风险并控制问题的发生，同时协助项目变更管理的控制和监控。

4.落实W项目的敏捷迭代开发方式。对于网络游戏项目，需求经常变化是常见的情况。因此，采用版本迭代的方式进行开发，先实施需求明确的版本任务，并在后续的迭代过程中逐步确认和开发后续版本的内容。这样可以避免频繁的返工现象，提高开发效率。

5.加强W项目人员的凝聚力。人力资源是软件项目的主要资源，在管理项目时应注重人性化管理，避免过多的考核对团队凝聚力的削弱。应加强项目组人员的团结建设，提高项目经理的领导能力，激发项目组中所有人员的积极性和战斗力，以促进项目进度的正常推进。

本研究针对于网络游戏的研究作者可以整理以下值得参考的四点：

1.基于对网络游戏项目进度控制的意义、难点和问题的理性分析，提出了适应网络游戏项目特殊性的项目进度管理方法。其中包括持续设立缓冲期任务形式、采用迭代式开发、跟进迭代布置任务包、调整工作计划等措施。针对不同迭代阶段，采取相应的项目进度管理措施，包括项目DEMO原型开发阶段、迭代研发阶段、项目封测及试运营阶段、上线运营期及更新维护阶段。

2.在项目管理方面，提出修正项目工期计算方法并实际应用于网络游戏项目中。同时结合优化WBS任务分解、变更管理措施、进度绩效措施、风险措施、自动化措施等方法，综合运用于项目管理中。采用日常观测法和定期观测法进行进度跟踪，及时修正项目进度。

3.针对项目开发过程，提出以下建议：采用敏捷迭代开发方法，加快迭代版本的周期。同时进行DevOps实施，自动化生产线的相关流程，从而节约人力，通过资源复用与整合的结合实现精细化管理。

4.针对项目延期问题，提出以下建议：避免技术选型复杂性和自研引擎所带来的时间浪费；关注团队疲劳和积极性不足，采取相应措施提高团队士气；优化研发流程，加强变更管理，以减少项目延期的发生。

# 6.2研究不足与展望

本研究是在掌控有限资源的情形下的研究，由于本人学术积累不足等因素，还存在管理见解方面的诸多不足。在以后的研究过程中会从以下方面继续探索：

1.本文对敏捷开发模式的研究尚浅，仅应用了部分敏捷开发思想，还有很多敏捷开发的思路与工具方法值得学习应用。本人也会继续研究学习这方面内容。2.目前本文研究仅限网络游戏的进度研究，随着实践和学习的进步，需要不断总结补充各种不同类型游戏项目的进度管理方法。因为就游戏内容来说，我国网络游戏市场已从单一转向多元，网游变为以电影、小说、动漫、历史等多元化的题材[30]。

3.目前比较流行的DevOps方法，还有很多内容需要学习借鉴，本人也自知尚浅，期望后续继续努力，补足这方面的知识，增取能够应用相关知识到实践。

4.本人职位权限有限，有些改进方案还没有得以真正落地实施，需要加强自已的努力，期望将来能够真正落地实施。而且在未来网络游戏与次世代游戏有融合的趋势，次世代的技术也是后续需要本人加强学习的领域[23]。

5.要真正实现项目进度管理的合理性和有效性，必须要有成本模块、资源管理、沟通管理等等相应的合理有效控制。在往后的工作学习下，我将继续学习研究项目管理知识体系，努力实践，探索研究更加合理的进度管理模式，使方案更具普适性与易用性，期望能够更好地利用项目管理知识体系改进项目。

# 参考文献

[1]刘睿.ZC公司游戏开发项目进度管理研究[D].北京理工大学,2016.  
[2]余建民.浅论项目施工管理中进度计划的管理[J].广东科技,2007,No.169(07):143-145.  
[3]王非.A公司新产品研发项目进度管理优化研究[D].南京航空航天大学,2020.[4]王晓丹.我国网络游戏产业的经济学分析[J].营销界,2021,(18):119-120.  
[5]克林顿·基思.游戏项目管理与敏捷方法[M].清华大学出版社,2021.  
[6]孙科炎.华为项目管理法[M].机械工业出版社,2014.  
[7]齐飞帅.A公司软件项目进度管理研究[D].北京邮电大学,2021.  
[8]李英龙,郑河荣.软件项目管理[M].清华大学出版社,2021.  
[9]方觉晓.游戏软件项目的范围与进度管理研究与实践[D].复旦大学,2010.[10]严驰.数字时代网络游戏出版管理的现实问题与困境突破[J].出版与印刷,2023,No.132(01):24-32.  
[11]颜涛.Y游戏公司研发人员绩效管理优化研究[D].华东师范大学，2022.[12]王暂雯.X公司软件开发项目进度管理改进研究[D].大连理工大学,2020.[13]王涛.A公司M项目进度管理优化研究[D].北京邮电大学,2021.  
[14]应向民.软件项目外包类员工激励机制建立的探索[J].中国管理信息化,2016,19(18):122.  
[15]谢添敏.项目管理理论在游戏项目开发中的应用D].复旦大学.2013.  
[16]徐鹏.网络游戏项目的研发进度控制研究[D].昆明理工大学,2015.  
[17] Dimitriadou, Anastasia, et al. "Challenges in serious game design and  
development: Educators experiences."[J]Simulation & Gaming,2021,52(2):132-152.[18]赵思奇.X公司A游戏项目开发进度管理研究[D].电子科技大学,2022.[19]赵建霖.A软件项目进度管理的改善研究[D].电子科技大学,2020.  
[20]周海洋.CY游戏公司产品开发项目管理研究[D].北京邮电大学,2019.  
[21] Clinton,Keith.Agile Game Development with SCRUM[M].Arrangement with theoriginal publisher,2015.  
[22] Chandler, Heather Maxwell.The Game Production Handbook[M].Jones &Bartlett Publishers,2017.  
[23]谢霄.次世代游戏与网络游戏比较分析[J].信息记录材料,2018,19(06):187-188.  
[24]Tynan Sylvester.体验引擎：游戏设计全景探秘[M]电子工业出版社,2015.[25]邵佳卿.基于游戏企业战略和风险管理的案例研究[J].现代商业,2021,No.617(28):147-149.  
[26]何瑞强.网络游戏的特点及现状分析[J].福建电脑,2019,35(05):68-70.  
[27]丁荣贵.抓好项目管理，实现制胜之道[J.项目管理评论 $2 0 2 1 , \mathrm { N o } . 3 7 ( 0 4 ) { : } 1 8 { - } 2 3 { + } 9 .$ [  
[28]张树森,金永成.国际化浪潮下网络游戏“出海”现状与策略研究——以腾讯游戏为例[J].新媒体研究,2021,7(16):101-106.  
[29]林郑雅.浅析韩国网络游戏的出口成功经验和启示——基于大热网游《绝地求生》[J].时代金融,2019,No.733(15):125-126+132.  
[30]肖施霞.我国网络游戏产业研究—-—基于SCP范式分析[J].商场现代化,2016,No.832(25):251-252.  
[31]易莲媛,孙佳山.中国游戏做对了什么？——游戏进口与中国游戏的想象力[J]长江文艺评论,2022,No.36(02):113-120.