# 基于多智能体的复杂工程项目进度风险评估仿真建模

# 颜功达董鹏文昊林

（海军工程大学管理工程与装备经济系武汉430033)

摘要为解决复杂工程项目结构复杂、周期长、风险因素多等特点导致的进度重大延误问题,运用Anylogic软件在考虑工序状态转换条件及处理行为的基础上，建立了“工序”“工序流”"风险因素”及“控制体”4种智能体元素，形成了由“风险因素”导致的“工序流”与“工序"之间的多重嵌套关系，由此建立基于多智能体的进度风险评估模型，并通过仿真实验完成了某型舰用柴油发动机维修项目的风险因素敏感性分析，得出应采取措施优先控制的重要工序中的风险因素。实验结果表明：该模型对于复杂工程项目进度风险评估问题具有较好的参考价值。

关键词多智能体，工程项目，进度风险，风险评估 中图法分类号TP391.9 文献标识码A

# Simulation Modeling of Complex Engineering Project Schedule Risk Assessment Based on Multi Agent

YAN Gong-da DONG Peng WEN Hao-lin (Departmentof Management Engineeringand Equipment Economic,Naval UniversityofEnginering,Wuhan 43oo33,China)

AbstractIn order to solve the problems of complex engineering projects schedule risk assessmentdue to their complex structure,long cycle and numerous risk factors,a project schedule risk assessment model based on the multi agent was establishedby the Anylogic software.Inthis model,a“process”agent,a“process flow”agent,a“risk factor”agentand a"control”agentwere were designed bytaking intoaccountthe processtate,transformationconditions andtheinternal processing behaviorof the state.Multiple nested relations between the“process flow”andthe“process”were formed by “risk factors”.The risk factors sensitivity analysis of a certain type of marine diesel engine maintenance project was completed through simulation experiments,then itcomeup with the risk factors in important processes that should be taken measures to give priorityto control.Experiment results show that the model for complex engineering project schedule risk assessment has a certain reference value.

KeywordsMulti agent,Engineering project,Schedule risk,Risk assessment

# 1引言

复杂工程项目范围宽、内容多、周期长、工序关联强，在实施过程中，技术、资源、环境等风险因素的不确定性高，项目进度目标管控难度大。进度管理对于项目的成功执行是至关重要的，如果不合理有效地安排项目进度，项目各组成结构之间会很难灵活协调运作[1]。然而，在项目实施过程中，进度往往存在不确定性[2]，任何潜在风险因素都有可能对项目工期产生直接或间接影响[3]，从而导致项目进度延误，造成巨大损失。而进度的延迟是预算超支的重要驱动因素[4]，因此，对影响进度的风险因素进行识别和评估对项目的成功管理十分必要。

复杂工程项目的进度风险评估一是要解决进度风险影响因素的准确识别问题；二是要解决项目复杂性特征对进度的影响程度问题。目前，已有的评估方法有网络计划技术[5、离散事件系统仿真[6]、系统动力学模型[7-8]等。网络计划技术本质是一种简单图形工具，只适用于工序关系相对明确的工程项目进度分析；离散事件系统仿真相比网络计划技术能够得到更为准确的进度统计值，但难以解决复杂工程项目中大量存在的返工问题，即重大返工或多次返工会导致项目成本预算超支以及无法在预期工期内完成；系统动力学模型更适用于从整体角度评估多因素、多循环的项目进度风险，但难以处理逻辑复杂的工序动态调整问题。

受工程项目结构复杂、风险因素多、多次返工等因素的影响，采用常用的网络计划图、离散事件系统仿真和系统动力学方法无法达到良好的评估效果。而多智能体建模技术的目标是将大而复杂的系统建设成小的、互相通信和彼此协调的、易于管理的系统[9]，本文运用多智能体中的消息组件及状态变迁组件对复杂工程项目进行建模，使复杂问题划归为单一对象，从而使每个对象达到良好的独立性和自主性，较好地解决项目多次返工问题以及返工路径中的工序逻辑复杂和动态调整问题,并通过对具体问题进行仿真实验来验证模型的适用性和实用性。

# 2复杂工程项目进度风险评估模型的建立

# 2.1建模思路

复杂工程项目的基本单元是工序，研究通过拆分复杂工程项目进度风险评估问题中的对象为工序、风险因素和工序流，由工序所构成的网络结构图形成了工程项目的主体，每项工序中可能存在一种或多种风险因素，而风险因素的产生导致了工序各种形式的返工，返工的形式可能是工序内部的返工，也可能是后续发现前序错误而引发前面多个工序的大返工，将这种返工形式命名为返工工序流，返工的多次积累被称为多次返工。

若干个工序构成复杂工程项目计划施工的主工序流，每个工序中对应存在若干个风险因素。若风险发生，则会引发与其对应的返工工序流，返工工序流中又包含了若干个工序，这样形成了一种工序流 $\twoheadrightarrow$ 风险因素 $\nrightarrow$ 工序流的触发结构，各类型对象之间的关系如图1所示。其中，蓝色圆圈代表工序，黄色圆圈代表风险因素，工序与方框代表工序流，实线箭头代表工序内部关系，虚线箭头代表工序与风险因素之间的触发关系。

![](images/db7b1a28005d2ef9e35e522d48fa45775d8b22335165dbe04054db14e1ab933a.jpg)  
图1各类型对象关系示意图(电子版为彩色)

# 2.2模型的建立

智能体是在动态环境中具有较高自治能力的实体对象。按照复杂工程项目进度风险评估的建模需求，为解决风险因素、返工流以及多次返工的逻辑构建问题，根据对象建立相应的智能体类型，分别为"工序”“工序流”"风险因素”3个智能体类型，并建立“控制体"智能体用以控制仿真并记录仿真数据。4个智能体之间的相互作用和联系共同构成了复杂工程项目进度风险评估模型，下面介绍这4种智能体的内部逻辑以及它们之间的相互关系。

# 2.2.1“工序"智能体

工序是工程项目进行的主体环节，将不同的工序看作同一类型，“工序"智能体内部仅需考虑工序进行状态的相关参数及存在的风险因素。下面分别从参数、状态、分支、变迁这4个方面对“工序"智能体的逻辑行为进行说明。

(1)设置"工序"智能体内部参数有初始工期、返工工期、前置工序序号、风险因素、总工期、剩余工期、完成状态、工序开始时间、工序结束时间等，其具体赋值由程序内部控制。

(2)根据工序所处的4种状态，设置状态有待工、进行、返工和完成。

（3)“工序"智能体中存在一个分支，用于判断风险是否产生。条件为工序中存在的各“风险因素"智能体的风险发生率与随机生成因子对比，生成数组结构，并依次触发与其对应的返工“工序流”,并转移到"返工流"状态。若"工序"中的"风险因素"均未被触发，则转移到"完成"状态。

(4)根据工序状态的转移条件及仿真需求，设计如图2所示的"工序"智能体状态图。

1)"进行"状态到分支的条件为剩余工期小于或等于0;   
2)"进行"状态到"返工流"状态的条件为"风险因素”智能

体中的风险确实发生；

3)分支到"完成"状态的条件为随机生成因子的发生概率大于风险因素的发生概率；

4)"完成"状态到"待工"状态的消息为接收到其他智能体发出的"重置"命令，条件为前置工序数不等于前置工序完成度。

![](images/f5c189c0a9a741d2af4f363f409236b8b973d4c455f586d27b5fda90afa57ebd.jpg)  
图2“工序"智能体状态图

# 2.2.2“工序流”智能体

工序流是工序运行的主体环境，“工序流"智能体可分为主“工序流"及返工“工序流”。只有在同一个“工序流"中的“工序"才能通过消息组件发生相互影响，因此"工序流”最主要的作用是将不同"工序流"中"工序"之间的消息影响进行隔离，其参数包含工序开始序号和工序终止序号，以及“工序"智能体的连接结构。“工序流"智能体的内部结构示意图如图3所示。

![](images/44aa0eef4e5b74f68e9caf8a2ee7c7644aeff5e762f238b2843c5fa852446cc6.jpg)  
图3“工序流"智能体的内部结构

“工序流"状态图如图4所示，下面仅对图4中的重要步骤进行说明。

1)当收到某“风险因素”智能体发出的工序流开始指令时，“工序流”由“待工"状态转移到"进行"状态；

2)当本“工序流"智能体内部“工序”全部完成时，“工序流”由"进行"状态转移到"完成"状态；

3)当本"工序流"接收到"重置"指令时由“完成"状态转移到“待工"状态。

![](images/61263b76744f04c411afc62085574ea884aa05bf37d64f42064a4bfad01837ba.jpg)  
图4“工序流"智能体的状态图

# 2.2.3“风险因素”智能体

每个工序存在不同个数、不同类型的风险因素，将每一种风险因素看作“风险因素"智能体。“风险因素"智能体在整个仿真过程中作为一个参数对象连接着“工序"智能体与“工序流"智能体，风险的发生导致工序返工，工序内部可以存在多种风险因素，而单个“风险因素"仅能触发一个返工“工序流”，因此"风险因素"与返工“工序流"成一一对应关系，其中主“工序流"由"控制体"智能体进行控制，返工“工序流”由“风险因素"智能体引导发生。其参数应包含其对应的返工“工序流”序号及风险发生率的分布情况。

# 2.2.4“控制体"智能体

在仿真主环境中添加一个“控制体"对象，该对象属于“控制体"类型，用于控制仿真实验的进行和协调“工序流"智能体。控制体在本仿真中的作用是用于控制主工序流、控制仿真次数、记录仿真结果以及发送“重置"指令来协调"工序”智能体的运转。下面我们从参数、状态、分支、变迁4个方面对智能体逻辑进行说明。

(1)设置“控制体"智能体内部参数有总仿真次数。

(2)在仿真进行的过程中，为防止消息异步传输而导致的仿真无法重置，采用添加强制重置手段在进入“仿真进行”状态前将最终工序的完成状态重置为未完成，以获得异步处理所需调整的时间。在仿真结束时，向所有智能体发送“仿真结束"指令，以停止整体仿真。

(3)根据是否达到总仿真次数，设置条件为“现仿真次数与总仿真次数相等”，记录单次仿真时间并插入数据到数据库。然后对现仿真次数进行迭代，并向所有智能体发出“重置"命令，更新仿真时间数据图。

（4)"控制体"智能体的状态图如图5所示，下面仅对图5中重要步骤进行说明。

1)单次仿真结束后由“进行"状态到"是否达到总仿真次数"分支的条件为最终工序完成状态是否为完成；

2)达到总仿真次数时，由“是否达到总仿真次数"分支到“结束"状态的条件为现仿真次数与总仿真次数相等。

![](images/4a9e7cbd680d239f4fef6c7a2aaa48026660fb262739f3ba0a7b5459c8fed0da.jpg)  
图5“控制体"智能体的状态图

# 3案例仿真及结果分析

舰船包括船体、动力系统、电力系统、船舶装置和作战系统等，是一个典型的复杂装备系统。目前，舰船维修工程项目普遍存在预算超支、进度拖延的现象，导致舰船无法按时完成训练任务，装备经费也没有得到合理有效的使用[10]。舰船维修的复杂性导致影响维修进度的风险因素大量存在，如备品备件质量不合格、工人疏忽、检查不到位、工艺不规范、技术更新不及时以及难以预料的自然灾害等，都会不同程度地增加维修工程项目进度控制的难度，进而导致项目进度的拖延，降低维修的质量和效率，影响舰船正常投入使用。

因此，本文选取某型舰用柴油机小修项目为案例进行仿真及分析，重点考虑工期因素，通过实际平均总工期与控制后的平均总工期之间的差距来体现复杂工程项目进度风险因素的影响程度，通过分析得出对维修工期产生重要影响、需要优先采取措施控制的主要风险因素，从而为复杂工程项目进度风险管理提供可靠依据。某型舰用柴油机小修项目的工序代号及名称如表1所列。

表1工序代号及其名称  

<table><tr><td>代号</td><td>工序名</td><td>代号</td><td>工序名</td></tr><tr><td>S1</td><td>柴油机拆卸</td><td>S14</td><td>摆式减震器分解、维修</td></tr><tr><td>S2</td><td>气缸盖分解、维修</td><td>S15</td><td>凸轮轴维修</td></tr><tr><td>S3</td><td>活塞连杆分解、维修</td><td>S16</td><td>离心式滑油过滤器分解、维修</td></tr><tr><td>S4</td><td>气缸套检修</td><td>S17</td><td>气缸装配</td></tr><tr><td>S5</td><td>齿轮箱检修</td><td>S18</td><td>曲轴组件检修</td></tr><tr><td>S6</td><td>螺旋泵分解、维修</td><td>S19</td><td>凸轮轴使动机构装配</td></tr><tr><td>S7</td><td>滑油泵分解、维修</td><td>S20</td><td>柴油机体检修</td></tr><tr><td>S8</td><td>冷却水泵分解、维修</td><td>S21</td><td>气阀间隙和相位调整</td></tr><tr><td>S9</td><td>活塞连杆组装配</td><td>S22</td><td>螺旋泵传动机构装配</td></tr><tr><td>S10</td><td>气缸套装配</td><td>S23</td><td>泵传动机构与罩壳装配</td></tr><tr><td>S11</td><td>柴油机油底壳检修</td><td>S24</td><td>平衡机构单元任务的相位调整</td></tr><tr><td>S12</td><td>气缸拆卸、分解</td><td>S25</td><td>结束工序</td></tr><tr><td>S13</td><td>空气分配器拆修</td><td></td><td></td></tr></table>

结合柴油机维修特点，借鉴相关领域专家经验，各工序的初始参数赋值如表2所列，其中 S7的返工工序流为 S2,S3 和S7，即重大返工；其他工序的返工工序流为工序本身，即工序内部返工。根据表2中各工序参数建立“工序”“风险因素”“返工工序流"智能体，在主工序流界面中设定初始工序及完成工序参数，并输出工序状态图、工序剩余工期折线图、平均工期折线图及平均时间，仿真界面如图6所示。

表2柴油机维修工序初始参数表  

<table><tr><td>S25 s S22；S23;S24；</td><td></td><td></td><td>S11;S17;S19;S20</td><td></td><td></td><td>8</td><td></td><td>g15</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td>οＳεＤ８Ｌ８Ｌ９９ＳＬεεΠΠΠиΠ９９Π６８</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td>初始工期</td><td></td></tr><tr><td rowspan="4">0</td><td rowspan="4">S21</td><td rowspan="4">S21 0.01</td><td rowspan="4">S21</td><td rowspan="4"></td><td rowspan="4"></td><td rowspan="4"></td><td rowspan="4">S18</td><td rowspan="4">S18</td><td rowspan="4">S13;S14;S15;S16</td><td rowspan="4">S5;S9;S10</td><td rowspan="4"></td><td rowspan="4"></td><td rowspan="4"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>S12</td><td></td><td>S12</td><td>S12</td><td>S12</td><td>S6;S7;S8</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td>前置序号</td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td>.0</td><td></td><td></td><td></td><td>0.03</td><td>0.02</td><td></td><td>0.03</td><td>0.02</td><td>风险发生率</td><td></td></tr><tr><td>０７ＺεＩＤ９９εＩεＩεＩＩ９９８／Πεε６Ｌ</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td>9</td><td>返工工期</td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr></table>

![](images/6d20ab9a8532a95337ba164fd21f8c5bf6d1df5d0a8276326b4a4836cc7ded9a.jpg)  
图6主工序流仿真界面示意图

假设柴油机维修所需的资源和经费充足，根据柴油机维修工序的特点及表2中的工序参数的初始赋值，建立基于智能体的柴油机维修进度风险评估模型并进行100000次仿真，得到未对风险进行控制下的平均总工期为 $7 5 , 5 6 5 { \mathrm { d } }$

然后通过调整单个工序的缺陷率得出平均总工期，与未对风险进行控制下的平均总工期进行比较得到总工期变化量，进而分析出每个工序缺陷率对维修总工期的影响程度，最后得到需要优先控制的维修工序的重要度排序。假设柴油机维修单个工序的缺陷率下降相同的比例所耗费的成本是一样的，于是通过人为调整每个工序的缺陷率使其降低 $30 \%$ ，得到仿真的平均工期如表3所列。

表3缺陷控制后工期变化量统计表  

<table><tr><td>工序 代号</td><td>风险初始 发生率</td><td>控制后 缺陷率</td><td>平均 总工期/d</td><td>总工期 变化量/d</td></tr><tr><td>S1</td><td>0.02</td><td>0.014</td><td>75.492</td><td>0.073</td></tr><tr><td>S2</td><td>0.03</td><td>0.021</td><td>75.502</td><td>0.063</td></tr><tr><td>S3</td><td>0.02</td><td>0.014</td><td>75.361</td><td>0.204</td></tr><tr><td>S4</td><td>0.03</td><td>0.021</td><td>75.539</td><td>0.026</td></tr><tr><td>S5</td><td>0.03</td><td>0.021</td><td>75.554</td><td>0.011</td></tr><tr><td>S6</td><td>0.05</td><td>0.035</td><td>75.329</td><td>0.236</td></tr><tr><td>S7</td><td>0.01</td><td>0.007</td><td>75.531</td><td>0.034</td></tr><tr><td>S8</td><td>0.01</td><td>0.007</td><td>75.484</td><td>0.081</td></tr><tr><td>S9</td><td>0.02</td><td>0.014</td><td>75.565</td><td>0</td></tr><tr><td>S10</td><td>0.02</td><td>0.014</td><td>75.517</td><td>0.048</td></tr><tr><td>S11</td><td>0.01</td><td>0.007</td><td>75.545</td><td>0.020</td></tr><tr><td>S12</td><td>0.01</td><td>0.007</td><td>75. 441</td><td>0.124</td></tr><tr><td>S13</td><td>0.02</td><td>0.014</td><td>75.495</td><td>0.070</td></tr><tr><td>S14</td><td>0.01</td><td>0.007</td><td>75.558</td><td>0.007</td></tr><tr><td>S15</td><td>0.02</td><td>0.014</td><td>75.541</td><td>0.024</td></tr><tr><td>S16</td><td>0.01</td><td>0.007</td><td>75.539</td><td>0.026</td></tr><tr><td>S17</td><td>0.03</td><td>0.021</td><td>75.545</td><td>0.020</td></tr><tr><td>S18</td><td>0.02</td><td>0.014</td><td>75.463</td><td>0.102</td></tr><tr><td>S19</td><td>0.04</td><td>0.028</td><td>75.486</td><td>0.079</td></tr><tr><td>S20</td><td>0.02</td><td>0.014</td><td>75.349</td><td>0.216</td></tr><tr><td>S21</td><td>0.001</td><td>0.0007</td><td>75.477</td><td>0.088</td></tr><tr><td>S22</td><td>0.01</td><td>0.007</td><td>75. 471</td><td>0.094</td></tr><tr><td>S23</td><td>0.01</td><td>0.007</td><td>75.538</td><td>0.027</td></tr><tr><td>S24</td><td>0.01</td><td>0.007</td><td>75.471</td><td>0.094</td></tr></table>

根据表3的仿真结果可以得出S6的缺陷率下降导致的工期减少量最高，其次是S20，S3,S12,S18，文说明调整S6的缺陷率对柴油机维修的总工期影响最大，因此采取一定的控制手段优先对S6，S20，S3、S12,S18的缺陷率进行控制可以

有效缩短项目总工期。

结束语进度风险评估直接关系到复杂项目的执行效率和质量，研究通过对舰用柴油机维修工序状态转移的逻辑条件进行分析，提出了一种基于多智能体的复杂工程项目进度风险评估方法，利用该方法得到了单个工序各项参数对复杂工程项目工期的影响，完成了某型舰用柴油发动机维修项目的进度风险重要度分析，这是采用一般的网络计划图所无法得到的。并且该模型还具有一定的扩展性，能够引入基于系统动力学的混合建模方法，将资源、成本等因素考虑进去使仿真更贴切实际。另外仅仅考虑缺陷率这一指标对风险的反应较为模糊，可在"进行”状态下对缺陷发生的规律所服从的某种分布展开研究。

# 参考文献

[1]ASSAF S A,AL-HEJJI S.Causes of delay in large constructionprojects[J].International Journal of Project Management,2006,24(4):349-357.  
[2] LUU V T,KIM S Y,TUAN N V,et al. Ogunlana. Quantifyingschedule risk in construction projects using Bayesian belief net-works[J].International Journal of Project Management,2009,27(1):39-50.  
[3] OKMEN O,OZTAS A. Construction project network evaluationwith correlated schedule risk analysis model[J].Journal of Con-struction Engineering and Management,2oo8,134(1):49-63.  
[4]JALILI Y,FORDA D N.Quantifying the impacts of rework,schedule pressure，and ripple effect loops on project scheduleperformance[J].SystermDynamics Review,2016,32(1):82-96.  
[5] 陈悦华，杨旭，杜厚磊.基于关键链的PERT项目群进度风险控制方法改进[J].施工技术，2015，44(6)：71-74.  
[6] 徐霄枭.建设项目施工阶段工期风险分析[D].深圳：深圳大学，2016.  
[7] 李存斌，陆龚曙，李鹏，等.建设项目进度风险管理的系统动力学模型[J].华东电力，2012，40(2)：178-182.  
[8] 柴国荣，许阳.进度视角下地铁项目施工安全风险的系统动力学分析[J].科技管理研究，2016，36(21)：85-90.  
[9] 李杨，徐峰，谢光强，等.多智能体技术发展及其应用综述[J].计算机工程与应用，2018，54(9)：13-21.  
[10］刘志强.ZB船装备系统中修项目进度控制研究[D].广州：华南理工大学,2011.