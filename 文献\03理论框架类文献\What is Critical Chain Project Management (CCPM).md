# What is Critical Chain Project Management (CCPM)?

<PERSON><PERSON><PERSON>, PMP

August 14, 2024

**Critical chain project management (CCPM)** is a framework for managing project schedules by identifying the longest sequence of dependent tasks and accounting for resource constraints. It reduces project completion time by buffering uncertainties and optimizing resource utilization, thus ensuring that projects are finished on time and within budget.

Dr. <PERSON><PERSON><PERSON> developed the concept of CCPM in 1997, which is related to his other concept, the theory of constraints. This theory of constraints helps you find bottlenecks in your process so you can overcome them and complete the project with minimal obstruction.

Before discussing the CCPM further, let’s understand the critical chain.

The critical chain is “the longest path in the [network diagram](https://pmstudycircle.com/project-network-diagram/) considering activity interdependence and resource constraints.”

![critical chain network diagram](https://pmstudycircle.com/wp-content/uploads/2014/02/critical-chain-network-diagram.jpg)

**Note:** Path “Start->C->D->E->F->End” is the critical chain.

Now, we come to the critical chain method.

The critical chain project management method is an updated form of the [critical path method](https://pmstudycircle.com/critical-path-method-cpm-in-project-management/). In this method, resource availability is considered when developing the project schedule.

The critical chain method uses a buffer instead of a float. These buffers eliminate the concept of float or slack.

The critical path is a particular case of the critical chain in which the project has unlimited resources.

## Key Elements of the Critical Chain

A critical chain has three components: critical path, feeding chain, and buffers.

### 1. The Critical Path

A critical chain network diagram can have multiple paths, and the path with the longest duration is the critical path, which is the duration of your project. Activities on a critical path are known as critical activity, and any delay in delay activity can affect the project schedule.

Critical chain project management activities can have four types of task dependencies: [finish to start](https://pmstudycircle.com/finish-to-start-relationship/), [finish to finish](https://pmstudycircle.com/finish-to-finish-relationship/), [start to start](https://pmstudycircle.com/start-to-start-relationship/), and [start to finish](https://pmstudycircle.com/start-to-finish-relationship/).

### 2. The Feeding Chain

In critical chain project management, feeding chains are non-critical paths of tasks that finally feed into the critical chain. They are less critical to the overall project timeline but still important. Feeding buffers are added between the end of the feeding chain and the start of the critical chain task to prevent delays in feeding chains from impacting the critical chain.

### 3. The Buffers

Buffers provide safeguards to project activities and critical paths. You add these buffers to your project activities, resources, and paths to compensate for possible delays in activities.

The buffers can be of three types:

#### Project Buffers

Project Buffers are placed at the end of the critical chain to absorb any delays from tasks, ensuring the project meets its overall deadline. They act as a cushion for the entire [project timeline](https://pmstudycircle.com/project-timeline/), protecting it from variability and unforeseen issues.

The gain will be added to this buffer if any activity finishes early.

Usually, the duration is 50% of the contingency that you have removed from each task. This can help you shift uncertainties from the tasks to the project buffer, improve efficiency, and reduce the schedule duration.

Although the critical chain starts at the beginning, it ends before the start of the project buffer, not at the project closure. This duration can include any time borrowed from the project buffer or exclude any duration added to the buffer.

#### Feeding Buffers

Feeding Buffers are positioned where non-critical tasks or feeding chains intersect with the critical chain. They prevent delays in these tasks from impacting the critical chain by providing a buffer to absorb any variability.

Feeding buffers are inserted between the last task on non-critical and critical chains.

Feeding buffers and the project buffer are calculated the same way. The duration of these buffers is based on some fraction of the safety removed from the tasks on non-critical chains.

#### Resource Buffers

Resource Buffers ensure that critical resources are available as needed, preventing delays due to resource constraints. They are scheduled to prompt resource availability at crucial points in the critical chain, avoiding interruptions in project progress.

Since the critical chain considers resource constraints, its duration will be longer than the critical path. However, you can compensate for this by removing contingencies from the activities.

The resources used in the critical chain are known as critical resources.

## Step-by-Step Guide to Using Critical Chain Project Management

You can follow the following steps to create a critical chain project management framework:

### 1. Identify Project Activities

Start by listing all the tasks required to complete the project. Then, break the project into smaller, manageable components ([work packages](https://pmstudycircle.com/work-package/)) and activities. This detailed task list should cover all project work. You can use [work breakdown structure (WBS)](https://pmstudycircle.com/work-breakdown-structure-wbs/) to systematically organize the project into its components.

### 2. Determine Dependencies and Find Sequences

After identifying activities, determine how they relate to each other. Identify which tasks must be completed before others can start (i.e., predecessors) and which tasks can only begin after others are completed (i.e., successors). This step involves mapping out the sequence of activities and understanding the logical flow, which is crucial for developing the project network diagram.

### 3. Estimate Task Duration

Estimate the time required to complete each activity. This can be done using historical data, expert judgment, or estimation techniques like [PERT (Program Evaluation and Review Technique)](https://pmstudycircle.com/pert-program-evaluation-and-review-technique/). Ensure that these estimates are realistic and account for uncertainties in performance, which helps create a more accurate schedule.

### 4. Create a CPM Network Diagram and Find the Critical Path

Develop a critical path method (CPM) network diagram to visually represent the project activities, as well as their dependencies and sequences. This diagram can help you identify the longest, critical path, which dictates the minimum project duration. Activities on this path have zero floats, which means that delays in these tasks will directly impact the project completion date.

### 5. Determine Task Durations with Buffers

Incorporate buffers into the project schedule to account for uncertainties and possible delays. Buffers are extra time added to task durations to absorb variability and ensure that delays in non-critical tasks do not affect the project timeline. This approach helps manage risks and maintain a more flexible schedule.

### 6. Adjust for Resource Constraints

Assess the availability of resources, such as personnel, equipment, and materials, and adjust the project schedule using resource leveling and smoothly accordingly. If resource constraints cause delays, you may need to reallocate resources, adjust task sequences, or extend timelines. This step ensures the project plan is realistic and achievable according to the available resources.

### 7. Develop the Critical Chain

Modify the CPM network diagram by incorporating resource constraints and adding buffers to the critical chain. The critical chain is the longest path, considering task dependencies and resource limitations. CCPM aims to optimize project duration and enhance efficiency by focusing on the critical chain and managing buffers effectively.

## Example of Critical Chain Project Management

Suppose you get a project to construct a building. You develop a schedule based on the critical path method and start the work.

However, during the execution of this project, you find out that:

* There is a cement shortage;
* Project equipment has been assigned to some other projects and/or
* Management has pulled a team member from the project for some other urgent work.

What will happen now?

Of course, this will cause a delay in your project.

So, where was the problem?

Did the critical path not identify the resources required by your project?

No, the critical path identified the resources for your activities.

So, where was the problem? What went wrong?

The problem was with resource allocation. Although the critical path identified the resources, it did not account for their limited availability. The project schedule was developed with the assumption that all resources would be available whenever they were needed.

Unfortunately, this could not happen, and the schedule was delayed.

Therefore, to resolve these issues, you apply resource constraints to your critical path, and the network becomes a critical chain network diagram, which is more realistic.

Now, you can complete your project with more confidence.

## Critical Chain Vs Critical Path Method

Critical chain management is the updated form of critical path management. CPM identifies the longest sequence of dependent tasks to determine the shortest project duration, emphasizing task scheduling and duration. It calculates the critical path to highlight which tasks cannot be delayed without impacting the project timeline.

The critical chain incorporates resource constraints and manages project buffers. While CPM focuses solely on task durations, CCPM adjusts for potential delays and variability by adding buffers to account for uncertainties and resource constraints. CCPM ensures that the project remains on track despite interruptions, emphasizing the efficient use of resources and the reduction of multitasking impacts.

CPM is ideal for simple scheduling, while CCM is suited for complex projects with resource limitations and variability.

## Summary

A critical chain method is a practical approach to developing the project schedule. It considers resource availability while drawing the network diagram.

Critical chain project management is one of the most important recent developments in project management. This method addresses many shortcomings of the critical path method, provides a realistic schedule, encourages team members to perform efficiently, and improves productivity.
