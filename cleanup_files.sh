#!/bin/bash

echo "开始清理指定后缀的文件..."
echo

# 定义要删除的文件模式数组
file_patterns=(
    "*_content_list.json"
    "*_layout.pdf"
    "*_middle.json"
    "*_model.json"
    "*_origin.pdf"
    "*_span.pdf"
)

# 计数器
total_deleted=0

# 遍历每个文件模式
for pattern in "${file_patterns[@]}"; do
    echo "正在查找模式: $pattern"
    
    # 使用find命令递归查找匹配的文件
    files_found=$(find . -name "$pattern" -type f)
    
    if [ -n "$files_found" ]; then
        echo "找到以下文件:"
        echo "$files_found"
        echo
        
        # 删除找到的文件
        while IFS= read -r file; do
            if [ -f "$file" ]; then
                echo "删除: $file"
                rm "$file"
                ((total_deleted++))
            fi
        done <<< "$files_found"
    else
        echo "未找到匹配的文件"
    fi
    echo "---"
done

echo
echo "清理完成！共删除了 $total_deleted 个文件。"
