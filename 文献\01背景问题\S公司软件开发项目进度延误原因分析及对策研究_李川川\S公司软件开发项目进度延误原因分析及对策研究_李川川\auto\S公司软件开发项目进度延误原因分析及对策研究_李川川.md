# 华东理工大学专业学位硕士论文

论文题目：S公司软件开发项目进度延误原因分析及对策研究专业学位类别： 工商管理（MBA）研究方向： 项目管理论文作者： 李川川指导教师： 杨洪涛 副教授

# 华东理工大学硕士专业学位论文

李川川

指导教师姓名： 杨洪涛副教授华东理工大学商学院

申请学位级别：硕士 专业名称：工商管理（MBA）论文定稿日期： 2018-4-4 论文答辩日期： 2018-5-20

学位授予单位： 华东理工大学学位授予日期： 2018年6月

答辩委员会主席：李英 教授  
评阅人：吴一帆副教授梁选翠研究员级高级工程师

# 作者声明

我郑重声明：本人恪守学术道德，崇尚严谨学风。所呈交的学位论文，是本人在导师的指导下，独立进行研究工作所取得的结果。除文中明确注明和引用的内容外，本论文不包含任何他人已经发表或撰写过的内容。论文为本人亲自撰写，并对所写内容负责。

论文作者签名：李20/8年 $\boldsymbol { \varsigma }$ 月25日

# S公司软件开发项目进度延误原因分析及对策研究

# 摘要

随着移动互联网的普及，移动支付行业正在快速发展。智能手机快速普及并且功能也越来越强大，不仅方便了人们随时随地的上网冲浪，更为人们提供了更加多样化的使用场景。其中基于NFC技术发展起来的手机公交卡应用场景逐渐被人们熟悉和使用。作者所在的S公司就是为了实现NFC移动支付场景而建立的创业公司。

S 公司作为创业公司，在项目管理上有着很多的不足。本文选择对S公司的软件开发项目的进度延误原因进行研究，通过访谈的形式从员工中搜集反馈并整理导致进度延误的原因，然后针对这些原因，通过问卷调查的方式筛选出主要原因：需求变动频繁、沟通不畅、项目人员数量及能力不够和项目进度管理薄弱。本文通过对这四个方面的原因进行分析，从各个角度找出更深层次的根本因素，并分别从需求变更控制及流程、沟通规划控制、项目人员的补充和能力提高以及加强项目进度管理等方面提出对策。最后通过实际案例验证本文提出的对策的有效性，为S公司项目进度延误状况的改善起到了很好的作用。

通过对S公司的实际问题进行分析，提出有针对性的解决方案，不仅可以很好的提高S 公司的进度管理能力，减少项目的延误率，同时对其他互联网软件行业的创业公司也具有一定的参考意义。

关键词：进度管理；进度延误；进度控制；对策研究

# The analysis and countermeasure research on the delay of software development project in S Company

# Abstract

With the popularity of mobile Internet, the mobile payment industry is rapidly developing. The rapid popularization of smart phones and more powerful functions, not only convenient for people to surf the Internet anytime, anywhere, but also provide a more diverse use of scene. Among them, the application of mobile phone transit card developed based on NFC technology is gradually familiar and used. The author's company is a startup that was created to bring NFC mobile payments to the scene.

As a start-up company, there are many deficiencies in project management. The author of this article chooses to study the reason of the delay of software development project in S company, collcts the feedback from the staff by interview and lists the reasons causes the delay of project schedule, then filter the main reasons from questionnaire for these reasons: frequent change of demand, poor communication, insufficient number and capacity of project personnel and poor project schedule management. Through the analysis of the root causes of these four aspects, the paper finds out the deeper fundamental factors from all angles, and separately from the demand change control and process,communication planning control， project staff recruitment and capacity enhancement, as well as to strengthen the project schedule management gives countermeasures. Finally， with actual case to verify the effectiveness of the proposed countermeasures for projects of S company, improve the status of project schedule delay.

By analyzing the actual problems of S company and putting forward the targeted solutions, it can not only improve S company's progress management, but also reduce the project's delay rate, meanwhile, it also has certain reference meaning for other startup companies in Internet software industries.

Key words: Project Schedule Management; Schedule Delay; Schedule Control; Countermeasure Research

# 目录

# 第1章绪论..

# 第2章理论基础.. 9

# 第3章S公司软件项目进度管理现状与绩效. 15

# 第4章S公司软件项目进度延误的原因分析 22

# 4.1.2通过问卷调查分析进度延误的主要原因. 24

4.2需求变动频繁 .25  
4.2.1多种原因导致需求变更. .25  
4.2.2需求变更缺乏控制. ..27  
4.3沟通不畅.. .28  
4.3.1缺少沟通规划. ..28  
4.3.2项目信息发布存在问题. .29  
4.3.3缺少沟通控制.. ..30  
4.4项目人员数量及能力不够 .. 30  
4.4.1研发人员数量及能力不够.. .30  
4.4.2项目经理的能力不足. ..32  
4.5项目进度管理薄弱.. ..3  
4.5.1进度计划不合理. ..33  
4.5.2进度控制能力不足. .35  
第5章S公司软件项目进度绩效改善建议 .37  
5.1减少需求变更并实施需求变更管理. .37  
5.1.1减少需求变更数量.. ..37  
5.1.2实施需求变更控制流程. ..39  
5.2加强沟通管理. ..41  
5.2.1制定沟通管理计划. ..41  
5.2.2提高信息发布效率. .43  
5.2.3增加沟通控制机制.. .44  
5.3补充项目人员并提高人员能力 .45  
5.3.1补充研发人员并提高技术能力. ..45  
5.3.2提高项目经理的个人能力. ..46  
5.4加强项目进度管理.. .47  
5.4.1制定合理的进度计划.. .47  
5.4.2提高项目进度控制能力. .48  
第6章案例分析.. .51  
6.1 X项目介... ...51  
6.2 X项目的实施. . 51  
6.2.1建立项目团队. .51  
6.2.2规划沟通管理. .52  
6.2.3制定进度计划.. .53  
6.3X项目的进度管理效果 .54

第7章总结与展望. 55

7.1本文总.. .55  
7.2未来展望 ..56  
参考文献. ..57  
附录A关于“S公司软件开发项目进度延误原因”的访谈提纲. ..60  
附录B关于“S公司软件开发项目进度延误原因”的访谈记录. ..61  
附录CS公司项目进度延误原因调查. .70  
致谢... .72

# 第1章绪论

# 1.1选题背景

现代社会生活中，互联网应用已经渗透到了各行各业，互联网在人们的日常生活中发挥了非常重要的作用。根据中国互联网信息中心最新发布的《中国互联网络发展状况统计报告》(2017)[显示，截至 2017年6月，中国网民规模达7.51亿，半年共计新增网民1992万人。互联网普及率为 $5 4 . 3 \%$ ，较2016年底提升了1.1个百分点。中国手机网民规模达7.24亿，较2016年底增加2830万人。网民中使用手机上网人群占比由2016年底的 $9 5 . 1 \%$ 提升至 $9 6 . 3 \%$ 。互联网给人们提供了各种各样的服务，包含即时通讯、搜索、新闻等应用类的服务；网络购物、外卖、旅行等商务交易类的服务；理财，网络支付等网络金融类的服务；游戏、文学、视频、音乐等网络娱乐类的服务；在线教育、网约车、在线政务等公共服务类的服务。互联网，尤其是移动互联网，为人们的日常生活提供了巨大的便利。

“互联网 $+ ^ { \dag }$ 的概念最早是由于扬在 2012年11月易观第五届移动互联网博览会上提出的。于扬指出，“其实今天这个世界上所有的传统服务都应该被互联网改变，如果这个世界还没有被互联网改变，它是不对的，一定意味着这里面有商机，也意味着基于这种商机能产生新的格局"[2]。李克强总理在2015年3月5日的政府工作报告上提出了“制定‘互联网 $_ { + } ,$ 行动计划”的想法。“互联网 $+ ^ { \dag }$ 概念的提出，震动了整个互联网和传统行业。在新时代新技术的背景下，传统行业如何借助互联网来拓展新渠道，研发新产品，成为众多传统公司的重要任务。互联网的发展，带动了大批创业公司的建立，从事互联网行业的公司越来越多，互联网提供的服务越来越多样化，互联网行业成为时下最热门的行业。互联网产品层出不穷，升级速度加快，产品功能越来越强大，对我们软件研发行业提出了更高的要求。为了满足互联网对新产品的需求，研发团队需要不断地学习新的技术，研发新的产品，软件项目的研发任务繁重。因此，软件开发项目对项目管理提出了更多的需求。

互联网产品最显著的特点就是更新速度非常快。互联网时代，用户是企业的衣食父母。为了满足用户需求，吸引越来越多的用户，原本需要做三个月甚至半年的产品，现在一个月就需要做出原型产品，发布给小部分用户试用并搜集反馈。在搜集反馈的同时，已经开始了产品第二代的定义和开发。快速迭代开发成了互联网软件开发的一个特征，只有这样，才能留住用户，保持领先进而发展新的用户。

作为互联网行业创业大军的一员，作者所在的S公司也是一个刚刚成立三年的小公司。S公司的主要业务是将传统公交一卡通的卡片应用迁移到智能设备中，包括智能手机和穿戴设备。目标是使用户外出搭乘公共交通工具时，只需要携带智能手机或者智能手表就可以刷卡乘车。由于传统的公交卡是在工厂里面做好之后再拿到服务处销售给用户的，并且卡片充值也是在传统的自助充值机和服务台人工操作的，因为有人工参与，其技术要求比较低。而集成于智能设备中的公交卡，是在用户购买设备之后发行的，并且支持用户在智能设备上直接联网充值，同时还提供公交卡的信息查询，卡片迁移等各种应用场景，技术实现难度上远超传统应用场景。因此，各地公交一卡通公司的旧系统也面临着大幅度的升级改造。为了加快城市公共交通一卡通公司应用的迁移速度，S公司不仅为设备厂商和公共交通公司建立联系，提供发卡和充值服务，而且还为技术能力不足的公共交通公司承建发卡和充值系统，为部分体量较小的智能设备厂商承建智能设备管理系统。

S公司的主营业务场景需要对传统行业进行升级改造，因此面临着很多从未遇到过的难题，有很多未知因素影响项目的进行。并且这个行业现在正处于急速发展阶段，每个城市都有待升级的系统，S公司承接的项目也越来越多，项目周期也越来越短，经常出现多个项目并行的情况。虽然S公司也在积极寻求更多的风险资本的加入，但是在现阶段，如何保证在不显著增加成本投入并且保障产品质量的前提下，使项目能够按期结束，从而不影响合作伙伴的产品上线以及新项目的启动，对S公司的业务拓展和未来发展至关重要。

# 1.2研究的目的和意义

# 1.2.1研究目的

S公司是一个刚刚成立不久的小型创业公司，很多项目在执行的时候随意性很大，没有标准的流程和规则。在创业初期，项目比较少的时候，尚能按需求完成项目。随着项目越来越多，公司管理层愈发发现出现延误的项目也越来越多。尽管公司已经有专职的项目经理加入，项目的管理相较之前已经有所改善，但是仍然无法将项目的延误概率控制在可接受的范围内。并且S公司所提供的服务具有很强的时效性，即提供给客户的产品和服务必须在固定的时间点之前准备完成，否则该产品和服务就有可能失去价值。所以项目的保质按时完成，对公司的发展至关重要。

本文的研究目的就是找出S公司项目进度延误的原因，并提出针对性的改善对策，帮助改善目前的项目管理现状，提高产品的准时交付率。

# 1.2.2研究意义

国家层面提出“互联网 $+ ^ { \dag }$ 的概念，喊出“大众创业、万众创新”口号，鼓励支持小型创业公司的发展。S公司在符合时代潮流的背景下创立，推动了移动支付行业的发展，设计和实现了方便人民生活的创新产品，并且得到了市场的验证。但是，公司在成长过程中不可避免的会遇到各种问题，其他创业公司也是一样。

本文的研究意义就是从互联网软件行业的创业公司出发，分析其在项目管理中存在的各种可能导致项目进度延误的原因，给出针对性的改善对策。本文的研究不仅能解决S 公司实际存在的问题，对行业内其他互联网软件创业公司也具有非常重要的参考意义。

# 1.3国内外研究现状

# 1.3.1进度管理理论与方法

1917年亨利·L·甘特发明了甘特图，作为进度管理的创新思想，成为当时管理思想的一次革命性发明。20世纪50年代后期，关键路径法（CPM）和计划评审技术（PERT）各自独立发展起来，运用网络图来表达项目中的各个活动的进度及其相互之间的关系。并且以此为基础，进行网络分析，确定关键活动和关键路径，计算最短工期。在网络分析技术方向上，20 世纪60 年代又发明了图形评审技术（GERT），引入了不确定的活动工期和概率性存在的工作分支。

当今的科技工作者根据实际项目实施过程中存在的问题，对这些已经比较成熟的进度管理方法做出了更多改进。CPM方法最困难的是准确获取关键路径。Shih-PinChen等人针对项目活动时间为模糊数的项目网络，开发了一个分析关键路径的方法[3]。该方法基于线性规划公式和模糊数排序方法，将模糊关键路径问题表示为具有目标函数模糊系统的线性规划模型，然后在线性和可加性的基础上使用排序法将线性规划模型转换为可以使用传统的解决方法来解决的问题，从而将关键路径和总持续时间导出。不仅如此，Shih-PinChen等人还提出了最关键路径和相对关键程度概念，符合实际并且更加易于使用[3]。FrancescoA.Zammori等人为了确定关键路径，不仅仅考虑任务的预期持续时间，而且还考虑了持续时间变化性，成本，共享资源，主要设计修订风险以及外部风险等关键参数，基于模糊逻辑和多准则决策等技术方法求得关键路径，使之更加接近项目的实际情况[4。在项目执行过程中，如果项目对整体时间有要求，或者因为一些风险问题导致项目可能延期，则需要对项目网络中的关键路径进行优化。JianxunQi等人利用浮点定理，分析了自由浮动和安全浮动特性，设计了一种找出次关键路径的算法。使用该算法可以实现局部优化的效果，从而实现整体的优化，并大大减少了计算量。这是一个全新的研究方向，不仅解决了次关键路径的问题，同时也可以为网络规划的研究提供新的理论和思路，具有重要的意义[5]。崔晓明等人以时标双代号网络计划图为基础，结合关键链法和实际进度前锋线法设计了一个进度控制系统，该系统融合了多种技术，利用各种技术的优势，从而能够更好的从各个层面对进度进行监控和控制，为项目管理人员提供了可靠的进度控制方案[]。汪若洋等人使用微粒群优化算法对传统的 PERT计划评审技术进行优化，求得所有项目时间方案中的最优解。同时在项目执行过程中，如果遇到偏差较大的工序，可用此方法再次计算获得余下所有工序的时间最优方案，从而重新矫正标准。该方法使得管理者可以对项目的全过程进行更为有效的控制，并且在出现偏差的时候可以及时作出有效的调整，并且最大限度的进行资源整合和调度[7]。PERT公式有个基本假设，即活动时间具有轻微的尾部分布，并且在这个范围内有一个恒定的方差。然而在实际的项目中，重尾现象普遍存在，而且不同活动的内在不确定性水平也不相同，因此需要更灵活的分布方式，以允许不同数量的分散和更严重的尾部事件的存在。同时，活动时间分布的尾部衰减也是以前没有充分考虑的一个关键因素。因此，Eugene

DavidHahn针对这些问题提供了一个新的分布方法，可以允许不同数量的分散和更多的极端的尾部事件的存在，将该分布方法集成到传统PERT 框架中能够更好的完善 PERT方法[8]。对于PERT 网络中存在的虚拟弧的问题，Nasser Eddine Mouhoub 等人提出了一种新的算法，可以构建具有尽可能小的虚拟弧数量的PERT网络，从而使项目的完成时间的计算更加简洁[9]。对于并行产品开发过程，Richard Graham Nelson等人通过概念化新产品开发过程，然后推导出在过程中对信息和通信复杂性进行建模的方法。并将以前的并行工程的研究结果扩展到这个模型中，提出了一个使用GERT方法分析并行新产品开发过程的时间计算模型[10]。

1997年，以色列物理学家Goldratt 博士提出了约束理论并将其应用于项目管理领域，提出了关键链和缓冲区的概念。关键链的发明为项目管理知识体系持续改进提供了重要的一步。周正龙等人结合MIS开发项目的特点，改进了关键链识别模型，有效的解决了MIS开发项目进度管理中的问题，并使用改进后的启发式算法来求解关键链识别模型[]。通过在项目中设置接驳缓冲和项目缓冲时间，能够有效的避免因为关键链上的任务不能按期完成而导致整个项目延期。缓冲区大小应该设置为多少，是关键链法中最重要的课题。只有最合理的缓冲区大小，才可以缩短整个项目的工期。很多学者在关键链缓冲区方面进行了研究。JunguangZhang等人提出了一种基于综合资源紧张度的缓冲区大小确定方法[12]。综合资源包含物力资源和信息资源等。通过根据边际收益递减规律确定资源可用性的临界值来确定物力资源密集度。通过设计结构矩阵分析活动间信息流计算信息交互产生的返工时间和信息资源紧张度。然后通过计算这两种资源的紧张度来确定和调整项目的缓冲区大小。这种方法能够合理的确定缓冲区大小，可以起到双倍优化项目的持续时间和成本。设置了缓冲区，还需要对缓冲区进行监控，才能够更好的控制项目的进度。别黎等人对关键链方法中的缓冲监控方法进行了改进，提出了动态缓冲监控方法。该方法要求根据项目实际的执行情况设置两个触发点，用来监控项目的执行情况。这种方法能够更好地反应项目执行信息，指导项目管理者及时采取更合适的项目控制策略，从而保证项目的按期完工[13]。Xuejun Hu等人提出了一种新的项目监控框架，引入了活动关键指标作为触发器，整合到缓冲监控过程中。并且为了更精确的控制系统，还提供了依赖于项目进展的动态行动阈值设置以及缓冲区渗透[14]。计算实验表明，相比于主要的缓冲区管理方法，集成的进度监视方法相对占优势，以更少的努力和更高的跟踪效率产生更好的控制行为。Annelies Martens等人通过在不同的项目阶段分配项目缓冲区来设置挣值管理/获得时间表（EVM/ES）的方式来表达容差限制。这个数值可以提供给项目经理准确可靠的项目执行预期结果信息，从而帮助更为准确的监控缓冲区[15]。

关键链法本身就是基于约束理论发展起来的。在项目中，对项目进度产生约束的是各类资源。因此，资源约束的项目调度问题管理更加贴近于项目进度管理的本质。从各种类型的资源约束出发，研究学者提出了各种算法来优化项目调度。ShaTao等人提出一种扩展的模拟退火算法来有效的解决大规模资源约束问题，该算法包含新的活动选择列表解决方案、进度生成方案以及邻居生成算法[16]。ErcanOztemel等人研究利用蜜蜂算法进行单资源，多模式，资源约束的模具项目调度的可能性，提出了一种基于蜜蜂的模具调度模型，并将其用于一系列具有不同维度的问题以用于概念验证[17]。Ali A.Yassine等人提出了两种新的遗传算法（GA）来调度项目活动，在多项目、资源受限和迭代的环境中，能够有效减少项目的整体工期[18]。RiponK.Chakrabortty 等人提出了一种基于鲁棒优化理念的方法，该方法考虑了具有确定性可再生资源限制但活动持续时间不确定的问题，在各种不确定的活动工期分布中，都能够产生相当好的解决方案[19]。SaberElsayed等人提出了一个解决资源受限的项目调度问题的通用框架，在搜索过程中自适应地使用各自具有多个搜索算子的元启发式算法，并且更多地强调执行更好的算法，引入了局部搜索方法[20]。Bernardo F.Almeida 等人对多技能资源约束项目的调度问题（MSRCPSP）进行了研究，开发了资源权重和活动分组两个概念，并将其应用于他们期初的启发式框架中[21]。新的启发式算法能够在非常短的时间内找出高质量的解决方案。K.Nino等人对多目标资源约束项目的调度问题（MORCPSP）提出了一个专用的局部搜索方法，可以最大限度的减少项目的最大完成时间和总加权开始时间[22]。Qi Hao等人提出了一种基于部分任务网络启发式的动态项目调度算法，用来解决复杂约束，动态环境和多项目进度协调问题，并且已经成功应用于基于网络的飞机维修管理系统[23]。AmolSingh提出了一种将资源约束下的项目优先级（或关键性）与项目进度计划相结合的多项目调度问题的混合算法，能够在项目优先时尽可能减少项目的生产周期以及惩罚成本[24]。

# 1.3.2软件项目的进度管理

软件项目中，人的因素对项目的成功有着至关重要的作用。软件开发人员的工作效率受到技术熟练度、任务分配的满意度、学习动机以及学习能力等因素的影响。而且这些因素还会随着项目的进行而动态变化。为了解决这种动态软件项目调度问题，ShenXiao-Ning等人结合实际项目中项目周期、成本、产品的稳健性和稳定性等多个目标，提出了基于Q学习的多目标双归档模块化算法（MOTAMAQ），以主动重调度的方式解决问题[25]。该算法可以根据不同的调度环境选择合适的搜索算子，使软件项目经理能够更深入地了解各目标之间的各种妥协，以便做出更加明智的决策。软件产品的开发不可避免的会引入各种bug，查找和修复bug占用了整个项目周期的很大一部分时间。因此，能够估算bug的修复时间可以给项目管理带来很大的数据支撑。Shirin Akbarinasaji等人利用BugzillaFirefox的开源数据，应用马尔可夫模型来预测每月可修复的错误数量，并使用蒙特卡洛模拟来预测给定数量的错误的总固定时间[26]。通过这个模型可以估算出bug的修复时间，并且与参考数据的结果较为一致。软件项目的质量取决于软件范围定义的完整性。IsmaulHassn等人提出了一种方法，通过与行业专家进行的电子调查，对软件项目中的各种要素进行实证评估，并对其进行排序和分配权重，建立一个记分卡。这种方法可以有效地计算项目范围定义的完整性[27]。软件项目的开发过程中，无论是因为客户的原因，还是因为产品的重新定义，经常会发生需求变更。如果对需求变更的管理没有做好，就会成为项目失败的一个最重要的原因。Shalinka Jayatilleke等人针对需求变更管理中的四个关键问题进行了研究并给出了答案，结合最新的需求变更管理的技术和实践，给软件从业人员提供了很好的参考资料，有效地协助项目经理做出更好的计划，增加项目的成功机会[28]。在软件项目开发模式方面，传统的有瀑布模型、迭代模型、螺旋模型等。随着软件项目的复杂度和多样性的增加，敏捷开发和看板管理被软件行业普遍使用。HowardLei等人就通过数据统计对敏捷开发和看板管理方法在软件开发项目中对项目管理方面的有效性进行了验证[29]。结果表明，看板管理方法在进度管理方面比敏捷方法更为有效。Xue-meiXie等人将关键链方法应用于软件开发项目，改进了缓冲区大小的设置方法，提出了改进的平方根误差（IRSE），基于启发式算法和优先级规则的调度方法来规划调度[30]。IRSE缓冲区设置方法可以提高缓冲区利用率，同时仍然保持高稳定性，缩短项目工期的积极作用。JingXiao等人针对软件项目调度问题（SPSP），设计了一个更为有效的算法，提出了一种称为ACS-SPSP 算法的蚁群优化（ACO)方法。该算法比其他遗传算法具有更高的计算效率和更高的命中率[31]。

从国内外最新的项目进度管理研究的情况来看，人们研究的方向都是为了更加准确地制定项目的进度计划，并且考虑各种因素，以保证项目进度不发生延误。这其中有用到各种算法和模型来提高关键路径和关键链的识别准确度，有设计规则或者模式来达到更好的进度监控效果等。这些研究成果都将提高和改善项目的进度管理水平。同时，理论的研究需要应用到实际的案例中。对于软件行业的进度管理，需要更多地结合行业及软件特性来研究适合行业项目的进度管理方案。

# 1.4研究的方法和内容

# 1.4.1研究方法

# （1）文献研究

通过查阅文献，包括从图书馆借阅项目管理书籍，从整体学习项目管理，到查阅国内外的专业期刊、论文等，了解项目进度管理最新的发展方向，解决的问题以及其解决方法，为分析S公司的软件项目进度延误问题的原因以及应对方法准备理论基础。

# （2）实地调查

通过对S公司项目参与人员进行访谈，了解他们曾经参与过的项目、这些项目是否发生过进度延误以及延误的原因，统计出导致进度延误的原因列表。再结合问卷调查，搜集全公司研发人员的反馈，统计导致项目进度延误的主要原因。

# （3）定性分析

根据S公司所实施的项目的特征，以及从项目参与人员那里统计到的各种问题，结合理论方法，对项目进度控制过程中存在的问题进行定性的分析，并提出自己的解决方案，以期改善S公司的项目进度管理现状。

# 1.4.2研究内容

论文结构如图1.1所示，全文共分七章：

![](images/5ef8ec73bb50bc4cc109275eac1fa804697bbffa23dfee23b5d15833c819b9c5.jpg)  
图1.1论文结构  
Fig.1.1 Structure of the dissertation

（1）绪论。阐述本文的选题背景、目的与意义，对互联网软件行业当前的情况进行介绍。然后梳理了当前项目进度管理的研究现状。最后介绍本文的研究方法、内容和整体框架。

（2）理论基础。首先对项目进度管理的理论进行简单介绍，然后描述了项目进度计划常用方法以及项目进度控制方法。最后介绍了软件项目的特点及其进度管理方法。

（3）S公司软件项目进度管理现状与绩效。对S公司的项目进度管理流程做了说明，并介绍了S公司的进度计划编制方法和进度控制方法。根据S公司的历史项目信息进行了绩效分析。

（4）S公司软件项目进度延误的原因分析。通过访谈了解经常出现的进度延误原因，然后通过问卷调查这些原因中最主要的因素，然后从需求变更、项目沟通、项目团队以及项目进度管理等方面分析了进度延误的原因。

（5）S公司软件项目进度绩效改善建议。根据第四章分析的结果，分别从四个方面提出了S公司改善绩效的方法和建议。

（6）案例分析。根据第五章给出的建议，结合实际项目，验证了这些建议对于改善S公司的进度延误状况起到了积极的作用。

（7）总结与展望。对全文内容进行总结，对下一步的研究方向进行展望。

# 第2章理论基础

# 2.1项目进度管理理论

# 2.1.1项目进度管理的概念

美国的项目管理协会（PMI）编制和出版的《项目管理知识体系（第五版）》中，将项目管理的过程分为启动、规划、执行、监控、收尾五个阶段[32]。进度管理与质量管理、成本管理又被称为项目管理的三要素。每个项目都要受到这三个要素的约束。在满足项目范围的定义下，平衡这三个要素之间的关系，就是项目管理最重要的内容。其中进度管理又叫时间管理，被分配在项目的五个阶段中的规划和监控两个阶段。进度管理在规划阶段需要完成：规划进度管理、定义活动、排列活动顺序、估算活动资源、估算活动持续时间、制定进度计划。进度管理在监控阶段需要完成控制进度的内容。

所谓项目进度管理，就是指在项目的实施过程中对项目各个阶段的进展情况与项目最终完成时间进行管理的工作。项目进度管理的实施，首先是要在规定的时间内制定出科学、合理、经济的进度计划，该进度计划不仅包括一级计划，还包括多级管理下的子计划。在计划拟定后按照计划实施项目，并在计划执行的过程中不定期的检查计划实施的情况，若计划出现偏差要及时处理，找出导致偏差的原因并采取必要的措施，适时调整原定计划至项目成功完成[33]。

# 2.1.2项目进度管理的过程

项目进度管理包括以下7个主要的过程[34]：

# （1）规划进度管理

为规划、编制、管理、执行和控制项目进度而制定政策、程序和文档的过程。一般来说，规划进度管理就是为进度管理过程设定可以依据的标准。不同的行业项目具有不同的特征，其进度管理所依据的标准应当符合其行业特征，具有实际可操作性和适用性。

# （2）定义活动

指识别出项目成员或者利益相关者为了完成项目而必须开展的具体的活动。活动或者任务构成了工作的基本要素。活动有一定工期以及资源和成本的要求。活动可以体现为工作分解结构（WBS）的每一项。这个阶段需要列出活动清单，分析活动的属性，列出里程碑等。

# （3）排列活动顺序

指识别活动之间存在的关系，确定其先后执行顺序。该过程需要输出项目进度网络图，变更申请以及更新活动清单和活动属性。

# （4）估算活动资源

指估算出项目团队为了完成项目活动所需要的资源，包括人力、原料和设备等。该过程需要完成的任务包括：估算活动资源需求，绘制资源分解结构，更新活动属性和资

源日历等。

（5）估算活动持续时间

指估算出每一项活动所需要的执行时间。

# （6）制定进度计划

根据活动顺序的分析结果以及估算的活动所需资源和活动所需的持续时间，制定项目进度计划。这个过程中需要完成项目进度计划表的编制，进度模型数据的整理，进度基线的设定以及更新资源需求、活动属性、项目日历和项目管理计划。

# （7）控制进度

指控制和管理项目进度的变更。这一过程需要完成的任务包括：测量绩效，申请变更，建议采取的纠偏措施，以及更新进度模型数据，进度基线，组织过程资产，活动清单，活动属性和项目管理计划。

# 2.1.3项目进度计划的方法

# （1）甘特图

甘特图（Ganttchart）又叫横道图、条状图(Barchart）。它是以图示的方式通过活动列表和时间刻度形象地表示出任何特定项目的活动顺序与持续时间。它是在第一次世界大战时期发明的，以亨利·L·甘特先生的名字命名，他制定了一个完整的用条形图表示进度的标志系统[35]。由于甘特图形象简单，在简单、短期的项目中，甘特图都得到了最广泛的运用。

甘特图可以结合WBS任务列表，根据对任务项的时间和资源评估，将其体现在甘特图中。管理者可以通过甘特图直观的看出哪些任务正在进行中，哪些任务已经延期，以及项目之间的关联关系。

# （2）关键路径法

关键路径法（CPM）是网络计划技术的一种表现形式。通过将一个项目中从开始到结束的所有活动绘制成一个网络图，从网络图中确定一条占用时间最长的活动路径，这条路径就是关键路径。关键路径的长度决定了项目的周期。通过对关键路径的优化，可以达到缩短项目周期的目的。关键路径法体现了项目活动之间的先后顺序关系，但是无法体现活动对资源的需求和成本的需求。

# （3）计划评审技术

计划评审技术（PERT）与关键路径法基本相同，也是基于网络图的分析技术，但计划评审技术针对的是不确定的项目活动时间，对活动时间的估计采用了三点估算法，即最短时间、最可能时间和最长时间。并且可以对活动时间计算方差。方差越大，即最短时间和最长时间之间的差值越大，表明该项活动所需要的时间估算越不准确，应当采取措施应对可能出现的问题。

# （4）图表评审技术

图表评审技术（GERT）也称为随机网络技术或决策网络技术，它可以对网络逻辑关

系和历时估算进行概率处理。它允许在网络逻辑和工作持续时间方面具有一定的概率陈述，亦即除了工作持续时间的不确定性之外，还允许工作存在概率分支。

# （5）关键链法

关键链法基于TOC理论，不仅考虑了项目活动的依赖关系，还考虑了资源、成本、技术等约束因素对项目活动的影响。根据活动间紧前逻辑关系和资源约束关系确定项目最长周期的活动序列，即关键链。并通过设定缓冲时间的方法，吸收项目进度管理中的不确定性，从而保证整个项目的按时完成。

# （6）里程碑法

里程碑是完成阶段性工作的标志，不同类型的项目里程碑不同。但一般来说在软件项目的生命周期中，重要的里程碑节点是相同的，项目立项、项目启动、需求分析、系统设计、软件编码、软件测试、系统试运行、项目验收这些阶段完成时均可作为里程碑。里程碑的有效管理和控制是保证软件项目成功的关键活动之一，需要考虑各里程碑事件的计划、内容、方案和阶段性交付件，并对应执行相关考核和验收。对里程碑的管理实质上是对项目风险的控制过程[36]。

里程碑管理包括两个部分：里程碑的设置和里程碑的控制[37]。

$\textcircled{1}$ 里程碑的设置

在项目开始之前，要制定出项目的里程碑。在每个里程碑开始之前，都要明确下一个里程碑是什么，完成时间是什么。合理的里程碑设置是管理好里程碑的前提。

$\textcircled{2}$ 里程碑的控制

每一个里程碑完成之后，都应该对这个里程碑里所完成的工作进行总结。总结该里程碑的实现所消耗的时间和资源等与计划的时间和资源的差距，根据总结情况，合理地调整后续的进度计划。如果某个里程碑耗时较长，还应该在执行过程中定期检查评估工作进度，根据需要调整，确保开发工作能够按照计划进行。

# 2.1.4项目进度控制的方法

项目进度控制的目的就是确保项目按照预定工期完成。项目经理在执行项目进度控制的过程中，需要及时地搜集整理项目的实际进度状况，并与计划进度对比，确认是否发生偏差。一旦发生进度偏差，则需要及时分析发生偏差的原因，并调整进度计划或采取合理的纠正措施，以便使后续工序能够按照新的计划执行，同时要求尽量使新的进度计划向原进度计划靠拢，从而不影响整体项目的完工时间。

常用项目进度控制方法有如下几种[38]:

（1）项目进度计划变更控制系统方法

进度变更控制系统定义了改变项目进度计划所需要遵循的程序。它包括了项目进度变更的申请程序、项目进度变更的审核和批准程序、项目进度变更的实施程序和责任分配、项目进度变更的控制程序和方法等一系列的控制程序和方法。

# （2）项目进度计划实施情况的度量方法

这是一种测定项目实际进度和实施情况，确定项目的实际进度完成情况与进度计划的要求之间的差距的管理控制方法。这种方法要求定期或不定期地对项目的实施情况进行报告，测定和评估项目实际实施情况，评定项目进度完成情况与进度计划要求之间的差距。分析项目实施过程中存在的进度问题及原因，确定需要采取的纠正措施。

# （3）追加计划法

这种方法根据出现的项目计划变动情况，使用追加计划来对原进度计划进行修订和补充。这种方法一般需要四个步骤来完成：分析项目的实施进度并找出存在的进度问题、确定合理的纠偏措施、制定追加计划和实施新的进度计划。

# （4）项目进度管理软件

通过项目进度管理软件，输入相应的项目进度数据，实时高效地分析项目实际进度与计划进度之间存在的差距，预测和分析可能被影响的工序，提供多种进度调整优化方案供项目经理选择。软件管理能够大大提高进度控制的效率。

# （5）偏差分析法

偏差分析法包括绝对偏差分析、相对偏差分析以及偏差成因分析。通过偏差分析，发现实际项目进度与计划进度之间的差距大小，从而能够帮助项目经理选择合适的纠正措施来缩小差距甚至消除差距，确保项目的按期完成。

上述方法在实际进度控制过程中经常相互结合使用，以便发挥各种方法的优势，协助项目经理更好地分析进度偏差，采取合理的纠偏措施，降低延期风险。

# 2.2软件项目的进度管理

# 2.2.1软件项目的特点

软件项目和传统项目有很大的区别，不同点有如下几点[39]:

（1）软件项目的产出内在关系复杂

软件项目的产出是一系列的软件产品。软件产品是无形的，不可见的，只能通过软件在特定设备中运行，才可以展示出软件的功能。而软件产品能够展示出来的，只是所有软件实现中的一部分，比如软件绘制的图形、软件控制的机械动作等。但是，为了达到这些有形的显示的效果，软件内部需要实现非常复杂的逻辑关系。软件产品根据规模大小不同，其内在所需的功能模块的数量，逻辑关系复杂度都是不一样的。软件产品的这一特性，使得需求方很难对软件产品做出非常精确的描述。因此，在软件实现过程中，很容易出现因为需求方和实现方对同一个功能的理解偏差，造成需求的变动。

（2）软件项目工期较短且紧迫

软件产品作为一种无形的产品，本质上是一堆二进制数据。所以，软件的升级和使用，不会产生实物成本。软件的这个特性，也使得软件产品天生具备更新速度快，推出周期短的特点。在软件行业内与对手竞争，对产品漏洞的快速修复和更新、对新产品的快速研发和推出，都是非常重要的能力。所以，软件项目的工期一般都比较短，很少会有超过一年的项目。而市场环境瞬息万变，软件产品的功能必须快速更新以应对市场的变化。软件项目的管理在这种情况下，具有不同于普通项目的管理难度。

（3）软件项目的产品主要靠脑力劳动生产

软件开发行业是智力密集型行业，软件的生产过程主要不是依靠人的体力劳动，更没有成型的生产流水线可以使用。软件行业经过几十年的发展，还是依靠人的脑力劳动来进行。软件开发是一种知识的生产，主要依靠人的创造性和逻辑思维，严重依赖开发人员的技能水平和经验。这样的生产过程具有不可见性和不确定性，因此难于控制其进度和质量。由此也可以看出，软件开发最主要的成本是人力成本。软件开发过程需要投入大量的、复杂的、高强度的脑力劳动。

（4）软件项目具有阶段性

软件开发项目一般都具有阶段性，即有明确的起止时间。阶段性决定了项目历时有限，必须对项目做好进度控制，在规定的时间内完成。另外，软件项目除了有明确的起止时间外，往往还把项目的整个过程划分为多个子阶段，每一个子阶段完成后都对应一个里程碑，要达到特定的要求并提交特定的产物。软件项目的执行过程一般会分为需求分析、系统设计、编码测试和运行维护等几个阶段，每个阶段结束后都要求提交特定的产物，比如需求分析阶段是需求规格说明书，系统设计阶段则是系统设计文档等。这样做的好处是可以把一个大的项目划分为多个子项目，便于管理，也有利于及早发现问题并加以改进。

（5）软件项目后期返工成本高

软件项目的质量控制有个显著的特点，那就是软件的问题，发现的越早，其解决成本越低，反之则越高。这是因为需求分析、系统设计和编码阶段都有可能引入质量问题，但是这期间已经基于错误的前提做了大量的工作，并且越是前面阶段引入的问题，其影响范围也越广，所以越到后面，解决问题所涉及的面也越广，所要投入的人力和时间也越多，因而解决成本就越高。

软件项目的这些特点，使得软件项目管理与传统项目管理有着很大的区别，也存在更多的风险因素，管理难度更大。

# 2.2.2软件项目的进度管理

软件项目有很多的开发模型可以选择，比如瀑布模型、快速原型模型、增量模型、混合模型等等。但无论是哪种开发模型，软件开发都要经过启动、需求、设计、编码、测试、验收等六个阶段。根据各阶段进度管理的特点，可以把软件项目的进度管理工作划分为四个阶段：计划阶段、需求阶段、实施阶段和收尾阶段[40]。

# （1）计划阶段的进度管理

在项目启动初期，项目负责人应根据客户的需求，明确项目的范围。然后根据工作内容，对项目所需的资源、成本以及时间给出合理的评估。制定项目进度计划，明确每一项活动的起止时间以及整个项目的开始时间和结束时间。作为整个项目计划工作的基础，项目进度计划要求必须详细、准确、合理。项目进度计划将是进行项目进度跟踪和

控制的重要依据。

为了方便对项目进度进行跟踪和控制，需要对项目中的重要节点进行设置，称之为里程碑。里程碑确定了软件开发各工作阶段的最后完成时间以及需要交付的阶段性工作成果。

对软件项目进度影响最大的就是需求变更。因此，必须从项目计划阶段开始，就对需求变更进行控制，明确需求变更的处理方法和规则。这将有助于减小需求变更对项目进度的影响。

# （2）需求阶段的进度管理

需求分析阶段的目标是获取详细、准确的客户需求，因此通过快速开发原型产品，可以发现项目实施过程中使用到的技术，不论是已经成熟的技术，甚至是可以复用的产品，还是新出现的，需要研究的技术，都可以帮助项目负责人合理评估项目活动所需要的时间，从而制定更为准确的进度计划表。同时需要加强与客户的沟通，通过有效的沟通，分析人员可以快速而准确的把握客户的需求，提高需求分析工作的进度和质量。

# （3）实施阶段的进度管理

实施阶段包括了软件开发的设计、编码和测试工作。这个阶段的工作主要由软件研发人员来完成。由于项目产品覆盖到很多技术领域，项目团队中需要有具有不同技能的技术人员组成。因此，项目负责人应当根据项目的特点，选择合适的技术人员组成项目团队。该团队应该能够解决该项目在执行过程中所遇到的所有问题，并且能够执行项目中的所有活动。合理的人员安排能够降低进度控制过程中出现延误的风险，保障进度。在项目实施过程中，应加强项目成员之间的沟通，并提高项目成员对项目整体的认识水平。这样才能够发挥项目成员的主观能动性，发挥所有人的监督和创造能力，及时发现项目中存在的问题并解决。

（4）收尾阶段的进度管理

在项目的收尾阶段，软件开发工作结束，产品验证工作开始准备。这个阶段需要做好软件产品的试运行的环境准备以及试运行的数据记录。这些数据将是产品验收是否通过的重要依据。其次，对于试运行过程中发现的问题，项目团队应认真分析原因，及时完成系统的修改和完善工作。此外，还需要完成文档资料的准备工作。根据合同要求或者客户的需求，整理软件产品的使用说明和相关的文档资料，帮助产品顺利验收。

# 第3章S公司软件项目进度管理现状与绩效

# 3.1S公司及项目简介

# 3.1.1 S公司简介

S 公司是2013年底在深圳成立的一家创业公司。目前 $8 0 \%$ 的员工都在上海办公，北京和深圳分别有一个办公室。由于是创业公司，前期的主要资源都投入在产品研发上，因此公司员工绝大部分都是研发人员。

S 公司目前主要的业务是运营一个集中式的应用发行平台，为各品牌手机及智能设备制造商提供应用发行及管理服务。所以其客户主要是手机厂商，包括小米、华为、一加、三星等。主要发行的应用是国内各个城市的公共交通一卡通应用。目前国内各城市的公交系统一直使用近场通讯技术（NFC）实现刷卡服务。公共交通作为市民最常使用的NFC场景之一，有着非常巨大的用户量。手机厂商为了提供差异化功能，吸引消费者，纷纷在其生产的手机上集成了NFC功能。随着NFC应用的普及以及NFC设备的大量上市，手机厂商之间的竞争也愈发激烈。而接入的公交城市，从北上广深开始，逐步延伸到二线城市甚至三线城市。S公司则负责将传统地方城市的公共交通一卡通应用迁移到智能手机上。这是一个覆盖了服务器、客户端、硬件设备的行业，技术需求复杂，业务难度大。

公司的组织架构如图3.1所示：

![](images/73797474ea63902e77f6b0678d9d137e9dd68cc2142c88b5e8f727f39dd0dd95.jpg)  
图3.1S公司组织结构图  
Fig.3.1 The organization chart ofS company

业务部负责拓展新的城市，说服交通一卡通公司与我们的系统对接，通过我们的系统将公交应用发行到手机上。如果交通一卡通公司没有能力建设公交卡发卡平台，我们可以提供此项服务，协助交通一卡通公司建设发卡平台，同时提供服务器环境搭建说明以及运维说明。业务部也负责跟手机厂商沟通，一起计划新的公交城市应用的上线和运营。因此，一个项目可能包含了与交通一卡通公司的服务器系统对接的工作，与手机厂商的服务器系统对接的工作以及可以应用于这个城市的交通一卡通Applet的开发工作等。

研发部下面的几个小部门各有其职责。

（1）TSM团队负责与手机厂商的服务器系统对接，为其提供交通一卡通应用发行功能，并且为其提供调用发卡及管理服务的底层SDK，方便手机厂商的手机钱包APP的开发。同时TSM团队还负责管理面向不同手机硬件的应用开通脚本，支持通过请求数据中的手机型号判断并选择相对应的应用开通脚本。

（2）公交团队负责与交通一卡通公司的服务器系统对接，并为TSM系统提供统一的应用发行接口。同时公交团队还负责协助交通一卡通公司开发交通一卡通公司端的服务器系统。该系统能够支持公交卡应用的发行、充值功能以及其他扩展功能。一旦S公司与交通一卡通公司的商务协议签署完毕，公交团队即开始系统建设和对接工作。

（3）安全模块团队负责实现交通一卡通城市可用的智能卡应用（Applet）的实现。该 Applet运行于手机设备中，其功能与普通公交卡中的应用功能一致。该项任务由于涉及到机卡兼容性，往往需要在系统环境搭建完毕，并成功地将Applet下发到手机中之后，再到当地进行兼容性测试，保证用户的刷卡体验。

（4）运营团队负责S公司内部的系统建设工作。主要是为了在系统运行过程中，实时的为业务运营部门提供数据查询、统计和账务的清结算服务。运营团队的工作内容主要是由S公司的内部需求产生。

（5）测试团队需要对公交团队和TSM团队开发的产品进行测试。这个测试涉及到交通一卡通应用的开通、充值、删除等。测试工作保证了系统的功能正常。

（6）项目管理团队负责所有项目的进度制定、进度跟踪、资源协调、客户沟通等项目管理任务。

# 3.1.2S公司项目情况介绍

S 公司所面向的客户主要是各个城市的交通一卡通公司和各个手机厂商。S公司所运营的应用发行平台，一边连接各个交通一卡通公司的发卡和充值服务器，另一边连接手机厂商的应用服务器。S公司起到了应用的集成和分发的功能，通过中转服务，将交通一卡通公司的公交应用下发到手机上。如图3.2所示。

S公司的项目主要分为以下两种类型：

# （1）公交接入项目

在业务部与交通一卡通公司签订合作协议之后，S公司需要为该城市建设所需要的服务器系统，将S公司的核心系统与交通一卡通公司的服务器连接，并且开发该城市可用的公交Applet。该项目需要公交团队、TSM团队和安全模块团队作为技术产品输出参与。

公交团队首先与交通一卡通公司进行沟通，确定交通一卡通公司的发卡需求，比如支持应用发卡、空中充值、卡片删除等功能，并明确资金清算模式及相关系统需求。同时为交通一卡通公司建立空中发卡平台，并支持数据的可视化导入、管理等功能。基于这些需求分解工作任务，确定开发计划。

TSM 团队需要协助手机厂商，通过配置相关的应用参数，允许手机厂商开通应用。

安全模块团队需要根据交通一卡通公司提供的应用规范，开发适用于该城市的Applet，并对该Applet的功能进行测试。在服务器系统连接成功并发出生产数据之后，还需要到该城市当地测试手机与POS机的兼容性，并解决测试中遇到的问题。

（2）公交上线项目

这类项Ⅱ一般是由于手机厂商的某一款手机预计上线某几个城市的交通一卡通应用而发起的。手机厂商自已对于业务的上线和推广一般都有其自己的规划和宣传。当确定了一个上线日期之后，手机厂商与S公司进行服务器对接和联调。手机厂商自已负责其钱包APP的开发和调试，S公司配合配置各种环境参数、数据等。当手机厂商能够成功的在其手机设备上开通公交卡应用之后，还需要带着开通应用的手机到城市当地进行实验室检测和生产环境测试。TSM团队主要负责了与手机厂商的对接工作，并配置相关的参数。安全模块团队负责提供实验室测试和生产环境测试的支持工作。公交团队负责提供交通一卡通公司数据的配置以及服务器的监控等支持工作。

![](images/c2e64c75f1cce4867243d5338bd7b9708b3ff7a4cef1e5d1cfe75b403ef65771.jpg)  
图3.2S公司产品系统结构Fig. 3.2Product architecturc of S company

# 3.2S公司软件项日进度管理现状

项目管理团队负责制定和控制S公司的所有项目的进度。目前项目管理团队仅有两名项口经理，其中一个项目经理在加入S公司之前已经有多年的项目管理经验，另外一名项Ⅱ经理是应届毕业加入S公司，从最基础的项目管理做起。

S 公司的软件开发项目会涉及多个团队，项目经理负责了研发部、运营部以及业务部各部门之间的协调，还包括研发部下属多个团队之间的任务配合。除了公司内部团队之间的沟通协调之外，与外部客户的沟通也是项日经理的主要职责。

S 公司的项目经理的工作从项目需求的提出开始，一直跟踪到项目结束。项目周期

根据项目的大小，从几周到几个月不等。因此每个项目经理在同一时间所负责的项目数量比较多，工作负荷较高。

S公司的项目进度管理现状如下：

# （1）进度计划制定

根据项目的具体情况不同，项目的进度计划制定原则也有所不同。S公司主要有两种项目计划制定的依据：

$\textcircled{1}$ 根据公司内部资源安排

对于一些不是很紧急，或者客户也没有明确的时间节点的项目，项目经理会协调项目相关团队主管确定参与的项目成员、工作量以及资源可用的时间，最终根据各团队给出的资源来安排整个项目的进度计划。该项目计划综合考虑了各项目成员的任务、其他并行项目的优先级以及外部资源协调等因素，项目周期一般较长，临时更改的概率较高。

$\textcircled{2}$ 根据客户给出的时间节点安排

对于客户的一些比较重要的项目，会有明确的结束时间。并且由于客户的重要性以及项目的重要性，S公司必须按照预期时间完成。对于这种项目，项目经理会根据这个项目截止日期倒推时间计划，再根据各团队的资源情况，协调研发部负责人以及业务部门，调整并行项目的优先级，确保该项目能够按期完成。

进度计划中的各项任务，是由项目经理根据自身经验，结合对项目的理解，粗略地分解出来的。各任务的负责人不会对任务的细节进行进一步的分解，只要对自已的任务所安排的时间没有异议即可。项目成员与项目经理在时间安排上达成一致之后，项目经理会将进度计划表邮件发送给全部项目成员、各团队负责人以及其他相关干系人。

S公司目前使用的进度管理工具是Microsoft project软件。在project软件中，体现了各项任务的起止时间和负责人。图3.3是某项目的进度计划表。

# （2）进度计划控制

S 公司的项目计划执行情况主要是通过项目经理与项目成员的口头沟通为主。项目经理每周会不定期地与正在执行任务的项目成员询问项目的执行情况。有些项目成员会在项目经理来询问之前就把自己的任务的执行情况主动告知项目经理，但更多的项目成员习惯于按照自己的节奏来进行工作，并不会主动提供任务执行情况信息。所以项目经理的不定期询问是S公司目前项目进行情况的主要搜集方式。

项目经理综合搜集到的项目执行情况，根据经验或者与团队主管沟通来确认是否存在进度延误风险。如果存在风险，则控制方法主要是通过降低其他任务的优先级、加班、增加其他资源等方式。如果还是无法按期完成，项目经理会直接与客户进行沟通，说明项目进行情况，取得客户的谅解。

项目进度的变更不一定会通知到所有项目成员，因为没有制度规定项目进度变更的流程。所以只有项目经理对项目的整体情况比较了解，而其他干系人只知道部分信息。

![](images/5a7dcacfec8d6df259e29f25b1368dfbd27f643e00fb5ac270ecf466d4f40ec8.jpg)  
图3.3S公司某项目的进度计划表  
Fig.3.3Schedule plan of a project in S company

# 3.3S公司软件项目进度绩效分析

经过查阅公司的项目记录资料发现，S公司在 2016 年全年共启动了37个项目，其中有19个项目发生了不同程度的延误和阶段性的延误，占所有项目的 $5 2 \%$ 。有3个项目因为各种原因被终止，占比 $8 \%$ 。图3.4统计了S公司2016年启动的37个项目的执行情况。

![](images/47d0f149d0fe4681fca9bf6d323790958fefe16fc9896e9eda28824a418c49a1.jpg)  
图3.4S公司项目执行情况Fig. 3.4 project implementation report of S company

（1）延期完成

由于各种原因，有 41%的项目发生了延期，但最终都完成并交付。这类项目主要是公交接入项目。因为在与交通一卡通公司进行对接的时候，交通一卡通公司虽然希望能在某一款手机上尽快上线。但由于没有具体的手机品牌要求，也没有确定的上线时间点，所以即便发生了延误，也不会对项目本身产生非常恶劣的影响。后期有手机要上线该城市的时候，只要这些项目是已经完成的状态，就不会影响手机的发布及上线计划。但不管是什么原因，项目的延误对公司内部的项目管理都产生了不利影响。

# （2）按期完成

从图3.4中可以看出，按期完成的项目只占2016年所有项目的 $2 4 \%$ 。通过查阅这 $2 4 \%$ 的项目类型可以发现，能够按期完成的项目主要是两种类型：内部需求的小型项目和非常重要的项目。内部需求的小型项目，因为不与外部客户发生联系，并且技术的难度要求较低，整体项目发生延误的风险因素较少。非常重要的项目，由于得到了公司的高度重视，因此在资源上有着其他项目不可比拟的优势。因此，即便重要的项目没有发生延期，其所占用的成本依然非常可观。

# （3）被终止

此类项目被终止的原因就是错过了手机发布的计划时间。按照原计划是要在手机上线前完成三个主要城市的应用对接工作，并且达到可以演示的效果。然而，由于各种各样的原因，项目未能在手机发布会之前结束，而手机发布会的时间也早就确定，不可能因为手机公交卡这个功能而延期，因此，客户不得已而取消了手机公交卡的宣传，在发布会中也没有提及。至于未来真正以上线为目的的项目，需要重新安排。当前以演示为目的的项目则被终止。

# （4）阶段性延误

有4个项目由于是在2016下半年启动，在数据搜集时，这些项目还在工期内，正在执行中，但已经出现了一些阶段性的延误。有两个项目因为其他项目无法释放人力资源，导致关键任务被推迟启动。有一个项目是因为与客户的协调出现问题，导致所需要验证的工作无法完成。最后一个项目是因为客户修改了手机上线时间，并且增加的未来要上线的城市，导致项目范围发生变动。

# （5）按计划执行中

此类项目同样是在2016年下半年启动，目前进展良好，尚未发生延误的情况。但因为在数据统计时项目还在进行中，目前无法判断整个项目是否能够按期完成。

从上述数据可以看出，S公司在项目的进度管理上的状况不容乐观。大量的进度延误的情况，不仅使得S公司在项目执行上浪费了很多资源，由于项目的延误或者失败，也会严重影响客户对S公司的印象，影响未来业务的拓展以及合作的更深一步的进展。因此，S公司急需改变目前的项目进度管理状况，提高项目执行的效率和成功率。

# 第4章S公司软件项目进度延误的原因分析

本章通过与S公司的核心员工面谈，搜集反馈并整理导致项目进度延误的原因。然后通过问卷调查的方式找出所有原因中最主要的四个原因。这四个原因分别是：需求变动频繁、沟通不畅、项目人员数量及能力不够、项目进度管理薄弱。接下来针对这四个原因，逐个分析产生这些原因的最根本的因素，通过剖析根本原因，为后面提出应对策略做准备。

# 4.1S公司项目进度延误原因调查

# 4.1.1通过访谈搜集项目进度延误的常见原因

本文作者选择了研发部比较核心的10位成员进行了访谈，访谈提纲请参考附录A，访谈记录请参考附录B。这10位成员覆盖了各个职位，包括一线员工和团队主管，包括项目经理和研发人员，尽量使访谈结果更为全面。通过对访谈记录中被访人员的反馈进行分析，发现其中的进度延误原因如表4.1所示。

综合各员工的反馈结果，将所有原因归纳如下，共8个原因：

# （1）需求变动频繁

需求的变动可能来自于客户，也可能来自于内部。变动的需求很多，并且没有对频繁变动的需求加以控制，使得各种各样的需求都能够直接到达项目研发人员那里。但是并不见得每一种需求都是合理的和必须的。这就导致了项目范围和进度的不可控。

（2）项目人员数量及能力不够

在实际调查中发现，项目人员的数量无法满足项目所需，所以每个人的工作负荷较高，没有额外的资源应对项目中的各种异常和风险。其次，由于项目人员的经验和技能参差不齐，能力较差的员工或者新人所负责的任务具有很大的延误风险。最后，由于很多项目所使用到的技术是之前未经验证的，第一次遇到的时候都需要花费更多的时间和资源来解决，所以使得项目的工期更加不准确。

# （3）沟通不畅

项目信息在项目团队内部及外部能够快速和准确地传递，会有利于项目的执行。而目前由于没有明确的项目沟通机制和管理方法，导致项目干系人无法准确把握项目信息，以至于关键的干系人无法及时提供帮助。

# （4）项目进度管理薄弱

进度计划对项目的执行有非常重要的指导作用，所以做出一个合理的项目进度计划并且能够严格按照计划控制项目进度，就可以大概率保证项目的按期完成。由于项目进度计划与实际进度之间的差距太大，并且没有合适的进度计划控制方法，导致进度计划流于表面形式，起不到指导作用。

# （5）项目预算太少

项目执行过程中必定会有各种开销，包括员工出差、卡数据购买、开卡圈存费用甚

至是招待费等。如果预算不够，就会导致一些活动无法顺利开展，进而影响项目的进行。

# 表4.1访谈记录分析

Table 4.1 Analysis of interview records   

<table><tr><td colspan="2">Table4.1Analysisofinterviewrecords</td><td>原因整理</td></tr><tr><td>访谈对象 杨X明</td><td>职位 项目经理</td><td>1．需求变动频繁</td></tr><tr><td rowspan="4"></td><td rowspan="4"></td><td>2．客户的原因</td></tr><tr><td></td></tr><tr><td>3. 项目人员数量及能力不够</td></tr><tr><td>4. 项目预算太少</td></tr><tr><td rowspan="3">刘x</td><td rowspan="3">项目经理</td><td>1. 客户的原因</td></tr><tr><td>2. 项目人员数量及能力不够</td></tr><tr><td>3. 需求变动频繁</td></tr><tr><td rowspan="2">刘X园</td><td rowspan="2">SE 应用开发工程师</td><td>4. 质量管理不严</td></tr><tr><td>1. 沟通不畅</td></tr><tr><td rowspan="2">谢X</td><td rowspan="2">测试主管</td><td>2. 项目进度管理薄弱</td></tr><tr><td>1. 项目人员数量及能力不够</td></tr><tr><td rowspan="2">王X巨</td><td rowspan="2">TSM主管</td><td>沟通不畅 2.</td></tr><tr><td>1. 沟通不畅</td></tr><tr><td rowspan="2">刘X辉</td><td rowspan="2">TSM服务器开发工程师</td><td>2. 需求变动频繁</td></tr><tr><td>1. 需求变动频繁 项目人员数量及能力不够</td></tr><tr><td rowspan="2">夏x刚</td><td rowspan="2">平台开发工程师</td><td>2. 3. 项目进度管理薄弱</td></tr><tr><td>1. 项目人员数量及能力不够</td></tr><tr><td rowspan="2">王X</td><td rowspan="2">公交集成开发工程师</td><td>2. 部门划分不合理</td></tr><tr><td>1. 项目人员数量及能力不够</td></tr><tr><td rowspan="2"></td><td rowspan="2"></td><td>沟通不畅 2.</td></tr><tr><td>3. 部门划分不合理</td></tr><tr><td>吴X林</td><td>公交集成开发工程师</td><td>1. 项目人员数量及能力不够</td></tr><tr><td>郭X兵</td><td>公交集成开发工程师</td><td>1．沟通不畅</td></tr><tr><td></td><td></td><td>2.项目进度管理薄弱</td></tr></table>

（6）质量管理不严

质量是项目管理的一个非常重要的维度。质量的好坏直接影响项目的进度，所以必须要保证质量满足需求。不过分要求高质量，避免影响项目进度，但更不能忽视质量，可能会导致返工，严重影响项目进度。由于质量管理比较弱，经常发生返工，导致项目周期延长。

# （7）部门划分不合理

部门内部的员工协作和跨部门的员工协作在效率上不可同日而语，尤其是技术部门。所以跨部门协作时，经常出现配合不及时或者支持力度不够的情况。跨部门协作还导致产品的复杂性提高，使得项目管理难度增加。

# （8）客户的原因

客户不仅是提需求的一方，在某些项目中还是直接的实施参与者。缺少了客户方的产出，项目就不能顺利执行。这类项目，必须管理好客户的时间节点，避免与公司内部项目的时间节点出现偏差，从而带来延误风险。

为了给S公司目前的项目进度延误情况提出改善的对策，需要从上述原因中找出最主要的原因进行分析，所以作者将所有八个原因放到了问卷调查中，分发给研发部的所有成员搜集反馈意见。

# 4.1.2通过问卷调查分析进度延误的主要原因

根据与10位核心成员的访谈结果，本文作者设计了一份针对S公司的进度延误原因的调查问卷（见附录C）。调查对象包括部门经理以及团队主管总共32人，共收回有效问卷29份。问卷的调查情况如图4.1。

![](images/257715c9b6368269441d2ad653cde048701a8d623414cbd90f945765f453e0bc.jpg)  
图4.1S公司问卷调查参与情况 Fig. 4.1 Questionnaire participation of S company

通过对回收的调查问卷的结果进行分析，统计出的数据如表4.2所示：

# 表4.2S公司项目进度延误原因的调查结果

Table 4.2The survey results of project schedule delay of S company   

<table><tr><td>序号</td><td>项目进度延误的原因</td><td>频数</td><td>频度%</td></tr><tr><td>1</td><td>需求变动频繁</td><td>18</td><td>62.07%</td></tr><tr><td>2</td><td>沟通不畅</td><td>16</td><td>55.17%</td></tr><tr><td>3</td><td>项目人员数量及能力不够</td><td>15</td><td>51.72%</td></tr><tr><td>4</td><td>项目进度管理薄弱</td><td>12</td><td>41.38%</td></tr><tr><td>5</td><td>客户的原因</td><td>6</td><td>20.69%</td></tr><tr><td>6</td><td>项目预算太少</td><td>5</td><td>17.24%</td></tr><tr><td>7</td><td>质量管理不严</td><td>5</td><td>17.24%</td></tr><tr><td>8</td><td>部门划分不合理</td><td>3</td><td>10.34%</td></tr></table>

根据表4.2中的统计数据看，频度排在前面四位的原因分别是：需求变动频繁、沟通不畅、项目人员数量及能力不够、项目进度管理薄弱。所以，这四个原因就是导致S公司软件项目进度延误的主要原因。下面针对这些原因逐个进行分析。

# 4.2需求变动频繁

从表4.2中的数据看，共有 $6 2 . 0 7 \%$ 的员工认为项目进度发生延误的原因是需求变动频繁。对于周期越长的项目，发生需求变更的可能性越大。项目执行过程中出现需求变动是非常正常的现象，也是不可避免的现象，但是，不加控制的需求变更，严重地影响了项目的顺利进行。

# 4.2.1多种原因导致需求变更

（1）项目初期定义需求的时候考虑不周

在开发一个软件产品之前，需要明确产品的各项功能要求，如果对产品的要求不清楚，或者有遗漏，就会给后期项目的顺利完成带来风险。业务部门在拓展业务的时候，会与客户频繁沟通。通过不断的沟通，解答客户的问题的同时了解客户的潜在需求。业务部门与客户所处视角相同，均对目标项目的产出有一个较为感性和粗糙的理解。所以业务部门的主要任务是与客户达成协议，并带回最初级的项目需求。然后，在明确合作意向之后，S公司会派出相关人员与客户开专题会议进一步细化需求。派出的人员中，有对公司系统架构非常熟悉的专家，也有安全模块安全方案方面的专家，还有运营客服的负责人。与客户讨论的问题也较为全面，争取一次性明确需求。但是这里存在一个很关键的问题，即对于产品的定义，没有哪一个人能够理解的比较深刻。各个方向的专家可以根据从客户那里获取到的比较笼统的需求，结合自己的专业知识细化并基本明确实现方案，这其中有很多细节客户并不理解，只能由各专家根据自己的理解自己决定。这样多个角度的需求合在一起之后是否会有问题，是否会存在遗漏的点，没有人能够从总体把握。在团队带回需求之后，项目经理会召集大家一起讨论确定项目范围。由于缺少从更高的视角对整个产品进行把握的资深产品经理，项目最终定义出来的项目范围也基本就是各位专家的结论组合，缺少从使用场景、功能特性以及目标用户等多个角度对产品的定义。这样的产品在开始实施的时候不会有问题，但当各个部门的产出结合在一起之后，在实际场景中使用时就会发现各种问题。S公司有多个项目存在这个问题。比如深圳通在开发工作已经完成并交付给测试团队测试的时候，测试负责人提出了一个能够极大地提高用户体验的特性，而且所有人都觉得这个特性非常重要，最终通过与客户沟通确认后，延长了项目周期以便增加此特性。比如在给苏州通配置服务器的时候，研发人员选择了公司常用的服务器配置，自认为肯定能够满足客户的需求。然而实际情况是，因为苏州通的应用范围很大，客户量随着智能手机的普及会有比较明显的增幅，公司常用的服务器配置只能满足短期的需求，长期来看需要再次升级。客户为了一步到位满足长期需求，要求S公司升级服务器配置，也因此延迟了产品的验收。

# （2）客户提出变更需求

客户是提出需求的一方，天生具有变更需求的权力。由于各种原因，在项目执行过程中，客户会修改需求，或者提出新的需求，打乱项目的计划。

$\textcircled{1}$ 客户对产品的理解具有一定的局限性

由于客户是提出需求的一方，对产品的功能和定位有比较清楚的认识，但对技术细节和行业深度背景知识了解有限。客户的产品经理在设计产品需求的时候，对产品的定位是基于客户团队的理解进行的。因此，随着项目的进行，客户也在慢慢的深入细节，对于产品真正的实现方法和原理慢慢的有了更进一步的理解，需求即随之而变。S 公司提供开发包给客户，使客户的钱包应用能够很方便的集成相关功能，包括卡片发行，查询和管理等功能。S公司提供的开发包一直采用的都是同步接口，即客户的钱包应用在调用开发包提供的接口的时候，需要等待接口的返回，然后才能继续执行接下来的步骤。随着项目的进行，客户逐步了解到，采用异步接口调用开发包接口的话，客户的钱包应用可以同时处理用户的其他请求，这样可以使钱包应用的界面处于激活状态，提高用户体验。但是异步接口的实现方式会大大增加开发包的开发复杂度。互联网公司以用户体验为至高追求，因此，客户要求S公司将接口修改为异步模式。

$\textcircled{2}$ 客户的内部团队存在协调问题

目前 S公司所提供的服务，属于市场上新兴的业务模式，具有创新性，也没有案例可以借鉴。客户的团队中，会有某一个部门负责新业务的探索和研究，会与S公司达成新产品研究的合作关系。在项目之初，为了产品原型能够尽快开发出来，让公司领导以及市场快速验证产品的创新性及接受度，因此在很多功能模块的实施上采取了够用即可的态度。但是随着项目的进行，客户内部其他相关团队会从自身利益出发，将原来尽快发布原型产品的诉求修改为在满足公司统一架构的产品框架之下的原型开发。需求的修改就会导致项目周期的变更。客户内部的两个团队，由于目标不同，导致内部的资源协调出现问题，使得项目无法顺利实施。在与某共享单车的合作中就出现了这种问题。客户的业务创新团队与S公司合作确定利用NFC技术对共享单车进行开锁的业务模式，并且前期也已确定实施方案和项目周期。但是在项目实施过程中，开锁的具体实现需要客户的车锁团队配合，而车锁团队出于自身利益考虑，在一些产品模块的实施上又提出了额外的要求，即相关关键服务需要部署在客户的服务器上。这个需求打乱了所有项目计划，工作量大幅增加，导致项目一再延期。

# （3）技术问题导致需求改变

软件产品的开发，尤其是新的业务类型和技术类型的产品研发，存在很多技术的不确定性。S公司所提供的服务，涉及到从手机硬件设备到手机软件、服务器软件以及公交一卡通公司服务器等多个环节，每个环节都可能存在因技术问题导致的需求变更。

手机需要与公交刷卡机以及地铁闸机进行交互，完成刷卡交易，其中就涉及到手机的安全模块内部运行的 applet 的兼容性问题。各个城市的公交环境比较复杂，有些城市的终端机器在十几年前就已经部署下去了。以前的技术标准仅仅要求本地发行的卡片能够正常刷卡即可，而现在的使用场景比以前要复杂的多。手机不仅仅支持公交应用，还支持银行卡应用、门禁应用等。同时，一个用户不仅仅可以开通本地的公交应用，还可以开通其他城市的公交应用。所以，多种应用的共存，导致了手机与刷卡终端之间存在兼容性问题。这些问题，在遇到之前都是未知数，一旦出现，就需要各相关方安排技术人员排查问题。很多问题会导致需求的改变，比如有些应用共存就会存在不可避免的问题，那么就只能修改业务规则，不允许用户同时开通互斥的应用。

手机上的安全模块一般是由恩智浦公司提供的，同时还提供该芯片的使用说明书。安全模块是一种比较特殊的产品，一旦出厂，就很难再对其进行修改。也就是说，如果安全模块存在bug，那只能修改产品需求以便绕过这个bug。某项目中，产品经理设计需要使用安全模块的一个接口来修改通讯参数，从而可以使得服务器的功能组件架构的更加合理。在相关人员讨论该方案的可行性之后，确定了实施步骤和周期，并且由 SE团队和服务器团队并行修改，尽快实施。实际情况是，SE团队在完成开发后，发现安全模块的这个接口并未达到预期效果。经过与恩智浦公司的技术支持人员进行沟通，提供了问题复现流程和方法，等待反馈。恩智浦是外企，国内支持人员与国外研发人员在沟通效率上都比较低，以至于反馈来的太慢。为了不使项目延期太久，产品经理只能重新修改设计，舍弃该方案。

# 4.2.2需求变更缺乏控制

需求变更在软件项目开发过程中是不可避免的。由于各种各样的原因发生了需求变更，研发人员就需要花费更多的时间来应对变更。所以研发人员对需求变更的意见非常大，往往会让研发人员产生厌恶心理。不良的心态会降低项目的执行效率和产品的质量，影响非常严重。

S 公司由于流程上缺乏控制，对于需求变更的把握完全靠项目经理。无论是客户方提出的需求变更，还是内部关于实现方案上的需求变更，当项目经理收到变更的请求后，首先是与项目相关人员讨论需求的变更涉及到的工作量和人员，然后尽量协调各个项目之间的优先级以及资源，满足需求。这里缺少了对变更需求的控制和过滤，从而让一些不合理的需求也加入到项目计划中，不仅浪费了资源，消磨了研发人员的斗志，最终项目也很有可能无法按时完成。比如在为华新公司接入门禁卡应用的项目中，华新公司提出需要在手机上创建门禁卡并且支持用户带着手机去物业管理处登记。在项目执行到一半的时候，华新公司又提出需要在手机上支持开通多张门禁卡，支持用户可以修改当前使用的门禁卡，方便用户可以在公司和小区使用。于是TSM团队针对多张门禁卡的需求重新设计了开通流程。然后没过多久，华新公司又提出在现有基础上增加空中写入门禁卡数据的功能。反反复复的需求变更，让负责门禁卡实现的员工天天加班，工作状态极差。而在整个项目完成并交付之后，华新公司并没有把空中写入的功能加入到实际产品中，因为华新公司没有足够的资源去接入足够多物业管理处，不能将这项功能做的比较完善，最终选择了放弃此功能。对于这种频繁变化的需求，没有控制，而是协商和接受并根据需求去修改，更可惜的是最终所实现的功能还没有被使用，白白浪费了大量的时间和资源。

因此，缺乏需求变更控制，接受所有的需求变更，并不会为公司带来更多的价值，反而会影响项目的正常进行，影响员工的工作士气，影响了公司的发展。

# 4.3沟通不畅

# 4.3.1缺少沟通规划

沟通规划的制定应该在项目初期就已经完成。S公司的软件开发项目，在项目启动时只有一次全员参与的项目启动会，然后就是各任务负责人、架构师等进行具体的项目需求分析和细节定义，并不存在沟通规划的过程。由于缺少沟通规划过程，导致项目执行过程中存在以下问题，进而影响到了项目的进度：

（1）缺少沟通需求分析导致项目进展缓慢

S 公司不存在沟通的需求分析过程，所以在项目管理过程中，项目经理只是不定期地发送一封项目进度状况邮件给部分项目相关人员。该邮件所包含的内容较多，部分内容也较为细节，但是内容是否全面，是否准确，是否符合接收者的需求，完全依赖于项目经理个人所掌握的信息。由于项目干系人获取不到自己所需要的准确的项目信息，很容易产生各种问题。

比如业务负责人想知道项目目前阶段对外部客户的需求是什么，以便能够提前安排客户拜访和协商等。对于业务负责人的这一信息需求，负责城市接入的同事认为，需要提前准备卡片生产数据，以便在产品上线时用户可以开卡；负责运营的同事认为，需要提前与公交公司协商好账务清算方式，在用户产生消费行为之后，需要有一个完整的账务清算流程；负责手机厂商的客户经理则希望提前预约公交公司实验室的使用时间，以便在应用上架可以开通的时候，首先进行实验室的验证。对于每个部门的同事而言，他们都有自己关心的需求，都需要业务负责人协助。对于业务负责人而言，他需要这些需求能够统一提出，统一协调和安排。由于缺少对业务负责人的沟通需求分析，各个部门之间没有协调，各自分别向业务负责人提出自己的要求，导致业务负责人需要反复与客户沟通，重复安排，使得项目的进展缓慢。

（2）传统的沟通技术无法提高效率

S 公司常用的沟通技术包括邮件、电话、面对面等。邮件适用于实时性要求不高的场景，电话方便比较着急的事情能够立即得到响应，而面对面可以使得信息的传递更为准确和及时。但是仅仅依靠这些传统的沟通技术，很多问题还是无法解决。比如因为某个问题需要多个成员一起讨论的时候，在非工作时间或者这些成员并不在同一个地方的话，那就需要会议组织者分别通知会议时间，而在会议开始的时候，很有可能有部分成员忘记会议时间，需要再次单独提醒，这使得准备会议就浪费了很多时间。S公司在北京、上海和深圳都有办公室，经常需要有电话会议的需求，在这方面急需优化电话会议技术。

# （3）会议效率低下

项目执行过程中会遇到各种各样的问题，有些问题牵扯到多个项目成员的时候，就需要大家一起开会讨论问题的解决方案。由于问题的复杂性，最初并不能确认可能受到影响的项目成员。遇到这种问题的时候，项目经理会立马召集产品经理、团队主管或技术专家等，就问题性质以及初步的解决方案进行讨论研究。讨论过程中，很有可能涉及到一些实现细节，需要临时将相关研发人员叫过来加入讨论。这种会议的效率非常低：第一，被召集的参会人员在会前对问题一无所知，所有信息的背景沟通和技术的确认都需要在会议上进行，导致会议时间很长，严重影响了项目人员的正常工作；第二，会议主持人是项目经理，而项目经理不能很好地把控讨论的方向，经过长时间的开会，参会人员也会疲惫，而结论仍然不够明确，问题无法解决，就可能需要第二次甚至第三次会议继续讨论。第三，会上经常出现对个别细节问题的穷根究底，因为部分细节使得参会人员越来越多，会议时间越来越长；第四，会议结束后，虽然大家对问题的解决方案或者下一步的计划都有一个自己的理解，并且也安排了会后每个人需要做的事情。但是由于没有跟踪，会议上安排的任务完成的质量不高。长时间且次数多的低效会议，大大的浪费了参会人员的工作时间，对项目的进度产生严重的影响。

# 4.3.2项目信息发布存在问题

# （1）缺少足够的信息发布

问卷调查中，问题“你希望从项目经理那里获得什么信息和帮助？”的反馈结果显示，有 $7 2 \%$ 的员工认为，需要项目经理“同步项目的进展”，可见项目成员普遍不了解项目的进度信息。

S 公司在项目执行过程中，项目的状态和进度信息，主要是由项目经理与项目成员面对面了解获取。但是项目成员没有合适的渠道可以获得项目的绩效信息。只有小部分项目成员比较积极地与项目管理者或者团队主管沟通，从而获得比较多的项目绩效信息。但是更多的项目成员主要是专注于完成项目活动，而疏于信息的获取，再加上项目成员大多属于性格内向型，不善于口头沟通获取信息，更加重了信息的不畅通。尽管在某些项目中，项目经理会发项目周报，但是由于周报所包含的内容对项目成员的具体任务没有针对性，而且发送的对象并不包含所有项目成员，并不能改善信息缺乏的情况。

项目信息的缺乏，使得项目成员只能看到自己所负责的活动，而对项目的整体情况缺乏了解。对整体情况缺乏了解，则导致了项目成员不能发挥自己的特长来帮助团队实现更多的目标，也不能方便项目成员根据整体情况调整自己所负责任务的安排、优先级等。

# （2）信息未经处理直接发布

项目经理日常工作中需要与客户保持联系，同步项目进展。有时候，客户对产品中的某个功能存在疑问或者提出要求，则通过邮件的方式向项目经理提出。S公司的项目经理在遇到自己无法回答的问题的时候，会直接将邮件转发到某个项目成员那里。但是

由于邮件中可能已经与客户有多次来回，前后所包含的事情也不止一件。这样的邮件在项目成员眼里会变得非常不容易理解，需要花费较多的时间才能找出问题，这浪费了项目成员较多的时间和精力。

# 4.3.3缺少沟通控制

# （1）对沟通的效果没有监控

在日常项目沟通过程中，经常出现一个邮件发出去，或者一件事情说完以后，没有任何后续动作。本来是项目成员已经发现的问题，并且也提出来让大家注意，但是最后还是因为这个问题导致了严重的后果。比如在某公交接入项目中，由于该公交城市的特殊性，存在多个保存交易明细的记录文件，所以需要服务器的同事在开发的时候特别注意要比以前的项目多出一个文件的读取操作。负责 Applet 开发的同事在开发的时候发现了这个不同点，并通过邮件的方式通知了负责服务器开发的同事，也抄送给了项目经理。但是在服务器上线后，才收到用户的反馈，说部分交易记录丢失。由此造成服务器端重新修改测试和上线，延长了项目周期。

（2）对沟通的渠道是否畅通没有监控

沟通的渠道是否畅通决定了沟通的效率高低。S公司由于存在多个研发团队，每个研发团队都有其明确所负责的产品。而大多数的项目都要涉及到这几个研发团队，所以团队之间的沟通在项目沟通中经常发生。一个团队经常需要另一个团队的支持，比如服务器团队需要Applet 团队提供应用管理指令序列，Applet团队需要TSM团队提供开卡脚本日志等。当一个团队需要另外一个团队协助时，另外一个团队因为考虑到协助的事情需要消耗较多的工作时间，往往采取了不太积极的态度，因此需要协助的事情完成的也比较慢。由于没有对这种低效率的沟通进行监控，项目经理和团队主管都不能从项目整体的角度重新安排任务的优先级，也不能优化沟通渠道使任务协作更顺利。

# 4.4项目人员数量及能力不够

# 4.4.1研发人员数量及能力不够

# （1）研发人员数量不足

S 公司由于是初创企业，公司现有资金无法支撑很大的研发团队，所以领导层在研发人员数量上有着严格的控制。S公司2016年所承接的项目数量达到了37个，而研发人员、项目管理人员以及团队主管，总共才32个人。往往一个项目又涉及到多个团队，所以每个人的工作负荷都很高。问卷中，对问题“最近是否遇到工作无法按时完成的情况？”的反馈显示， $7 9 \%$ 的人在近期的工作中已经出现了无法按时完成工作的情况。

由于任务多而研发人员少，所以每个研发人员身上都承担了很多任务。有些任务是项目中的活动，研发人员自身就是活动的负责人。还有很多任务是项目执行过程中临时出现的意外状况，需要某些研发人员协助处理的。这种意外情况在项目初期很难能够被预测到，并且没有被考虑到项目活动中。基于这些原因，项目在实际执行过程中，经常会出现由于研发人员的任务过重，没有足够的时间完成任务的情况，甚至有些研发人员，因为项目的压力而放弃了对产品质量的坚持。问卷调查结果显示有 $4 8 \%$ 的人因为要赶工期在期限前交付产品而降低了质量要求。由于人员不够导致任务无法按时完成或者产品质量下降，会给项目的按时结束带来风险。如果因为要保证其中一个项目的进度而调整研发人员的任务安排，则对其他项目带来更大的风险。部分员工为了完成任务，忽视了质量要求，给项目的按期结束埋下了严重隐患。

除了研发人员本身的工作负荷较高带来的项目进度风险之外，研发人员的可替代性差也是非常重要的一个原因。

项目的实施需要依靠合适的研发人员共同协作才能完成。软件研发人员的可替代性又比较差，再加上S公司的产品所涉及的技术多种多样，所需要的人才也是各不相同，并且每一个岗位的人往往只有一两个人，更多的只有一个人负责。所以，S公司的项目实施就存在着很大的风险。因为，一旦项目成员中有人出现意外情况需要暂时离开工作岗位的时候，项目就会面临无人顶替的情况，暂离的项目人员所负责的活动也只能暂停，其所产生的影响根据不同的人有所不同，但毫无疑问的是，对项目的按期完成带来了莫大的风险。

# （2）研发人员的专业能力需要提升

虽然S公司有 $7 5 . 8 6 \%$ 的技术人员具有超过五年的工作经验，工作经验可谓相当丰富。但是作为S公司所服务的行业是传统的公交支付领域，是一个长期没有技术更新的行业。所有参与调查的人员中，服务器开发的人员和测试的人员各占据了 $3 7 . 9 3 \%$ ，是技术部门的主要构成人员。这部分人员虽然具有多年的工作经验，但几乎没有行业经验。传统的公交支付领域，只有公交公司及其系统承建商等相关公司才具备行业经验。而S公司的这些服务器开发人员和测试人员，从来没有在这些企业供职过。所以，这些员工并不具备相关的行业经验和知识，工作中遇到无法理解的行业特有问题时，一般是通过与Applet开发人员沟通交流来解决。但是Applet开发人员数量很少，而需要补充专业知识的人员太多，必定存在一些人由于各种原因无法得到及时的支持，从而导致工作中出现纰漏。

# （3）缺少高素质的研发人员

高素质体现在学习研究能力、问题分析解决能力和系统架构能力上。这些能力与工作经验、学历并无直接关系。所以，高素质的研发人员需要管理层去发现和培养。

S 公司最初在每一个团队中，都至少有一名高素质的研发人员负责处理疑难问题，设计系统架构。然而，由于公司缺乏合理的晋升和培养制度，没有办法满足高素质人员的学习需求，职业发展需求，再加上薪资上没有相应的体现，导致部分人员流失。

同时，服务器的开发技术革新速度很快，很多大公司都已经形成了自己的技术架构，并且也研发出了适合大并发量、高可用率的服务器开发和运营技术。而S公司目前的研发人员的主要精力都放在了具体的项目上，并没有专业的研发人员是负责技术研究的。因此在项目中出现的各种技术问题，没有有效的技术方案应对，也就无法快速地解决。

项目团队缺少技术高超并且能够快速学习新技术的高素质研发人员，间接导致了项目执行过程的不顺利，意外情况频出，而应对能力有限。

# 4.4.2项目经理的能力不足

# （1）缺乏对技术的基本理解

S 公司目前有两名项目经理，其中一名具有超过五年的工作经验，并且之前供职于芯片研发公司。另外一名项目经理是应届生，刚刚入职不到一年。

对于一个项目经理来说，具备项目管理的理论知识和常用方法是必须的技能。然而在软件行业，项目经理还需要了解到产品的基本功能和常用的研发模式。对于产品功能的了解，可以帮助项目经理判断各项活动在出现延误风险时，需要同步的项目成员以及可能影响到的其他活动，以便及时做出合理的决策来规避风险。了解软件产品常用的研发模式，可以降低项目经理与项目成员之间的沟通成本，统一认识基线，以便项目经理能够更准确的获取项目活动的执行情况，并且做好与客户的信息传达。S公司的项目经理并不是研发出身，对技术没有深刻的理解。而且项目经理以前的工作经历与S公司目前的行业也不相符，没有相应的行业背景。因此，S公司的项目经理非常缺乏软件行业的专业知识。在客户沟通方面，项目经理所起到的作用仅仅是传递信息，而更准确的信息还需要项目研发人员与客户直接沟通。在对项目进度进行控制的时候，只能简单的了解某项活动是否完成或者是否存在延误风险，而对于此项活动对其他活动的影响，还需要召集相关人员开会讨论来确定。因此，缺乏专业知识的项目经理在进度控制过程中，无法让团队之间的协作更加高效。

（2）缺乏专业的项目管理能力

S 公司的项目经理并没有经过专业的项目管理培训。项目经理之一是有着超过五年的工作经验，大部分时间是负责项目管理工作。另外一名项目经理是应届毕业生。所以，S 公司的两位项目经理，在进行项目管理工作的时候，主要还是根据以往的经验以及项目的需要来行事。经过多个项目的磨练和经验的总结，S公司会逐渐形成一些有利于项目管理的机制，比如说项目周报机制，要求每一周或者双周发一次项目周报，汇报项目目前的进展以及存在的问题。但是依靠经验和项目磨练，远远不能满足S公司目前的发展速度。项目执行过程中，对于干系人的不明确，会导致一些关键人物对项目情况不知情，无法最大效率地促进项目的实施。比如小米武汉通的上线项目，技术团队已经基本完成了产品的开发，接下来需要预约武汉通的实验室并提供测试手机以便通过武汉通的验证。预约以及准备手机的工作，并非是技术部门来做的，而是需要商务团队，一方面与武汉通公司签订相关协议，协调测试时间，另外一方面与手机公司协调测试手机。因为技术开发的过程占整个项目的大部分时间，所以项目周报也基本都是发给技术团队和业务团队的领导，但是没有把负责小米和武汉通的两位业务的同事加入进来。所以导致了后来在送测之前，需要紧急的去处理商务问题。

由于在项目管理过程中没有根据项目管理规范来执行，很多重要的事情被忽略，被轻视，反而需要在项目后期花费更多的时间来弥补，给项目进度带来极大的风险。

# 4.5项目进度管理薄弱

# 4.5.1进度计划不合理

（1）项目活动工期估算不准确

软件项目的活动工期受到多方面因素的影响。对于S公司来说，由于成立时间较短，很多产品的基础框架和核心平台都正处于演化完善的过程中，因此经常遇到如下几点原因导致工期的不准确：

$\textcircled{1}$ 技术因素

一项活动是否能够在规定的时间内完成，取决于活动的实现者是否能够顺利的解决活动中遇到的所有问题。这些需要解决的问题中，最不可控的就是技术问题。对于已经解决的技术，直接拿来使用的话，可以比较容易地估算解决周期。但如果是一个未知的技术，解决的速度更依赖于活动实现者的技术能力和问题解决能力。因此，对于不同的研发人员，同一难度的问题的解决时间也是不一样的。即便是活动实现者自己负责估算自己所负责的活动工期，也有可能忽略一些潜在的技术难点，导致实际活动工期延长。比如为实现门禁功能，需要使用到一个叫做M4M的技术。恩智浦的芯片已经预置了该功能相关模块，但是要将其利用起来，需要开发人员了解Mifare的相关知识，并且还需要通读M4M规范所定义的管理机制和各种操作接口，然后才可以设计出比较完善的实现方案。在S公司，要实现这个功能，必须是有行业背景的开发人员才能操作，其他人员仅仅是理解都会存在困难。而具有行业背景的开发人员，要想真正理解并成功实施门禁的创建删除等管理操作，还需要有较好的英文阅读能力和专业技术理解能力。所以针对这类项目的活动的工期评估就会存在很大的不确定性。因为在理解技术原理的过程中会不断地发现额外的工作量，这些工作量是没有办法在真正开始工作之前预先评估出来的。

$\textcircled{2}$ 软件的可复用性

对于整体架构设计做的比较好的产品，其中很多模块是可以复用的。在设计之初，架构师就已经考虑到该模块在未来可能使用的场景。理想情况下，模块仅需实现一次，未来就不需要在该模块上花费额外的工期，而是可以直接拿来使用。但如果该模块在设计的时候没有考虑到当前项目的特别需求，直接拿来使用会出现问题，也就会需要花费额外的时间进行修改调整。由于对于软件的可复用性没有可量化的标准，因此对于可复用的任务的工期，如果按照直接拿来使用的情况评估，工期会大大减少，但是并不符合实际情况。公交团队的主要任务就是将各地的公交系统接入到S公司，统一为各手机厂商提供发卡、充值等服务。针对不同的城市，其基本功能是一致的，所以架构师在设计的时候就已经定义好了公交团队所需要实现的服务器的接口，并且提供了一个服务器的代码模板作为新城市接入的基础，以便保证接口的统一并提高项目的开发速度。领导希望在这种基础架构下，每一个城市的接入时间不超过两个星期，然而实际情况并非如此，模板仅仅是一个基础，距离每个城市的个性化需求还有很大距离。比如深圳通，作为一个大城市，希望给用户带来更为便利的使用体验，提出了移资码的概念，即用户在使用深圳通应用的时候，如果有更换手机的需求，可以在手机APP中发起申请，将余额退回到用户的银行账户，而用户的公交卡应用将被删除，同时，深圳通提供一个移资码给用户，可以方便用户在新的设备上开通深圳通应用的时候，不需要再次支付开卡费用。这个功能在原有模板的基础上增加需要额外耗费多少时间，开发工程师需要仔细研究需求并评估。新的问题是，该功能是否需要集成到模板中，以便未来在接入其他城市的时候直接使用，需要架构师决定。这个决定也影响了工作量，影响了工期。

$\textcircled{3}$ 软件项目结构复杂

每个项目都是唯一的，因此每个项目的需求都不一样。作为企业开发软件项目，需要考虑到未来产品的开发效率，因此在设计框架和功能的时候，目标不仅仅是为了满足当前项目的需求，同时还要考虑公司产品整体架构的合理性、健壮性和高效性。所以，对于项目中各个功能模块的开发，需要考虑的点比较多，比如需要考虑该模块与其他模块的相互调用关系、验证逻辑，还需要考虑该模块所使用的数据库技术、通讯技术等是否符合公司产品的需求，还需要了解整个项目中的数据存储问题、日志问题等。因为相关的需求点比较多，所以在工期评估的时候，很容易忽略部分功能，从而导致工期评估出现误差。S公司内部存在多个服务器模块，包括公交团队所负责的公交接入模块，以及TSM 团队所负责的手机接入模块、订单模块等。在模块开发初期，都没有考虑到重复请求的处理规则，大家都只默认客户只会发送一个请求过来。在系统运行过一段时间之后发现，客户端发送的请求间隔时间有长有短，存在重复的实际情况。而针对重复请求的处理逻辑，是放在公交接入模块还是放在手机接入模块，团队内部存在争论。比如公交接入模块在完成一次开卡或圈存操作之后，是否需要主动同步订单状态到订单系统，还是由订单系统主动来查询，也是一个争论不休的话题。由于系统架构的复杂，各个团队都有自己的理由来阐述自己的结论。这类问题点多且容易遗漏，项目后期发现时已经影响到了项目进度了。

上述原因只要在项目中存在一两个，就会导致项目执行的周期无法完全按照进度计划的安排来进行。S公司目前的活动工期估算是依据研发人员的经验和对活动的理解来确定的。因此，对于活动比较简单，研发人员比较熟悉的情况，工期估算就会比较准确。而如果遇到上述原因中的任何一个，其估算出来的工期就会存在很大的偏差，从而使得整个项目进度计划变的不够合理。

（2）项目活动顺序安排不合理$\textcircled{1}$ 同一资源负责的活动之间没有缓冲

S 公司目前的项目活动安排基本上是根据所需要的人力资源按照顺序安排的。同一个资源负责的活动，按照时间顺序排列，中间基本没有缓冲时间。所以一旦某一个活动或者某一个资源出现了问题，后续很多活动都要受到影响，而且更多的情况下是整个项目的周期都可能被延长。项目经理对于这种情况的处理，经常只能是加班，而加班只能应对一部分情况，比如意外出现的额外工作量。如果加班也不能解决，而其他资源并不能替代当前的资源，那就只能延长项目周期。在小米深圳通的项目中，负责Applet开发的同事突发急性阑尾炎，住院一个星期。由于当时正处于Applet调试修改过程中，需要积极配合现场的测试工作，所以领导临时安排了另外一个 Applet 开发的员工临时顶替，继续修改和支持的工作。但是这个同事对深圳通Applet 的实现架构和需求并不熟悉，需要一段时间理解代码，期间还需要跟生病的同事电话沟通一些问题，在代码修改进度和技术支持上虽然也已经尽力配合，但是仍然无法达到预期目标。项目虽然在进行，但是超过了预期完成时间。由于没有安排任何缓冲时间，后续所有工作都受到了延期影响。

$\textcircled{2}$ 外部依赖的活动没有加入计划

S 公司的大多数项目是需要与客户的系统对接的。在系统集成测试时，需要客户的系统也处于就绪状态才可以执行。因此客户系统的准备情况的跟踪和督促也应该是项目的必备活动之一。S公司目前的项目进度计划表并没有体现这一点。一旦公司内部系统已经准备就绪，之后再发现客户的系统仍然在准备过程中，项目团队的资源要么只能白白浪费，要么就临时切换到其他项目中，从而导致项目资源的控制混乱。客户系统的推迟也导致了整个项目周期的延迟。而资源的切换，更增加了资源的使用成本。在与某共享单车合作的NFC开锁的项目中，S公司负责手机端Applet的开发和应用的空发操作，而共享单车公司负责锁端的开锁判断逻辑以及服务器的推送更新逻辑的实现。双方统一制定了时间节点并各自开发。当S公司完成开发准备联合调试的时候才发现，共享单车公司内部由于资源安排问题，无法按时完成其所负责的开发任务。S公司此时无法继续联合调试，相关项目人员被重新安排到其他项目中，此项目在S公司内部被设为暂停状态。

# 4.5.2进度控制能力不足

（1）项目经理不具备足够的权力

项目经理的职责就是带领项目团队完成项目的目标。项目经理了解项目执行过程中的所有信息，并且能够根据项目中出现的问题，合理决策，调派资源，确保项目按计划执行。

S 公司的项目组织结构采用了职能型组织结构。项目经理安排资源需要通过各团队主管的同意，并且由主管分配资源参与项目。所以，项目经理只能负责项目中各种资源的协调、各部门以及客户间的项目沟通、进度的跟踪。对于进度的控制、团队的管理等方面，项目经理没有足够的权力。

项目经理没有管理项目成员的工作范围、任务安排等具体事务的权利。在项目进度出现延误的时候，为了赶上进度，必须调整个别项目成员的工作内容、工作时间等。这个时候，项目经理需要找到相关团队的主管，去介绍项目的具体情况，延误的原因，修正进度的方案等，然后再安排具体人员的工作内容。这无疑降低了问题解决的效率。如果遇到其他项目争抢资源的情况，那么项目经理需要更多的精力去协调资源。在很多时候，这会成为项目延误的一个主要原因。

不仅仅是在管理项目成员的工作安排上，在对团队本身的管理上，项目经理也需要一定的权力来实施。在项目团队确定之后，项目经理需要确定项目团队的工作方式，让所有团队成员调整为相同的工作节奏。项目经理需要及时地对表现出色的项目成员进行鼓励，对其工作成果给出积极的评价，甚至跟上级领导申请激励措施等。项目经理需要对表现较差，甚至影响团队氛围的成员提出批评、给予惩罚甚至是踢出团队。只有这些管理的权限，项目经理才能组建一支高效率的项目团队。

# （2）缺乏有效的进度控制方法

S 公司目前使用Microsoftproject 软件管理项目进度。这是一个专业的项目管理软件，并且有非常丰富的功能可以使用。但是，S公司的项目经理还只是用到了其最为基本的功能，利用Project软件制定进度计划。

一旦进度发生延误，项目经理需要确认延误情况，受影响的活动等，从project软件中无法得到这些信息，原因是在制作项目进度的时候，对于活动之间的依赖关系，没有在软件中正确的设置，所以调整其中一个活动的周期，其他活动并不会跟随改变，以至于会显示出资源冲突的警告，更无法直接生成进度报表等信息。对于一些项目成员承担多个活动的情况，需要并行执行的时候，软件中也没有正确的设置资源百分比，所以默认的活动安排按照百分之百的占用资源排列的。这个不符合实际情况，所以在遇到问题的时候，可参考的价值不大，经常出现项目经理按照自己的搜集的信息和理解强行调整进度计划或者协调资源，由此造成更多后续任务被影响。

由于没有能够充分利用工具提供的功能，项目经理为了完成日常项目管理工作，不得不仍然依靠记忆，纸笔等记录各项任务的完成情况，通过不断的沟通来更新情况。对于项目延误的偏差，无法用数据的方式体现，也无法给出可靠的修正方案。

# 第5章S公司软件项目进度绩效改善建议

从前文对S公司软件项目进度延误的原因的分析结果来看，导致S公司项目进度延误的主要原因有需求变动频繁、沟通不畅、项目人员数量及能力不够和项目进度管理薄弱等。本章针对每个原因给出具体的可行的解决方案，以期改善S公司的进度绩效状况，提高S公司的资源使用效率和工作效率，从而保证S公司在激烈的市场竞争中保持领先地位。

# 5.1减少需求变更并实施需求变更管理

# 5.1.1减少需求变更数量

需求变更是软件项目开发过程中不可避免的现象，只能努力从各个角度来降低需求变更的次数，从而减小对项目进度的影响。

# （1）制定需求分析流程

需求分析不是一个简单的工作，它需要一个规范化的操作流程。从对客户负责的角度出发，同时也是为了降低自己的开发成本，必须与客户进行充分有效的沟通，确保获取到准确的需求。需求分析的过程分为以下几个步骤：获取用户需求、分析用户需求、编写需求文档、评审需求文档、管理需求。需求分析的工作流程参见图5.1所示。

![](images/000bb155c2c7dfc8a7ea7ce0a803ac33abf088ca79e975b091b22ab66d09c6ec.jpg)  
图5.1需求分析流程Fig.5.1 Requirements analysis process

做好需求分析的第一步就是搜集需求。需求搜集可以采用多种方法。可以通过面对面的方式与客户开会讨论需求，当场确定整个需求的基本框架。在对需求做了初步的消化和整理之后，接下来可以通过电话、邮件、网络聊天工具等渠道了解需求细节，对于涉及到多个角色的需求沟通，可以组织电话会议直接沟通。但不论是哪一种需求调研方式，都需要做好笔记，在与客户交流完毕之后，需要对交流的结果进行整理、归纳、分类，以便后续继续分析。对于客户提出的每一个需求，都要了解清楚客户选择这么做的原因，对于客户没有提到，但是根据自身经验判断有可能有关联的需求，也需要与客户确认并询问客户的意见。对于客户提出的不合常理的需求，提出自己的理解和实现方案，确定是不合理需求应给出理由。集中精力去理解用户想要做的是什么，而不是怎么做。并且在与客户沟通的过程中，要仔细观察客户的思维方式和描述方式，发现客户提出的需求中所隐含的其他需求。在选择交流的客户方的对象时，应尽量要求客户方的负责人、业务人员及相关领导出席，避免技术人员提出的需求过于随意和不负责任。从不同背景的客户方人员那里了解他们对这些需求的理解，尽量全面地把握需求的本源。

需求的分析与需求的搜集是同步进行的。需求分析的结果可能会要求更多的需求搜集的动作，而需求搜集的结果将帮助需求分析。需求分析应尽可能详细地描述客户的需求，可以采用业务流程图的方式描述整个系统的业务活动以及各系统之间的接口和边界；采用数据流程图来描述数据流关系，对于复杂的数据流关系可以配以数据字典；可以开发原型产品来展示用户界面以及各功能模块，供客户取舍；采用实体关系图描述实体、属性、关系三者之间的联系等。需求分析文档应包含用户的所有需求，包括功能性的需求和非功能性的需求。

针对需求分析的结果设计系统架构以及业务流程，并且在需求分析文档中体现设计的思路和各个模块及接口的定义。要求公司内部各部门的资深员工以及行业专家、产品经理等，对需求的整体进行审阅，提出疑问和建议，进一步完善需求和优化设计，完善需求分析报告。

需求评审阶段要邀请客户方相关技术人员、业务人员及相关领导对需求报告进行审核。公司应派出具有足够技术功底和交流能力的员工，负责给客户方讲解需求分析中的所有内容。对于每一个模块，每一个接口，都要清晰地说明设计的思路和考虑的因素。最终目的是通过需求评审，获得客户方对需求分析的认可。只有在客户确认该项目需求没有问题的情况下，才能启动项目的开发工作。

按照流程进行需求分析，能够帮助系统分析员从实际使用场景出发，避免遗漏重要的功能，降低项目后期变更需求的几率。同时也能够主动发现客户潜在的需求，提前考虑，而不用等到客户自己发现之后再变更需求。做到这一点，就能够把常见的需求变更的情况提前到需求分析阶段。

# （2）提前验证未知技术

软件项目中会存在很多技术问题。而技术问题的解决效率取决于研发人员的技术能力。因此，为了使项目进度更为可控，避免在项目执行过程中遇到难以解决的问题而改变需求，应当尽早分析所使用的技术方法，并且安排资源对其进行验证。

需求分析一般由系统分析员来负责。但在软件行业中，不仅有产品经理、项目经理，还有系统架构师等。有一些实现方式是系统分析员无法确认的，应及时召集产品经理、系统架构师对相关技术问题进行确认，除非是曾经做过并且熟悉其实现方式的，按照经验评估时间之外，必须找到合适的专业人员进行初步研究，明确相关的硬件、环境和方法。

技术验证应该摒除无关联的事务，专注在技术点上。通过模拟环境、精简结构或找到替代环境等方式，达到验证技术的目的。研究人员需要具备将问题简单化的能力，抓住问题的核心，去除不相干的事务的影响，快速而准确地完成验证工作。

前期的技术验证工作并不会浪费研发的资源，反而能够让项目的进度更为流畅。无论如何，相关的技术难点在项目实施过程中都必须实现。而在需求分析阶段就安排专业的资源攻克技术难点，正是对人力资源的合理利用，也为项目后期的顺利实施扫除了障碍。从项目整体的角度来说，实际上是提高了项目执行的效率。

# 5.1.2实施需求变更控制流程

需求变更会引起软件的重新设计、重新编码、修改测试案例、修改相关文档以及调整项目计划等额外工作，增加了项目成本，拖延了项目进度，加大了项目成员的工作量。因此，设计一个清晰的、明确的、可控的、有效的需求变更工作流程，有利于过滤不必要的、无意义的变更需求；有利于促进软件项目开发过程中项目成员的合作，明确各自的工作任务；有利于对需求变更的处理阶段进行实时监控，有效跟踪。因此，作者设计了需求变更流程，参见图5.2所示。

![](images/58f2c9ac1fc4574dabadf07cd34464604e5414845c3ccfe305af139e5bf0b7b4.jpg)  
图5.2需求变更流程 Fig. 5.2 Requirements change process

# （1）变更请求的提交

对需求变更的管理，要从提交变更请求开始。需求变更请求必须包含该变更产生的最原始的信息，以方便后续对变更进行评审。需求变更请求书应至少包含以下内容：

$\textcircled{1}$ 需求变更对应的项目名称及其编号：方便以后查找该项目总的需求变更次数，由此带来的影响等。$\textcircled{2}$ 项目对应的客户名称及类型：用于以后分析哪种用户更容易提出需求变更，帮助管理者进行分析和管理。$\textcircled{3}$ 需求变更的主题及其内容介绍：说明需求变更的范围。$\textcircled{4}$ 需求变更的来源：说明为什么要提出该变更。$\textcircled{5}$ 需求变更的优先级：说明该变更的重要性。如果不实施这个变更可能带来的影响。$\textcircled{6}$ 需求变更的提交人和提交日期：明确需求变更提交人的身份以及是在项目的哪个阶段提出的需求变更。

可以参考表格5.1来填写需求变更申请。

表5.1需求变更说明书  
Table 5.1 Requirements change specification   

<table><tr><td colspan="5">需求变更说明书</td></tr><tr><td>项目名称</td><td></td><td>项目编号</td><td></td><td></td></tr><tr><td>客户名称</td><td></td><td>客户类型</td><td></td><td></td></tr><tr><td>变更申请人</td><td colspan="2"></td><td>变更申请时间</td><td colspan="2"></td></tr><tr><td>变更类型</td><td colspan="3">□新增需求 □需求变更 □内部改进</td><td>□产品缺陷</td><td>□其他</td></tr><tr><td colspan="6">提出需求变更的原因</td></tr><tr><td colspan="6"></td></tr><tr><td colspan="6">需求变更优先级说明</td></tr><tr><td colspan="6"></td></tr><tr><td colspan="6">需求变更的描述</td></tr><tr><td colspan="6"></td></tr></table>

（2）需求变更的评审

通过对需求变更进行评审，过滤合理的可行的需求变更，并且评估需求变更对项目带来的影响，做好评估和跟踪。

$\textcircled{1}$ 初审：项目经理首先从需求变更申请表提供的信息入手，判断该需求变更是否有效，提交内容是否详细，该需求变更是否必须在当前项目中实施等。如果项目经理判断该需求变更申请不需要在当前的项目中实施，即将申请退回。对于下一阶段或其他项目可能需要实施的需求变更申请，则记录在案，供以后的项目管理使用。

$\textcircled{2}$ 复审：经过初审的筛选，提交给变更控制委员会进行复审。变更控制委员会由各个部门资深员工以及产品经理等组成。变更控制委员会需要对该需求变更申请进行评估，确认需要增加多少工作量，实施的复杂度有多大，不实施会有哪些影响，对进度的影响会有多少，需要增加多少成本，其他变更与该变更之间的关系，是否存在依赖，测试需要注意哪些问题等。变更控制委员会综合考虑各个因素，最终给出是否执行变更的意见。

初审负责过滤需求变更申请，减轻复审工作的压力。复审综合各方面的考虑，决定是否接受变更申请。

# （3）需求变更的任务分配和实施

由于需求变更会影响到整个项目的进度、工作量以及成本，所以一旦评审通过，项目经理就需要为该需求变更分配相应的资源和责任人，并明确每个人需要执行的变更的内容，时间和预期的结果，并将其同步到项目进度计划表中，便于后续进行跟踪和管理。所有变更相关的信息，都要同步到所有项目干系人，使大家对项目的变化都有了解，避免无效工作的发生。

另外，在需求变更完成后，项目经理应记录下该需求变更对项目的成本、进度产生的影响，并且将变更控制委员会的评估结果一并附上，通过数据的积累，为以后的需求变更提供宝贵的历史经验。

# （4）需求变更的结果验证

与软件开发任务一样，任何对产品的开发和修改都要经过最终的测试才能交付。因此针对需求变更的内容，测试部门需要准备相应的测试案例，对变更的内容进行测试，确保所有变更有效并且不影响其他功能。

# 5.2加强沟通管理

# 5.2.1制定沟通管理计划

沟通管理计划需要在项目初期就启动制定，由项目经理来完成。制定沟通管理计划需要从以下几个方面着手：

（1）分析项目干系人及其沟通需求

针对S公司的项目特点，项目干系人至少应包含：研发成员、项目经理、业务负责人、客户、各团队主管、产品经理、架构师、公司领导等。对于研发成员，项目经理需要了解其所负责的功能模块。对于项目中该模块所有相关的需求、方案、进度、计划等信息，都需要同步到相关研发成员，并且尽量详细和突出重点。对于业务负责人，需要定期提供项目进展信息以及与客户相关的沟通需求、资源协调等。对于客户来说，提供项目的大概进展情况，主要体现对客户的要求以及内部时间节点安排、变更等。对于团队主管、产品经理、架构师等，需要同步项目整体方案、计划、进度绩效等。对于公司领导，需要同步项目的整体计划、进度绩效，是否需要高层协助等。根据不同的干系人的不同沟通需求，有针对性的、及时的项目信息沟通，提高项目的执行效率。

（2）采用更加高效的沟通技术

需要语音互动的沟通，可以使用一些互联网语音工具，由会议发起者设置预订的开会时间和需要参会的人员。开会之前软件自动提醒会议即将开始，会议开始后，参与人员都会收到直接拨打过来的电话，被动接听，避免部分人员忘记参会。软件自动弹出工具界面，方便参会人员记录会议内容等。

需要共享的项目信息放在一个可以通过手机APP访问到的网站上，由个别人员维护，可以向特定的人员展示特定的信息，方便项目成员随时随地的获取自己所关心的项目细节。

需要演示的沟通，可以采用远程桌面共享软件，方便在不同地方的参与人员都可以即时地看到演示内容，提高沟通效率。

（3）建立高效的会议制度

会议占用了一群人的时间，是为了集中相关人员合力解决一件事情而组织的。高效的会议可以减少对员工日常工作的影响，可以帮助快速达到会议的目的：形成结论，可以确保会议的效果。

$\textcircled{1}$ 会前准备

会议必须有明确的议题，并且提前通知到参会人员。参会人员需要针对该议题提前思考，想好问题、方案或者目标结论。  
会议主持人需要明确会议议程。议程由主持人提前与部分参会人员确定，列出会议中需要讨论的议题，以及所需要达成的目标。  
确定参会人员。会议应该尽量少的选择参会人员，只有需要发表意见和给出建议的人员需要参与。其余会议议题相关人员，可通过详细的会议记录以及会议结论通知到位。  
确定会议时间。应选择大多数参会人员比较方便的时间，尽量降低会议对人们的工作产生的影响。一般来说，会议时间可以放在早晨和临下班前。早晨员工尚未进入工作状态，不会对员工的日常工作产生不利影响。临下班前员工也处于较为疲惫的状态，可以开会来调剂一下工作思路。

$\textcircled{2}$ 会议控制

会议主持人应简短的介绍会议的背景和会议的目的，然后按照议题顺序逐个讨论。会议中需要有一个人作为会议管理者，掌控会议的讨论方向，不过度发散，以形成结论为目标。会议管理者还要控制议题的讨论时间。对于比较复杂的问题，会上无法短时间内形成结论的话，会议管理者需要尽快结束讨论并形成临时结果，会后另行安排时间进行专题讨论。然后将会议引入下一个环节。

会议应有一个会议记录员，负责将会议中讨论的过程和结果记录下来，会议纪录应尽量全面，包含讨论过程中形成的临时结论、最终结论和接下来的行动计划。

$\textcircled{3}$ 会后跟踪

将会议纪要发送给相关人员，并明确会议结论以及下一步的计划和安排，确定相关负责人并由专人进行跟踪和汇报。

# 5.2.2提高信息发布效率

（1）通过丰富沟通渠道提高信息流通效率

沟通的渠道可以非常丰富，每种渠道都有其长处。团队成员在沟通问题的时候，应当积极主动报以交换信息、解决问题的态度不卑不亢的沟通，而被沟通的对象也应该以公司利益为主要考量，提供自己力所能及的帮助。

$\textcircled{1}$ 采用敏捷开发机制中的每日站会制度

项目经理及项日成员可以安排每日早晨九点半到十点之间，找个办公室的角落或者会议室，围成一圈，逐个介绍个人昨日的工作内容以及今日的安排，并且把自己的遇到的问题抛出来，把自己工作的经验分享出来。这样可以帮助项目经理精确地把握项目的进展，及时发现潜在的问题。同时项目成员之间可以交换意见和看法，互相提供帮助，并且对项目整体的理解逐步加深，使之能够更加主动地考虑完成项目所需要做的事情。

$\textcircled{2}$ 使用专业的沟通软件

钉钉是阿里推出的一款工作通讯软件，并且支持手机和PC版本，还可以通过添加插件支持任务管理、任务提醒、日报提交等等功能。借助钉钉软件，项目成员应该将自己的工作进度体现在钉钉上，方便其他成员可以及时了解相关信息。对于出差在外的员工，还可以通过钉钉与其他员工进行语音聊天以及电话会议，方便沟通并提高工作效率。利用钉钉上的石墨文档功能，可以实现多人同时维护和查看同一个文档，文档中体现各个项目的当前状态以及各种需要注意的信息，使得项目信息的存储和同步更为及时和准确，使得项目成员对项目的整体信息以及各个细节都能够有指定的地方查询并能够及时掌握，从而提高沟通效率。

$\textcircled{3}$ 多用白板

沟通的目的是为了让别人理解你所说的内容。项目成员之间需要沟通很多技术细节，而且经常是跨领域的。为了让大家都能清晰和直观的理解技术架构和业务流程，主讲人应当多使用会议室的白板，通过边画边说的方式，方便别人理解和接受。而且白板上的内容应该拍照记录，以便以后回忆时可以作为参考。

$\textcircled{4}$ 留下文字记录

有一些信息是让别人知道就好，还有一些信息需要别人记住。这个时候沟通应该借助多重手段来实施。比如你有一件非常重要和复杂的事情需要别人协助，那么你除了要通过书面的方式（邮件或者聊天软件）将你的需求清晰地描述给对方，还要通过电话的方式及时通知到对方，并且介绍背景信息，帮助对方理解你的需求。而且书面的东西方便留存，方便对方随时查询，避免信息的遗漏。特别建议所有沟通的信息，最终都通过邮件进行确认和备份，以便在时隔很久之后，可以通过邮件搜索和查询，避免因为聊天工具中的记录丢失导致信息的丢失。

$\textcircled{5}$ 定期发布项目状态

采用项目周报机制，根据项目情况可以一周或两周发布一次。周报中需要分级体现项目状态、进度绩效和未来的预测。对于每期有变化的内容，使用特别的颜色着重显示。方便不同的阅读者可以很容易地找到自己关心的内容。根据不同的干系人，通过邮件发送项目周报及相关内容。对于公司内部，还应将周报上传至指定网络位置，方便项目成员随时查看。同时也方便累积历史记录，未来可以通过对历次的项目周报进行分析，来不断的改善项目进度管理。

（2）对项目信息进行初步处理

项目经理主要负责项目信息的搜集，筛选和分发。项目经理负责了员工与领导之间，公司内部与客户之间的连接，所以项目经理最清楚各种项目信息的来龙去脉。项目经理在分发信息的时候，首先自己将项目信息进行初步消化，对存在疑问的地方，先与信息来源进行沟通，尽量提前将所有信息理清，明确问题；其次考虑接收者所需要关心的内容是哪一部分，将关键部分摘取出来着重显示，提高接收者获取关键信息的速度；最后根据接收者的个人特点，对项目信息所相关的问题背景介绍清楚，方便接收者能够理清问题的上下文，准确理解问题内容。通过对分发出去的信息进行初步处理，可以使得该信息传递的有效性大大提高，降低接收者忽视或误解的几率。

# 5.2.3增加沟通控制机制

# （1）对沟通的效果进行监控

首先确保接收者收到了需要沟通的信息。从沟通规则上要求全员必须遵守，在收到对方发来的邮件、聊天消息、信件等之后，必须立即回复已收到。该机制可以保证信息不遗漏。如果发送者在一段时间内没有收到对方的确认，可以认定为对方尚未获取该信息，发送者可以更换沟通方式另行传达，确保接收者获取信息。

其次确保接收者准确理解了沟通的信息。对于需要后续任务的信息，在接收者收到信息之后，需要反馈后续任务的安排，或者关于后续任务的讨论计划等，以此提供一个大概的时间点，便于发送者进行跟踪。对于需要接收者准确理解的信息，则要求对方通过回复的方式进行确认，可以描述其对该信息的理解，也可以反馈在理解了该信息之后需要注意的事情等。

最后根据沟通效果对沟通规划进行更新。如果沟通效果较差，接收者无法高效地获取信息或者无法准确理解信息，则需要从沟通规划上考虑解决方案，通过更改沟通内容、沟通方法、沟通工具等方式，提高沟通的效果。

（2）对沟通的渠道是否畅通进行监控

项目经理作为项目的核心，可以了解到项目执行过程中所进行的各种沟通动作。但是有些项目成员之间的沟通，或者与某些项目团队外部的公司员工的沟通，可能不在项目经理的视线范围内。所以要求项目成员必须了解，所有与项目相关的沟通，包括信息的通知以及协助的请求，都需要知会项目经理，以便项目经理能够及时发现问题并及时跟踪解决。项目经理需要关注日常工作中，哪些沟通所占用的时间超过了正常水平，哪些沟通该发生的时候还未发生，该出现效果的时候还未出现。项目经理需要对不正常的沟通进行干预，了解沟通中存在的问题。不仅要解决沟通中的问题，还需要在沟通规划中增加新的沟通规则以避免此类问题。

# 5.3补充项目人员并提高人员能力

# 5.3.1补充研发人员并提高技术能力

（1）评估缺口并招聘研发人员

S 公司已经面临着多个项目占用同一资源，造成大多数研发人员工作负荷较重的情况。为了缓解这种情况，需要对当前正在进行的以及未来一段时间内可能启动的所有项目进行整体评估，对研发资源的需求进行估算并确定技术、能力及经验等方面的要求，安排人事部门启动招聘计划。招聘可通过常规招聘网站进行，同时更加建议通过内推进行。内部员工推荐自己曾经合作过的前同事，可以在最大程度上保障新人的素质，从而提高招聘的效率并以最快的速度补充研发资源。

除了招聘新的研发人员以外，还需要关注存在瓶颈的资源。比如有些岗位可能只有一个人的情况，需要考虑这个人的备用资源。需要保证公司内部至少有两个人有能力承担同一个任务，当其中一个人因为个别情况无法执行任务时，另外一个能够替补保证项目的持续进行。这就需要团队主管及管理层在日常管理和任务安排以及技能培训时，着重在这个方面加以注意，有意降低每个员工的不可替代性。

（2）增加内部培训并提供训练机会

从公司层面制定培训机制，每周至少安排一次培训，由各团队主管根据其队员的能力决定是否需要参与。每周每个团队至少有一次内部的技术交流活动，交流话题由团队主管负责从团队成员搜集来的反馈中筛选，并且与其他团队同步交流话题及安排，方便有共同需求的其他团队成员一起参与。部分团队有技术共通的地方，可以组织在一起进行培训。有些团队有针对新人的入门培训，其他团队也可以参与了解。这些培训设定为全员可参与，部分人员必须参与的模式，并通知公司全部员工。此类培训主要目的是技术和知识的普及和入门。对于团队内部的培训，由团队主管负责制定计划，根据团队所负责的任务，所使用的技术以及行业内的新技术等，明确需要培训交流的话题，并分派到每一个团队成员身上，负责研究整理。此类技术交流，以学习为目的，必须做到理解和基本掌握。

培训的频率根据实际情况动态调整，要求初期必须按照上述频率安排，但随着培训的深入以及团队成员的学习，一些入门的话题和基础的知识就不必再重复。根据实际工作中遇到的问题，搜集并分析相关知识点，集中培训。培训过程必须录音录像，方便新员工从视频入手，鼓励利用业余时间和公司的资料提升个人能力。积极收集员工关于培训的建议以及需要补充的知识点，不断地丰富培训内容，必要时重新安排完整的培训并录像，以便更新视频并提高培训质量。

除了培训，还要给员工提供机会训练。只有通过训练，员工才能够真正的掌握和使用相关技术。团队主管或相关项目经理在安排具体任务时，应该有意针对员工的技术能力和培训安排，及时地安排相关任务，并指派其他对此技术比较熟练的团队成员给予指导。在满足项目所需的时间的前提下，同步提高团队成员的技术能力。

# （3）发掘有潜力的员工并组建研究组

团队主管以及公司领导至少需要了解团队和公司内部，哪些员工有较强的学习能力和研究精神。对有这些特质的员工，应提供更多的学习机会和成长机会。公司内部可以成立一个研究组，负责研究新技术、新产品。这个小组的主要任务并不在具体的项目上，因此可以有更多的精力把技术细节和更高级的使用方法研究透彻。小组不仅负责新技术的研究和实现，还需要对组外其他团队提供技术培训。这样可以让有能力的员工以最快的速度学习和吸收新技术，并且以最高效的培训和指导，提升整个公司的技术水平。其次，在项目需求分析时，对于不确定的技术难点的实现以及工期评估，都可以由研究组进行前期技术验证，提供专业的和更加准确的工期评估。

# 5.3.2提高项目经理的个人能力

（1）提高项目经理的专业知识水平

项目经理的职业生涯中最好有过行业相关的经历，如果没有，则需要特别学习。比如作为S公司的项目经理，应该了解移动支付的技术背景、了解智能卡应用的特征和使用方法、了解服务器开发常用的模式和方案、了解公交领域常用的服务器对接模式、对账模式等等。

针对S公司目前的两位项目经理：一位有多年项目经理的工作经验但缺乏专业知识；另一位是应届毕业生直接负责项目管理，S公司需要针对他们的专业知识进行培训。要求项目经理必须参与公司组织的各种培训，并安排团队主管随时提供技术讲解和培训支持。项目经理针对培训中遇到的相关知识，不懂的地方由团队主管解答。加强对项目经理的专业知识考核，作为项目经理的工作业绩评估的重要参考指标。在每年中和年末的考核中，搜集所有项目成员对项目经理的专业知识水平的反馈。对于长期在专业知识上无法提升的项目经理，建议调整岗位从事其他合适的工作，然后再招聘或内部转岗合适的人员负责项目管理。

（2）提高项目经理的项目管理能力

项目管理知识体系复杂，必须经过专业老师的完整的培训。S公司需要从外部寻找培训资源，针对项目经理的项目管理专业知识进行培训。同时要求项目经理利用专业知识，结合S公司实际的项目管理状况，对所有已经完成或正在进行的项目进行分析，整理其中不符合项目管理流程规范的操作，分析适用于S公司的改进策略，给出分析报告。通过项目经理的学习和实践，不仅能够对以往的项目进行总结复盘，汲取经验，同时也能够提高项目经理的项目管理能力，通过实践理解项目管理知识，以便在未来的项目中更好的结合实际，发挥专业能力。

# 5.4加强项目进度管理

# 5.4.1制定合理的进度计划

（1）提高活动工期估算的准确性$\textcircled{1}$ 提前验证未知技术

项目中如果使用到未经验证的技术，需要在任务分解时，将该技术点的需求描述清楚，然后安排相关人员提前研究该技术的可行性。该预研工作需要资深的工程师来进行，尽快给出是否可行的确认。而对于活动负责人，需要结合资深工程师的确认结果，了解实际项目中所需的实现的方法，评估工作量，给出活动工期。因为实际工期还跟活动负责人的技术能力和工作效率有关，所以在活动负责人给出的估算工期的基础上，还需要由资深工程师再次确认，尽量缩小和实际工期之间的误差。

$\textcircled{2}$ 明确可复用模块的功能

由于软件项目中所用到的模块具有可复用性，并且开发复用性较好的模块，也是软件公司提倡的做法。所以为了减少可复用模块的活动工期评估误差，首先需要具有完善的开发文档，能够准确地描述可复用模块的功能及使用要求，以便在后续项目使用时能够根据文档准确地判断出是否可以直接使用，或者还需要对其做哪些升级。其次需要架构师在项目中第一次设计该模块的时候，综合考虑未来一段时间内，可能使用到该模块的其他项目的需求，尽量设计一个既满足当前项目需求，又能够覆盖其他项目需求的功能模块。虽然该模块的开发属于当前项目需要执行的任务，但开发的过程必须以模块设计的功能和实现方法来进行，不能仅仅只满足当前项目需求。从公司角度来说，应要求架构师搜集尽量多的项目信息，综合以前出现过的类似需求，将该模块的功能设计考虑的尽量全面。最后，在决定需要对模块进行升级的情况下，还需要架构师重新审核模块功能，重新整理各个项目的需求，以完善模块功能为目的来评估活动时间。

$\textcircled{3}$ 不断细化系统架构和功能点

由于软件项目的复杂性，模块与模块之间的关系，模块与数据库、通讯层的关系，模块要满足系统架构的需求等，需要实现的功能点比较多。所以，为了更准确的评估活动工期，首先需要将模块的功能、实现方案、技术细节等全部定义清楚，然后才可能将任务准确的分解和评估。为了避免功能点的疏忽，首先需要架构师与产品经理进行充分的沟通，明确所有功能点，包括潜在的功能点。然后架构师要与活动负责人进行充分沟通，明确实施过程中可能存在的细节问题。通过多次重复执行该过程，逐步完善模块的功能定义文档，降低功能点遗漏的概率。

除了通过上述比较具体的措施来减少活动工期估算的误差外，还需要从统计的角度，将员工评估的活动工期与实际活动工期搜集起来，分析其中存在的关系，误差分布大小及概率，以便在未来的项目中，能够根据这个统计结果合理地为每一个活动预留误差时间，避免对整个项目周期产生不利影响。

（2）合理安排活动顺序$\textcircled{1}$ 根据关键链理论在项目中加入缓冲时间

S 公司经常出现一个研发人员参与多个项目情况。为了降低多个项目争抢资源的概率，在给研发人员分配项目任务的时候，要考虑到该研发人员目前已经承担的其他项目职责以及预计工期，然后在安排该项目活动的起止时间点时，要跟其他项目之间的时间节点之间空出一段时间作为缓冲时间。根据任务的类型，缓冲时间可以是半天到一天。对于任务活动类似的项目，可以并行执行的时候，需要在原来项目的活动时间的基础上，按照一定的比例扩展活动时间使之覆盖两个项目的活动，保证原项目的活动按期完成，又留有足够的时间继续完成该项目的活动。这种情况容易造成比较大的误差，所以在该活动之后，应该与下一个活动之间安排一个缓冲时间。并且这个活动时间的评估，需要资深研发人员的参与，尽量合理。

$\textcircled{2}$ 考虑客户的服务系统准备情况

S 公司的软件项目的特征是，主要的产品都是需要运行在服务器上提供24小时运营服务的。这类软件在开发和测试的时候，都需要其他模块的配合。对于公司内部不同部门的不同模块，项目经理可以从整体上安排任务顺序，避免所需要的服务不可用导致研发任务无法进行的情况。但是，有些产品需要对外调用客户的系统服务。如果这个时候客户的系统服务不可用，项目经理或者业务部去协调，都不会有很好的效率，会严重影响项目进展。因此，制定项目进度计划的时候，需要着重考虑客户的因素。

首先尽量降低对客户的依赖。为了使研发进度更为顺畅，在工作量不大的情况下，研发部可以从整体效益上决定，是否可以模拟客户的系统服务，用于对内部提供测试服务。在S公司是有一部分系统服务可以这么实现。前面介绍过，有部分城市的技术水平较低，自身无法建设系统服务的时候，S公司可以协助建设。这样，S公司就天然地具备了某些城市的模拟系统的能力。但是对于手机厂商的服务系统来说，模拟的成本较高，暂时还不合适去实现。但降低对客户的依赖仍然是S公司软件产品研发的一个方向。对客户的依赖越低，项目按期完成的几率就越高。

其次，项目进度计划编制的时候，按照客户系统不可用的情况考虑。S公司的软件项目研发，并非每一个模块都需要与客户的系统服务对接才可以运行。那么，在安排任务先后顺序的时候，应当以客户的系统不可用的情况为前提，将可以提前实施的任务放在一个子项目中。也就是说，这个子项目的完成后，客户的系统服务不具备，项目就无法继续进行。一旦客户的系统服务上线，研发团队就可以继续下一个子项目的开发。这样的话，项目的进度计划应该分为多个阶段。进度控制的目标是保证每个阶段不发生拖延。而下一个阶段的启动时间，则由客户系统状态决定。

# 5.4.2提高项目进度控制能力

（1）赋予项目经理更多权力

为了使项目经理能够更好的行使项目管理功能，需要赋予项目经理足够的权力，引导和管理项目团队按照既定目标完成项目。

$\textcircled{1}$ 赋予项目经理强制权

S公司目前属于职能型组织架构，所有项目成员在工作上都直接受团队主管的领导，所以项目经理在下达任务时，需要经过团队主管的同意，效率较低，并且也无权做出有利于项目的规章制度。所以建议将项目成员的工作管理权力和团队管理权力直接赋予项目经理，使项目经理能够直接安排项目成员所负责的任务，提出需要满足的要求等。既然是一个团队，就要有团队适用的工作规章制度，包括上下班时间、工作态度、沟通方式等，项目经理都要做出统一要求，以便减少工作中可能出现的各种协调问题。给予项目经理强制权，有助于在项目发展到一种必须强制纠偏的时候，能够快速高效地解决问题；有助于项目经理打造一个积极向上、纪律严明的高效团队。

$\textcircled{2}$ 赋予项目经理奖励惩罚权

为了打造一个高效率的项目团队，除了强制权以外，项目经理还需要一些激励和惩罚措施，以便鼓励好的行为，禁止不好的行为。对于努力工作、积极帮助他人等给团队带来积极影响的员工，项目经理应该及时地给予激励，包括在项目团队内部公开表彰，号召其他成员向其学习，包括在工作绩效考核上给予较高的评价，使其在年中和年末业绩考核时拿到较高的分数。根据员工为团队贡献的大小，还可以向上级申请物质激励。对于影响团队氛围，破坏项目协作的员工，项目经理要及时给予警告、处罚、甚至踢出团队等措施，避免给团队带来不好的影响。项目经理要做到奖罚分明，坚决阻止和铲除影响团队绩效的人和事情，促使整个团队向着有利于项目执行的方向前进。

# （2）制定进度控制流程

进度控制是一项复杂的工作，仅仅依靠项目经理和进度管理软件都无法达到理想的效果。S公司需要制定一套进度控制流程，并要求严格按照流程执行。在制定了合理的进度计划的前提下，应按照以下流程完成进度控制管理，并且使用Microsoftproject软件辅助进度控制。

进度控制按照图5.3所示流程执行。以下详细说明每一步所需要执行的任务。

$\textcircled{1}$ 使用 Microsoft project 软件制定进度计划。在 Microsoft project 软件中，将所有分解出来的活动、活动工期、负责人、所占资源百分比等数据完整输入。设置各项活动之间的依赖关系、缓冲时间等。保证软件中所展示的进度计划与前面进度计划制定阶段所定义的结果相同。

$\textcircled{2}$ 收集进度信息。项目经理需要明确每日一次的进度同步机制，要求每一位项目成员都将当前所负责的活动的完成情况以百分比的形式进行同步，并对该活动是否存在延期风险进行说明。项目经理收集整理各项活动的进度情况，并更新到Microsoftpro ject软件中。

$\textcircled{3}$ 比较进度偏差。使用Microsoftproject软件提供的报表功能，查看是否存在进度落后的任务、进度延迟的任务以及关键任务是否有可能受到影响。如果存在任何与计划进度不一致的偏差，则需要及时对偏差进行修正。

![](images/5039ba1e816af1eaa8aaaa7c490a72615fd4d412dba111876b911b23328fa496.jpg)  
图5.3进度控制流程 Fig.5.3 Schedule control process

$\textcircled{4}$ 纠正偏差。当偏差出现时，项目经理需要立即与相关活动负责人进行沟通，了解偏差产生原因。针对不同的原因提出相应的纠正措施。比如，如果偏差产生原因是技术难题导致的，则需要项目经理协调资深研发人员协助解决；如果偏差原因是因为工作量过多导致的，则需要活动负责人加班赶工，或者寻求其他资源协助；如果偏差是因为需求变动导致的，项目经理需要重新审核进度计划，确认需求变动后进度计划作出相应的调整，等等。尽量保持进度计划不作变更的情况下纠正偏差。

$\textcircled{5}$ 提交进度变更请求。如果因为偏差纠正措施无法完全消除活动延误带来的影响，必须修改进度计划的话，则由项目经理提出进度变更请求。进度变更请求需要发给部分干系人进行审核。比如业务团队的领导需要审核变更后的进度计划是否会影响客户的验收、是否会导致客户的损失等问题，项目团队成员需要确认自己所负责的活动在起止时间变更后是否会出现资源冲突的问题，等等。变更请求需要经过各方的审核确认后，才可以更新进度计划并据此执行。否则，项目经理需要协调更多资源和方案来解决偏差问题。

$\textcircled{6}$ 更新进度计划。项目经理根据延误活动造成的影响，更新Microsoftproject软件中的项目活动信息，使用软件自带的自动调整功能重新安排各项活动的起止时间。

# 第6章案例分析

为了验证上一章提出的改善对策的有效性，本章通过S公司的一个实际项目，将部分改善对策进行实际应用。通过将各个对策与实际案例相结合，对以前存在问题的管理过程进行优化，排除影响进度的各个潜在因素，保障进度的顺利进行。

# 6.1X项目介绍

X项目的目标是在A城市完成公交接入服务的开发，并在小米手机上完成上线工作。这是一个比较典型的公交上线项目，类似的项目以前已经进行过多次，未来也会继续。但是该项目由于同时包含了公交接入部分和手机上线部分，涉及三个公司，并且内部多个团队参与，项目的复杂性较高，所以存在的可能导致进度延误的因素也比较多。在该项目中实施改善对策，能够得到领导的支持和团队的认可，从而用实际案例体现改善对策的有效性。

参与该项目的成员从各个部门抽调，由团队主管分配。公司外部有相关的业务部同事负责协调。项目组织结构如图6.1所示，虚线部分是公司内部项目团队。

![](images/4dc5440faab19755e02ba52f75053f00e531ea903851163a59e40e0ab4e034fc.jpg)  
图6.1X项目团队结构图Fig.6.1 Team Chart of Project X

# 6.2X项目的实施

# 6.2.1建立项目团队

为了解决在项目执行过程中，因为项目人员不足导致进度延误的情况，要求参与项目的研发人员都由团队主管安排一个备份人员。备份人员需要了解项目的基本要求和其对应的项目成员所负责的工作，了解其代码实现的框架、逻辑等，做到随时可以替补。研发人员的安排如表6.1所示。

表6.1研发人员安排  
Table 6.1 Arrangement of R&D Member   

<table><tr><td>团队</td><td>项目成员</td><td>备份人员</td></tr><tr><td rowspan="3">TSM 团队</td><td>刘X辉</td><td>夏X刚</td></tr><tr><td>朱X</td><td>毛X龙</td></tr><tr><td>周X</td><td>刘X辉</td></tr><tr><td>公交团队</td><td>郭X兵</td><td>吴X林</td></tr><tr><td rowspan="2">安全模块团队</td><td>刘X园</td><td>朱X林</td></tr><tr><td>祝X君</td><td>刘X园</td></tr><tr><td rowspan="2">测试团队</td><td>廖X</td><td>钱X霞</td></tr><tr><td>张X峰</td><td>曹x</td></tr></table>

为了提高项目进度控制能力，项目经理由资深的杨X明担任，并赋予项目经理相应的管理权限，使之可以对项目成员的工作绩效进行评价，可以安排项目成员的工作内容和优先级。项目结束后，项目经理要给出每一位项目成员的绩效评估，作为年终考核的参考之一。

# 6.2.2规划沟通管理

针对S公司缺乏沟通管理的问题，在X项目中，按照前文提出的沟通管理建议，首度应用沟通管理过程。

# （1）制定沟通计划表

根据项目干系人所处的位置和所负责的任务，分析干系人关心的项目信息，得出表6.2所示项目沟通计划表。

表6.2项目沟通计划  
Table 6.2 Project Communication Plan   

<table><tr><td>项目干系人</td><td>所需信息</td><td>频率</td><td>方法</td><td>责任人</td></tr><tr><td>高层领导</td><td>总体进度</td><td>每周</td><td>邮件</td><td>项目经理</td></tr><tr><td>项目经理</td><td>任务完成百分比，障碍点，风险</td><td>每天</td><td>项目管理软件/聊天软 件/面对面/短会</td><td>研发人员</td></tr><tr><td>架构师</td><td>功能变动、方案变更</td><td>不定</td><td>邮件/聊天软件/电话</td><td>研发人员</td></tr><tr><td>客户经理</td><td>总体进度、关键节点、对客户的要 求</td><td>每周</td><td>邮件/聊天软件/电话</td><td>项目经理</td></tr><tr><td>业务负责人</td><td>总体进度、关键节点、对公交城市 的要求</td><td>每周</td><td>邮件/聊天软件/电话</td><td>项目经理</td></tr><tr><td>研发人员</td><td>总体进度、进度细节、需求变更</td><td>每周</td><td>项目管理软件/邮件</td><td>项目经理</td></tr><tr><td>研发人员</td><td>系统架构需求、实现方案变更</td><td>每周</td><td>邮件/聊天软件/电话</td><td>架构师</td></tr><tr><td>研发人员</td><td>问题同步、方案同步、进度同步</td><td>每天</td><td>面对面/电话</td><td>研发人员</td></tr><tr><td>核心成员</td><td>阶段性状态同步</td><td>每两周</td><td>会议</td><td>项目经理</td></tr><tr><td>客户</td><td>关键节点</td><td>每周</td><td>邮件/电话</td><td>客户经理</td></tr></table>

（2）明确沟通技术选用不同的沟通技术应对不同的沟通需求。根据前文的建议，采用钉钉电话会议的

方式，处理异地同事的沟通需求。公司层面在钉钉电话中采购了足够的时长供项目团队使用。采用石墨文档的技术在网上共享项目信息，负责信息发布的各人，在石墨文档上及时更新自己所负责的任务信息，以便于项目经理以及其他团队成员及时获取。采用远程桌面的方式与外地同事沟通PPT或工作指导。通过这些沟通技术，提高项目执行过程中的信息流通效率，从而提高工作效率。

# （3）制定信息发布规则

对于项目组的日常沟通，要求所有项I成员都必须对接收到的信息立即做出确认，信息发送者要跟踪确认信息已经传达。

对于项目信息的每天同步，每日早10点所有已到员て集合，花20分钟同步项目中存在的问题，各自的进展。针对个别问题发起的会议、电话等，必须留下书面记录，通过邮件或者聊天软件的方式同步所有相关项日成员。对于沟通中存在的问题，项目成员有责任提出问题和优化建议，由项目经理牵头改善。

对于项目进度发布，由项目经理在石墨文档上。不同的文档可以分享给不同的人，根据干系人沟通需求，分组发布。

# 6.2.3制定进度计划

为了提高项目工期评估的准确性，由产品经理和架构师明确产品的整体架构和模块功能，明确该项目中需要开发的模块，可重用的模块，需要更新的模块以及更新方法等。在所有工作任务明确的前提下，由资深工程师协助活动负责人评估工期，使之尽量接近实际情况。

在项目活动安排方血，为了避免部分关键活动的延期导致整个项目进度延误，在关键活动之后增加相应的缓冲时间。

最后制定出进度计划表如图6.2所示：

![](images/5e612e9aae03337885fbd44c8d80b846bed624978a4075c40d5eb8682800c331.jpg)  
图6.2X项目进度计划表 Fig. 6.2 Schedule Plan of X Project

# 6.3X项目的进度管理效果

通过在X项目中运用第五章中提出的建议：制定沟通管理计划、提高信息发布效率，补充研发人员、合理安排进度计划以及提高项目进度控制能力等方法，X项目的执行过程比较顺利，项目的进度管理效果得到了公司领导的一致认可。

在之前的同类项目的实施过程中，员工普遍感到项目进度不清晰，预期完成时间可靠性较低，对项目的成败关心较少。同时也经常出现因为某些bug导致返工的情况，测试部门的抱怨较多。在实施了新的项目管理方式之后，项目进度按照预期计划进行，已经顺利进入试商用阶段，项目成员普遍对项目的执行情况感到满意。项目进度管理的改善主要体现在以下几个方面：

（1）项目成员普遍关心项目执行情况

每一个最基层的研发人员都非常清楚每时每刻项目的进行情况。所有详细信息都可以从在线文档上查看，并且被允许更新与自己相关的内容。同时还可以看到其他项目成员的进展状态，不仅了解了其他成员所负责的任务，所使用的技术，还可以开阔自己的视野，提高对产品的理解。项目成员就会自然而然的期望项目的顺利进行，关心着可能出现的问题，并主动提供力所能及的帮助，及时消除阻碍因素。

（2）实际进度保持在计划进度之前

虽然在一些关键环节预留了缓冲时间，但是活动负责人仍然按照自己的节奏完成项目活动，大多数情况下比较顺利，并未使用到缓冲时间。所以项目的实际进度在某些活动中是在计划进度之前的。偶尔出现了部分员工由于个别问题消耗了过多的时间，但是由于缓冲时间的存在和备份人员的支持，项目的进度并未受到影响。在项目经理的积极沟通和主动协调下，项目的实际开发工作完成时间超过计划时间。

# （3）产品质量比较稳定

由于在项目执行过程中，所有项目成员积极沟通，所有产品实现的细节和存在的风险都在每日早会上向全体成员通告，这使得负责测试的项目成员能够提前准备相应的测试案例，并且主动跟相关的活动负责人进行沟通，了解实现细节。同时协助活动负责人准备自测脚本，从而提高了每次提交的产品的质量水平。测试团队对研发团队所提交的产品质量的满意度提升，也提高了产品测试的效率，减少了返工的几率。

# 第7章总结与展望

# 7.1本文总结

移动互联网的飞速发展，催生了一大批新兴行业。创业门槛降低，创业公司如雨后春笋般兴起，给人们带来了越来越多的创新产品，丰富了使用场景，给人们的生活带来了极大的便利。然而，能够一直生存下去，并且能够获得盈利，是所有创业公司都必须面对的问题。所以，确保项目保质按时完成，不仅能够给公司带来利润，还能提升公司在行业内的信誉，是保障公司顺利发展的必要条件。

本文以S公司的项目进度管理为研究对象，以项目进度管理理论和方法为基础，结合S公司项目实际执行情况，通过面对面访谈以及问卷调查的方式搜集了导致项目进度发生延误原因，并对这些原因进行了深度的分析研究。根据S公司当前的企业状况，分别从项目需求变更方面、项目沟通方面、项目人员方面以及项目进度管理方面进行分析，分别找出可能导致进度延误的因素，并从相应的角度给出了解决这些问题的对策。本文的研究主要体现在以下几个方面：

（1）从需求变更的角度分析，发现不仅有多个原因导致需求在项目执行的过程中发生改变，而且面对众多需求变更的提出，缺乏审核和过滤。频繁的需求变更会导致项目周期一再拖延，对于项目的实施非常不利。所以，为了保证项目能够在合理的时间内完成并达到客户的需求，必须从一开始就做足准备，针对可能导致项目执行过程中发生变更的需求，提前分析和解决，提前将可预见的因素消除。然后加强需求变更的流程控制，过滤不重要的和不合理的需求变更，保留重要的和合理的需求变更并严格按照流程进行控制，降低对整个项目周期的影响。

（2）从项目沟通的角度分析S公司在沟通规划、管理和控制方面存在的问题。为了保证项目干系人对项目的进度信息有足够的了解，减少沟通中存在的障碍，必须向所有项目成员明确沟通规划，要求大家按照统一的规则交流信息，并且通过各种沟通方式提高沟通的效率，加快项目信息在团队中的流通效率，避免因为沟通导致项目延误。最后对沟通的效果进行监控，不断地优化沟通规划，提高管理能力。

（3)从项目人员的角度分析了人员数量不足的情况以及项目人员在个人能力方面的不足。创业公司给人的最大的印象就是比较辛苦，S公司也不例外。所以，招聘必须的人才加入公司并且提高员工的个人能力，是解决这个问题的根本办法。高负荷的工作对于团队的发展是不利的，关键能力的不足对于项目也是不利的。本文针对这两个问题给出了解决办法。

（4）从项目进度管理的角度分析了进度管理薄弱的原因。没有合理的进度计划和严格的控制流程，进度计划就会流于形式，没有指导意义。缺乏进度计划的指导作用，就会让项目处于失控状态，延期就自然而然的发生了。所以，首先要求必须合理地评估活动工期并且合理安排活动顺序，制定一个具有可行性的进度计划，然后利用进度管理工具，并赋予项目经理相应的管理权力，扫除进度控制过程中存在的制度问题、技术问题，使进度控制变的可执行、有效果，从而保障项目按期结束。

通过以上几个方面的原因分析给出对策，相信能够为S公司未来的项目管理提供有效的支持，降低项目进度延误发生的概率。

# 7.2未来展望

移动互联网的发展正处于黄金时期，越来越多的创业公司不断涌现，但是并不是每一个公司都能走到最后。每一个创业公司都面临着类似的问题，处理好成本投入与项目产出的关系，以尽量小的代价最快的速度将产品推向市场赢的用户的肯定，是每一个创业公司的目标。本文从项目进度管理的角度出发，对互联网软件企业的项目进度问题进行了调查和分析，提出了一系列的对策来解决可能导致项目进度延误的原因，希望能够给互联网软件行业的项目管理提供一定的参考，帮助互联网软件行业提高项目进度管理水平。

由于本文是以S公司作为对象进行的研究，所得出的研究成果并不一定能够应用于其他所有互联网软件初创企业。S公司有其特殊性：涉及从服务器技术、客户端技术到嵌入式技术等多种技术，涉及面较广；主要客户是各个城市的公交一卡通公司，这类公司成立时间较久，软件基础较老；所提供的服务与智能设备厂商密切相关。这一系列的特殊性，使得S公司的项目与其他互联网软件企业的项目有着较大的差异，可能会对本文所得出的研究结果的普适性有一定的影响。另外，本文所研究的问题点，也可能与其他互联网软件企业不完全相符，或各有其自己的重点。但是，本文的研究方法仍然对项目进度的管理具有一定的借鉴意义。对于互联网软件行业的其他类型企业的，建议可以在参考本文的基础上，进行更多其他方面的进度管理研究，以获得更具有广泛意义的研究结论，促进进度管理理论与方法的发展。

参考文献 [1]中国互联网络信息中心．中国互联网络发展状况统计报告[R].2017.1-2 [2]腾讯科技．于扬：所有传统和服务应该被互联网改变[EB/OL]. http://tech.qq.c0m/a/20121114/000080.htm,2012-11-14/2017-2-15. [3] Shih-PinChen, Yi-JuHsueh.A simple approach to fuzzy critical path analysis in project networks[J]. Applied Mathematical Modelling, 2008, 32(7): 1289-1297 [4] Francesco A. Zammori，Marcello Braglia, Marco Frosolini. A fuzzy multi-criteria approach for critical path definition[J]. Intermational Journal of Project Management,   
2009,3(27): 278-291 [5] Jianxun Qi, Xiuhua Zhao. Algorithm of Finding Hypo-Critical Path in Network Planning[J].Physics Procedia, 2012,(24): 1520-1529 [6]崔晓明，马力．软件项目进度控制方法研究[J].计算机工程与设计，2010,(12):   
2754-2761 [7] 汪若洋，熊选东，张亮忠，王松锋．基于微粒群优化算法的计划评审技术改进[J]．计 算机应用,2012,32(6):1734-1737 [8] Eugene David Hahn. Mixture densities for project management activity times: A robust approach to PERT[J]. European Journal of Operational Research. 2008, 2(188): 450-459 [9] Nasser Eddine Mouhoub, Abdelhamid Benhocine, Hocine Belouadah. A new method for constructing a minimal PERT network[J]. Applied Mathematical Modelling. 2011, 9(35):   
4575-4588 [10]Richard Graham Nelson, Amir Azaron, Samin Aref. The use of a GERT based method to model concurrent product development processes[J]. European Journal of Operational Research.2016, 2(250): 566-578 [11]周正龙,董雄报，左园．MIS 开发项目进度管理的关键链识别研究[J].科技管理研 究,2014,(19): 177-194 [12]Junguang Zhang, Xiwei Song, Estrella Diaz. Project buffer sizing of a critical chain based on comprehensive resource tightness[J]. European Journal of Operational Research.   
2016,1(248): 174-182 [13]别黎，崔南方．关键链动态缓冲监控方法研究[J]．中国管理科学,2010,(6):97-103 [14]Xuejun Hu, Nanfang Cui, Erik Demeulemeester, Li Bie. Incorporation of activity sensitivity measures into buffer management to manage project schedule risk[J]. European Journal of Operational Research. 2016,2(249): 717-727 [15]Annelies Martens,Mario Vanhoucke. A bufer control method for top-down project control[J]. European Journal of Operational Research. 2017,1(262): 274-286 [16]Sha Tao, Zhijie Sasha Dong. Scheduling resource-constrained project problem with alternative activity chains[J]. Computers & Industrial Engineering. 2017,(114):288-296 [17]Ercan Oztemel, Ayse Aycim Selam. Bees Algorithm for multi-mode, resource-constrained project scheduling in molding industry[J]. Computers & Industrial Engineering. 2017, (112):187-196   
[18]Ali A. Yassine， OmarMostafa, Tyson R. Browning. Scheduling multiple, resource-constrained, iterative, product development projects with genetic algorithms[J]. Computers & Industrial Engineering.2017,(107):39-56   
[19]Ripon K. Chakrabortty, Ruhul A. Sarker, Daryl L. Essam. Resource constrained project scheduling with uncertain activity durations[J]. Computers & Industrial Engineering. 2017,(112):537-550   
[20]Saber Elsayed， Ruhul Sarker, Tapabrata Ray，Carlos Coello Coello. Consolidated optimization algorithm for resource-constrained project schedulingproblems[J]. Information Sciences. 2017,(418,419):346-362   
[21]Bernardo F. Almeida, Isabel Correia,Francisco Saldanha-da-Gama.Priority-based heuristics for the multi-skill resource constrained project scheduling problem[J]. Expert Systems with Applications. 2016,(57):91-103   
[22]K. Nifo, G. Mejia, L. Amodeo. A Multi-Objective Dedicated Local Search for Project Scheduling Problem[J]. IFAC-Papers OnLine. 2017,12(49):875-880   
[23]Qi Hao, Weiming Shen, Yunjiao Xue， Shuying Wang.Task network-based project dynamic scheduling and schedule coordination[J]. Advanced Engineering Informatics. 2010, 4(24):417-427   
[24]Amol Singh. Resource Constrained Multi-project Scheduling with Priority Rules & Analytic Hierarchy Process[J]. Procedia Engineering. 2014, (69):725-734   
[25]Shen Xiao-Ning, Minku Leandro L.,Marturi Naresh，Guo Yi-Nan， Han Ying.A Q-learning-based memetic algorithm for multi-objective dynamic software project scheduling[J]. Information Sciences. 2018,(428):1-29   
[26]Shirin Akbarinasaji, Bora Caglayan, Ayse Bener. Predicting bug-fixing time: A replication study using an open source software project[J]. Journal of Systems and Software. 2018, (136):173-186   
[27]Ismaul Hassan, Naveed Ahmad, Behjat Zuhaira. Calculating completeness of software project scope definition[J]. Information and Software Technology. 2018,(94):208-233   
[28]Shalinka Jayatilleke， Richard Lai. A systematic review of requirementschange management[J]. Information and Software Technology. 2018, (93):163-185   
[29]Howard Lei， Farnaz Ganjeizadeh， Pradeep Kumar Jayachandran， Pinar Ozcan.A statistical analysis of the effects of Scrum and Kanban on software development projects[J]. Robotics and Computer-Integrated Manufacturing. 2017, (43):59-67   
[30]Xue-mei Xie, Guang Yang, Chuang Lin. Software development projects IRSE buffer setings and simulation based on critical chain[J]. The Journal of China Universities of Posts and Telecommunications. 2010,1(17):100-106   
[31]Jing Xiao, Xian-Ting Ao, Yong Tang. Solving software project scheduling problems with ant colony optimization[J]. Computers & Operations Research. 2013, 1(40):33-46   
[32]Project Management Institute.项目管理知识体系指南[M].北京:电子工业出版社,

2013.

[33]王克娜.项目进度管理[J].现代经济信息,2014,(18)：111[34]凯西，施瓦尔贝.IT项目管理[M].北京:机械工业出版社,2010.[35]毕春霞．甘特图在门诊导诊管理中的应用[J]．中国实用护理杂志,2010,(8):78-79[36]肖来元，吴涛，陆永忠，武剑洁．软件项目管理与案例分析[M].北京:清华大学出版

社,2009.[37]张琦，林碧英．浅谈软件开发过程管理中的进度管理[J].中国电力教育，2008,(S2):

99-101[38]陈川．航天B院D1武器系统研制项目的进度管理研究[D]．上海:华东理工大学,2016.[39]刘宇柯.基于CMMI的软件开发项目管理研究[D].广东:广东工业大学,2015[40]贾郭军．软件项目实施过程中的进度管理研究[J]．西安科技大学学报，2004，24(2):

221-224

# 附录A关于“S公司软件开发项目进度延误原因”的访谈提纲

# 访谈目的

了解S公司项目参与人员对导致项目进度发生延误的原因和看法。

一 访谈方式面对面的访谈

# 三、 访谈对象

S 公司研发部门的10位核心成员

# 四、提问提纲

（1）访谈开场语

你好，我现在在做一个关于本公司项目进度延误原因的调查和分析，可能需要耽误你几分钟的时间，分享一下你认为的可能导致项目进度延误的原因有哪些。下面有一些问题，希望你能够完全根据自己实际参与项目中的体会来回答。整个访谈的内容会对公司保密，仅用于论文研究，谢谢。

（2）访谈对话第一部分：对话部分

$\textcircled{1}$ 你的职位是什么？  
$\textcircled{2} 2 0 1 6$ 年一年内，你参与了哪几个项目？  
$\textcircled{3}$ 参与的项目中，有几个发生了延误？  
$\textcircled{4}$ 你认为导致这些项目进度延误的原因有哪些？  
$\textcircled{5}$ 请分别对刚才提到的几个原因，给出你的改善建议。

第二部分：访谈结束语非常感谢你的配合，谢谢！

# 附录B关于“S公司软件开发项目进度延误原因”的访谈记录

# 访谈一

# 访谈对象

杨X明

二、 访谈时间2017年3月3日

三、访谈人本文作者

# 四、访谈内容

（1）你的职位是什么？项目经理

（2）2016年一年内，你参与了哪几个项目？

华为的五个项目（华为岭南通、华为深圳通、华为北京、华为售后系统、华为手表），小米的五个项目（小米深圳通、小米岭南通、小米北京、小米武汉通、小米苏州通），乐视的三个项目（乐视北京、乐视深圳通、乐视岭南通），岭南通拉卡拉，自动化场测平台，魅族的六个项目（魅族深圳通、魅族岭南通、魅族北京、魅族武汉通、魅族苏州通）。

（3）参与的项目中，有几个发生了延误？

共参与了20个项目。只有六个是按期完成的，剩下的项目里面，有六个项目是延误完成的，有三个项目被终止了，有两个项目目前看来已经发生了延误，最后三个项目还在进行中。

（4）你认为导致这些项目进度延误的原因有哪些？

第一个就是客户的问题，他们对产品什么都不懂，都得我们教。之前定义的那些需求是他们自己理解的，根本不合理，后面又要求改。而且，他们的人也不了解业务，自己的服务器做的一塌糊涂。我们的服务做好了也不能跟他们联调。

第二个是我们的开发人员也不够。那么多城市，每个人都负责了好几个，找这个人说没空，找那个也说没空，任务没法安排。现场调试的时候，SE小组也不派人过去，现场遇到的问题解决起来太慢。

第三个是项目的预算不够。在项目执行过程中，总是会有费用支出的情况，比如手机路测的时候，不仅需要SE部门的同事去现场测试，还需要临时在手机上充值才能测。这些都需要费用，都需要消耗项目部的预算。预算不够的话，该出的差出不了，或者该准备的设备准备不来，都会影响项目进度。

（5）请分别对刚才提到的几个原因，给出你的改善建议。

对第一个问题，培养客户是少不了的，只能说，跟有经验的客户合作会顺利一些，跟没有经验的客户就麻烦一些。另外，就是对没有经验的客户，我们强硬一些，帮他们设计好产品架构，就按照这个来做就好了。商务也别什么都答应，按照我们的来就没那么多问题。

对第二个问题，主要是我们的人太少，而且很多都是没有经验，能力不足的人。出现什么大问题，没人能解决。所以公司得花大价钱招一些牛人进来。现在项目也多，牛人得招，普通还不错的员工也得招，要不然每个人都累的够呛，谁还能干活？

对第三个问题，需要适当的增加项目预算。未来需要充分考虑每一个项目会需要多少次的出差，因为差旅费占比比较大。并且预留一部分资金作为测试资金。现在这一块儿明显是出现了短板，如果预算跟不上，那就需要想其他办法来避免资金不足带来的问题。

# 访谈二

访谈对象 刘X

二、 访谈时间2017年3月3日

三、访谈人本文作者

# 四、访谈内容

（1）你的职位是什么？项目经理

（2）2016年一年内，你参与了哪几个项目？

腾讯手表上线北广深，飞亚达手表上线广州深圳，锤子手机上线北广深，一加手机上线北广深和苏州武汉重庆。

（3）参与的项目中，有几个发生了延误？

共参与了14个项目。基本都发生延误了。锤子手机和一加手机的项目，我们都完成了，但是比原计划的时间要长。飞亚达手表的项目还没做完，但是已经比计划时间晚了。腾讯手表的项目还在跟进，目前没什么问题。

（4）你认为导致这些项目进度延误的原因有哪些？

主要是客户的问题，飞亚达是做手表的，根本不懂移动支付行业，更不知道怎么做嵌入式安全模块，这块儿问题太多了。

出现延误的项目，比如腾讯手表，需要用蓝牙连接，我们公司就一个做安卓APP的，蓝牙开发也不熟悉，这块儿花了很长时间。一加想上的城市太多，他们自己没有规划好步骤，我们准备好了他们没准备好。还什么功能都想要，过两天又变了。我们只能按照自己的理解先给他做出来，要不然做不完了。

还有质量问题。交出去的东西不能老出问题。一个原因应该是研发部门没有做好自测工作。另一个原因可能是测试部门现在的测试能力也不够，容易遗漏测试点吧。

（5）请分别对刚才提到的几个原因，给出你的改善建议。

如果要继续做手表穿戴一类的产品的话，我们得多招几个懂安卓，懂蓝牙的人，否则无法应付。

像一加这样的手机厂商，开发能力不足，需求又搞不清楚的，我们最好做个功能全面的模板城市，引导手机厂商按照模板的功能来提需求，这样我们就不用每个需求都单独开发了。

对于质量问题，需要从研发部门，测试部门都要重视，制定一些必要的流程保证质量。

# 访谈三

# 访谈对象

刘X园

访谈时间2017年3月6日

# 三、 访谈人

本文作者

# 四、访谈内容

（1）你的职位是什么？SE应用开发工程师

（2）2016年一年内，你参与了哪几个项目？主要参与了各个城市的应用开发，并且提供支持。

（3）参与的项目中，有几个发生了延误？

我知道的有小米北京、一加苏州这几个出现了延误。但是总共有几个项目发生延误，不太清楚。因为很多项目我虽然参与了，但是大部分是支持性的工作，具体的项目进度我不知道。

（4）你认为导致这些项目进度延误的原因有哪些？

感觉我们的项目经理做的不太好，因为很多项目都需要我支持，但是又不跟我同步项目信息，也不明确我具体参与项目的工作量和时间，我就没办法把时间预留出来。所以每次来找我支持或者解决问题的时候，没办法及时提供，毕竟手头还有很多其他的事情要做。

（5）请分别对刚才提到的几个原因，给出你的改善建议。

项目经理应该把一个项目具体要做哪些事儿，大概什么时候需要做列出来。我的事情也很多，所以，有些需要我做的事情，只要安排好时间，我就好提前准备。要是出现项目延误的情况，及时通知到我，这样我可以立即调整接下来的任务顺序和优先级，最大程度上配合项目的顺利进行。

# 访谈四

访谈对象 谢X

二、 访谈时间2017年3月6日

三、访谈人本文作者

# 四、访谈内容

（1）你的职位是什么？测试主管

（2）2016年一年内，你参与了哪几个项目？每个项目多多少少都会参与，但是具体的工作有下面的人来做。

（3）参与的项目中，有几个发生了延误？总共有15个项目发生了延误，但是最终完成了。

（4）你认为导致这些项目进度延误的原因有哪些？

第一个原因是测试人员不够，而且测试能力不足。项目多，测试工作量大，目前还缺乏能够开发自动化测试工具的人员，所以部分测试案例是依靠手工测试的，所以效率较低。

第二个原因是沟通问题。项目经理或者研发人员提出的测试需求没有做到合理安排。有时候会有很多项目在短时间内反复提测，也给我们带来了很大的工作压力。如果有提测的需求，项目经理或者研发人员应该提前一定的时间给出警告，否则测试团队无法及时安排资源。

（5）请分别对刚才提到的几个原因，给出你的改善建议。

测试人员不够的问题，这个需要公司再给一些名额，尤其是有编程能力的测试人员的名额。我们需要提高测试效率，就需要从工具开发上入手，使用工具来代替人工操作。

沟通的问题，需要项目经理制定提测的规则，比如每个项目在什么时间节点才可以提测，提前给出提测预计时间，方便我们安排资源。项目的进度如果发生了延误，也要及时同步到我们这边，是否需要加班赶工，还是重新评估时间，都需要及早确认。

# 访谈五

访谈对象 王X巨

二、 访谈时间2017年3月6日

# 三、 访谈人

本文作者

四、访谈内容

（1）你的职位是什么？TSM主管

（2）2016年一年内，你参与了哪几个项目？

参与了所有公交上线的项目，包括一加、锤子、华为、小米等等手机厂商。上线的城市有广州、深圳、北京、苏州、武汉、重庆等。

（3）参与的项目中，有几个发生了延误？我所参与的项目中，有14个项目出现延误。

（4）你认为导致这些项目进度延误的原因有哪些？

与客户的沟通协调问题。因为TSM需要与客户的系统对接。客户的系统要是没有准备好，我们怎么急都没用。

客户的需求不固定，经常发生变化。前期方案沟通的时候确定的实现方法，在实现过程中会由于他们那边的问题改变。比如他们确定的应用标志编码规则就因为多个公交共存的问题多次变过。我们这边就需要根据要求修改。

（5）请分别对刚才提到的几个原因，给出你的改善建议。

与客户的沟通协调是项目经理的责任。项目经理需要明确我们的任务对客户方的系统的依赖是什么。不能只定我们自己的任务计划，客户的计划也得跟踪。否则一旦双方衔接不上，一方就会工作停滞。

确定的需求不能经常变。实在要变的，也必须经过我们的同意。内部必须考虑需求变动的合理性，不合理的就拒绝。合理的需求，加入的任务计划中，根据需要重新调整进度计划表。

# 访谈六

访谈对象 刘X辉

二、 访谈时间2017年3月7日

三、访谈人本文作者

# 四、访谈内容

（1）你的职位是什么？TSM服务器开发工程师

（2）2016年一年内，你参与了哪几个项目？参与了华为小米和一加的项目，应该有15个左右。

（3）参与的项目中，有几个发生了延误？

华为的项目只有一个岭南通发生了延误，小米的基本都延误了，一加的项目全部延误。

（4）你认为导致这些项目进度延误的原因有哪些？

客户的需求变动是主要原因。这里面华为做的好一些，华为会考虑的比较周全，做的时候完全按照最初的设定实现。一加做的最差，什么都不懂，需求都乱提。

其次就是人员不够的问题。我自己负责了这么多项目，需要关心的事情太多，每天基本没时间做项目以外的事情。

还有就是，项目的情况同步的太少，平时主要精力都在具体的事情上，根本不了解项目的进展情况，所以经常出现多个项目交叉争抢资源的情况，我只能一个一个做。

（5）请分别对刚才提到的几个原因，给出你的改善建议。

客户的需求需要控制，项目经理要挡一下，不能提什么就做什么。

继续招人，有些项目很成熟了，可以招一些新人承担。这样我也可以腾出时间做些其他的事情。

项目经理要定期同步项目进度，特别是与自己相关的项目和任务，才好规划接下来的工作任务和优先级。

# 访谈七

# 访谈对象

夏X刚

访谈时间2017年3月7日

三、 访谈人本文作者

# 四、访谈内容

（1）你的职位是什么？平台开发工程师

（2）2016年一年内，你参与了哪几个项目？岭南通工厂模式发卡平台、自动化场测平台、华为售后平台和华为手表的项目（3）参与的项目中，有几个发生了延误？华为手表的项目延期了，其他三个项目按期完成。

（4）你认为导致这些项目进度延误的原因有哪些？主要是因为我对手表底层和蓝牙相关的技术不熟悉，边学边开发，效率比较低。

其次是由于我们的部门划分非常清晰，每个部门只关系自己所负责的任务。但是往往一个项目，一个任务会涉及到的技术知识是跨部门的，所以当我有问题去请教其他部门的时候，往往得不到很及时的帮助。如果我们一个组内的人员能够具备各方面的知识的话，那么我请教组内人员会方便很多。

（5）请分别对刚才提到的几个原因，给出你的改善建议。

要么招有相关经验的人做他擅长做的事情，要么给我足够的时间让我学习和成长。时间紧，又不熟悉相关技术，肯定会延误。

另外建议可以将项目相关的所有人拉到一个组内，并且强调小组的目标是完成项目，所有如果项目实施过程中有任何问题，所有组员都有责任和义务去主动、协助解决。这样会对项目的执行有很大的好处。

# 访谈八

访谈对象 王X

二、 访谈时间2017年3月8日

三、访谈人本文作者

# 四、访谈内容

（1）你的职位是什么？公交集成开发工程师

（2）2016年一年内，你参与了哪几个项目？北京、苏州、重庆在各手机上的上线。共11个项目。

（3）参与的项目中，有几个发生了延误？

有6个项目发生了延误。没有延误的是华为和小米上线北京的项目。因为北京的优先级比较高，资源向北京倾斜。

（4）你认为导致这些项目进度延误的原因有哪些？

资源不足。我们服务器的同事，每个人身上都同时承担着好几个项目，经常发生资源紧张的情况。只能是优先完成重要的项目，其他项目缓一缓。

沟通问题。每个人同时参与多个项目，需要把工作安排的合理紧凑并且不耽误项目进度不容易。所以我们需要非常清楚每个项目当前的进度，存在的问题，各种预期事务的时间点等。知道这些，才能有助于我们合理安排手头的任务和时间，甚至是加班。否则当事情来临的时候，就已经晚了。

部门划分问题。服务器的同事普遍对SE的内容不了解，所以项目执行的时候，需要经常跨部门去请教 SE 团队的同事一些问题。由于是跨部门，效率较低，导致问题解决的速度很慢。如果这两个部门合并，或者能有同一个人安排工作的话，效率会提高很多。（5）请分别对刚才提到的几个原因，给出你的改善建议。

资源不足的问题，招人是首先能想到的办法，但是新人还需要培训，短期内也承担不了太多事情。另外，就我们现在这几个人，也都有成长的空间，所以可以增加内部培训，提升团队成员的能力，可以承担更多的任务。

沟通的问题，我建议是定义好沟通的渠道，不管是邮件也好，短会也好，只要渠道和方法确定了，把需要沟通的内容确定了，就能确保所有人对项目的理解一致。

部门划分问题，是跨团队沟通问题，需要领导层做出决策，合并团队是最好的方案。

访谈九

# 访谈对象

吴X林

访谈时间2017年3月8日

# 三、 访谈人

本文作者

# 四、访谈内容

（1）你的职位是什么？公交集成开发工程师

（2）2016年一年内，你参与了哪几个项目？武汉和深圳通的接入项目，分别在华为、小米、一加、锤子等手机上上线。

（3）参与的项目中，有几个发生了延误？除了华为深圳通的项目之外，其他都有不同程度的延误。

（4）你认为导致这些项目进度延误的原因有哪些？

小米一加在深圳通的上线项目延误，一个是因为客户也是刚刚上线手机产品，很多问题都是没有遇到过的，所以解决起来不太顺利。另外也是因为我刚到公司不久，这些项目刚刚接触，很多内容还不熟悉，需要学习。所以项目做起来没那么快。等华为上线深圳通的时候，我对深圳通的上线流程已经比较熟悉了，而且很多东西都是做过的，所以做起来比较顺利。

其他方面的原因，可能还有因为手头上的事情比较多，哪个急就做哪个，所以没人催我的项目，就拖的后面一点。没有总体的规划和安排。在评估完成时间的时候，也估的不准确。

（5）请分别对刚才提到的几个原因，给出你的改善建议。

从个人的角度来说，技术和业务方面还需要继续学习，包括现在公司使用的各种基础架构、相关原理和常用做法，都得学习。

从公司角度来说，最好能够安排资深的员工给我们做个培训，并且留下培训资料，这样可以加快我们对公司产品和技术的了解和学习。同时项目经理也应该更加清晰的告知各个项目的时间点，方便我们安排手头的工作。

# 访谈十

# 访谈对象

郭X兵

访谈时间

2017年3月8日

# 三、访谈人

本文作者

# 四、访谈内容

（1）你的职位是什么？公交集成开发工程师

（2）2016年一年内，你参与了哪几个项目？主要是岭南通的项目，对岭南通的支持，在各个设备上上线。

（3）参与的项目中，有几个发生了延误？

岭南通拉卡拉的项目和岭南通工厂模式发卡平台的项目是按期完成的，其他设备上线的项目都有延期。

（4）你认为导致这些项目进度延误的原因有哪些？

沟通的问题吧，因为这么多手机厂商，有华为、小米、一加、锤子、魅族，还有拉卡拉手环，很多项目都是在并行的。做了这个就顾不了那个。也没有什么地方可以查看某个项目当前的情况，只能问项目经理。

还有就是项目进度管理做的不好，开始定的进度计划表太粗，后面项目执行的时候，差距又太大，越往后越没有价值。最真实的情况还是得问项目经理。即便是进度延误了，项目经理不来问，我们也不会主动提，越拖越严重。

（5）请分别对刚才提到的几个原因，给出你的改善建议。

还是要明确项目的信息存放在哪儿，或者通过什么方式同步给我们，并且要保持足够实时。我需要关心的是跟我负责的城市相关的，每个设备厂商的状态是什么，需要我什么时候准备好什么东西去跟他们联调。

进度计划表得准确，不管是发生了什么延误的情况，还是更新了计划，都得体现在进度计划表中，并且固定一种方式和渠道，定期反馈项目情况，保证项目不脱离控制。

# 附录CS公司项目进度延误原因调查

1．工作年限[单选题]○ 一年及以下〇一年到三年○ 三年到五年〇 五年以上

2.工作职责[单选题]

测试服务器开发  
O Applet开发  
O 运营项目管理其他

3．最近是否遇到工作无法按时完成的情况？[单选题]

O 是否O

4．你认为导致项目延误的原因是哪些？[多选题]

□需求变动频繁  
□项目人员数量及能力不够  
□沟通不畅  
□项目进度管理薄弱  
□ 项目预算太少  
□质量管理不严  
□部门划分不合理  
□客户的原因

5．你觉得还有哪些原因导致了项目进度的延误？[填空题]

6.你是否有过为了在期限前交付产品而降低质量要求？[单选题]

O 是否0

7．你希望从项目经理那里获得什么信息和帮助？[多选题]

□同步项目的进展  
□ 提出与自己相关的需求  
□告诉自己做什么事儿  
□协调所需资源  
□控制需求变动  
□其他

# 致谢

本文是在我的导师杨洪涛副教授的悉心指导下完成的。杨老师的学识渊博、治学严谨、态度谦和，在他的项目管理课程教授以及论文指导过程中，使我受益匪浅。从文章最初的论文课题的选择、开题报告的撰写以及文章的机构安排等，老师都给予了非常耐心细致的指导。在此我向我的导师杨洪涛副教授表达由衷的感谢！

此外我还要感谢华东理工大学的各位老师，在工商管理课程学习期间，教授了很多专业知识和实战理论，这些在我论文写作过程中都发挥了很大的作用。同时还要感谢我的同学们，在这三年的学习生活中给我提供了很大的支持和帮助。

感谢S公司提供的大量的项目资料以及鼎力支持，使我的论文能够顺利完成。

感谢家人一路的陪伴和鼓励，使我有毅力和决心反复地修改和优化论文。

最后向百忙之中评审本文的各位专家老师表示衷心的感谢！

# 卷内备考表

# 本卷情况说明

本卷共 柒拾贰 页

立卷人检查人立卷时间