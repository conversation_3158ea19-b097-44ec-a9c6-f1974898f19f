# The Pipeline for the Continuous Development of Artificial Intelligence Models - Current State of Research and Practice

Monika <PERSON> $^ \mathrm { a }$ , <PERSON> $^ { \mathrm { a , b } }$ , <PERSON> $\mathbf { c }$

$^ a$ University of Innsbruck, 6020 Innsbruck, Austria $^ { b }$ Blekinge Institute of Technology, 371 79 Karlkskrona, Sweden $c$ Software Competence Center Hagenberg GmbH (SCCH), 4232 Hagenberg, Austria

# Abstract

Companies struggle to continuously develop and deploy Artificial Intelligence (AI) models to complex production systems due to AI characteristics while assuring quality. To ease the development process, continuous pipelines for AI have become an active research area where consolidated and in-depth analysis regarding the terminology, triggers, tasks, and challenges is required.

This paper includes a Multivocal Literature Review (MLR) where we consolidated 151 relevant formal and informal sources. In addition, nine-semi structured interviews with participants from academia and industry verified and extended the obtained information. Based on these sources, this paper provides and compares terminologies for Development and Operations (DevOps) and Continuous Integration (CI)/Continuous Delivery (CD) for AI, Machine Learning Operations (MLOps), (end-to-end) lifecycle management, and Continuous Delivery for Machine Learning (CD4ML). Furthermore, the paper provides an aggregated list of potential triggers for reiterating the pipeline, such as alert systems or schedules. In addition, this work uses a taxonomy creation strategy to present a consolidated pipeline comprising tasks regarding the continuous development of AI. This pipeline consists of four stages: Data Handling, Model Learning, Software Development and System Operations. Moreover, we map challenges regarding pipeline implementation, adaption, and usage for the continuous development of AI to these four stages.

Keywords: continuous development of AI, continuous (end-to-end) lifecycle pipeline for AI, MLOps, CI/CD for AI, DevOps for AI, multivocal literature review   
2022 MSC: 08-09, 99-00

# 1. Introduction

An increase in available data and computing power, as well as improving algorithms, allow exploring the options of AI in many different application fields. AI and its subcategories, Machine Learning (ML) and Deep Learning (DL), enable new intelligent products and services to achieve a specific goal [3]. To harness the power of AI, it is necessary to deploy and integrate AI models into production systems and to assure the quality of the resulting continuously evolving and self-adapting systems [F24, 46, 47, 38]. However, quality assurance requires thorough attention to guarantee safe and reliable behaviour and increase the accountability and responsibility of the involved AI systems [38, 47, 13, 23]. AI characteristics such as the inherent non-determinism lead to a certain degree of uncertainty [46, 47, 38].

One possible solution for ensuring quality during the development of AI are automated end-to-end CI/CD lifecycle pipelines [32]. These pipelines are well established in traditional software development however need more research when adapting them to AI models because these pipelines not only need to handle code but also data and the model itself in addition to a large system-level complexity [7, I18]. These pipelines focus on automating and monitoring all phases of system development, such as the integration, testing, and deployment, as well as the infrastructure management.

These pipelines for the continuous development of AI are currently highly researched, where a synthesis of the current research provides an evidence-based foundation of the established work to avoid misconceptions, discover gaps in the knowledge field and assist research in exploring the phenomenon with further studies. Thus, the main goal of this paper is to systematically identify relevant conceptual ideas, as well as synthesize and structure research in the area of pipelines for the continuous development of AI. Three research questions (RQ) have been derived from this overall goal, which we answer in this paper via a Multivocal Literature Review (MLR) and follow-up interviews with practitioners from academia and industry.

1. Which terms are commonly used to describe a pipeline for continuous development of AI? How do these terms differ in their specific meaning?   
2. Which tasks have to be handled by pipelines for the continuous development of AI? What are possible triggers for starting the pipeline for continuous development of AI?   
3. What are potential challenges when implementing, adapting, and using a pipeline for continuous development of AI?

The remainder of the paper is structured as follows: Section 2 presents necessary background knowledge on continuous software engineering and DevOps. Furthermore, this section also provides an overview of related work with regard to the pipelines for the continuous development of AI and describes the novel contribution provided by this paper. Section 3 explains the information extraction and taxonomy creation methodologies. Section 4 introduces the terminology, triggers, taxonomy, and challenges. Section 5 applies TensorFlow Extended (TFX), a lifecycle pipeline for AI, to the proposed taxonomy. Section 6 handles the threats to validity and Section 7 concludes the paper and introduces future work.

# 2. Background and Related Work

# 2.1. Continuous Software Engineering and DevOps

This section broadly specifies the main background knowledge required for this paper. Firstly this section covers the general terms continuous software engineering, CI/CD, and DevOps followed by a more detailed description of these terms in the context of AI. For a more detailed description of the AI related terms, please see Section 4.1.

According to the established roadmap for continuous software engineering by Fitzgerald and Stol [8], continuous software engineering describes the continuous development lifecycle, which includes continuous practices and concepts, such as Continuous Integration (CI), Continuous Delivery (CD), continuous delivery and Development and Operations (DevOps) [8]. CI is a process that focuses on integrating code changes to the main software repository while automatically ensuring software quality [I57, I69, I40, 43]. CD describes the tasks after CI and delivers or releases the new and tested software features to a staging or test environment [12, 18, I69, I62]. Continuous Deployment requires that CD already deployed the software to some environment other than production to ensure that the software can be continuously and automatically deployed to the production environment and to the actual users [18, I69, 8]. CI/CD for AI are techniques to automate the deployment process for AI models [F13, F75] (see Section 4.1.2.

DevOps is a continuous software development approach that includes several principles and practices, such as CI, CD, and continuous deployment to manage a software system lifecycle. The term consists of Development (Dev) and Operations (Ops). Dev uses agile methods, such as Scrum or Kanban, and allows a self-directed and self-organized software development with several teams [I59, I69, 19]. Ops includes the tasks necessary to run an application, such as infrastructure management [I69]. DevOps for AI not only takes into consideration traditional software development but focuses on the added complexity of AI development, such as data handling [F59] (see Section 4.1.1.

MLOps expands DevOps and takes into consideration the added complexity of developing ML based applications [I34] (see Section 4.1.3.

The (end-to-end) lifecycle management describes the handling of specific tasks for the continuous development of AI, which starts with data collection and finishes with the deployment and monitoring of the AI model [F1, F16, F76] (see Section 4.1.4).

Continuous Delivery for Machine Learning (CD4ML) is the technical implementation of MLOps concept to automate the pipeline [F44, I54] (see Section 4.1.5).

# 2.2. Related Work

Over the past years, a large number of publications focused on the topic of pipelines for the continuous development of AI (e.g., [F69, 7, I44, F44, I32, 1, 36, 21, 36, F2, 27, 50, 24]) as well as data handling [33, 41, 5]. In Table 1, we list work that is most closely related to our study regarding the continuous development of AI due to their similar methodology (Systematic Literature Review (SLR) and Multivocal Literature Review (MLR)). The table describes the scope and research goal of related studies, and it indicates how the work maps to the three research questions addressed by our paper.

Table 1: Overview of related work ( $\bullet$ indicates full or partial coverage of targeted RQs)   

<table><tr><td>Reference</td><td>Research method</td><td># pers Pa-</td><td>period</td><td>terms tasks chal-</td><td>lenges</td><td>ReviewRQ1: RQ2: RQ3: Differences in scope or goal</td></tr><tr><td>[18] Karamitsos et al. (2020)</td><td>SLR</td><td></td><td></td><td></td><td></td><td>applied traditional DevOps practices to AI</td></tr><tr><td>[15] John et al. (2021)</td><td>MLR</td><td>29</td><td>1.2010- 8.2020</td><td>.</td><td>.</td><td>context:edge/cloud/hybrid architectures</td></tr><tr><td>[16] John et al. (2021)</td><td>MLR</td><td>19</td><td>1.2015- 3.2021</td><td>.</td><td></td><td>maturity model based on MLOps</td></tr><tr><td>[F42] Lwakatare et al. (2020)</td><td>MLR&amp; interviews</td><td>8</td><td></td><td></td><td></td><td>how well CD is applied to ML-enabled systems</td></tr><tr><td>[6] Figalist et al. (2020)</td><td>SLR&amp; framework</td><td></td><td></td><td>.</td><td>.</td><td>context: ML-based software analytics/BI solutions</td></tr><tr><td>[25] Lo et al. (2021)</td><td>SLR incl. grey lit.</td><td>231</td><td>1.2016- 1.2020</td><td></td><td></td><td>context: federated learning</td></tr><tr><td>[34] Nascimento et al. (2020)</td><td>SLR</td><td>55</td><td>1990- 2019</td><td></td><td>1</td><td>relationship between SE practice &amp; AI development</td></tr><tr><td>[29] Mboweni et al. (2022)</td><td>SLR</td><td>60</td><td>2015- 2022</td><td></td><td></td><td>term MLOps and main themes in literature</td></tr><tr><td>[9] Fredriksson et al. (2020)</td><td>SLR</td><td>43</td><td>before 12.2019</td><td></td><td></td><td>context: (semi-） automatic labelling of data types for ML</td></tr><tr><td>[48] Testi et al. (2022)</td><td>SLR</td><td></td><td>2015- 2022</td><td></td><td></td><td>classify pipeline types, challenges for AI development in pipeline</td></tr><tr><td>[21] Kolltveit et al. (2022)</td><td>SLR</td><td>24</td><td>after 2015</td><td></td><td></td><td>operationalise AI</td></tr><tr><td>[22] Kreuzberger et al. (2022)</td><td>SLR&amp; interviews</td><td>27</td><td>before 5.2021</td><td></td><td>.</td><td>preprint only:principles and profession for realizing MLOps</td></tr><tr><td>[26] Lorenzoni et al. (2021)</td><td>SLR incl. grey lit.</td><td>33</td><td>1.2010- 6.2020</td><td></td><td></td><td>preprint only;applicability of SE and ML per author, no aggregated taxonomy of tasks</td></tr><tr><td>[53] Xie et al. (2021)</td><td>systematic405 mapping study</td><td></td><td>before 7.2020</td><td></td><td></td><td>preprint only; mapping study of AI model lifecycle management (no focus on tasks)</td></tr><tr><td>Our study</td><td>MLR&amp; interviews</td><td>151</td><td>2010- 2021</td><td></td><td></td><td>AI pipelines: definition of terms, taxonomy of tasks, challenges</td></tr></table>

The following section provides an extensive analysis of the mentioned related work from Table 1.

Firstly, several related studies [18, 15, 16, F42, 6, 9, 21, 22, 26] are based on a limited amount of identified primary sources, not covering relevant insights from the wide range of existing literature. Our study is based on a comprehensive analysis including over 150 papers.

Secondly, some literature reviews in related work focus on a specific application context such as edge/cloud/hybrid architectures [15], ML-based software analytics and business intelligence applications [6], or federated learning [25]. In contrast, our study covers the full scope of AI models, independently of a specific application context.

Thirdly, a range of different research questions are investigated in related studies, not or only partially related to the definition of terms (RQ1), pipelines for the continuous development of AI and triggers for starting the pipeline (RQ2), and pipeline-related challenges (RQ3).

1. Definition of terms: SLR or MLR based papers in the identified related work do not elaborate on the definition of terms for continuous development of AI. Mboweni et al. [29] and Kreuzberger [22] provide a foundationbased definition on MLOps. Definitions for related terms (e.g., CI/CD for AI) are not considered.

2. Pipelines for the continuous development of AI: Related studies target various different approaches and research goals. For instance, Karamitsos et al. [18] base their CI/CD pipeline for AI on a literature review focusing on ”traditional” DevOps principles, not covering AI specific tasks due to the non-existence in DevOps pipelines. Generally, less emphasis is placed on tasks necessary to develop AI continuously. John et al. [16] proposes a MLOps maturity model consisting of tasks for data handling, development and release of the ML model. Lwakatare et al. [F42] executed a MLR to identify how well CD is applied to ML-enabled systems and proposed levels of automation, where the first level indicates the manual process and the fifth level is the fully automated and integrated process where CD is incorporated into the ML workflow process [F42]. Fredriksson et al. [9], for example, also executed a SLR but only cover approaches to label different data types to be used for supervised training. Testi et al. [48] summarize different types of MLOps pipelines, such as ML-based software systems, ML use case applications, ML automated framework where tasks of this automated framework are briefly summarized. Kolltveit et al. [21] do not consider all required tasks for generating an AI model but focus on operationalising the model via packaging, integration, deployment, serving, inference and monitoring and logging. Kreuzberger et al. [22] cover principles within technical components (e.g., reproducibility achieved via feature store) and required professions to build the pipeline. Lorenzoni et al. [26] identified which software engineering processes and practices can be applied to solve issues arising during the development of ML models. However, the paper does not contain an in-depth analysis of the CI/CD phases, and how developers implement them. Moreover, their paper does not consider continuous execution of such phases, as described in this paper. Xie et al. [53] executed a systematic mapping study to identify demographic data, such as when and where the papers were published, which research methods were applied, and which subtopics were covered in the literature. However, they did not focus on the continuous development tasks but used search terms focusing on characteristics of the lifecycle, such as traceability, reproducibility, guidelines, and transparency.

As indicated, DataOps is also addressed in related work by Rodriguez et al. [41], Munappy et al. [33], and Ereth [5] with the focus on the first step of the pipeline for the continuous development of AI, namely data handling. With this paper, we expand their work by investigating all necessary tasks of the entire pipeline including the deployed and monitored of an AI model.

3. Triggers: No information regarding triggers of the pipeline for the continuous development is covered by the related SLRs and MLRs.

Pipeline related challenges: Challenges covered by related work mostly focus on the development of AI in general, but not on the pipeline itself. For instance, Nascimento et al. [34] illustrate the relationship and dependencies between software engineering practices based on the SWEBOK knowledge areas and respective challenges regarding the development of AI systems. Kreuzberger et al. [22] focus on organisational, ML system, and only very superficially on operational challenges for adopting MLOps, which is the core of this paper’s challenges. Figalist et al. [6] identify challenges during prototyping, deployment and update, but they specifically focus on AI models for software analytics and business intelligence. Testi et al. [48] illustrate AI specific challenges which do not focus on the implementation of the continuous pipeline, such as data labelling. Kolltveit et al. [21] cover exclusively challenges regarding operationalising the AI model.

Related work, which does not follow the same methodology as our study also focuses on challenges occurring throughout the pipeline for the continuous development of AI systems. These studies, however, identify general challenges of individual tasks during the development process of AI applications. For instance, Paleyes et al. [37] identify data collection as a challenge, covering issues regarding related storage location and understanding the data set’s structure. Baier et al. [2] differentiate challenges occurring during the pre-deployment (e.g., data structure, data quality, and governance) and deployment (e.g., detecting and handling data drifts) as well as non-technical challenges (e.g., expectation management, trust, transparency). Lewis et al. [24] only briefly mention general challenges regarding ML system development, summarized to data management, modelling and operationalisation, with the aim to evaluate how well available tools are able cope with these challenges.

# 3. Methodology

In the following, this section covers the three employed research methods (MLR, taxonomy creation strategy and qualitative analysis) to derive the main research contributions. Figure 1 illustrates a general overview of the research design consisting of a MLR proposed by Garousi et al. [10] to identify existing literature, taxonomy development method based on Usman et al. [49] to map the identified aspects in literature to a taxonomy, and the interviews’ deductive category definition based on Mayring [28]. These research methods are further explained in the following sections.

![](images/41c94479f7a12fe288e0f690f9f2585948bbd537673613eca1a6cfce2141fc83.jpg)  
Figure 1: Study design for MLR based on [10], taxonomy development method based on [49], and deductive category definition based on [28]

# 3.1. MLR

A MLR was executed to provide a thorough overview and aggregated evidence of important perspectives of pipelines for the continuous development of AI. This allows not only to include published literature but also allows to include grey literature. Grey literature is essential because using a lifecycle pipeline for AI is an emerging research topic in software engineering where formal literature has not been sufficiently published yet [10]. The following approach is based on the paper ‘Guidelines for including grey literature and conducting multivocal literature reviews in software engineering‘ provided by Garousi et al. [10].

Table 2 depicts the general selection criteria that apply to both selection processes of formal as well as informal sources.

Table 2: General selection criteria for formal and informal sources   

<table><tr><td></td><td>Inclusion Criteria</td><td>Exclusion Criteria</td></tr><tr><td>Year of pub- 2010-2021 lication</td><td></td><td>before 2010</td></tr><tr><td>Language Accessibility</td><td>English Full text needs to be accessi-Parts of source available ble</td><td>Any other language</td></tr><tr><td>Relevance</td><td>swer the main research ques- tions (e.g. Does the source fo- cus on pipelines for the con- tinuous development of AI)</td><td>Relevant information to an-Sources that focus on us- ing AI to implement DevOps such as Artificial Intelligence for IT Operations (AIOps) or which focus only on team re- lated processes (e.g.team collaboration)</td></tr></table>

Table 3 depicts specific selection criteria for formal sources. Based on these criteria we derived 37 formal sources as the start data set. Afterwards, we followed Wohlin’s proposed backward and forward snowballing procedure [51]. We derived the final data set that comprises 79 papers between the 15th of April and the 30th of May 2021. When citing formal sources, this paper uses the prefix F in combination with a number.

In order to retrieve formal/scientific sources, we executed an initial exploratory search with several search terms (see 3.1 to collect a start data set [10]. We followed the guidelines proposed by Kitchenham [20] and Wohlin and Jalali [51, 14]. We used Google Scholar as the search engine to retrieve an unbiased start data set. According to Yasin et al. [55] results from Google Scholar in combination with grey literature extracted from Google sufficiently extracts necessary sources similar to searches with other databases, such as ScienceDirect, IEEE, ACM digital library and Springer Link.

Table 4 depicts the specific selection criteria for informal sources. Based on these criteria we derived informal sources between the 31st of May and the 26th of June 2021. When citing informal sources, the prefix I is used in combi

Table 3: Selection criteria for formal sources   

<table><tr><td></td><td>Inclusion Criteria</td><td>Exclusion Criteria</td></tr><tr><td>Quality cri- teria</td><td>Primary peer-reviewed sources</td><td>No review process, secondary studies</td></tr><tr><td>Search strat- egy</td><td>Google Scholar</td><td>Other databases</td></tr><tr><td>teria</td><td>Stopping cri-First 100 search hits</td><td>Search hits after</td></tr></table>

nation with a number.

We adopt quality assessment criteria to select the sources based on their relevance and to check whether the informal literature search results are valid and free of bias [10]. We derive the sources based on the proposed general web search engine strategy where we use conventional web search engines [10]. Regarding the stopping criteria, we selected two strategies. Firstly, effort bounded looks at a predefined number of search engine hits. Secondly, theoretical saturation identifies whether no new concepts emerge from additional search results. Theoretical saturation was not achieved in seven out of twenty cases.

Table 4: Selection criteria for informal sources   

<table><tr><td>Inclusion Criteria</td><td>Exclusion Criteria</td></tr><tr><td>teria</td><td>Quality cri-Fulfill Garousi et al.&#x27;s [10] deviation of Garousi et al.&#x27;s quality assessment criteria [10] quality assessment crite- ria</td></tr><tr><td>Search strat-General web search engine egy (Google, YouTube)</td><td>Specialized databasesand websites (e.g. Stackover- flow)，contacting individuals directly</td></tr><tr><td>Stopping cri- Effort bounded: Google (100 teria search hits)</td><td>Searchhitsaftereffort search hits)，YouTube (12 boundedandtheoretical saturation are fulfilled Theoreticalsaturationre- quired,otherwise additional hits searched:Google (50 search hits)， YouTube (20 search hits)</td></tr></table>

Figure 2 illustrates the used ten search terms which were based on the established terms of continuous software engineering [8]. Additional search strings were extracted during an informal pre-search. Example for the used search strings are Artificial Intelligence AI AND Continuous Integration CI, Machine Learning Operations AND MLOps, and Machine Learning Operations OR MLOps.

![](images/8b0f3a258c8ff17419962b874532ee76b761fe42e984d9075820ee9dd36bc779.jpg)  
Figure 2: Search terms used for the Multivocal Literature Review (MLR)

The MLR identified 151 relevant sources, out of which 79 papers (approximately $5 3 \%$ ) were formal sources and 72 informal sources. The extraction process and the retrieved formal and informal literature were documented in a systematic map which is available online [44].

To extract the necessary categories from the literature, a we executed a descriptive qualitative synthesis. Kitchenham et al. [20] require to document the extracted information in a tabulated and consistent manner based on the previously defined research questions. For further information on the tabulation of the extracted information, please refer to [44]. We derive further subcategories of the research questions as suggested by Stol et al. [45] and create the taxonomy as proposed by Usman et al. [49]. We adopted Stol et al.’s [45] coding strategy, namely open and axial coding to break down, examine, compare, conceptualize, and categorize information. We base the categories on a previous pilot study. These categories were closely related to the terms used in continuous software engineering, hence commonly accepted within the field [49]. Because the predefined set of categories did not cover all tasks handled with the pipeline for the continuous development of AI, we added additional categories via an iterative process.

After selecting the source and extracting necessary categories, we executed a test-retest process proposed by [20] to evaluate the rater’s data extraction consistency. Further information is provided in Section 6.

# 3.2. Taxonomy Creation

We categorize the extracted data via a taxonomy based on the revised taxonomy creation strategy proposed by Usman et al.’s [49]. Firstly, we defined the units of the classes/categories which are based on DevOps phases because they are commonly accepted within the field. We add extracted information to the respective class/category via a qualitative approach [49]. For the classification structure type, we use a a facet approach because research on pipelines for the continuous development of AI applications is still a new and evolving field. The facet approach allows us to easily adapt the taxonomy if further research is done on this topic. The identified facets comprise the stages Data Handling, Model Learning, Software Development and System Operations.

# 3.3. Qualitative Analysis

We check via a qualitative approach if the derived information from the literature is correct and comprehensive enough to provide a thorough depiction of existing knowledge. For this, we conducted interviews because they allowed us to explore and understand individual experiences from a sample by outlining the complexity and diversity of the observed environment [31]. To select the interview participants, we adopt a stratified sampling technique [40] with the three groups illustrated in Table 5.

Based on the selection criteria, we identified nine participants. Table 6 provides an overview of the involved participants.

The semi-structured interviews included introductory questions, questions about different definitions of pipelines for the continuous development of AI, tasks handled via these pipelines, and an evaluation of the proposed taxonomy as well as challenges when implementing, adapting, and using such a pipeline. We conducted the interviews between the 30th of July and the 10th of September 2021 with an average duration of 49 minutes. We analysed the interviews according to Mayring [28] with the summarizing qualitative content analysis. We adopted the deductive category definition to extract information regarding the pipeline. In addition, we categorized the information on theoretically derived aspects from the MLR [28]. Thus, we derive the coding agenda from the identified stages and tasks from the previously mentioned taxonomy creation. By doing so we unambiguously assign the participants’ statements to the identified categories [28].

Table 5: Defined categories for the stratified sampling   

<table><tr><td></td><td>Category 1</td><td>Category 2</td><td>Category 3</td></tr><tr><td>Area of ResearchAcademia</td><td></td><td>Industry</td><td>Industry (Start-ups)</td></tr><tr><td>Experience with Yes AI</td><td></td><td>Yes</td><td>Yes</td></tr><tr><td>Experience with lifecycle pipelines</td><td>Yes</td><td>Yes</td><td>Should know concept of continuous lifecycle pipeline</td></tr><tr><td>Generalexperi-on lifecycle ence</td><td>Published work</td><td>Extensive &amp; regular deployment of AI AI development developers management for AI(no data scientists)</td><td>and deployment</td></tr></table>

<table><tr><td>pant</td><td>Partici- Knowledge pipelinesforthecon-</td><td>tinuous developmentof</td><td>about</td><td>Expe- rience</td><td>Industry</td><td>Region</td><td>Cate- gory</td></tr><tr><td>A</td><td>AI Team lead for AI initia-5.5 tive un- &amp; supervised al-years gorithms</td><td></td><td></td><td></td><td>Social Me- dia</td><td>America</td><td>2-In- dustry</td></tr><tr><td>T</td><td>Research projects: contin-10 uous software engineering practices for traditional and AI software</td><td></td><td></td><td>years</td><td>Academia</td><td>Finland</td><td>1 Aca- demia</td></tr><tr><td>Z</td><td>Sales Engineer for MLOps1 year platform,AI development</td><td></td><td></td><td></td><td>MLOps software</td><td>America</td><td>2-In- dustry</td></tr><tr><td>P</td><td>Team Lead AI application engineering</td><td></td><td></td><td>2 years</td><td>ysis</td><td>Text anal-Austria</td><td>3-In- (Start- dustry</td></tr><tr><td>R</td><td>ment analysis, speech as-</td><td>Technical Team Lead for3.5 ML deliveries (e.g. senti-</td><td></td><td>years</td><td>Automotive Germany 2 - In-</td><td></td><td>ups) dustry</td></tr><tr><td>B</td><td>sistant) for services and solutions):years AI innovations &amp; image</td><td>Research (Area manager5.5</td><td></td><td></td><td>Research/ Consul- tancy</td><td>Austria</td><td>2 - In- dustry</td></tr><tr><td>C</td><td>processing Implementation tinuous</td><td>pipelineforthecon-years development</td><td>of4</td><td></td><td>Modeld.io: MLOps pipeline</td><td>America</td><td>3- In- dustry (Start-</td></tr><tr><td>V</td><td>Kubernetes &amp; ML Research(Seniorre-3 search project manager):years pipeline for the contin-</td><td></td><td></td><td></td><td>Research/ Consul- tancy</td><td>Austria</td><td>2 -In- dustry</td></tr><tr><td>D</td><td>Regulatory compliance in2 MLOps&amp; Certification</td><td>body for AI</td><td></td><td>years</td><td>Academia (Health- care)</td><td>Finland</td><td>1 Aca- demia</td></tr></table>

Table 6: Overview and background of interviewed participants

# 4. Results

This section presents the information obtained from the literature and interviews. Firstly, the different terminologies are elaborated, followed by identified triggers to start/restart the pipeline. The following section elaborates on the created taxonomy which describes the pipeline for the continuous development of AI and included tasks. The final subsection explores challenges regarding the implementation, adaption and usage of pipelines for the continuous development of AI.

# 4.1. Terminologies

The following subsections describes several terms, their main characteristics and differences, including (1) DevOps for AI, (2) CI/CD for AI, (3) MLOps, (4) (End-to-End) Lifecycle Management, (5) CD4ML. Figure 3 illustrates the main characteristics of the terms. All terms share a common understanding and describe the automation for the continuous development and improvement of AI models via pipelines. The interviewees indicated that the differences between the terms are unknown in practice. According to three of the interviewed participants, the terms describe the process of adapting the standard software lifecycle to AI modelling in order to minimize the time between iterations and to ease the whole process of continuously developing and deploying AI models.

# 4.1.1. DevOps for AI

Some authors use the term DevOps for AI to describe ’[...] methods for managing the software lifecycle’ [F59] as stated by Rausch et al. It is seen as a standard for modern software development to ensure higher data, as well as code quality [F59, I8]. DevOps for AI is a concept and agile practice to reduce time and resources between deployment iteration cycles [F59, F33, I8].

# 4.1.2. CI/CD for AI

CI/CD are key enablers or techniques for DevOps to stabilize, optimize and automate the deployment process of AI models [F13, F75, I7]. According to

![](images/a67992f4a9127315946c6483b9ae390985d71d320b7f045ea3a52166b83230a8.jpg)  
Figure 3: Summary of terms - DevOps for AI, CI/CD for AI, MLOps, (end-to-end) lifecycle management, and CD4ML

Karlas et al. CI/CD supports the ’[...] deployment to the infrastructure used to serve models in production’ [F33]. Thus, CI/CD takes into consideration not only validating and testing code and components, but also handles the (semi-)automatic and iterative validation and testing of data, data schemas, and models [I34, F33].

# 4.1.3. MLOps

According to Google Cloud MLOps ’[...] appl[ies] DevOps principles to ML systems’ [I34] and participant Z and R confirm that MLOps is an extension of DevOps. According to Raj et al. it is an $\ ' [ \ldots ]$ emerging method to fuse machine learning engineering with software development’ [F58]. This statement is underlined by other sources as well [F58, F45, F64, F44, F76, F46, I44]. Breuel defines it as following: ’MLOps is a set of practices that combines ML, DevOps and Data Engineering, which aims to deploy and maintain ML systems in production reliably and efficiently’ [I8].

The key difference between other terms is that MLOps strongly takes into consideration the company’s culture and illustrates how cross-functional teams, such as data analysts, system operators, as well as data and software engineers collaborate via a harmonized process [F64, F54, F32, F46, I67, I59, I61, I27].

This statement was confirmed by participant T and R.

Similar to DevOps for AI, MLOps helps with the continuous, quick, seamless and reliable deployment of multiple AI versions which are deployed in a heterogeneous and distributed environment via infrastructure and tools [F58, F14, F46, F24]. A new practice, called Continuous Training (CT) is introduced that according to Google Cloud $\dot { \left[ \dots \right] }$ is concerned with automatically retraining and serving the models’ [I34]. Therefore, CT uses collected feedback and production data [I34, F33, I34, I2, I36].   
The AI model quality is strongly dependent on the used data sets and the model is only a small part of the entire software system [I69, I68, F44, I22, I39, I1, I44]. This statement was underlined by participant Z.   
According to the Google Cloud documentation [I34], MLOps can be divided into three different levels of maturity depending on the degree of automation. Another definition by Microsoft was identified during the interviews where five levels of technical implementation of MLOps are defined1.

# 4.1.4. (End-to-End) Lifecycle Management

This term includes the word management, which describes the handling of specific tasks of the continuous development of AI [F70, I26]. Essential management tasks start with data collection and finish with AI model deployment and monitoring in production [F1, F16, F76, F19, F48], as verified by participant T. Vartak et al. further specifies tasks included in the model management such as ’[...] tracking, storing and indexing large numbers of machine learning models so they may subsequently be shared, queried and analyzed’ [F70]. Based on these tasks, the (end-to-end) lifecycle for AI models describes a well-fitted pipeline that should achieve the best possible quality and stability of the AI components via several iterations until the model cannot be improved any further [F33, F16, F49, F50]. During these iterations, several data sets, artifacts, models and application configurations are created, which need to be

managed, searched, shared and analysed [F70, F76]. For example, it is crucial for Brumbaugh et al. to $\ ' [ \ldots ]$ have correct values for the features that correspond to the timestamp of the labels’ [F16].   
Several authors use the term continuous pipeline or workflow to describe the automatic execution and reiteration of tasks to ensure the lifecycle management of AI [F7, F50, F76, F40, F8]. According to Barrak et al. a ’[...] pipeline of tools [...] automate[s] the collection, preprocessing, cleaning and labelling of data.’ [F7]

The term end-to-end lifecycle management benefits from the idea of automation for the whole model lifecycle. The term uses well-established concepts from software development to cope with many model iterations, such as DevOps in combination with CI/CD [F76, F5, I5] and MLOps [I39]. However, in contrast to MLOps, lifecycle management does not focus on the interpersonal collaboration between different teams [F48].

# 4.1.5. CD4ML

CD4ML is a technical implementation of the MLOps concept to automate the pipeline for the continuous development of AI [F44, I18, I50, I49, I54]. Therefore, CD principles are used to span the AI lifecycle management and apply them to AI applications [F44, I16, I65]. Participant T as well as the extracted information from the literature identify that this term is proposed, promoted and heavily influenced by Thoughtworks [F44, I59, I10], which defines CD4ML as $\ ' [ \ldots ]$ a software engineering approach in which a cross-functional team produces machine learning applications based on code, data, and models in small and safe increments, that can be reproduced and reliably released at any time in short adaptation cycles’ [I50].

# 4.2. Triggers

The following section discusses four trigger types, including (1) feedback and alert systems, (2) orchestration service and schedule, (3) repository, and (4) other triggers. AI models need to be iteratively adapted and retrained to provide reliable quality in production over a long period of time [F9, F8, I7, I3]. Therefore, according to Moesta et al. [I35] and two participants (R and Z), context-specific triggers depending on the AI model, business requirements and retraining strategies exist that start or restart the pipeline. For example, triggers may take into consideration the optimal threshold where the benefits obtained by an updated (i.e., retrained) model outweigh the effort involved in the retraining [F9, I14, F35, F65, I43]. Participants R and D indicated that it is a trial and error process to minimize resource consumption where several different team members identify the appropriate approach. Thus, triggers combining different approaches may also be feasible [F59, F58].

# 4.2.1. Feedback and Alert Systems

Collected feedback during runtime or alerts may be used to trigger the pipeline [F22]. Three interviewees identified data events as a potential trigger, whereas information extracted from literature also covers data and model changes [F59, I63, I52].

A monitoring system monitors and collected data from production to trigger the pipeline in case of irregular data events such as data updates and data drifts [F15, F8]. Data drifts occur when the distribution within the data set changes [I16, F71, F25, I13]. This occurs when data varies due to seasonal changes, or any other insertion, deletion or update of data values [I7, F45, F38, F9, F76]. The interview participants highlighted the deletion of data. For example, participant T mentioned that due to privacy restrictions and the data regulation requirements in Europe, users have the right that their associated data is forgotten. Thus, according to participant T ’ [...] it is only fair that the deleted data is no longer used in the ML model’. Data updates may also happen if the shape of the data, such as table or constraint definitions, may change due to schema updates based on software updates, requirement changes or migrations [I16].

According to participants T, R and D, data updates should improve the model. To avoid triggering the pipeline continuously, triggers may occur periodically or when a specific threshold is attained [F2, F56, F15]. Similar to the results obtained from the literature, it is ambiguous for participant D what the appropriate amount of new data to change the outcome of a model is. According to participant R, the changes need to be extensive enough to significantly impact the model. Not mentioned in the extracted literature’s information, however, indicated by participant R, is that event streaming platforms, such as Apache Kafka or other event hubs, may be used to semi-automate the triggering process.

Model updates may be triggered due to the deterioration of the model’s performance, and scores in production below a specific threshold, also called model or concept drift [F45, F76, F30, F39, I45, I66]. It occurs when the problem the model was designed to solve changes, and this problem needs to be reformulated [I63, I58, I46]. Technical performance scores indicating a trigger are throughput, latency, and the utilization of a Graphics Processing Unit (GPU) [F75, I7]. Two participants (Z and C) use these performance metrics as triggers.

# 4.2.2. Orchestration Service and Schedule

Additional triggers are an orchestration service or a scheduled time [F59, F16, I42]. For example, one participant triggered their pipeline once a week. On the one hand, one may argue that fixed schedules hinder the pipeline to be reactive enough or needless pipeline executions are triggered [F8]. On the other hand, schedules help to optimize the retraining frequency, allocation of computing resources and execution order of pipeline jobs, which is especially important if edge and cloud resources are involved [F11, I34, F39, F60].

# 4.2.3. Repository

Repository updates are used as traditional triggers to guarantee that the latest changes are tested and available to the users. For example, pull requests to the repository as a commit or merge requests identify changes to the data, model or code [I5, I54, I10, I13, I38, I55]. When using this approach, participant T indicated that the data set and the appropriate code should be in the same system and under the same source control.

# 4.2.4. Other Triggers

Although manual triggers are sparsely elaborated in the collected literature [F42, I42, I31, I5], three out of nine participants use triggers that involve human interaction to identify if sufficient data is available. One of the reasons is that the new data needs to be labelled manually. Another reason is that humans can better estimate if their model requires retraining and if the improved quality still satisfies the user’s needs.

Another possible trigger is a change of the infrastructure, hardware, or architectural constraints to maintain the performance and functionalities of the AI model [F39, F29, I26, F72]

# 4.3. Pipeline

This section covers the four pipeline stages including their tasks when triggering the pipeline. The four stages are (1) Data Handling - executing data handling, followed by the stage (2) Model Learning - implementing the model development, the stages (3) Software Development - building the AI application, and(4) System Operations - focusing on a smoothly running system and information collection in production. Figure 4 illustrates these stages as a taxonomy that we derived from the literature and the interviews. However, Garcia et al. [F25] as well as two participants (P and T) emphasized that the pipeline and task execution strongly depends on the individual context, e.g. organizational policies for running the pipeline, and whether implementing the tasks outweigh the costs. In four out of nine participants’ organizations (A, B, C and V), the pipelines are not fully automated.

![](images/25d3329a063064ca7771cf62fe2bf89499de2d46afcb97191af96333cbdc6057.jpg)  
Figure 4: Continuous lifecycle pipeline for AI applications adapted from [F40] and [F69]

As depicted in Figure 4, the pipeline is not linear but relies on feedback loops throughout the process. This is a key characteristic in agile development and allows to include continuous feedback to improve the AI model and the collection of relevant data from production [F2]. For example, collecting data in production improves the training data set which ultimately increases model quality and efficacy [I29].

Ensuring quality is an integral part of the whole engineering procedure carried out in several steps such as data, model and system-specific tests [F45, I32]. The intention of this paper is not to give an all-encompassing picture of quality assurance techniques used for AI. It only depicts approaches which are applicable for continuous pipelines and which we identified during the MLR. In addition, the scope and types of tests strongly vary from implementation to implementation.

# 4.3.1. Data Handling

The stage Data Handling covers the end-to-end lifecycle of data curation. Not only allows the pipeline to handle tasks more efficiently, but also the quality of a AI model strongly depends on the data availability, quality, and preprocessing techniques [F2, F57, F6, I63]. The data pipeline manipulates the initial data via intertwined tasks, such as data preprocessing, testing, versioning, and documentation, until the data can be used for model training [F57]. A study conducted by Hummer et al. [F28] indicates that the data handling uses 7% of the total execution time, but this time can be reduced due to parallelized computing procedures [F54, F7, F49, F28, F16]. This is possible because workflows may be specified as a Directed Acyclic Graph (DAG) [F54, F7, F49, F28, I59].

Initially, data is prepared for model design and training [I63]. Therefore, data collection including data injection, preparation, labelling and feature extraction needs to be executed to transform the raw data. This step is often defined in form of rules part of a script that defines how the raw data should be manipulated, transformed and compared [F45, F42, I63]. Some steps may be skipped, if the data set was already preprocessed in previous iterations [I46]. It is essential that the data handling and transformations undertaken during data preprocessing in the pipeline are consistent with the data handling in production to avoid a training-serving skew [F9, I70, I9, I40, I31]. Three authors proposed to use TFX, an end-to-end lifecycle management platform provided by Google, to avoid the training-serving skew by exporting the tasks for data transformations that are again used in production and the training and serving pipeline does not need syncing [F9, F56, F52, I70]. However, sometimes deviations in the data preprocessing pipelines is desirable because data set and its size differs, persistent data stores provide the data for training whereas data in production is non-static where data needs to be processed fast [I31, I64].

Data can be collected from multiple distributed on-premise data centres, external public or private cloud storage [F64, F40, F6, F16, F57]. The data sets may be already available (e.g., open source or internally available) or needs to be collected from multiple devices where the data may be stored in different formats, such as tabular data, logs, key-value stores or input files [F2, F32, F16, F1, F33, F29, F55]. In cases where not enough data can be extracted, three interviewees (P, B and V) stated that they synthetically generate data to balance the data set. If too much data is available, the data set is reduced where participant A mentioned the risk of introducing bias.

The preparation strongly depends on the type of data [F29, F58, F64, F16]. For example, pipelines may discard incomplete or irrelevant data or outliers and noisy records [F58, F2, F32, F63, F51, I51], anonymize data [F51], (windowed/bucketed) aggregate data [F16], or decompress and resize images, of filter and tokenize text [F58, F64, F15]. In addition, numeric data may be normalized via feature scaling [I42, F57]. The MLR only extracted the z-transformation and Box-Cox transformations [F6] for data preparation but did not extract further information on the technical details, algorithms or implementations of the data preparation tasks.

Data labelling is necessary for supervised learning as indicated by participant $\mathrm { P }$ and C. Therefore, each record receives a meaningful ground truth label indicating the expected output. Other learning techniques, such as reinforcement learning, use demonstrations as labelled data [F2, F32]. To automate this process, the lifecycle management platform ease.ml for example uses a model runner which identifies corresponding labels for input features [F33]. Unsupervised algorithms do not require labels, thus this task can be skipped [F57, F27]. For feature extraction or feature engineering, necessary patterns need to be manually or automatically discovered and extracted, which is strongly dependent on the AI model’s context and algorithm used [F58, F64, F2, F16, F6, F31, F63]. Automatic feature selection techniques include, e.g., particle swarm optimization [54], recursive feature elimination, principal component analysis [17], and auto encoders [35]. For supervised learning, extracting relevant features guides the model training and identifies which features are worth exploring as input for the AI model [F33, F6, F31, F55]. One important part the pipeline should provide consistent feature extractions of the offline and online inference environment, which may be achieved by a point-in-time correctness. This is necessary because for the training data set a vector consisting of features and associated labels is used. If features from the future are used in the vector which are not part of the labels, data leakages may occur [F16, F56].

# Data Quality Assurance

The pipeline runs continuously, thus, reusable components for testing data are necessary to avoid introducing bugs in the data and propagating bugs down to the model training [F9, I64, F15, F4]. This makes it easier to identify faulty data in the beginning, before it negatively influences the model and computation resources are wasting for producing inadequate results [I64, F15, F55, F5, I59]. Participant R and B emphasized that the better the data quality is, the better the model results are.

Data and feature validation validates batches of data. These batches can either be evaluated via a single-batch or inter-batch validation. Single-batch validation assumes that data batches collected in succession do not differ drastically and comply with a specific shape [F15, F55]. Thus, a stable description, such as a data schema, identifies expected features, feature types and values, and the correlation of different features [F9, F56, F15, I48]. Participant C also uses manually or automatically created data schemas. Information provided in this schema are the expected type, presence, valency [F9, F15, F18], and distribution of categorical values [F55, I7, I64]. Inter-batch validation identifies differences between different batches, such as training and serving data or successive training data sets [F15]. Inter-batch validation identifies changes in the statistical characteristics or the encoding of feature values, such as using Boolean values instead of anticipated $\mathit { 1 }$ and $\boldsymbol { \mathit { 0 } }$ [F18, F15, F31, F55]. Statistical characteristics are discovered via calculating the distribution distance between training and serving data. Therefore, a distance threshold is calculated to identify if the deviation results in an error. The distance is calculated via two distributions including its probabilities [F18]. Other examples for calculating the statistical characteristics are Kullback-Leibler divergence, cosine similarity, or statistical goodness-of-fit tests. These approaches are implemented in TFX[F15]. Participant A and R check the right shape of data as common validation practice.

Data can also be validated via a data quality measurement which identifies how suitable the data is to meet the users’ requirements [F4]. Data quality can be measured via the six dimensions of data quality, namely completeness, uniqueness, consistency, validity, accuracy and timeliness [F64, F2, F4, I44]. Completeness identifies how well the used fraction of the data set represents the corresponding main data set or real-world data. Uniqueness states that no duplicates occur in the data set. Consistency identifies the extend to which semantic rules of a data set are violated. Validity identifies if all data values comply with a specific format. Accuracy defines how well the data is suited and certified to execute a specific task. Timeliness describes if the data set is up-to-date for a task [I44]. Another way of identifying if the available data is sufficient to meet the user requirements is an automatic feasibility analysis proposed by ease.ml. It calculates a bayes error based on already extracted features which are used for training. The Bayes error estimator identifies the minimum error rate achievable by any classifier. Then, this error is compared to a desired target performance, such as the accuracy. If ease.ml identifies the data set as insufficient, the user can clean up the data manually based on a list of dirty examples, or collect more data [F1, F61, F62, I44].

Unit tests can consist of tests that verify that the data ingestion works correctly [F40]. Unit tests can either state the input, execute a transformation and state the expected output [I48] or use data schemas. These tests identify if there are differences between the schema and the assumptions in the code [F15].

# Data Versioning

AI models require massive amounts of data for model training which needs to be stored and versioned to guarantee traceability and compliance with regulations, such as the General Data Protection Regulation [F76, F69]. Participant C, V and D highlighted the need for a full data lineage if important models are based on this data. Hence, not only data and its dependencies but also the data processing steps need to be versioned. In practice, however, storing the entire lineage of data is fairly impossible due to space restrictions. Thus, only the latest version is stored in participant P, T and Z’s case and the previous versions are suspended.

The results of the MLR did not explicitly focus on the data storage location, such as cloud or in-house storage. Participant V and D indicated that they stored their data in-house due to the user’s preference and regulations regarding for private data, such as patient records.

Data is stored via uniquely identified data snapshots or via reference to the original raw data set [F22, F33, F2, F20, F50]. If all snapshots cannot be stored due to space constraints, the delta in the data set can be versioned [I32].

Because traditional version control systems, such as Git, cannot handle the amount of data [F7, F20, F30], new systems and tools, such as Data Version Control (DVC) have been introduced. DVC can store large files due to an external storage which can be combined with Git via a lightweight metadata file including a hash to indicate the data set version and data set location. The metadata file is then tracked via Git [F7, I49, I37]. Several space constraints occur with edge devices where data transfer costs should be avoided. Thus, appropriate locality-aware data stores store the data lineage [F60].

In addition, dependencies, data processing steps and extracted features should be versioned. This allows to compute different versions of the data set if required. Dependencies can store the relationship between a data set which was used for training or testing and the ML model version [F42, F76, I46, F5]. Miao et al. [F50] and participant T suggest versioning the data processing steps including applied code and metadata [F42, F2, I59]. Extracted features are stored in a centralized repository, such as feature stores, where the definition, access and storage of the features is standardized [I34, I23]. For instance, the lifecycle management platforms used by Uber (Michelangelo) [I23], Facebook (FBLearner) [I13] or Bighead [F16] provide feature stores. This avoids to repeatedly extract feature sets where similar features may have different definitions [F76, F16, I70].

# Data Documentation

Documentation includes guidelines with concrete actions such as feature cleaning or naming conventions applicable to data files or folders [F55, F35, I7]. Documentation should be extensive enough to support auditability of AI models. Auditability defines a reviewing process where responsibilities and potential risks associated with the usage of an AI model are identified and root causes can be analyzed in case of a failure. Auditability for AI still is an emerging topic that is missing generally established mechanism and regulations [F14]. Although documentation is essential, in practice only one-third of the participants rely on manual data documentation, whereas the others do not document their data related information due to missing guidelines and software support.

# 4.3.2. Model Learning

After data handling, the pipeline executes tasks associated with the AI model learning, such as model design, model training, quality assurance and improvement. Further tasks comprise metadata capturing, versioning of the model and its dependencies, and documentation. According to Zhou et al. [F76], this stage and its respective tasks are most essential throughout the pipeline for the continuous development of AI.

Design

Firstly, the pipeline should support taking decisions regarding the model design, such as the appropriate hypothesis [F47, I41] or regarding the selection of reusable model components which align with the problem domain [F27, F47, I64]. Pipelines should support context-specific decisions on the best suitable algorithm, feature selection, hyper-parameter setting, data set split, and potential reduction of the training data set [F44, F2, F32, I64, I57]. The MLR results did not reveal the concept of Automated Machine Learning (AutoML) within pipelines which takes over design decisions, such as finding an appropriate algorithm, model selection, data selection, and parameter tuning. Participant C illustrated that modeld.io ease the decision making process by providing AutoML.

# Model Training

The pipeline implementation of the model training is dependent on the architecture, distribution and amount of available computation resources [F39, F54, F76] and context-specific algorithms, such as unsupervised learning, deep learning, and reinforcement learning [F58, F33, F10, F31, F63, I42]. Two participants identified that TFX and MLFlow provide the necessary implementations or support for using AI libraries.

AI Model Quality Assurance The pipeline for the continuous development of AI aims to provide faultless, reliable and secure software and to guarantee a trusted decision space [F58, F40,

F39]. Thus, fail-safe measures need to be introduced via relevant, sufficient and repeatable tests to cover all potential cases due to the continuous pipeline [F23, F43, F23, F8], which is especially important if models’ decisions impact human’s well-being or can cause harm [F58, F43, I6, I16], as in participant D’s case.

Due to these repetitive tests, test data sets are used more often which may lead to overfitting. Overfitting occurs when the model’s version gets adapted throughout the pipeline cycles to pass the test [F1, F61, F33, I44, I45]. To test the model for overfitting, three metrics may be used, such as measuring the difference between the validation and test data set [I25], the Akaike information criteria or the Bayesian information criteria [F73].

However, in practice automated tests are not always possible. For instance, participant D indicated that experts had to validate the model’s decisions individually. Participant $\mathrm { P }$ also stated that they executed quality assurance tests manually due missing configurations in the pipeline but they planed on automating the tests in the future.

In AI the goal is to optimize a specific metric throughout the pipeline’s lifecycles instead of simply satisfying functional requirements [F74]. For instance, the pipeline compares statistical evaluation metrics with metrics from previous model versions [F51, F74, I47, F5, I48]. Participant A and P endorsed to use traditional quality metrics for AI, such as F1 scores, precision and accuracy. Another approach mentioned by participant A is a Normalized Discounted Cumulative Gain for measuring the effectiveness for their search engine model. Gerostathopoulos et al. [F26] proposed a learnability metric specifically adapted for using it during the CI/CD of AI models. Learnability is measured via five SCORE learnability facets, such as solution quality, convergence, overhead, robustness and effect. This SCORE metric was specifically adapted for using it during the CI/CD of AI models [F26, F52].

The financial technology sector requires fair models [I24, I1], thus Huang et al. [I24] established an ethics-by-design metric for the model quality assurance in the continuous development pipeline. This metric uses approaches for feature explainability2, such as Local Interpretable Model Agnostic Explanations (LIME), Partial Dependency Plots (PDP), and SHapley Additive exPlanations (SHAP), to identify whether and how much each feature contributes to the final prediction [F14, F73, I49]. In practice, interviewee R, A and B stated that they did not include checks for bias in the pipeline’s model quality assurance due to their non-critical domains.

# Model Improvement

The extracted sources from the MLR do not identify any new pipeline-specific model improvement techniques that specifically focus on previous lifecycles. Pipeline implementations use established context and model specific improvement techniques. For instance, pipelines support model compression or pruning [F28, F34, F12], model hardening [F27, I11, I20], and hyper-parameter optimization [F68, F31, F12, F30, I12].

# Metadata Capture

Metadata comprise provenance information which is necessary to govern the modelling lifecycle as well as to receive additional information on the data set and AI model [F48, F27, F5]. For example, model specific metadata comprises relevant information on the execution and deployment of AI models, network architecture [F19], training [F40, F42, F19, F70], instance metadata and runtime metadata [I31, F28, F75, F19]. Further metadata includes information about the model’s location, registration data, and information on the start and end data [F40, F42, F45, F58, F39, F16]. Although feature extractors provided by several lifecycle management tools, such as TFX automatically extract metadata [F27, I64].

# Model Versioning

Model versioning not only captures version model artifacts but also model dependencies to backtrack or reproduce different model versions that quickly evolve over time [F70, F48, F49, F50, F37, I7, F53, I34, F25]. Although literature highlights the importance of model versioning, several aspects why this is not possible were mentioned by three participants (A, P and B). They indicated that they did not store all models but only stored key model versions due to resource restraints or lack of interest in previous versions. In addition, Granlund et al. [I18] stated that they cannot store model versions due to strict guidelines regarding the patient’s data and missing isolated, in-house hardware resources. The inability to store model versions was not solved by any of the extracted pipeline tasks.

Model dependencies capture the relationship to related elements, such as the associated data set, source code and configuration files. This allows to recreate or load a specific model without having to rerun multiple iterations to find the appropriate model [F30, F40, F49, F50, I32]. Additionally, model versioning stores the associated log files [I33] and evaluation results of a model. This allows to check whether the model versions improve continuously throughout the continuous lifecycle [F40, F43, F33, F70].

Because AI model versioning is more complex and requires more storage capacities due to the continuous development, standard version control systems, such as Git cannot be used as model repositories [I8]. Potential alternatives are container registries where images are versioned [I56] or model repositories that store model versions including code, metadata, test results and dependencies [F19, I59, I35]. During the interviews, participants proposed storage facilities of MLFlow, H2O, DataRobot and Git Large File Storage.

Model Documentation

Documentation proves that the model adheres to the regulations and restrictions and works as expected. This is required to receive certifications, which is especially important in the healthcare sector according to participant D. However, seven out of nine participants identified model documentation as a good practice however they hardly ever create and maintain an up-to-date and consistent documentation. Based on the MLR results, software support for the model documentation during the pipeline is not established yet.

The MLR however, already established the information necessary in the model documentation. For instance the documentation should outline the purpose (e.g., requirements and hypothesis) and the different methodologies to achieve the set purpose (e.g., technical decisions, such as chosen model and algorithm design) to effectively mitigate AI related risks [I59, I21]. Commonly, decisions made in the model creation need some rework. Thus, it is essential to document which model and test design decisions have already been tried out and which results were achieved [F25, F63]. Additionally, documented assumptions explain the reached decisions [I21].

# 4.3.3. Software Development

After the model is developed, it must be prepared for deployment. Therefore, the pipeline orchestrates the stage Software Development and its related tasks, such as packaging, system-level quality assurance as well as system versioning. Package The build process packages the code and model logic into build artifacts which are deployed in production [F71, F75, F38, I31]. The best model is selected and registered in the model registry [F40].

Several authors propose transforming the registered model into a systemindependent, deployable and hardware-optimized format [F43, F75, F17]. Potential formats established in the pipeline packaging task are ONNX, SavedModel, TensorRT, Predictive Model Markup Language (PMML), and Portable Format for Analytics (PFA). ONNX $^ 3$ is an open, standardized format built that represents AI models by using a common set of operators and file format to allow interoperability and serialization of models [I42, I63]. When the pipeline handles Tensorflow models, it is transformed to SavedModel and TensorRT format [[F43, F75]. Another possible format is PMML4, which describes statistical and data mining formats and transforms them into an Extensible Markup Language (XML) configuration file [F17, F74, I63]. The PFA $^ 5$ is a model interchange format that provides a safe execution environment for AI algorithms [I63].

An essential task in this stage is to containerizes the model and its dependencies to avoid compatibility issues [F19, F21, F39, F38, F17, I11, I7, F5]. Extracted sources most often mention Docker [F21, F39, F38, F17, F54, F20]. In addition, three interviewees (participant R, C and V) confirm using Docker extensively. Research on other approaches is sparse, however essential for increasing efficiency in cloud computing [I11]. Thus, future research suggests using Virtual Machine (VM)s, especially lightweight VMs, type 1 hypervisors, or uni kernels also called library operating systems [I11].

Software-level Quality Assurance According to the paper ‘Hidden Technical Debt in Machine Learning Systems‘ [42], AI models represent only a small part of the whole system landscape where many data sources and software applications interact [F9, I24, I18, F31]. Software-level quality assurance should identify the correct behaviour of the whole system landscape before the system is deployed in production [I50, I48, I19]. Participants D and R agreed on the importance of this type of quality assurance whereas participant C expressed some concerns because the integration and respective testing are not model related and should be handled via a different CI/CD pipeline.

The continuity of the pipeline requires automated software-level quality assurance tests to frequently execute the tests and compare the results. For instance, integration tests check if different services work together correctly [I40], or check whether the obtained model prediction is correctly transferred to the whole system by comparing this test to results of the model quality assurance [F40, I33, I28]. The pipeline can automatically execute compatibility checks between interfaces and the Application Programming Interface (API) endpoints [I10, I49], which is essential according to participant R when AI models are integrated as microservices. Automated stress and robustness tests identify whether the whole software can perform under expected conditions [F59, I30] by evaluating operational metrics such as throughput, latency, and resource usages [I48]. For example, Uber’s end-to-end lifecycle management platform Michelangelo uses an internal benchmarking system to profile certain software parts. This allows measuring how quickly inferences are run for a specific model based on real-life data [I19].

However, extracted sources reveal shortcomings in the automation of available test strategies to efficiently use them during the pipeline. For instance, because user acceptance tests require human involvement [I40, F23, I17], Fehlmann and Eberhard [F23] tries to solve it via Quality Function Deployment (QFD). This strategy collects customers’ expectations and needs and generates a test coverage matrix where a support vector machine generates test cases [F23].

Versioning

Packaged models and the respective quality assurance results of the software level are stored [I18, I7, I19]. In addition, it is also necessary to version the pipeline and its associated tasks [F28, F16, I59]. For instance, TFX versions the pipeline as an artifact or source code regarding the implemented pipeline tasks [I2, I50] Additionally, ModelDB stores the pipeline as a sequence of actions in a relational database [F70]. GitHub repositories may store an Azure DevOps pipeline [I46]. Moreover, the used pipeline version references the associated model versions or vice versa [F16].

Documentation

Documenting information about the development stage was not handled by any paper from the literature review but was mentioned by participant T. He proposed to document the quality assurance process and associated outcome and any additional information necessary for a software release. The participant raised the research gap on software tools automatically handling and updating the documentation.

# 4.3.4. System Operations

The proposed framework’s stage System Operations handles the deployment of the AI model into the system landscape and handles the continuous monitoring of the data, model, and system.

Deploy

The deployment is an essential task to make the model available to others, enable collaboration and avoid knowledge silos [F39, F69]. But before deploying the model, the first three out of four criteria need to be fulfilled.

1. All preceding pipeline tasks are executed successfully, such as the quality assurance test suite [F76, F40, F1, F31].   
2. The pipeline identifies the best model that is not necessarily the newly trained model [F58, F54, F52, F34]. However, the research gap arises how to compare model versions fairly and without any bias. Not only does the pipeline need to be aware of whether the validation uses the same data but also if the evaluation remains the same [F25, F34, F65]. One approach to guarantee comparability is to use an unseen dataset [F1].   
3. The model fulfills user-defined deployment criteria, such as a specified increase in accuracy [F1, F60] or, according to participant P, a specific benchmark.   
4. Some pipelines may require human involvement, such as manual validation of tests, before deploying the AI model to production [I46, I5, I52]. This is also essential in participant P’s case.

The model is deployed on different environments, such as cloud-managed serving platforms, server disks or remote storage, that vary in their infrastructure and strongly depend on the anticipated traffic and financial resources [I52,

I62, I58, I32, F68, F3]. Cloud-managed serving platforms have the advantage that they can scale to accommodate the need for high-performance computing, low-latency, and memory-intensive requirements [I6, F17]. According to the interview participants, also cloud solutions are preferred.

Deployment Strategy

Continuous experimentation is a deployment strategy that allows gathering (user) feedback during runtime [F21, F6, F9]. Examples are A/B tests [F21, I56], canary releases [F9, F52] and shadow deployments [I51, I65, I49] [I32, I11, I14]. Participant A uses these deployment strategies to identify potential network effects. If the AI model is used for critical decision-making in the medical sector, deployment strategies which do not influence the system behaviour, clinical performance and safety of the patients should be used. If issues are encountered, rollbacks to already packaged models are necessary as identified during the interviews (R, A and T).

Monitoring

The pipeline also monitors the AI model in production and collects necessary information to improve the non-deterministic model over time [I59, I10, F36, I64, I17]. Therefore, the monitoring results should be systematically mapped to the different model versions [F67].

The MLR results reveal that the monitoring can be split up into four aspects. Firstly, the monitoring systems collect input and output data to use for future training [F48, I30, F15, F55, F5, F35, I11, F58, F73, I34]. In addition, this helps to quickly identify a drift in the data set due to changes in the statistical characteristics of distribution[F5, F35, F73, I11]. Secondly, monitoring tools observe the model performance to identify if the performance deteriorates when using real-life data [F40, F21, I63]. Thirdly, it collects traditional software monitoring aspects, also called Key Performance Indicator (KPI)s during runtime [F40, F21, F17]. For instance, KPIs consist of response time, minimal latency and throughput [F40, F37, I40] or resource usage and network statistics, which are especially important if the AI application is deployed as a Service [F75, I7]. Fourthly, may be collected to identify the impact on business outcomes, such as user engagement [I7, I40, I49]. According to participant P, operational telemetry data is essential because although the model improves, the subjective user perception of the model may diminish. To monitor the previously mentioned aspects, participants A, P and T use Google Error Logs and Postman to identify the correct behaviour and performance of API requests.

# Environment and Infrastructure Handling

AI applications may be deployed in several different environments or operational stages. The environments have varying hardware, operating system and software version dependencies [F28, F16]. Although the model operates on different environments, it must provide persistent output for a specific input [I60]. If for instance, multiple computing platforms use the same AI application, the pipeline should support cross-platform abstraction. This allows abstracting the configuration management and low-level API calls. For example, SageMaker expects a custom Docker image including predefined entry points, Watson Machine Learning requires a zip archive which includes the code for training a model. This zip archive is then deployed in a container in the cloud [F28].

# 4.4. Challenges

This section elaborates 25 challenges focusing on the implementation, adaption and usage of pipelines for the continuous development of AI. Figure 5 maps all identified challenges to general requirements or the presented framework’s four stages, Data Handling, Model Learning, Software Development, and System Operations. Figure 5 uses different colours for each challenge to indicate how often the sources state the challenge. For instance, yellow indicates that we extracted the challenge less or equal to 5 times, whereas dark blue indicates that we identified the challenge between 26 and 30 times. This section also covers a more thorough description of challenges which occurred more or equal to 16 times.

![](images/445320aef3f07bc81a7f10470358840a40b6de24e6fbe3ee88941d5a8b548545.jpg)  
Figure 5: Challenges occurring during the implementation, adaption and usage of pipelines for the continuous development of AI - labelled challenges based on the number of occurrences42 starting from yellow $( \leq 5 )$ to dark blue $( 2 6 - \overbrace { 3 0 } )$

Table 7: Overview of challenges regarding the implementation, adaption and usage of pipelines for the continuous development of AI   

<table><tr><td>Stage</td><td>Challenge</td><td>Description</td></tr><tr><td></td><td></td><td>Data collec- - comply with local data regulations [F6, I18]</td></tr><tr><td></td><td></td><td>tion &amp; inte- - transform data from various schema regimes into</td></tr><tr><td></td><td>gration</td><td>universal format [F2, I44]</td></tr><tr><td></td><td></td><td>-automatic feature labelling [F56, F41]</td></tr><tr><td></td><td></td><td>-how to automatically identify which data source</td></tr><tr><td></td><td></td><td>augments features [F56]</td></tr><tr><td></td><td></td><td>Data prepa- - pipeline should identify how data preparation ef-</td></tr><tr><td></td><td>ration</td><td>fects model quality [I44] - cleaning raw data, the pipeline struggles to identify</td></tr><tr><td></td><td></td><td>which effect noise or uncertainty have on the model</td></tr><tr><td></td><td></td><td>quality [I44]</td></tr><tr><td></td><td></td><td>reasonable &amp; - unclear data anomaly alerts [F9,F56]</td></tr><tr><td></td><td></td><td>sound alerts- correct &amp; reasonably strict alerts [F9, F56]</td></tr><tr><td></td><td></td><td>- unclear what optimal amount of alerts is [F9]</td></tr><tr><td></td><td></td><td>- flexible &amp; automated data validation techniques</td></tr><tr><td></td><td></td><td>to avoid unreasonable customization but allow for</td></tr><tr><td></td><td></td><td>custom checks [F9] Data trace- - automatic versioning methods for data sets and</td></tr><tr><td></td><td>ability</td><td>associated model [I46]</td></tr><tr><td></td><td>Optimum</td><td>- store information on data governance [I49]</td></tr><tr><td></td><td>time</td><td>- what are signs for optimal retraining time [F44, toI32,F6]</td></tr><tr><td></td><td></td><td>updatea - pipeline ought to handle conceptual coupling be-</td></tr><tr><td></td><td>model</td><td>tween data,model and pipeline [F7] - how can it handle online model retraining (pipeline is not triggered) &amp; model is not static artifact [I49]</td></tr></table>

Table 7 – continued from previous page   

<table><tr><td>Stage</td><td> Challenge</td><td>Tabie7-continhuedronrpreviouspage Description</td></tr><tr><td></td><td>Data</td><td> set - how to distribute data to training,validation and</td></tr><tr><td></td><td>split</td><td> test data set based on pipeline&#x27;s knowledge[F65]</td></tr><tr><td></td><td></td><td>test updates - test automation of self-adaptive models through-</td></tr><tr><td></td><td>&amp; overfittingout lifecycle [I15]</td><td>- how can the pipeline efficiently reuse tests and data</td></tr><tr><td></td><td></td><td>set without overfitting the model [F65,F25, I44]</td></tr><tr><td></td><td>pipeline</td><td>Slowdown - how should pipeline handle latency due to data preparation [F57]</td></tr><tr><td></td><td></td><td>Model evolu- - how can various versions of AI models and all the</td></tr><tr><td>tion</td><td></td><td>dependencies be stored efficiently [F40,F42,F49]</td></tr><tr><td></td><td></td><td>research identified that frequent model updates also require corresponding changes to various soft-</td></tr><tr><td></td><td></td><td>ware artifacts (e.g. build files).A challenge for the pipeline is to reduce this overhead [F7]</td></tr><tr><td></td><td>Package</td><td>-handle complex models that includes other models</td></tr><tr><td></td><td>model&amp;[F41] software in- dependently</td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td>&amp;forwardsF65]</td><td>Backwards- how to ensure compatibility of a data schema [I49,</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td>compatibil- ity</td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td>suming applications</td><td>withcon-tegration strategies for multiple consuming appli-</td><td>Compatible- the pipeline needs to support non-standardized in-</td></tr></table>

Table 7 – continued from previous page   

<table><tr><td>Stage</td><td>Challenge</td><td>Description</td></tr><tr><td></td><td></td><td>Cross-model - How can the pipeline anticipate &amp; prevent cross-</td></tr><tr><td></td><td>inference</td><td>model inferences (e.g. latency) [F9, F52]</td></tr><tr><td></td><td>Compare</td><td>- How to identify optimum model for deployment</td></tr><tr><td></td><td>models</td><td>based on stored information (when e.g. informa-</td></tr><tr><td></td><td></td><td>tion regarding model&#x27;s behaviour with non pre-</td></tr><tr><td></td><td></td><td>dictable production stream is not available) [F44,</td></tr><tr><td></td><td></td><td>F49, F50,I23, I44]</td></tr><tr><td></td><td>pects should[F27]</td><td>Whatas- - Which information helps to improve the pipeline</td></tr><tr><td></td><td>be</td><td>mon- - How should the pipeline most efficiently integrate</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td>itored</td><td>&amp;human interaction [I18]</td></tr><tr><td></td><td>how</td><td></td></tr><tr><td></td><td></td><td>Hidden feed- - How can pipeline prohibit propagating wrong</td></tr><tr><td></td><td>back loops</td><td>training data (e.g. bias) [F2, F15, F66]</td></tr><tr><td></td><td>Multiple</td><td>- How can pipeline adapt to specific requirements</td></tr><tr><td></td><td>environ-</td><td>(e.g. resources, security） of varying environments</td></tr><tr><td></td><td>ments/em-</td><td>[F28,F46, I10, I4]</td></tr><tr><td></td><td>bedded</td><td></td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td>Systems</td><td></td></tr></table>

Flexible & – How can the pipeline cater to custom selected   
customizable tools, services, engineering practices, libraries,   
pipeline frameworks and platforms in every step of the AI lifecycle (e.g. data processing, quality control) [F28, F61, F68, F45, I5, I1, I9]   
Reusable – Pipeline ought to provide predefined templates and patterns to minimize configuration effort while maintaining flexibility [F16, F28] (Participant R)

Table 7 – continued from previous page   

<table><tr><td>Stage</td><td>Challenge</td><td>Description</td></tr><tr><td></td><td></td><td>Faulttoler- - How can pipeline handle failures in its underlying</td></tr><tr><td></td><td>ance</td><td>execution environment due to different tools and infrastructures plugged together without interrup-</td></tr><tr><td></td><td></td><td>tion [F28, F9, I6]</td></tr><tr><td></td><td></td><td>How can the pipeline recover from intermittent</td></tr><tr><td></td><td></td><td>failures (e.g. inconsistent data) [F11] Scaling&amp; - Pipeline needs to automatically adopt &amp; efficiently</td></tr><tr><td></td><td>resource allocation</td><td>distribute resources due to the model&#x27;s unpre- dictable complexity [F45,F16,F16,I11,F12, I50]</td></tr><tr><td></td><td></td><td>(participant C) -How can the pipeline optimize its different tasks</td></tr><tr><td></td><td></td><td>(e.g.data processing could be optimized via</td></tr><tr><td></td><td></td><td>declarative abstraction） [F65] Multi-tenant - How can the pipeline prioritize different users all</td></tr><tr><td></td><td>setting</td><td></td></tr><tr><td></td><td></td><td>triggering the same pipeline [F1, F34]</td></tr><tr><td></td><td></td><td> How should the pipeline allocate resources to the</td></tr><tr><td></td><td></td><td></td></tr><tr><td></td><td></td><td>tenants in a non-discriminatory manner [F34]</td></tr><tr><td></td><td></td><td>Security en- - How can the company-specific security environ-</td></tr><tr><td></td><td>vironment</td><td>ment integrate the continuous pipeline (participant</td></tr><tr><td></td><td></td><td>Z</td></tr><tr><td></td><td></td><td>Regulations- How can tasks throughout the pipeline support</td></tr><tr><td></td><td></td><td>country-specific regulations (e.g. privacy， neces-</td></tr><tr><td>Training-</td><td></td><td>sary documentation， GDPR,certifications） (par- ticipant A,T,P,R,B,V,D)</td></tr></table>

# 4.4.1. Data Handling

Challenges during the stage Data Handling elaborate challenges regarding data collection and integration, preparation, data quality assurance with reasonable and sound alerts and data versioning with the respective data traceability. The most often mentioned data stage issue is the data collection and integration with over 16 mentioned cases. The pipeline is challenged with complying with local data regulations [F6]. For example, the healthcare sector may require to keep the data sets within the organization. Thus, the pipeline should be able to work with restricted on-premise computation resources [I18]. In addition, participants R and D stated that the pipeline should handle data governance and ownership constraints.

In addition, the pipeline should automatically transform data from various schema regimes into a universal format. Additionally, the pipeline should store the data transformation and data preparation steps in a machine-readable form [F2, I44]. Moreover, the pipeline faces the issue of automatically encoding data into features with the help of self-labelling consistent, and accurate instrumentation [F56, F41].

# 4.4.2. Model Learning

Specific challenges occurring during the stage Model Learning elaborate challenges regarding finding the optimal time to update a model to save time and resources, model quality assurance faces the challenges to split the data set, test updated and the potential overfitting and the introduced latency that slows down the pipeline. In addition, most often mentioned was that versioning should depict the model evolution and ensure reproducibility [I51, I58]. Versioning is a static procedure, however, the pipeline needs to be capable of tracking constantly learning models and the conceptual coupling between the respective data sets, model and pipeline [I49, F7, F5, F7, I50]. Additionally, the pipeline’s versioning should provide enough information to identify the reason for performance and prediction changes [F73].

# 4.4.3. Software Development

Specific challenges occurring during the stage Software Development elaborate challenges regarding packaging model and software independently to provide scalability which was challenging for participant P. The necessary tracking provenance becomes challenging if a complex model combines several other models which were trained on different data sets. It becomes even more challenging if the training data set’s transformations are highly-heterogeneous [F41]. The pipeline should also allow for for backwards and forwards compatibility of a data schema [I49, F65]

# 4.4.4. System Operations

Specific challenges occurring during the stage System Operations elaborate challenges regarding the deployment, such as ensuring that the model is compatible with the consuming application, cross-model inference and comparing models. Monitoring related challenges focus on what aspects are worth monitoring and hidden feedback loops. The challenge during the environment and infrastructure handling emphasizes the issue of handling multiple environments and embedded systems which was identified in 12 sources. This challenge occurs if AI applications are deployed to the cloud, such as multiple on-premise servers, edge devices, dedicated clusters or any combination [F28, F46, I10]. Because these environments handle resources and security differently, the pipeline needs to adapt to these specific requirements [F28, F46, I4]. For instance, edge devices provide different hardware architecture, such as arm64X or x86 or sensor setups, thus the pipeline needs to provide personalized docker containers automatically tailored to the architecture. This should allow improving hardware stability [I42].

# 4.4.5. General Requirements for the Pipeline

General challenges which cannot be mapped to a specific stage of the framework focus on the need of a flexible, customizable, reusable and fault tolerant pipeline. Elastic scaling is also a pipeline specific challenge because capacity changes. The required multi-tenant setting, integration into the already existing security landscape, and missing regulations pose challenges as well.

The pipeline has to be flexible and customizable to avoid becoming too restrictive [I53] and to allow using preferred and established tools, services and engineering practices [F28, F61, F68]. Thus, pipelines need to combine quickly evolving tools, libraries, frameworks and platforms in every step of the AI lifecycle management to meet specific requirements [F45, I5, I1, F68, I9]. The need for flexibility and customizability were confirmed by more than half of the participants (R, A, P, Z and C) because pipelines are very context-specific and strongly depend on the use case. Participant V uses TFX for their AI development and they discovered that the infrastructure handling is very difficult for them and for their customers. For example, TFX requires Kubeflow which only works on Linux. Participant V, indicated that his customers wanted to execute the TFX pipeline by themselves but did not have an infrastructure based on Linux.

The demand for computing capacities varies heavily due to the increase in the algorithms’ unpredictable complexity [F45, F16, F16]. Thus, elastic scaling is required to provide the right amount of hardware to handle capacity changes [F45, F16, F16]. The pipeline needs to adapt to either horizontal or vertical scaling which is strongly dependent on the infrastructure and platform limitations [I11]. In addition, the pipeline needs to be capable of efficiently distributing these resources. This is especially important if device capabilities are heterogeneous such as differences in the Central Processing Unit (CPU) complexity, memory, number of cores or bandwidth [F12, I50].

# 5. Discussion

The following section provides a discussion on how to map an existing pipeline for the continuous development of AI to the proposed framework. Whereas the proposed framework provides a thorough depiction of tasks in the lifecycle management pipeline, the implemented pipeline may vary in practice. For instance, pipelines may vary due to resource constraints, difficulties in specific implementations and because a task’s benefit does not outweigh the costs. In addition, the execution order of the tasks strongly depends on the specific context, such as organizational policies for running the pipeline. The most often mentioned pipeline collected via the MLR is TFX. TFX combines different components to enable a flexible and customizable pipeline. An orchestrator, such as Kubeflow, manages and triggers these components. In the proposed framework, the term tasks refer to the same concept as TFX’s components. Figure 6 illustrates the TFX’s components mapped to the framework. Components for the data preprocessing consist of ExampleGen, StatisticsGen, SchemaGen, and Transform, which are explained in the next subsection.

![](images/d72994518b6581e360d3bc90d89b4c3e6bb7d77abfc9d7d2d35f8a5aa44156d4.jpg)  
Figure 6: Map TFX’s components to the proposed framework’s stages and tasks; The circle with the number illustrates the execution order in TFX for each stage.

# 5.1. Data Handling

TFX’s ExampleGen component stores consumed data from different file types (e.g., csv) in an appropriate format for the following components. With the help of Apache Beam, a unified programming model for defining and executing data processing workflow, ExampleGen can read different data formats and sources. For instance, ExampleGen handles file-based data (e.g. CSV, Amazon S3, Google Cloud Storage, general text files), Messaging (e.g. Apache Kafka, Kinesis streams, JMS, Amazon Simple Queue Service), FileSystem-based data (e.g. Hadoop, Google Cloud Storage), Databases (e.g. Apache Cassandra, MongoDB, Google Cloud Datastore) and others. TFX’s ExampleGen transforms the ingested data to customizable spans, versions and splits. A span groups together data based on context-specific characteristics, such as a day. Spans include several versions when the data is processed, resulting in a new version within the span. ExampleGen then splits each version into training and evaluation data sets.

StatisticsGen uses ExampleGen’s output as input and calculates descriptive statistics for the data set.

SchemaGen uses these statistics to construct a schema automatically. The schema includes information about the (absence/presence of) data types, ranges, categories, distribution etc. Developers can modify and adapt the generated schema.

ExampleValidator uses the generated schema as input to validate the data set via the TensorFlow Data Validation to ensure data quality. As suggested in the MLR, it compares the data statistics against the constructed schema, compares training and serving data to identify training-serving skews, and identifies data drifts by evaluating a series of data.

Opposed to the proposed framework, Transform handles feature engineering after the data validation. Transform converts the ExampleGen’s data set via SchemaGen’s data schema using TensorFlow Transform. Feature transformations include embedding (mapping features to a low dimensional space), vocabulary generation (convert non-numeric features into integers), value normalization (scale numeric data without distorting differences in its range), and enriching text feature (extract features from raw data, e.g. n-grams).

# 5.2. Model Learning

Opposed to the proposed framework, TFX firstly improves the model via hyperparameter tuning, then trains the model and then validates it. Regarding model learning, TFX improves the model by tuning hyperparameters via their Tuner that uses the Python KerasTuner. As input, Tuner requires the training and evaluation data set, tuning logic such as the model definition, hyperparameter search space and the Protocol buffers that include instructions for serializing structured data.

Trainer trains an AI model via Python’s TensorFlow API. This task uses ExampleGen’s data set and the transformed features to train two models. One model gets deployed to production for inference, and TFX uses one model for evaluation. TFX also allows warm-starting the training by using an existing model for further training. In addition, during model training, developers can simultaneously compare multiple model runs, due to saved information in the ML Metadata Store.

TFX’s Evaluator performs analysis on the training results and allows to look at the model’s behaviour for individual slices of data sets. This ensures identifying well-performing models for the entire data set but poorly for a data point. This task also allows comparing models to find the optimum model that does not necessarily need to be the lastly trained model. Moreover, standard Keras metrics calculate the accuracy, precision, recall etc.

# 5.3. Software Development

TFX does not explicitly handle any software development-specific tasks in their primary pipeline, such as packaging or system-level quality assurance. ML Metadata versions and documents software-specific information.

# 5.4. System Operations

TFX’S InfraValidator checks whether the model complies with the production environment. To do so, it launches a sand-boxed model server based on manually configured environment details, such as type of CPU, memory, or accelerators to evaluate the compatibility between the model server binary and the model that should get deployed.

After successfully evaluating the model concerning the specific production serving environment, TFX’s Pusher pushes the validated model to the appropriate deployment environment. Depending on the deployment target Pusher supports model repositories such as Tensorflow Hub, Javascript environments (tensorflow.js), native mobile applications (TensorFlow Light), or server farms (Tensorflow Serving).

TFX versions all the artifacts produced by every component over several executions. Therefore, it uses a ML Metadata Store, a relational database that stores the properties of trained models and the respective data set, evaluation results, execution records and the provenance of data objects. Thus, the metadata store allows identifying which features impacted the evaluation metrics. It also allows only rerunning necessary components, such as skipping data processing when the developer only adapts the model hyperparameters.

# 6. Threats to validity

This section discusses the four possible main threats to validity according to Wohlin et al. [52] as well as how we mitigated these threats. In addition, the section discusses the scope of this study Conclusion validity is restricted because a qualitative approach was chosen. To improve the interviews’ reliability of treatment implementation, we implemented and tested an interview plan. In addition, the semi-structured approach allowed us to ask follow-up and clarification questions to reduce misunderstandings and ensure a thorough understanding.

Internal validity may occur because only one researcher conducted the MLR. Therefore, as proposed by Kitchenham and Charters [20], we execute a testretest approach at the end of the initial source selection to provide internal consistency in the decisions to include or exclude the source as well as information extraction. The random sample comprised 30 papers where only two papers were excluded instead of included. This results in a consistency of 93,3%. We calculate the Test-retests value for categorizing the data of the included sources. Therefore, we once more extracted the information from these 30 randomly selected sources. The average difference in the information extraction comprises 23%. This value may seem high, however, some categories only had one supporting source during the test, and during the retest, we identified another source which is a 100% increase from test to retest.

Regarding the construct validity, one may argue that the literature review is an interpretation of the meaning of the collected literature. Thus, to minimize construct validity, the selected literature resources were carefully evaluated via two quality checklists proposed by Kitchenham and Charters’ [20] for formal literature and Garousi et al.’s checklist [11] for grey literature. In addition, we avoided construct irrelevance or construct under-representation as described by Messick [30] and applied to literature studies by Dellinger [4] via six countermeasures. (1) The selection process was rigorously documented, (2) the studies were assigned a 5-point Likert scale to identify the amount of contribution. (3)

Over-representation was avoided by combining similar sources from the same authors and companies. (4) The described checklists identified unwarranted sources which were excluded. (5) Contrary findings were also included as well as (6) the search strings allowed to include many relevant study findings. Regarding the external validity, we applied appropriate countermeasures, such as extensive search terms, including sources from academic studies as well as grey literature provided by industry, in addition to a two-stage selection process of the interview partners. Regarding the two-stage selection process, the target group consists of three different categories of interview partners to incorporate several points of view. The categories comprise people from academia and industry. Interview partners from the industry can already use a continuous end-to-end lifecycle management pipeline for AI or are start-ups that develop AI applications and the associated lifecycle pipeline. These interviews also helped to evaluate and extend the results obtained from the MLR to provide a general depiction of tasks necessary for the continuous development of AI.

The scope of this study is to cover a comprehensive framework for the continuous development of AI models. Thus, this paper combines research on AI, ML and DL. The paper does not differentiate between them because results from the MLR treated them similarly and only the implementation of tasks in data handling or model learning slightly varies. In addition, the scope comprises different learning types, such as supervised, unsupervised and reinforcement learning. However, we did not consider special cases of learning techniques, such as federated learning or transfer learning.

# 7. Conclusion

In this paper, we aimed to provide a comprehensive, evidence-based foundation of established research on pipelines for the continuous development of AI. Thus, the main goal was to systematically identify relevant conceptual ideas, as well as to synthesize and structure the research in the area. To achieve this goal, we extracted 151 relevant formal and informal sources via a Multivocal Literature Review (MLR) and we executed nine semi-structured interviews [44]. Based on this information, we identified and compared five primarily used terms, such as DevOps for AI, CI/CD for AI, MLOps describing an extension of DevOps, the term end-to-end lifecycle management to describe the continuous execution of development and deployment tasks, and CD4ML describing the technical implementation of MLOps.

The paper also investigated potential main triggers, such as feedback and alert systems, orchestration service with a scheduled time, traditional repository updates, and manual triggers.

These triggers start the execution of the pipeline, which consists of four stages: (1) Data Handling, (2) Model Learning, (3) Software Development and (4) System Operations. The stage Data Handling comprises the repetitive end-to-end lifecycle of data-related tasks, such as pre-processing, quality assurance, versioning, and documentation. The stage Model Learning uses the output of the data handling and illustrates the tasks associated with the model development, such as model design, training, quality assurance, model improvement metadata capture, versioning, and documentation. After the the pipeline handles the model learning, the stage Software Development prepares the model for deployment via packaging, software level quality assurance, and system versioning. The final stage System Operations deploys the AI model to a specific environment via different deployment strategies and monitors the system.

Furthermore, the paper maps 25 potential challenges regarding the implementation, adaption, and usage of pipelines for the continuous development of

AI to the previously mentioned four stages. Data Handling related challenges encompass data collection and integration. Model Learning related challenges identify that versioning should depict the model evolution and ensure reproducibility. Software Development requires the pipeline to provide backward and forward compatibility of the model and data schema. The stage System Operations needs to handle multiple environments where cross-model inferences may occur. Ultimately challenges resulting from the pipeline requirements, such as the need for a flexible, customizable, and scalable pipeline are elaborated.

Regarding future work, we plan to further explore, evaluate and compare the pipeline concepts by prototypical implementations of demonstrators using available platforms that provide a pipeline for the continuous development of AI. Additional candidate platforms are, for instance, MLFlow, Uber’s lifecycle platform for AI, called Michelangelo, ModelDB, or Facebook’s FBLearner. The identified tasks and resulting taxonomy in this paper may then be compared with the already available tasks supported by the mentioned platforms. In addition, challenges for implementing, adapting, and using the pipeline for the continuous development of AI can also be tracked during the implementation and demonstration to complement our findings from the literature.

Acknowledgement: This work was supported by the Austrian Research Promotion Agency (FFG) in the frame of the project ConTest [888127] and the COMET competence center SCCH/INTEGRATE [865891, 892418]. We also thank all interview participants for their valuable input and feedback.

# List of General References

[1] Ahmad Alnafessah et al. “Quality-Aware DevOps Research: Where Do We Stand?” In: IEEE Access 9 (2021), pp. 44476–44489. doi: 10.1109/ ACCESS.2021.3064867.   
[2] Lucas Baier, Fabian J¨ohren, and Stefan Seebacher. “Challenges in the deployment and operation of machine learning in practice”. In: (2019).   
[3] Philip Boucher. Artificial intelligence: How does it work, why does it matter,and what can we do about it? Brussels: European Parliament, 2020. isbn: 978-92-846-6770-3.   
[4] Amy Dellinger. “Validity and the review of literature”. In: Research in the Schools 12 (2005).   
[5] Julian Ereth. “DataOps-Towards a Definition”. In: Proceedings of the Conference Lernen, Wissen, Daten, Analysen (LWDA). Aug. 2018. url: https://ceur-ws.org/Vol-2191/paper13.pdf.   
[6] Iris Figalist et al. “An End-to-End Framework for Productive Use of Machine Learning in Software Analytics and Business Intelligence Solutions”. In: Product-focused software process improvement. Ed. by Maurizio Morisio, Marco Torchiano, and Andreas Jedlitschka. Vol. 12562. LNCS sublibrary, SL 2, Programming and Software Engineering. Cham: Springer, 2020, pp. 217–233. isbn: 978-3-030-64147-4. doi: 10 . 1007 / 978-3-030-64148-1{\textunderscore}14.   
[7] Lukas Fischer et al. “AI System Engineering—Key challenges and lessons learned”. In: Machine Learning and Knowledge Extraction 3.1 (2020), pp. 56–83.   
[8] Brian Fitzgerald and Klaas-Jan Stol. “Continuous software engineering: A roadmap and agenda”. In: Journal of Systems and Software 123 (2017), pp. 176–189. issn: 0164-1212. doi: 10.1016/j.jss.2015.06. 063. url: https://www.sciencedirect.com/science/article/pii/ S0164121215001430.   
[9] Teodor Fredriksson, Jan Bosch, and Helena Holmstrm Olsson. “Machine Learning Models for Automatic Labeling: A Systematic Literature Review”. In: Proceedings of the 15th International Conference on Software Technologies - ICSOFT (July 2020), pp. 552–561. doi: 10.5220/ 0009972705520561. url: https://orcid.org/0000-0003-2854-722X.   
[10] Vahid Garousi, Michael Felderer, and Mika V. M¨antyl¨a. “Guidelines for including grey literature and conducting multivocal literature reviews in software engineering”. In: Information and Software Technology 106 (2019), pp. 101–121. issn: 0950-5849. doi: 10.1016/j.infsof.2018. 09.006. url: https://www.sciencedirect.com/science/article/ pii/S0950584918301939.   
[11] Vahid Garousi, Michael Felderer, and Mika V. M¨antyl¨a. “The need for multivocal literature reviews in software engineering”. In: Proceedings of the 20th international conference on evaluation and assessment in software engineering (EASE). New York: ACM, 2016, pp. 1–6. isbn: 9781450336918. doi: 10.1145/2915970.2916008.   
[12] Johannes Gmeiner, Rudolf Ramler, and Julian Haslinger. “Automated testing in the continuous delivery pipeline: A case study of an online company”. In: 2015 IEEE Eighth International Conference on Software Testing, Verification and Validation Workshops (ICSTW) (2015), pp. 1– 6.   
[13] David J. Hand and Shakeel Khan. “Validating and Verifying AI Systems”. In: Patterns (New York, N.Y.) 1.3 (2020), p. 100037. doi: 10. 1016/j.patter.2020.100037.   
[14] Samireh Jalali and Claes Wohlin. “Systematic literature studies”. In: 2012 ACM-IEEE International Symposium on Empirical Software Engineering and Measurement (ESEM). Ed. by Per Runeson. Piscataway, NJ: IEEE, 2012, p. 29. isbn: 9781450310567. doi: 10.1145/2372251. 2372257.   
[15] Meenu Mary John, Helena Holmstr¨om Olsson, and Jan Bosch. “Architecting AI Deployment: A Systematic Review of State-of-the-Art and State-of-Practice Literature”. In: Software Business. Ed. by Eriks Klotins and Krzysztof Wnuk. Cham: Springer International Publishing, 2021, pp. 14–29. isbn: 978-3-030-67292-8.   
[16] Meenu Mary John, Helena Holmstrom Olsson, and Jan Bosch. “Towards MLOps: A Framework and Maturity Model”. In: Proceedings - 2021 47th Euromicro Conference on Software Engineering and Advanced Applications, SEAA 2021 (Sept. 2021), pp. 334–341. doi: 10.1109/SEAA53835. 2021.00050.   
[17] Ian Jolliffe. “Principal Component Analysis”. In: Encyclopedia of statistics in behavioral science. Ed. by Brian Everitt and David C. Howell. Chichester: Wiley, 2005. isbn: 0470860804. doi: 10.1002/0470013192. bsa501.   
[18] Ioannis Karamitsos, Saeed Albarhami, and Charalampos Apostolopoulos. “Applying DevOps Practices of Continuous Automation for Machine Learning”. In: Information 11.7 (2020), p. 363. doi: 10 . 3390 / info11070363.   
[19] Gene Kim et al. The DevOps handbook: How to create world-class agility, reliability, & security in technology organizations. First edition. Portland OR: IT Revolution Press LLC, 2016. isbn: 9781942788003.   
[20] Barbara Ann Kitchenham and Stuart Charters. “Guidelines for performing Systematic Literature Reviews in Software Engineering”. In: 2 (2007).   
[21] Ask Berstad Kolltveit and Jingyue Li. “Operationalizing Machine Learning Models - A Systematic Literature Review”. In: Proceedings - Workshop on Software Engineering for Responsible AI, SE4RAI 2022 (2022), pp. 1–8. doi: 10.1145/3526073.3527584.   
[22] Dominik Kreuzberger, Niklas K¨uhl, and Sebastian Hirschl. “Machine Learning Operations (MLOps): Overview, Definition, and Architecture”. In: (May 2022). doi: 10.48550/arxiv.2205.02302. arXiv: 2205.02302. url: https://arxiv.org/abs/2205.02302v3.   
[23] Valentina Lenarduzzi et al. Software Quality for AI: Where we are now? 2020.   
[24] Grace A. Lewis, Ipek Ozkaya, and Xiwei Xu. “Software Architecture Challenges for ML Systems”. In: Proceedings - 2021 IEEE International Conference on Software Maintenance and Evolution, ICSME 2021 (2021), pp. 634–638. doi: 10.1109/ICSME52107.2021.00071.   
[25] Sin Kit Lo et al. “A Systematic Literature Review on Federated Machine Learning”. In: ACM Computing Surveys (CSUR) 54.5 (May 2021). issn: 15577341. doi: 10 . 1145 / 3450288. arXiv: 2007 . 11354. url: https : //dl.acm.org/doi/10.1145/3450288.   
[26] Giuliano Lorenzoni et al. Machine Learning Model Development from a Software Engineering Perspective: A Systematic Literature Review. 2021. url: http://arxiv.org/pdf/2102.07574v1.   
[27] Silverio Mart´ınez-Fern´andez et al. “Software Engineering for AI-Based Systems: A Survey”. In: ACM Transactions on Software Engineering and Methodology (TOSEM) 31.2 (Apr. 2022). issn: 15577392. doi: 10. 1145/3487043. arXiv: 2105.01984. url: https://dl.acm.org/doi/ 10.1145/3487043.   
[28] Mayring, Philipp, 1952-. Qualitative Inhaltsanalyse : Grundlagen und Techniken. 12., ¨uberarbeitete Auflage. P¨adagogik. Weinheim: Basel: Beltz and [Gr¨unwald]: Preselect.media GmbH, 2015. isbn: 9783407293930. url: https://bibsearch.uibk.ac.at/**********.   
[29] Tsakani Mboweni, Themba Masombuka, and Cyrille Dongmo. “A Systematic Review of Machine Learning DevOps”. In: International Conference on Electrical, Computer, and Energy Technologies, ICECET 2022 (2022). doi: 10.1109/ICECET55527.2022.9872968.   
[30] Samuel Messick. “Standards of Validity and the Validity of Standards in Performance Asessment”. In: Educational Measurement: Issues and Practice 14.4 (1995), pp. 5–8. issn: 07311745. doi: 10.1111/j.1745- 3992.1995.tb00881.x.   
[31] Matthew B. Miles, A. Michael Huberman, and Johnny Salda˜na. Qualitative data analysis: A methods sourcebook. Edition 3. Los Angeles et al.: Sage, 2014. isbn: 9781452257877.   
[32] Alok Mishra and Ziadoon Otaiwi. “DevOps and software quality: A systematic mapping”. In: Computer Science Review 38 (2020), p. 100308. issn: 1574-0137. doi: 10.1016/j.cosrev.2020.100308. url: https:// www.sciencedirect.com/science/article/pii/S1574013720304081.   
[33] Aiswarya Raj Munappy et al. “From Ad-Hoc data analytics to DataOps”. In: Proceedings - 2020 IEEE/ACM International Conference on Software and System Processes, ICSSP 2020 20 (June 2020), pp. 165–174. doi: 10.1145/3379177.3388909. url: https://doi.org/10.1145/ 3379177.3388909.   
[34] Elizamary Nascimento et al. Software engineering for artificial intelligence and machine learning software: A systematic literature review. Nov. 7, 2020. url: http://arxiv.org/pdf/2011.03751v1.   
[35] Andrew Ng. “Sparse autoencoder”. In: CS294A Lecture notes 72.2011 (2011), pp. 1–19.   
[36] Anh Nguyen-Duc et al. “A Multiple Case Study of Artificial Intelligent System Development in Industry”. In: ACM International Conference Proceeding Series (Apr. 2020), pp. 1–10. doi: 10.1145/3383219. 3383220.   
[37] Andrei Paleyes, Raoul-Gabriel Urma, and Neil D. Lawrence. Challenges in Deploying Machine Learning: a Survey of Case Studies. Nov. 18, 2020. url: http://arxiv.org/pdf/2011.09926v2.   
[38] Wolter Pieters. “Explanation and trust: what to tell the user in security and AI?” In: Ethics and Information Technology 13.1 (2011), pp. 53–64. issn: 1388-1957. doi: 10.1007/s10676-010-9253-3.   
[39] Jim Pivarski, Collin Bennett, and Robert L. Grossman. “Deploying Analytics with the Portable Format for Analytics (PFA)”. In: KDD2016. Ed. by Balaji Krishnapuram et al. New York, NY: Association for Computing Machinery Inc. (ACM), 2016, pp. 579–588. isbn: 9781450342322. doi: 10.1145/2939672.2939731.   
[40] Oliver C. Robinson. “Sampling in Interview-Based Qualitative Research: A Theoretical and Practical Guide”. In: Qualitative Research in Psychology 11.1 (2014), pp. 25–41. issn: 1478-0887. doi: 10.1080/14780887. 2013.801543.   
[41] Manuel Rodriguez, Pires de Ara´ujo, Luiz Jonat˜a, and Manuel Mazzara. “Good practices for the adoption of DataOps in the software industry”. In: Journal of Physics: Conference Series 1694 (2020), p. 012032. doi: 10.1088/1742-6596/1694/1/012032.   
[42] D. Sculley et al. “Hidden Technical Debt in Machine Learning Systems”. In: Advances in Neural Information Processing Systems. Ed. by C. Cortes et al. Vol. 28. Curran Associates, Inc, 2015. url: https://proceedings. neurips.cc/paper/2015/file/86df7dcfd896fcaf2674f757a2463ebaPaper.pdf.   
[43] Daniel St˚ahl and Jan Bosch. “Modeling continuous integration practice differences in industry software development”. In: Journal of Systems and Software 87 (2014), pp. 48–59. issn: 0164-1212. doi: 10.1016/j. jss.2013.08.032. url: https://www.sciencedirect.com/science/ article/pii/S0164121213002276.   
[44] Monika Steidl, Michael Felderer, and Rudolf Ramler. Multivocal Literature Review & Interviews: Continuous End-to-End Lifecycle Management Pipeline for Artificial Intelligence. Version 1. Feb. 2022. doi: 10. 5281/zenodo.6384341. url: https://zenodo.org/record/6384341.   
[45] Klaas-Jan Stol, Paul Ralph, and Brian Fitzgerald. “Grounded Theory in Software Engineering Research: A Critical Review and Guidelines”. In: Proceedings of the 38th International Conference on Software Engineering (2016). doi: 10.1145/2884781. url: http://dx.doi.org/10. 1145/2884781.2884833.   
[46] Peter Stone et al. “Artificial Intelligence and Life in 2030: One Hundred Year Study on Artificial Intelligence: Report of the 2015-2016 Study Panel”. In: Stanford University (2016). url: https://ai100.stanford. edu/2016-report.   
[47] Chuanqi Tao, Jerry Gao, and Tiexin Wang. “Testing and Quality Validation for AI Software–Perspectives, Issues, and Practices”. In: IEEE Access 7 (2019), pp. 120164–120175. doi: 10.1109/ACCESS.2019.2937107.   
[48] Matteo Testi et al. “MLOps: A Taxonomy and a Methodology”. In: IEEE Access 10 (2022), pp. 63606–63618. issn: 21693536. doi: 10.1109/ ACCESS.2022.3181730.   
[49] Muhammad Usman et al. “Taxonomies in software engineering: A systematic mapping study and a revised taxonomy development method”. In: Information and Software Technology 85 (2017), pp. 43–59. issn: 0950-5849.   
[50] Hironori Washizaki et al. “Studying Software Engineering Patterns for Designing Machine Learning Systems”. In: Proceedings - 2019 10th International Workshop on Empirical Software Engineering in Practice, IWESEP 2019 (Dec. 2019), pp. 49–54. doi: 10 . 1109 / IWESEP49350 . 2019.00017. arXiv: 1910.04736.   
[51] Claes Wohlin. “Guidelines for Snowballing in Systematic Literature Studies and a Replication in Software Engineering”. In: Proceedings of the 18th International Conference on Evaluation and Assessment in Software Engineering. EASE ’14. New York, NY, USA: Association for Computing Machinery, 2014. isbn: 9781450324762. doi: 10.1145/2601248. 2601268.   
[52] Claes Wohlin et al. Experimentation in software engineering. Berlin: Springer, 2012. isbn: 9783642290442. doi: 10.1007/978-3-642-29044- 2. url: http : / / site . ebrary . com / lib / alltitles / docDetail . action?docID=10650365.   
[53] Yuanhao Xie et al. Systematic Mapping Study on the Machine Learning Lifecycle. 2021. url: http://arxiv.org/pdf/2103.10248v1.   
[54] Bing Xue, Mengjie Zhang, and Will N. Browne. “Particle swarm optimization for feature selection in classification: a multi-objective approach”. In: IEEE transactions on cybernetics 43.6 (2013), pp. 1656– 1671. doi: 10.1109/TSMCB.2012.2227469.   
[55] Affan Yasin et al. “On Using Grey Literature and Google Scholar in Systematic Literature Reviews in Software Engineering”. In: IEEE Access 8 (2020), pp. 36226–36243. doi: 10.1109/ACCESS.2020.2971712.

# List of Formal References

[F1] Leonel Aguilar Melgar et al. Ease. ML: A Lifecycle Management System for Machine Learning. 2021. doi: 10.3929/ETHZ-B-000458916.   
[F2] Saleema Amershi et al. “Software Engineering for Machine Learning: A Case Study”. In: 2019 IEEE/ACM 41st International Conference on Software Engineering: Software engineering in practice. Piscataway, NJ: IEEE, 2019, pp. 291–300. isbn: 978-1-7281-1760-7. doi: 10.1109/ICSESEIP.2019.00042.   
[F3] Josu D´ıaz-de Arcaya et al. “PADL: A Modeling and Deployment Language for Advanced Analytical Services”. In: Sensors (Basel, Switzerland) 20.23 (2020). doi: 10.3390/s20236712.   
[F4] Shelernaz Azimi and Claus Pahl. Continuous Data Quality Management for Machine Learning based Data-as-a-Service Architectures. 2021.   
[F5] Florian Bachinger and Gabriel Kronberger. “Concept for a Technical Infrastructure for Management of Predictive Models in Industrial Applications”. In: Computer aided systems theory - EUROCAST 2019. Ed. by Roberto Moreno-D´ıaz and Franz Pichler. Vol. 12013. Lecture Notes in Computer Science. Cham: Springer International Publishing, 2020, pp. 263–270. isbn: 978-3-030-45092-2. doi: 10.1007/978-3-030-45093- 9{\textunderscore}32.   
[F6] Amitabha Banerjee et al. “Challenges and Experiences with MLOps for Performance Diagnostics in Hybrid-Cloud Enterprise Software Deployments”. In: 2020 USENIX Conference on Operational Machine Learning (OpML 20). USENIX Association, 2020. url: https://www.usenix. org/conference/opml20/presentation/banerjee.   
[F7] Amine Barrak, Ellis E. Eghan, and Bram Adams. “On the Co-evolution of ML Pipelines and Source Code – Empirical Study of DVC Projects”. In: Proceedings of the 28th IEEE International Conference on Software Analysis, Evolution, and Reengineering (SANER). Hawaii, USA, 2021.   
[F8] Denis Baylor et al. “Continuous Training for Production ML in the TensorFlow Extended (TFX) Platform”. In: 2019 USENIX Conference on Operational Machine Learning (OpML 19). Santa Clara, CA: USENIX Association, 2019, pp. 51–53. isbn: 978-1-939133-00-7. url: https:// www.usenix.org/conference/opml19/presentation/baylor.   
[F9] Denis Baylor et al. “TFX: A TensorFlow-Based Production-Scale Machine Learning Platform”. In: KDD2017. Ed. by Stan Matwin, Shipeng Yu, and Faisal Farooq. New York, NY: Association for Computing Machinery Inc. (ACM), 2017, pp. 1387–1395. isbn: 9781450348874. doi: 10.1145/3097983.3098021.   
[F10] Hind Benbya, Thomas H. Davenport, and Stella Pachidi. “Artificial Intelligence in Organizations: Current State and Future Opportunities”. In: SSRN Electronic Journal (2020). doi: 10.2139/ssrn.3741983.   
[F11] Scott Boag et al., eds. Scalable multi-framework multi-tenant lifecycle management of deep learning training jobs. 2017.   
[F12] Sudershan Boovaraghavan et al. “MLIoT: An End-to-End Machine Learning System for the Internet-of-Things”. In: Proceedings of the International Conference on Internet-of-Things Design and Implementation. New York, NY, USA: ACM, 2021, pp. 169–181. isbn: 9781450383547. doi: 10.1145/3450268.3453522.   
[F13] Markus Borg. “The AIQ Meta-Testbed: Pragmatically Bridging Academic AI Testing and Industrial Q Needs”. In: Software Quality: Future Perspectives on Software Engineering Quality. Ed. by Dietmar Winkler et al. Cham: Springer International Publishing, 2021, pp. 66–77. isbn: 978-3-030-65854-0.   
[F14] Aur´elien Bourgais and Issam Ibnouhsein. “Ethics-by-design: the next frontier of industrialization”. In: AI and Ethics (2021). issn: 2730-5953. doi: 10.1007/s43681-021-00057-0.   
[F15] Eric Breck et al., eds. Data Validation for Machine Learning. 2019.   
[F16] Eli Brumbaugh et al. “Bighead: A Framework-Agnostic, End-to-End Machine Learning Platform”. In: 2019 IEEE International Conference on Data Science and Advanced Analytics. Ed. by Lisa Singh. Piscataway, NJ: IEEE, 2019, pp. 551–560. isbn: 978-1-7281-4493-1. doi: 10.1109/ DSAA.2019.00070.   
[F17] Camilo Castellanos, Carlos A. Varela, and Dario Correal. “ACCORDANT: A domain specific-model and DevOps approach for big data analytics architectures”. In: Journal of Systems and Software 172 (2021),

p. 110869. issn: 0164-1212. doi: 10.1016/j.jss.2020.110869. url:

https://www.sciencedirect.com/science/article/pii/S016412122030259   
[F18] Emily Caveness et al. “TensorFlow Data Validation: Data Analysis and Validation in Continuous ML Pipelines”. In: Proceedings of the 2020 ACM SIGMOD International Conference on Management of Data. Ed. by David Maier et al. New York, NY, USA: ACM, 2020, pp. 2793–2796. isbn: 9781450367356. doi: 10.1145/3318464.3384707.   
[F19] Ryan Chard et al. “DLHub: Model and Data Serving for Science”. In: 2019 IEEE 33rd International Parallel and Distributed Processing Symposium. Piscataway, NJ: IEEE, 2019, pp. 283–292. isbn: 978-1-7281- 1246-6. doi: 10.1109/IPDPS.2019.00038.   
[F20] R. Ciucu et al. “Innovative Devops for Artificial Intelligence”. In: The Scientific Bulletin of Electrical Engineering Faculty 19.1 (2019), pp. 58– 63. doi: 10.1515/sbeef-2019-0011.   
[F21] Jean-Philippe Corbeil and Florent Daudens, eds. Deploying a Cost-Effective and Production-Ready Deep News Recommender System in the Media Crisis Context. 2020.   
[F22] Behrouz Derakhshan et al. “Continuous Deployment of Machine Learning Pipelines”. In: EDBT. 2019.   
[F23] Thomas Fehlmann and Eberhard Kranich. “A Framework for Automated Testing”. In: Systems, Software and Services Process Improvement. Ed. by Murat Yilmaz et al. Cham: Springer International Publishing, 2020, pp. 275–288. isbn: 978-3-030-56441-4.   
[F24] Grigori Fursin, Herve Guillou, and Nicolas Essayan. CodeReef: an open platform for portable MLOps, reusable automation actions and reproducible benchmarking. Jan. 22, 2020. url: http : / / arxiv . org / pdf / 2001.07935v2.   
[F25] Rolando Garcia et al., eds. Context: The missing piece in the machine learning lifecycle. Vol. 114. 2018.   
[F26] Ilias Gerostathopoulos et al. “Automated Trainability Evaluation for Smart Software Functions”. In: 2019 34th IEEE/ACM International Conference on Automated Software Engineering : 10-15 November 2019, San Diego, California. Los Alamitos, CA: IEEE Computer Society, 2019, pp. 998–1001. isbn: 978-1-7281-2508-4. doi: 10.1109/ASE.2019.00096.   
[F27] Gharib Gharibi et al. “Automated end-to-end management of the modeling lifecycle in deep learning”. In: Empirical Software Engineering 26.2 (2021). issn: 1382-3256. doi: 10.1007/s10664-020-09894-9.   
[F28] Waldemar Hummer et al. “ModelOps: Cloud-Based Lifecycle Management for Reliable and Trusted AI”. In: 2019 IEEE International Conference on Cloud Engineering. Los Alamitos, California, Washington, and Tokyo: Conference Publishing Services, IEEE Computer Society, 2019, pp. 113–120. isbn: 978-1-7281-0218-4. doi: 10.1109/IC2E.2019.00025.   
[F29] Stuart Jackson, Maha Yaqub, and Cheng-Xi Li. “The Agile Deployment of Machine Learning Models in Healthcare”. In: Frontiers in big data 1 (2018), p. 7. doi: 10.3389/fdata.2018.00007.   
[F30] P. S. Janardhanan. “Project repositories for machine learning with TensorFlow”. In: Procedia Computer Science 171 (2020), pp. 188–196. issn: 1877-0509. doi: 10.1016/j.procs.2020.04.020. url: https://www. sciencedirect.com/science/article/pii/S1877050920309856.   
[F31] Meenu Mary John, Helena Holmstr¨om Olsson, and Jan Bosch. “Developing ML/DL Models: A Design Framework”. In: Proceedings of the International Conference on Software and System Processes. ICSSP ’20. New York, NY, USA: Association for Computing Machinery, 2020, pp. 1–10. isbn: 9781450375122. doi: 10.1145/3379177.3388892.   
[F32] Lim Junsung et al. “MLOp Lifecycle Scheme for Vision-based Inspection Process in Manufacturing”. In: 2019 USENIX Conference on Operational Machine Learning (OpML 19). Santa Clara, CA: USENIX Association, 2019, pp. 9–11. isbn: 978-1-939133-00-7. url: https://www.usenix. org/conference/opml19/presentation/lim.   
[F33] Bojan Karlaˇs et al. “Building Continuous Integration Services for Machine Learning”. In: Proceedings of the 26th ACM SIGKDD International Conference on Knowledge Discovery & Data Mining. Ed. by Rajesh Gupta. ACM Digital Library. New York,NY,United States: Association for Computing Machinery, 2020, pp. 2407–2415. isbn: 9781450379984. doi: 10.1145/3394486.3403290.   
[F34] Bojan Karlaˇs et al. “Ease.ml in action: towards multi-tenant declarative learning services”. In: Proceedings of the VLDB Endowment 11.12 (2018), pp. 2054–2057. issn: 2150-8097. doi: 10.14778/3229863.3236258.   
[F35] Gabriel Kronberger, Florian Bachinger, and Michael Affenzeller. “Smart Manufacturing and Continuous Improvement and Adaptation of Predictive Models”. In: Procedia Manufacturing 42 (2020), pp. 528–531. issn: 23519789. doi: 10.1016/j.promfg.2020.02.037.   
[F36] Deborah Leff and Kenneth T. K. Lim. “The key to leveraging AI at scale”. In: Journal of Revenue and Pricing Management (2021). issn: 1476-6930. doi: 10.1057/s41272-021-00320-3.   
[F37] Zhuozhao Li et al. “DLHub: Simplifying publication, discovery, and use of machine learning models in science”. In: Journal of Parallel and Distributed Computing 147 (2021), pp. 64–76. issn: 0743-7315. doi: 10 . 1016/j.jpdc.2020.08.006. url: https://www.sciencedirect.com/ science/article/pii/S0743731520303464.   
[F38] Wei-Chen Liu, Yu Ting Chiang, and Tyng-Yeu Liang. “A Development Platform of Intelligent Mobile APP Based on Edge Computing”. In: 2019 Seventh International Symposium on Computing and Networking Workshops (CANDARW). [Place of publication not identified]: IEEE, 2019, pp. 235–241. isbn: 978-1-7281-5268-4. doi: 10 . 1109 / CANDARW . 2019.00048.   
[F39] Alvaro Lopez Garcia et al. “A Cloud-Based Framework for Machine Learning Workloads and Applications”. In: IEEE Access 8 (2020), pp. 18681– 18692. doi: 10.1109/ACCESS.2020.2964386.   
[F40] Lucy Ellen Lwakatare, Ivica Crnkovic, and Jan Bosch. “DevOps for AI Challenges in Development of AI-enabled Applications”. In: 2020 28th International Conference on Software, Telecommunications and Computer Networks (SoftCOM). Ed. by Dinko Beguˇsi´c. [Piscataway, NJ]: IEEE, 2020, pp. 1–6. isbn: ***********-099-6. doi: 10.23919/SoftCOM50211. 2020.9238323.   
[F41] Lucy Ellen Lwakatare et al. “A Taxonomy of Software Engineering Challenges for Machine Learning Systems: An Empirical Investigation”. In: Agile Processes in Software Engineering and Extreme Programming. Ed. by Philippe Kruchten, Steven Fraser, and Fran¸cois Coallier. Cham: Springer International Publishing, 2019, pp. 227–243. isbn: 978-3-030-19034-7.   
[F42] Lucy Ellen Lwakatare et al. “From a Data Science Driven Process to a Continuous Delivery Process for Machine Learning Systems”. In: ProductFocused Software Process Improvement. Ed. by Maurizio Morisio, Marco Torchiano, and Andreas Jedlitschka. Cham: Springer International Publishing, 2020, pp. 185–201. isbn: 978-3-030-64148-1.   
[F43] Vladimir A. Makarov et al. “Best practices for artificial intelligence in life sciences research”. In: Drug Discovery Today (2021). issn: 1359-6446.   
[F44] Sasu M¨akinen et al. Who Needs MLOps: What Data Scientists Seek to Accomplish and How Can MLOps Help? Mar. 16, 2021. url: http : //arxiv.org/pdf/2103.08942v1.   
[F45] Yannick Martel et al. “Software Architecture Best Practices for Enterprise Artificial Intelligence”. In: INFORMATIK 2020, pp. 165–181. doi: 10.18420/INF2020{\textunderscore}16.   
[F46] Silverio Mart´ınez-Fern´andez et al. “Developing and Operating Artificial Intelligence Models in Trustworthy Autonomous Systems”. In: Research Challenges in Information Science. Ed. by Samira Cherfi, Anna Perini, and Selmin Nurcan. Vol. 415. Lecture Notes in Business Information Processing. Cham: Springer International Publishing, 2021, pp. 221–229. isbn: 978-3-030-75017-6. doi: 10.1007/978-3-030-75018- 3{\textunderscore}14.   
[F47] Manil Maskey et al. “Machine Learning Lifecycle for Earth Science Application: A Practical Insight into Production Deployment”. In: IGARSS 2019 - 2019 IEEE International Geoscience and Remote Sensing Symposium. IEEE, 2019, pp. 10043–10046. isbn: 978-1-5386-9154-0. doi: 10. 1109/IGARSS.2019.8899031.   
[F48] Hui Miao, Amit Chavan, and Amol Deshpande. “ProvDB: Lifecycle Management of Collaborative Analysis Workflows”. In: Proceedings of the 2nd Workshop on Human-In-the-Loop Data Analytics. New York, NY, USA: ACM, 2017, pp. 1–6. isbn: 9781450350297. doi: 10.1145/ 3077257.3077267.   
[F49] Hui Miao et al. “ModelHub: Deep Learning Lifecycle Management”. In: ICDE 2017. Piscataway, NJ: IEEE, 2017, pp. 1393–1394. isbn: 978-1- 5090-6543-1. doi: 10.1109/ICDE.2017.192.   
[F50] Hui Miao et al. “Towards Unified Data and Lifecycle Management for Deep Learning”. In: ICDE 2017. Piscataway, NJ: IEEE, 2017, pp. 571– 582. isbn: 978-1-5090-6543-1. doi: 10.1109/ICDE.2017.112.   
[F51] Mona Nashaat et al. “M-Lean: An end-to-end development framework for predictive models in B2B scenarios”. In: Information and Software Technology 113 (2019), pp. 131–145. issn: 0950-5849. doi: 10 . 1016 / j . infsof . 2019 . 05 . 009. url: https : / / www . sciencedirect . com / science/article/pii/S0950584919301247.   
[F52] Christopher Olston et al. “TensorFlow-Serving: Flexible, High-Performance ML Serving”. In: (2017).   
[F53] Yang Peili et al. “Deep learning model management for coronary heart disease early warning research”. In: 2018 the 3rd IEEE International Conference on Cloud Computing and Big Data Analysis (ICCCBDA 2018). Piscataway, NJ: IEEE, 2018, pp. 552–557. isbn: 978-1-5386-4301- 3. doi: 10.1109/ICCCBDA.2018.8386577.   
[F54] Istv´an P¨ol¨oskei. “MLOps approach in the cloud-native data pipeline design”. In: Acta Technica Jaurinensis (2020). doi: 10.14513/actatechjaur. 00581.   
[F55] Neoklis Polyzotis et al. “Data Lifecycle Challenges in Production Machine Learning”. In: ACM SIGMOD Record 47.2 (2018), pp. 17–28. issn: 0163-5808. doi: 10.1145/3299887.3299891.   
[F56] Neoklis Polyzotis et al. “Data Management Challenges in Production Machine Learning”. In: SIGMOD’17, May 14-19, 2017, Chicago, IL, USA. Ed. by Rada Chirkova, Jun Yang, and Dan Suciu. New York, NY, USA: ACM, 2017, pp. 1723–1726. isbn: 9781450341974. doi: 10.1145/ 3035918.3054782.   
[F57] Aiswarya Raj et al. “Modelling Data Pipelines”. In: 2020 46th Euromicro Conference on Software Engineering and Advanced Applications (SEAA). IEEE, 26.-28.08.2020, pp. 13–20. isbn: 978-1-7281-9532-2. doi: 10.1109/SEAA51224.2020.00014.   
[F58] Emmanuel Raj, Magnus Westerlund, and Leonardo Espinosa-Leal. Reliable Fleet Analytics for Edge IoT Solutions. Nice, France, Jan. 12, 2021. url: http://arxiv.org/pdf/2101.04414v1.   
[F59] Thomas Rausch and Schahram Dustdar. “Edge Intelligence: The Convergence of Humans, Things, and AI”. In: 2019 IEEE International Conference on Cloud Engineering. Los Alamitos, California, Washington, and Tokyo: Conference Publishing Services, IEEE Computer Society, 2019, pp. 86–96. isbn: 978-1-7281-0218-4. doi: 10.1109/IC2E.2019.00022.   
[F60] Thomas Rausch et al. “Towards a Serverless Platform for Edge AI”. In: 2nd USENIX Workshop on Hot Topics in Edge Computing (HotEdge 19). Renton, WA: USENIX Association, 2019. url: https://www. usenix.org/conference/hotedge19/presentation/rausch.   
[F61] Cedric Renggli et al. “Ease.ml/ci and Ease.ml/meter in action: towards data management for statistical generalization”. In: Proceedings of the VLDB Endowment 12.12 (2019), pp. 1962–1965. issn: 2150-8097. doi: 10.14778/3352063.3352110.   
[F62] Cedric Renggli et al. “Ease.ml/snoopy in action: Towards automatic feasibility analysis for machine learning application development”. In: Proceedings of the VLDB Endowment 13.12 (2020), pp. 2837–2840. issn: 2150-8097. doi: 10.14778/3415478.3415488.   
[F63] Luis Rivero et al. “Deployment of a Machine Learning System for Predicting Lawsuits Against Power Companies: Lessons Learned from an Agile Testing Experience for Improving Software Quality”. In: 19th Brazilian Symposium on Software Quality. Ed. by Ivan Machado et al. New York, NY, USA: ACM, 2020, pp. 1–10. isbn: 9781450389235. doi: 10. 1145/3439961.3439991.   
[F64] Mirella Sangiovanni, Gerard Schouten, and Willem-Jan van den Heuvel. “An IoT Beehive Network for Monitoring Urban Biodiversity: Vision, Method, and Architecture”. In: Service-Oriented Computing. Ed. by Schahram Dustdar. Cham: Springer International Publishing, 2020, pp. 33– 42. isbn: 978-3-030-64846-6.   
[F65] Sebastian Schelter et al. “On Challenges in Machine Learning Model Management”. In: IEEE Data Eng. Bull. 41 (2018), pp. 5–15.   
[F66] Johann Schleier-Smith. “An Architecture for Agile Machine Learning in Real-Time Applications”. In: Proceedings of the 21st ACM SIGKDD International Conference on Knowledge Discovery and Data Mining, August 10 - 13, 2015, Sydney, Australia. Ed. by Longbing Cao et al. New York, NY: ACM, 2015, pp. 2059–2068. isbn: 9781450336642. doi: 10.1145/2783258.2788628.   
[F67] Marc Schreiber, Kai Barkschat, and Bodo Kraft. “Using Continuous Integration to organize and monitor the annotation process of domain specific corpora”. In: 2014 5th International Conference on Information and Communication Systems (ICICS). Piscataway, NJ: IEEE, 2014, pp. 1–6. isbn: 978-1-4799-3023-4. doi: 10.1109/IACS.2014.6841958.   
[F68] Derrick C. Spell et al. “Flux: Groupon’s automated, scalable, extensible machine learning platform”. In: 2017 IEEE International Conference on Big Data. Ed. by Jian-Yun Nie et al. Piscataway, NJ: IEEE, 2017, pp. 1554–1559. isbn: 978-1-5386-2715-0. doi: 10.1109/BigData.2017. 8258089.   
[F69] Damian A. Tamburri. “Sustainable MLOps: Trends and Challenges”. In: 2020 22nd International Symposium on Symbolic and Numeric Algorithms for Scientific Computing (SYNASC). 2020, pp. 17–23. doi: 10.1109/SYNASC51798.2020.00015.   
[F70] Manasi Vartak et al. “ModelDB: a system for machine learning model management”. In: Proceedings of the Workshop on Human-In-the-Loop Data Analytics - HILDA ’16. Ed. by Carsten Binnig, Alan Fekete, and Arnab Nandi. New York, New York, USA: ACM Press, 2016, pp. 1–3. isbn: 9781450342070. doi: 10.1145/2939502.2939516.   
[F71] Chandrasekar Vuppalapati et al. “Automating Tiny ML Intelligent Sensors DevOPS Using Microsoft Azure”. In: 2020 IEEE International Conference on Big Data (Big Data). IEEE, 2020, pp. 2375–2384. isbn: 978- 1-7281-6251-5. doi: 10.1109/BigData50022.2020.9377755.   
[F72] Sven Wachsmuth et al. The Robot Head ”Flobi”: A Research Platform for Cognitive Interaction Technology. 2012.   
[F73] Fan Yun et al. “Understanding Machine Learning Model Updates Based on Changes in Feature Attributions”. In: (2020).   
[F74] Matei Zaharia et al. “Accelerating the machine learning lifecycle with MLflow”. In: IEEE Data Eng. Bull. 41.4 (2018), pp. 39–45.   
[F75] Huaizheng Zhang et al. “MLModelCI: An Automatic Cloud Platform for Efficient MLaaS”. In: Proceedings of the 28th ACM International Conference on Multimedia. Ed. by Chang Wen Chen. ACM Digital Library. New

York,NY,United States: Association for Computing Machinery, 2020, pp. 4453–4456. isbn: 9781450379885. doi: 10.1145/3394171.3414535. [F76] Yue Zhou, Yue Yu, and Bo Ding. “Towards MLOps: A Case Study of ML Pipeline Platform”. In: 2020 International Conference on Artificial Intelligence and Computer Engineering (ICAICE). IEEE, 2020, pp. 494– 500. isbn: 978-1-7281-9146-1. doi: 10.1109/ICAICE51518.2020.00102.

# List of Informal References

[I1] Beena Ammanath et al. MLOps: Industrialized AI: Scaling model development and operations with a dose of engineering and operational discipline. 2021. url: https://www2.deloitte.com/us/en/insights/ focus/tech-trends/2021/mlops-industrialized-ai.html (visited on 08/10/2021).   
[I2] Architecture for MLOps using TFX, Kubeflow Pipelines, and Cloud Build. 11.08.2021. url: https://cloud.google.com/architecture/architecturefor-mlops-using-tfx-kubeflow-pipelines-and-cloud-build (visited on 08/14/2021).   
[I3] Matthew Arnold et al. Towards Automating the AI Operations Lifecycle. Mar. 2020. url: http://arxiv.org/pdf/2003.12808v1.   
[I4] David Aronchick. CI/CD + ML == MLOps - The Way To Speed Bringing Machine Learning To Production - David Aronchick: DeliveryConf. Seattle, 2020. url: https://www.youtube.com/watch?v=uOCR4Xw-BZ8 (visited on 08/12/2021).   
[I5] David Aronchick et al. Git-based $\mathit { C I } \ / \ \mathit { C D }$ for Machine Learning & MLOps. 2020. url: https : / / www . iguazio . com / blog / git - based - ci-cd-for-machine-learning-mlops/ (visited on 08/09/2021).   
[I6] Akshay Arora et al. ISTHMUS: Secure, Scalable, Real-time and Robust Machine Learning Platform for Healthcare. 2019. url: http://arxiv. org/pdf/1909.13343v2.   
[I7] Kate Baroni. Getting AI/ML and DevOps working better together — Blog e aggiornamenti di Azure — Microsoft Azure. Ed. by Microsoft Azure. 2018. url: https://azure.microsoft.com/it-it/blog/getting-aiml-and-devops-working-better-together/ (visited on 08/08/2021).   
[I8] Cristiano Breuel. ML Ops: Machine Learning as an Engineering Discipline: As ML matures from research to applied business solutions, so do we need to improve the maturity of its operation processes. 2020. url: https://towardsdatascience.com/ml-ops-machine-learning-asan-engineering-discipline-b86ca4874a3f (visited on 08/15/2021).   
[I9] Roger Creus Castanyer, Silverio Mart´ınez-Fern´andez, and Xavier Franch. Integration of Convolutional Neural Networks in Mobile Applications. Mar. 2021. url: http://arxiv.org/pdf/2103.07286v1.   
[I10] Continuous Delivery for Machine Learning (CD4ML) Webinar Series. url: https://www.thoughtworks.com/continuous- delivery- formachine-learning (visited on 08/09/2021).   
[I11] de la R´ua Mart´ınez, Javier. Scalable Architecture for Automating Machine Learning Model Monitoring. 2020.   
[I12] Paul Duvall. Continuous Delivery for Machine Learning With AWS CodePipeline and Amazon SageMaker: AWS Summit. Berlin, 2018. url: https: / / dzone . com / articles / screencast - continuous - delivery - for - machine-learnin (visited on 08/10/2021).   
[I13] Dillon Erb. CI/CD for Machine Learning & AI: FirstMark’s Data Driven NYC. 2019. url: https://www.youtube.com/watch?v $=$ JpWUw4co75s (visited on 08/11/2021).   
[I14] Yochay Ettun. Making your machine learning operational with CI/CD. Ed. by cnvrg.io. 2019. url: https : / / cnvrg . io / ci - cd - machine - learning/ (visited on 08/09/2021).   
[I15] Michael Felderer and Rudolf Ramler. Quality Assurance for AI-based Systems: Overview and Challenges. Feb. 2021. url: http : / / arxiv . org/pdf/2102.05351v1.   
[I16] Emily Gorcenski. Continuous Delivery for Machine Learning (CD4ML): Keeping Models fresh with Continuous Intelligence: Data Engineering Meetup Berlin. Berlin, 2019. url: https://www.youtube.com/watch? v=UzVa5azAHkc (visited on 08/10/2021).   
[I17] Emily Gorcenski. Continuous Delivery For Machine Learning: Patterns And Pains - Emily Gorcenski: DeliveryConf. 2020. url: https://www. youtube.com/watch?v=bFW5mZmj0nQ (visited on 08/14/2021).   
[I18] Tuomas Granlund et al. MLOps Challenges in Multi-Organization Setup: Experiences from Two Real-World Cases. 2021. url: http://arxiv. org/pdf/2103.08937v1.   
[I19] Yu Guo et al. Under the Hood of Uber ATG’s Machine Learning Infrastructure and Versioning Control Platform for Self-Driving Vehicles. Ed. by Uber. 2020. url: https://eng.uber.com/machine-learningmodel-life-cycle-version-control/ (visited on 08/14/2021).   
[I20] Abhishek Gupta and Erick Galinkin. Green Lighting ML: Confidentiality, Integrity, and Availability of Machine Learning Systems in Deployment. 2020. url: http://arxiv.org/pdf/2007.04693v1.   
[I21] Mark Haakman et al. “AI lifecycle models need to be revised. an exploratory study in fintech”. In: arXiv preprint arXiv:2010.02716 (2020).   
[I22] Yaron Haviv. Webinar: MLOps automation with Git Based CI/CD for ML: Cloud Native Computing Foundation (CNCF). 2020. url: https: //www.youtube.com/watch?v=VCUDo9umKEQ (visited on 08/12/2021).   
[I23] Jeremy Hermann and Mike Del Balso. Meet Michelangelo: Uber’s Machine Learning Platform. Ed. by Uber. 2017. url: https://eng.uber. com/michelangelo-machine-learning-platform/ (visited on 08/14/2021).   
[I24] Chong Huang, Arash Nourian, and Kevin Griest. Hidden Technical Debts for Fair Machine Learning in Financial Services. 2021. url: http:// arxiv.org/pdf/2103.10510v2.   
[I25] Frances Ann Hubis, Wentao Wu, and Ce Zhang. Quantitative Overfitting Management for Human-in-the-loop ML Application Development with ease.ml/meter. 2019. url: http://arxiv.org/pdf/1906.00299v3.   
[I26] Konstantinos Katsiapis et al. Towards ML Engineering: A Brief History Of TensorFlow Extended (TFX). 2020.   
[I27] Nate Keating. An introduction to MLOps on Google Cloud. 2020. url: https://www.youtube.com/watch?v=6gdrwFMaEZ0 (visited on 08/13/2021).   
[I28] Edward Kent and Paul Doran. Continuous deployment of machine learning models by Edward Kent & Paul Doran: Devoxx. London, 2019. url: https://www.youtube.com/watch?v=wnx5yYVf2hQ (visited on 08/13/2021).   
[I29] Asif Khan. Continuous Delivery for AI Applications: QCon.ai. 2018. url: https://www.youtube.com/watch?v=OgedlOxeT2c (visited on 08/11/2021).   
[I30] Alexander Lavin et al. Technology Readiness Levels for Machine Learning Systems. Nov. 2021. url: http://arxiv.org/pdf/2101.03989v1.   
[I31] Yong Liu and Andrew Brooks. Continuous Delivery of Deep TransformerBased NLP Models Using MLflow and AWS Sagemaker for Enterprise AI Scenarios - Databricks: Spark $^ +$ AI Summit 2020. 2020. url: https: //databricks.com/session_na20/continuous-delivery-of-deeptransformer-based-nlp-models-using-mlflow-and-aws-sagemakerfor-enterprise-ai-scenarios (visited on 08/09/2021).   
[I32] Sasu M¨akinen. “Designing an open-source cloud-native MLOps pipeline”.

In: (2021).

[I33] Theodore Meynard and Jean Carlo Machado. Laying the foundation of our open source ML platform with a modern CI/CD pipeline and MLflow. Pydata Berlin Meetup, 2021. url: https://inside.getyourguide. com/blog/2021/3/3/-open-source-ml-platform-with-a-modernci-cd-pipeline-and-mlflow (visited on 08/09/2021).   
[I34] MLOps: Continuous delivery and automation pipelines in machine learning. 10.08.2021. url: https : / / cloud . google . com / architecture / mlops - continuous - delivery - and - automation - pipelines - in - machine-learning (visited on 08/10/2021).   
[I35] Mary Grace Moesta and Peter Tamisin. Productionalizing Models through CI/CD Design with MLflow: Spark + AI Summit 2020. Ed. by Databricks. 2020. url: https://databricks.com/de/session_na20/productionalizingmodels-through-ci-cd-design-with-mlflow (visited on 08/09/2021).   
[I36] Jan Mulkens. MLOps, Automated Machine Learning Made Easy: ML Conference. 2020. url: https://www.youtube.com/watch?v=4n2SYq0bbnw (visited on 08/11/2021).   
[I37] Elle O’Brien. Continuous Integration for Machine Learning. 2021. url: https://www.youtube.com/watch?v $\mathbf { \equiv }$ A3OEaaiGPhk (visited on 08/11/2021).   
[I38] Shivani Patel. MLOps feature dive: CI/CD with GitHub Actions. 2020. url: https://www.youtube.com/watch?v=qRi1zy4lNZw (visited on 08/11/2021).   
[I39] Shivani Patel and Jordan Edwards. MLOps: Accelerating Data Science with DevOps - Microsoft: DOES19. Las Vegas, 2019. url: https : / / www.youtube.com/watch?v $=$ pqppGvTJm-A (visited on 08/10/2021).   
[I40] Nick Pentreath. Continuous Deployment for Deep Learning: Spark + AI Summit Europe. Amsterdam, 2019. url: https://www.youtube.com/ watch?v=qwyW0pHz9ag (visited on 08/13/2021).   
[I41] Matthias Popp. Comprehensive Support of the Lifecycle of Machine Learning Models in Model Management Systems. 2019. doi: 10.18419/OPUS10690.   
[I42] Emmanuel Raj. “Edge MLOps framework for AIoT applications”. In: (2020).   
[I43] Thomas Rausch, Waldemar Hummer, and Vinod Muthusamy. PipeSim: Trace-driven Simulation of Large-Scale AI Operations Platforms. June 2020. url: http://arxiv.org/pdf/2006.12587v1.   
[I44] Cedric Renggli et al. A Data Quality-Driven View of MLOps. 2021. url: http://arxiv.org/pdf/2102.07750v1.   
[I45] Cedric Renggli et al. Continuous Integration of Machine Learning Models with ease.ml/ci: Towards a Rigorous Yet Practical Treatment. Jan. 3, 2019. url: http://arxiv.org/pdf/1903.00278v1.   
[I46] Sasha Rosenbaum. CI/CD for Machine Learning. Ed. by InfoQ. QCon, 2020. url: https : / / www . infoq . com / presentations / ci - cd - ml/ (visited on 08/08/2021).   
[I47] P. Santhanam, Eitan Farchi, and Victor Pankratius. “Engineering reliable deep learning systems”. In: arXiv preprint arXiv:1910.12582 (2019). url: https://arxiv.org/abs/1910.12582.   
[I48] Danilo Sato. CD4ML and the challenges of testing and quality in ML systems: TensorFlow London Meetup. Ed. by Thoughtworks. London, 2020. url: https://www.youtube.com/watch?v=adexWmMYLcw (visited on 08/10/2021).   
[I49] Danilo Sato, Arif Wider, and Christoph Windheuser. Continuous Delivery for Machine Learning: Automating the end-to-end lifecycle of Machine Learning applications. 2019. url: https://martinfowler.com/ articles/cd4ml.html (visited on 03/12/2021).   
[I50] Danilo Sato, Arif Wider, and Christoph Windheuser. Continuous delivery for machine learning: Getting machine learning applications into production is hard. 2019. url: https : / / www . thoughtworks . com / insights/articles/intelligent- enterprise- series- cd4ml (visited on 08/10/2021).   
[I51] Alejandro Saucedo. A Practical CI/CD Framework for Machine Learning at Massive Scale: FOSDEM 2020. 2020. url: https://archive. fosdem.org/2020/schedule/event/a_practical_cicd_framework_ for_machine_learning_at_massive_scale/ (visited on 08/09/2021).   
[I52] Daniel Schruhl and Christoph Windheuser. Continuous Delivery for Machine Learning Applications with Open Source Tools: ML Conference. 2020. url: https://www.youtube.com/watch?v=ub9XIgcUMAQ (visited on 08/14/2021).   
[I53] Roman Seyffarth. Machine learning: Moving from experiments to production. 2019. url: https : / / blog . codecentric . de / en / 2019 / 03 / machine-learning-experiments-production/ (visited on 08/15/2021).   
[I54] Michael Shtelma and Thunder Shiviah. Continuous Delivery of MLEnabled Pipelines on Databricks using MLflow: Spark $^ +$ AI Summit 2020. 2020. url: https://www.youtube.com/watch?v $=$ Gjns_Z0zxt8 (visited on 08/14/2021).   
[I55] Xavier Sierra. Our continuous deployment pipeline. 2018. url: https:// techblog.fexcofts.com/2018/09/03/our-continuous-deploymentpipeline/ (visited on 08/10/2021).   
[I56] Aditi Singhal and Vivek Kumar. Continuous Integration and Continuous Delivery for Machine Learning Models (CodeLabs Tech Talk 2020): CodeLabs Tech Talk. 2020. url: https://www.youtube.com/watch?v $=$ YjrkIsDZ0Hc (visited on 08/12/2021).   
[I57] Helge Spieker and Arnaud Gotlieb. Towards Testing of Deep Learning Systems with Training Set Reduction. 2019. url: http://arxiv.org/ pdf/1901.04169v1.   
[I58] Srivatsan Srinivasan. An Introduction to MLOps: Nuts and Bolts of MLOps. 13.03.2021. url: https : / / www . youtube . com / watch ? v = K6CWjg09fAQ (visited on 08/10/2021).   
[I59] Vlad Stirbu et al. Extending SOUP to ML Models When DesigningCertified Medical Systems. Mar. 17, 2021. url: http://arxiv.org/pdf/ 2103.09510v1.   
[I60] Stumpf, Kevin, Bedratiuk, Stepan and Cirit Olcay. Michelangelo PyML: Introducing Uber’s Platform for Rapid Python ML Model Development. 2018. url: https://eng.uber.com/michelangelo-pyml/ (visited on 08/15/2021).   
[I61] Rohit Tandon and Sanghamitra Pati. ML-Oops to MLOps. 2021. url: https : / / www2 . deloitte . com / us / en / blog / deloitte - on - cloud - blog/2021/ml-oops-to-mlops.html (visited on 08/10/2021).   
[I62] Ram Mohan Vadavalasa. “End to end CI/CD pipeline for Machine Learning”. In: International Journal of Advance Research, Ideas and Innovations in Technology 6.3 (2020). url: https : / / www . ijariit . com / manuscript/end-to-end-ci-cd-pipeline-for-machine-learning/.   
[I63] Larysa Visengeriyeva et al. Machine Learning Operations. Ed. by INNOQ. 2021. url: https://ml-ops.org/ (visited on 08/09/2021).   
[I64] Jarek Wilkiewicz, Kevin Haas, and Doshi, Tulsee, Katsiapis, Konstantinos. From Research to Production with TFX Pipelines and ML Metadata: Google I/O 2019. 2019. url: https://blog.tensorflow.org/ 2019/05/research- to- production- with- tfx- ml.html (visited on 08/10/2021).   
[I65] Christoph Windheuser and Danilo Sato. MLOps: Continuous Delivery for Machine Learning on AWS: AWS Whitepapers & Guides. 2020. url: https : / / d1 . awsstatic . com / whitepapers / mlops - continuous - delivery- machine- learning- on- aws.pdf?did $=$ wp_card&trk $=$ wp_ card (visited on 08/10/2021).   
[I66] Doris Xin et al. Production Machine Learning Pipelines: Empirical Analysis and Optimization Opportunities. 2021. url: http://arxiv.org/ pdf/2103.16007v1.   
[I67] Runyu Xu. “A Design Pattern for Deploying Machine Learning Models to Production”. In: (2020).   
[I68] Hasan Yasar. Leveraging DevOps and DevSecOps to Accelerate AI Development and Deployment. 2020.   
[I69] Hasan Yasar. Software Development AI and DevOps. 2020.   
[I70] Monte Zweben. How Optimizing MLOps Can Revolutionize Enterprise AI. 2021. url: https : / / www . infoq . com / articles / optimizing - mlops-enterprise-ai/ (visited on 08/10/2021).