# 北京交通大学

# 硕 士 专 业 学 位 论 文

SP 软件项目开发进度管理研究 Research on development schedule management of SP software project

作者：秦香宇

导师：顾元勋

北京交通大学

2022 年 5 月

# 学位论文版权使用授权书

本学位论文作者完全了解北京交通大学有关保留、使用学位论文的规定。特授权北京交通大学可以将学位论文的全部或部分内容编入有关数据库进行检索，提供阅览服务，并采用影印、缩印或扫描等复制手段保存、汇编以供查阅和借阅。同意学校向国家有关部门或机构送交论文的复印件和磁盘。学校可以为存在馆际合作关系的兄弟高校用户提供文献传递服务和交换服务。

(保密的学位论文在解密后适用本授权说明)

学位论文作者签名： 导师签名：破之勒签字日期：2022年5月30日 签字日期：2022年5月30日

# 北京交通大学硕 士 专 业 学 位 论 文

SP软件项目开发进度管理研究

Research on development schedule management of SP software project

作者姓名：秦香宇 学 号：20140419导师姓名：顾元勋 职 称：教授专业学位类别：工程管理 学位级别：硕士

北京交通大学2022 年 5 月

# 致谢

本科毕业，受家庭条件和自身眼界所限就直接参加工作里，没有继续读研深造，但是工作的三年里，一直想要再回到校园，感受校园生活的单纯和美好。感谢命运的眷顾和我自己的努力，在2020年我重新踏入里校园。在北交大两年的学习生活中我也收获颇多。

非常感谢我的导师，在我的研究生课程学习上给了我很好的建议，也给我的工作发展指明了方向。在论文选题、开题、撰写上给了我很多实质上的启发和帮助。就是有一个小遗憾，由于课程时间安排的问题，一直没有机会上一节老师的课。

非常感谢研究生课程的第一个老师 IT 项目管理的老师，她让我对项目从开发视角开始转变管理的视角，这是一个非常好的开始。感谢吕海军老师，高级管理课程对我而言完全是打开了新世界的大门。还有其他很多课程的老师。

也非常感谢我的同学们，在研究生学习和生活中给了我很多的帮助。开学遇到的第一个同学，他像一个老大哥一样帮助了我和其他同学很多。还有很多帮助我的同学，非常感谢大家。

一晃两年就要过去了，感谢在校园的学习和生活，下一个阶段，希望老师和同学们的工作和生活都顺顺利利。

# 摘要

SP软件项目作为公司核心业务的技术支持，随着业务的飞速发展，SP软件项目的开发进度不足以支撑业务发展的速度，加之SP软件项目的需求也在频繁变更，直接导致 SP 软件项目在前期开发中遇到进度失控的问题。为了改变现状，降低项目的延期风险，支持业务的快速发展，开始尝试运用 Scrum 敏捷框架和关键链来改造整个项目和团队。

本文首先阐述了论文的研究背景、目的和意义，然后通过文献研究法，归纳了项目进度管理概念、进度管理方法、关键链理论、敏捷开发概念、Scrum 敏捷开发框架，分析了关键链项目管理方法和 Scrum 敏捷框架在软件项目开发进度管理中的适用性和优越性。

其次，通过案例研究法重点分析 SP 软件项目开发进度管理的现状和问题，主要介绍了项目背景、需求范围，分析了项目的组织结构和项目开发模型上的问题，总结了项目进度计划和控制问题原因。然后提出了对应的改造方案，第一、项目组织结构由弱矩阵式改造为项目式，很好的解决了项目资源冲突，和不能灵活响应需求变化的问题；第二、采用敏捷开发模型，该模型即能积极响应需求变化，又能够快速交付产品，解决了常用的增量模型不能应对频繁的需求变更，导致项目进度延期的问题；第三、整个项目采用 Scrum 敏捷框架，用户故事描述需求来快速安排项目进度计划，Sprint 对项目进行分解迭代，固定有规律的节奏帮助团队清楚开发速度，同时提高效率，通过四个会议串联整个开发流程，使得SP项目更合理的做项目进度计划和更有效进度控制；第四、关键链项目管理方法关注了人的行为因素和资源约束，通过该方法对需求复杂或资源有限的 Sprint，能更有效的进行项目进度的控制，避免项目延期。

最后，通过对比分析法，展示和分析了 SP软件项目开发进度管理改造方案的实施效果，来证明改造方案的有效性和论文结论的严谨性。

本文运用 Scrum 敏捷框架结合关键链方法，通过对SP软件项目的开发进度管理方案的研究和总结，为之后的项目进度管理提供方法和模型，也可以运用到相似的业务为导向、需求变化快的企业，为其软件项目开发进度管理提供解决方案。

关键词：项目进度管理；Scrum敏捷框架；关键链

# ABSTRACT

SP software project is the technical support of the company's core business. With the rapid development of business, the development progress of SP software project is not enough to support the speed of business development. In addition, the requirements of SP software project are changing frequently, which directly leads to the problem of out of control progress in the early development of SP software project. In order to reduce the risk of project delay and support The rapid development of business, we began to try to use scrum agile framework and critical chain to transform the whole project and team.

This paper first expounds the research background, purpose and significance of the paper, and then summarizes the concept of project schedule management, schedule management method, critical chain theory, agile development concept and scrum agile development framework through literature research method, and analyzes the applicability and superiority of critical chain project management method and scrum agile framework in software project development schedule management.

Secondly, through the case study method, this paper focuses on the current situation and problems of SP software project development schedule management, mainly introduces the project background and demand scope, analyzes the problems in the project organization structure and project development model, and summarizes the reasons for the problems of project schedule and control. Then the corresponding transformation scheme is put forward. First, the project organization structure is transformed from weak matrix type to project type, which solves the problems of project resource conflict and inability to respond flexibly to demand changes; Second, the agile development model is adopted, which can not only actively respond to demand changes, but also quickly deliver products, which solves the problem that the commonly used incremental model can not deal with frequent demand changes, resulting in the delay of project progress; Third, the whole project adopts scrum agile framework, and the user story describes the needs to quickly arrange the project schedule. Sprint decomposes and iterates the project, and fixes a regular rhythm to help the team understand the development speed and improve efficiency. The whole development process is connected in series through four meetings, so that the SP project can make more reasonable project schedule and more effective progress control; Fourth, the critical chain project management method pays attention to human behavior factors and resource constraints. This method can more effectively control the project progress and avoid project delay for sprint with complex demand or limited resources.

Finally, through the comparative analysis method, the implementation effect of SP software project development schedule management transformation scheme is displayed and analyzed to prove the effectiveness of the transformation scheme and the preciseness of the conclusion of the paper.

Using scrum framework and critical chain method, this paper studies and summarizes the development schedule management scheme of SP software project, provides methods and models for the subsequent project schedule management, and can also be applied to similar business oriented enterprises with fast changing needs to provide solutions for their software project development schedule management.

KEYWORDS：Project Schedule Management;Scrum Framework;Critical Chain

目录  
摘要... . iii  
ABSTRACT.. . iv  
1 绪论....  
1.1 研究背景.. . 1  
1.2 研究目的和意义.. .. 2  
1.3 研究的内容与论文框架. .. 2  
1.3.1 研究内容.. . 2  
1.3.2 论文框架.. . 3  
2 相关理论基础. . 5  
2.1 项目进度管理理论. .. 5  
2.1.1 项目进度管理概念. .. 5  
2.1.2 项目进度管理方法. .. 5  
2.1.3 关键链项目管理方法.. ... 7  
2.1.4 关键链理论对软件项目开发进度管理的适用性. .... 8  
2.1 敏捷开发理论. ... 8  
2.2.1 敏捷开发概述.. .. 8  
2.2.2 Scrum 敏捷开发框架.. ..... 10  
2.2.3 Scrum 敏捷开发在软件项目进度管理中的优势. ..12  
2.3 文献述评.. . 13  
3 SP 软件项目开发进度管理的现状与问题分析. ..15  
3.1 SP 软件项目概述.. ... 15  
3.1.1 项目背景.. . 15  
3.1.2 项目目标和范围.. ............................... .................. ....... 17  
3.2 SP软件项目组织结构分析.. ............ ..... 18  
3.2.1 组织结构概述.. .. 18  
3.2.2 组织结构特点.. ... 20  
3.2.3 组织结构存在问题.. .. 21  
3.3 SP软件项目开发模型分析.. ..21  
3.3.1 开发模型概述. . 21  
3.3.2 开发模型特点.. 22  
3.3.3 开发模型存在问题.. 22  
3.4 SP 软件项目进度管理现状. .23  
3.4.1 项目工作分解结构. . 23  
3.4.2 项目进度计划.. . 23  
3.4.3 项目进度控制.. . 25  
3.5 SP 软件项目进度管理问题分析.. ..26  
3.5.1 进度计划制定不科学. . 27  
3.5.2 进度管理和控制不完善. . 27  
3.5.3 需求变更造成的进度延迟问题. . 28  
3.5.4 项目进度管理问题原因分析. . 28

# 4 SP 软件项目开发进度管理的改造方案. .31

# 4.1 项目组织结构的改造. 31

# 4.2 运用Scrum敏捷框架改造项目开发模型. .33

# 4.3 运用Scrum敏捷框架改造项目进度管理. ..35

# 4.4 Scrum 敏捷框架结合关键链项目管理的进度管理优化. ..37

# 5 SP 软件项目开发进度管理改造方案的实施效果. .49

5.1 新旧项目组织结构对比.. 49  
5.2 新旧项目开发模型对比. 49  
5.3 新旧项目进度管理对比.. 50  
5.3.1 进度计划制定对比. 50  
5.3.2 进度控制对比. 50

# 5.4 Scrum 敏捷方法和关键链方法的成效. .50

5.4.1 需求完成情况. 51  
5.4.2 进度延期和加班情况. 52  
5.4.3 项目质量情况. 52  
5.4.4 业务部门的评价.. 53

# 6 结论与展望. 54

6.1 结论.. . 54  
6.2 不足与展望. . 54  
参考文献.. . 56  
独创性声明.. .. 58  
学位论文数据集. 59

# 1 绪论

# 1.1 研究背景

随着互联网技术的迅猛发展，以大数据、人工智能技术为等代表的软件开发项目需求日益旺盛，这也给了大量具备创新能力的软件科技公司以可乘之机。然而，在全民互联网时代下，信息瞬息万变，导致软件项目的需求的变化也非常快，给目前的软件项目管理，尤其是软件项目开发进度的管理提出了严峻的挑战。

进度管理是软件项目开发管理的核心内容之一。对项目团队来讲，进度管理不好会导致严重的问题。首先使得团队的工作效率低下，会导致项目团队成员加班加点工作却仍然不能很好完成项目的情况频频出现，团队成员非常辛苦却得不到好的绩效，员工满意度会明显降低，进而最终导致人才的流失，项目陷入困境，对项目管理着来说是致命的；其次，会影响企业的整体发展，如果项目不能按期完工，会导致需求方业务方对研发的满意度降低，如果企业因此失去一个商业机会，一次创收机会，将会是一个更严重的损失，因此管理好项目进度尤其重要。

与此同时，面对经济环境的快速变化，需求方对软件项目交付时间要求越来越高。如何能更准确的估算出开发项目所需要的资源，能规划出来项目工期安排是重点也是难点问题。尤其在信息瞬息万变的情况下，如何能既顺应变化，又能控制住项目进度显得尤为重要。

传统的软件项目开发模型，适合需求明确且周期较长的项目。但受信息化浪潮的影响，客户需求愈发的多样化、个性化，且频繁变更，大大增加了项目开发的风险，甚至会导致项目“流产”。企业如果要想在竞争中获得优势，就需要新的软件研发方法论、流程和进度控制技术。敏捷开发方法得到了众多研发团队的追捧，因为它的开发速度更快，能够灵活应对频繁的需求变化。相比传统开发模式，其创造商业价值和投资回报的效率更高的特点使其获得了更多企业的青睐。

本人所在公司是一家互联网公司，SP 软件项目是支撑公司核心业务的重要项目。SP 项目主要依托内的全国 100 多家实体特药药房，为药房的进销存管理、患者管理、运营管理等提供软件系统支持，主要的需求方是公司的业务部门，业务部门同时也是 SP 项目资源和费用的承担方。市场环境快速变化的新形势下，面对业务方越来越多的需求和需求的频繁变更，研发的速度却远远跟不上，导致项目进度的严重拖后，业务需求方满意度也在逐步降低。究其原因是项目管理上的问题，特别是项目进度管理缺乏科学、适合的项目进度管理方法。对于业务需求方的要求，传统的增量开发模型明显已经无法适应，公司的研发部门也在思考引入新的开发方法来改变目前的困境，提高业务方的信心，提高部门话语权，也同时增强公司的核心竞争力。本文侧重运用敏捷的方法论和关键链方法为SP软件项目开发的进度管理提供优化方案。

# 1.2 研究目的和意义

根据公司几个软件系统，主要是 SP 项目中软件系统在开发中遇到的问题，比如资源冲突、任务延期的情况，说明进度管理有严重问题。在项目的研发资源有限、时间紧张的情况下，通过良好的项目进度管理方法来提高项目开发效率是十分重要的。选题将重点放在研究进度管理理论和实践上，总结SP 项目开发前期存在的问题，并提出解决相关问题的方案，改造优化项目进度管理。为SP项目后期开发以及公司以后其它的软件项目在项目进度管理积累经验，为之后的公司项目实施提供参考案例，也为业务为导向，需求变化快的同类型企业，在软件开发项目管理上提供借鉴意义。

# 1.3 研究的内容与论文框架

本论文的研究目标，主要是以 SP 软件开发项目为研究对象，总结了公司背景、SP 项目的背景、组织架构、开发模型等，阐述了 SP 软件项目开发的前期项目进度落后的情况。然后对 SP 项目进行深入分析，分析 SP 项目在项目进度管理方面的关键问题即原因。最后针对这些问题，通过引入科学的项目管理理论作为解决方案模型，即采用 Scrum 敏捷开发的方法论，结合关键链项目管理方法的模型，来解决项目资源冲突、项目进度落后问题，改变SP 软件开发项目进度管理现状，保证项目顺利进行。

# 1.3.1 研究内容

论文将通过 SP 项目在实施前期项目过程中暴露的普遍问题，比如组织架构不合理，开发模型不合适，进度计划制定有缺陷，进度控制方法不科学、需求变更控制不合理等问题，通过研究进度管理理论和实践，分析SP项目存在问题，并提出解决相关问题的方案，以此提高项目进度管理的质量。

敏捷方法在很多企业已经有应用，但是敏捷方法论和关键链项目管理方法结合的方法相结合对项目进度进行管理的研究还较少。本文将敏捷框架中使用关键链项目管理方法，通过在 SP 软件项目进度管理中的应用，改善 SP 软件项目开发进度管理现状，解决进度管理中存在的问题，证明敏捷方法与关键链法相结合能有效提高工程进度管理质量，为公司及相关企业提供进度管理的有效方法及借鉴案例。

# 1.3.2 论文框架

本文首先通过文献归纳与分析，对软件开发项目进展管理的关键要素进行分析，构建软件开发项目进展管理方案模型，即敏捷开发和关键链相结合的模型。然后通过案例研究法，针对 SP 软件项目开发进度管理的具体案例，分析了该项目进度管理的现状以及存在的问题，并运用关键链理论和敏捷开发方法的方案模型进行优化，最后对比分析评价优化方案的实施效果。

本文的研究框架如图1.1所示。

本文共分为六章，第二章到第五章是本文的主体内容：

第一章绪论主要阐述论文的研究背景、研究意义和目标，论文内容和论文研究框架。

第二章是从关键词出发，对项目进度管理、关键链、敏捷理论、Scrum 敏捷框架的理论进行梳理和归纳，也对相关研究成果进行了分析与评述，为后续形成进度管理改造方案的模型提供理论基础。

第三章针对 SP 软件开发项目进度管理的具体案例，阐述了该的项目现状，介绍了该项目的背景、目标和范围，分析了该项目组织结构、开发模型、进度管理方面的问题。本章为案例研究的资料收集提供参考。

第四章针对 SP 软件开发项目进度管理的现状和问题提出优化方案，并详述了在组织结构、开发模型、进度管理方面的具体改造方案。

第五章是改造方案的实施效果评价，将新旧方案的各个方面进行对比分析，并对优化方案实施效果进行总结和评价。

第六章是结论部分，总结了论文主要研究内容、结论，对研究的局限性进行总结，并提出后续的研究和应用建议。

![](images/2d3f671a9dda8699e8c397521fc37b13163ef25d49f3171121d6a7a86642b723.jpg)  
图 1.1 论文框架图  
Fig.1.1 The overall structure of this paper

# 2 相关理论基础

# 2.1 项目进度管理理论

# 2.1.1 项目进度管理概念

项目是一个组织为实现其目标，在一定的时间期限内，通过投入一定的人员和资源，开展一种独特性的一次性工作[1]。《PMBOK $^ \mathrm { \textregistered }$ 指南》中对项目的定义是为创造独特产品、服务或成果而进行的临时性工作[2]。不同的定义，但是可以分析出项目的基本特性：

（1）临时性，有明确起始时间是项目的最重要的特性之一，同时也是有别于其他工作的最大区别。（2）独特性，项目要以交付独特产品、服务或成果等目标为启动的前提。（3）不确定性，基于项目临时性与独特性意味着在项目的执行中会出现诸多变化，随着社会经济的发展，项目的需求易变性也越来越高，项目中的风险也逐渐增多，而这些都是项目不确定性的表现。

项目进度管理始终是项目管理中的核心内容，项目进度管理简单来说就是要保证项目团队在规定的时间内完成项目。项目进度管理的研究主要研究的是项目进度的计划和控制策略，通过分析在项目进度管理过程中存在的广泛性的问题，采用相应的方案制订进度计划，并实施动态监控来保证项目工程顺利运行[3]。项目进度管理主要涉及项目进度计划制定和项目进度控制两部分。项目进度计划的目的是通过编制项目进度计划，使项目各项活动任务的实施形成一个有机整体[4]。项目进度计划的制定步骤主要包括制定工作分解结构，估计时间、成本和需要的资源，然后明确角色与责任，最后制定项目进度表。项目计划控制与实施主要包括测量进度（例如用挣值分析法跟踪项目进度），当发现活动实际进度与计划出现较大偏差时，分析偏差原因，采取措施，调整计划使活动与计划相吻合。

# 2.1.2 项目进度管理方法

常用的进度计划和控制理论和方法包括工作分解结构（WBS）、甘特图（Ganttchart）、关键路径法（Critical Path Method, CPM）和计划评审技术（ProgramEvaluation and Review Technique, PERT）等[5]。

工作分解结构（WBS）是项目开展的基础工作，目的是将项目按照一定的规则进行分解，将项目分解成一个个任务，每个任务再分解成一项项工作，再把一项项工作分配到各个不同角色的项目成员，直到无法分解为止。需要注意，在WBS的每个层级，父级代表了其全部子级的工作总和，不能遗漏任何部分的工作量[6]。

1917 年美国科学家亨利·劳伦斯·甘特（Henry Laurence Gantt）提出的一种项目进度管理方法——甘特图（Gantt Chart），其特点是简单、直观[7]。甘特图是小型项目管理中编制项目进度计划的主要工具。对于大型复杂的项目，由于其未能表示出各项活动之间的关系和显示影响项目工期的关键，因此，应用起来有一定的困难和局限性[8]。

关键路径法（Critical Path Method, CPM）是由美国杜邦公司（DuPont）的负责人 Morgan Walker 和雷明顿兰德公司（Remington Rand）提出的，是一种用网络图来表示项目计划的管理方法。美国杜邦公司的项目中在使用了关键路径法后，项目工期和成本都得到了大幅下降[9]。主要采用关键路径法对工程进度状况进行监控，获取关键路径步骤如下：

(1) 确定各个活动之间的依赖关系，然后对任务进行排序。(2) 用箭线将具有紧前紧后关系的活动连接起来。(3) 估计活动的持续时间，并通过顺推和逆推两种方法计算出所有活动的时间值，具体包括最早开始时间、最早结束时间、最晚开始时间、最晚结束时间、总时差和自由时差[10]。

(4) 所有总时差为零的任务所组成的路线，就是关键路径。

活动持续时间在关键路径上的总和即为工程工期。能否按时完成关键路径上的所有任务，决定了整个工程会不会拖后腿。如果有一项工作没有按时完成，那么它后续的所有工作最早启动的时间就会往后推延，然后整个工程就会整体往后推延。如果说在关键的路径上提前完成了任务，那么它的后续任务也可以提前启动，那么工程的总体进展也是往前走的。所以项目管理的重点是在关键的中间的路径上要注意活动的问题。

关键路径法也存在的一些问题。比如在进度计划制定时未能考虑资源的约束和需求范围蔓延等问题，特别是软件项目的进度管理，由于软件项目对应的业务需求变更快，人员流动率高，这些进度管理方法在软件项目开发中的管理方面显得捉襟见肘。

计划评审技术（Program Evaluation and Review Technique, PERT）的原理是利用网络顺序逻辑关系和加权时间的估计，计算每项工作的持续时间。计划评审技术首先应用于美国海军的北极星导弹研制项目，为这个项目节省了原计划时间的$2 0 \% ^ { [ 1 1 ] }$ 。但是计划评审技术的缺点也很明显，对项目路径的假设过于理想化，因为在计划评审技术中，有且仅有一条主要线路，这条线路的所有任务时间均按正态分布模型计算，但是在实际项目研发中，多数项目存在不确定因素和突发事件，因此，项目时间的估计很难准确[12]。

# 2.1.3 关键链项目管理方法

传统的项目进度管理方法在各行各业项目的实践中取得了很好的效果，但是项目延期、超支等现象依然屡见不鲜。基于项目管理面临的种种问题，关键链项目管理方法，日益引起了关注。

关键链项目管理方法（CCPM）思想来源于，20 世纪70年代末以色列物理学家、企管大师、哲学家、教育家 Goldratt 基于优化生产技术提出了约束理论，该理论认为系统的制约因素决定系统的有效产出。1997 年，Goldratt 将约束理论（TOC）引入项目管理领域，提出了关键链项目管理（Critical Chain Project Management，CCPM）方法[13]。将关键链定义为一系列相互依赖的，决定了项目最短工期的作业序列[14]。

关键链理论被提出后，引起了国内外众多学者和项目管理人员的研究和实践。1998 年，Z.D. Radovilsky 博士认为缓冲区的大小是可以借鉴排队论模型计算出来的[15]。1999 年，P. Barber, C. Tomkins, A. Graves 等在《分散式现场管理-案例研究》期刊中发表了对关键链方法的评论，而且通过一个成功案例验证了关键链项目管理方法的有效性和优势[16]。在同一年，L.P. Leach 也在项目管理杂志上发表了《关键链项目管理提高项目绩效》一文，文章对关键链法在提高项目管理效果进行了分析 s [17]。2002 年，K.T. Yeo 和 J.H. Ning 共同在国际项目管理期刊上发表了《在工程采购-建造（EPC）项目中整合供应链和关键链概念》，提出关键链项目管理方法结合供应链管理管理新方法[18]。通过许多的项目实际案例，关键链项目管理方法逐渐发展成熟。

关键链项目管理方法实施中缓冲区设置十分重要。关键链项目管理理论认为，研发人员在进行工期估计时会加入大量的安全时间。但由于学生综合症和帕金森定律、资源受限等因素的影响，在项目早期安全时间可能已经被浪费掉。为此，关键链项目管理方法是先将各项活动的安全时间去除，然后在网络图结尾设置项目缓冲，来应对项目的不确定性并保护关键链，缓冲的大小也体现了项目的不确定性程度。

关键链设置缓冲已经很多学者的研究及论证。Goldratt提出以关键链上工序被剪掉的安全时间总和的 $50 \%$ 作为项目缓冲。Newbold 提出了用根方差法设置缓冲[20]。国内有许多学者对关键链缓冲区的设置也进行研究，尤其近10年内就有很多。

杨健、张晓丽于 2013 年在《基于绩效偏差阈值的军用软件项目挣值管理研究》中提出的进度控制算法，基于绩效偏差阈值与关键路径，将挣值理论应用到项目进度控制和成本控制中[21]。2020 年马萍、樊燕燕、魏兴华、杨海贝在《基于改进灰色关键链的缓冲设置方法研究》文章中利用三角模糊数描述工期的不确定性，引入并改进灰色理论，更科学的计算缓冲区大小[22]。

总体说来，缓冲的加入对于克服学生综合征，提高工作效率，确保项目按期完工具有至关重要的意义

# 2.1.4 关键链理论对软件项目开发进度管理的适用性

软件项目的生命周期一般包含项目立项，项目启动、需求分析、系统设计、系统开发、系统测试、系统上线、项目验收和上线后评估等个阶段[23]。软件开发项目的进度管理主要包含项目管理者围绕项目需求和规则规范编制计划，并在软件开发中跟踪项目实际执行情况，检查实际进度是否与计划偏差，分析偏差原因，采取措施调整，保证软件产品顺利上线[24]。关键链项目管理方法（CCPM）对提高软件开发效率，控制软件项目进度非常适用：

首先，软件开发项目不论采用瀑布开发模式还是敏捷开发模式，都是要按照件项目的生命周期即从立项到上线的活动进行开发的。但是这些活动经常是由不同的团队协作完成，而且经常有大量并行的任务在同一时刻存在，各个团队共用资源，会遇到资源冲突的，而且各团队开发的需求部分相互依赖，在实际开发过程中，也经常会出现因为某一个地方延期导致其他地方一起延期的情况。关键链（Key Chain）项目管理方法对于这种存在大量资源约束的多项目管理非常适用。

其次，由于软件开发项目中所有活动的工作量往往是团队根据以往的经验估计的，再加上由于项目团队成员能力参差不齐，对业务理解程度的不同，技术熟练程度不同，受到学生综合症（Student Syndrome）等因素的影响，人工估计时间会多出很多安全时间，采用关键链项目管理方法则能够有效的消除这些安全时间，使得开发效率得到有效的提高。

最后最重要的，需求变更频繁是软件开发项目普遍的特点，一些需求的优先级可能会随时改变，从而引起任务活动顺序的改变，关键链法可以弹性估计活动时间，关键链通过动态监控项目进度变化，控制项目进度，以应对需求频繁变化。

# 2.1 敏捷开发理论

# 2.2.1 敏捷开发概述

根据 Scrum 联盟2017 年年底发布的调查结果显示，有 $9 8 \%$ 的人确认自己所在的公司计划在将来使用敏捷的管理方法来管理项目。2001年，包括Martin Fowler和 Jim High smith 在内的 17 名极客在美国犹他州的雪鸟滑雪山庄，共同探索有关软件开发未来发展的理念，并且发表了著名的敏捷宣言，敏捷联盟成立。同年，著名的 Agile Software Development with Scrum 发布。到了 2006 年，敏捷应用进入热潮，在大规模软件开发中开始应用敏捷，包括 Google、Microsoft、IBM、Amazon、华为、腾讯等公司[25]。

敏捷软件开发是一系列基于迭代和增量流程的软件开发方法论[26]。敏捷宣言由敏捷价值观和原则组成。敏捷宣言强调：

（1）个体和交互高于流程和工具，敏捷强调‘人’，人最清楚如何完成任务，要尊重人的意见和想法。（2）可以工作的软件高于面面俱到的文档，这里强调的是要把重点放在工作的软件上，让文档服务于软件，而不能把工作的焦点放在文档上。（3）客户合作高于合同谈判，和合作方创建良好的合作关系共同解决问题要比逐条谈判合同的细节更重要。（4）响应变化高于遵循计划，认为变化是一件好事，项目是流动的，因此项目有变化是正常的，必须随时调整[27]。

敏捷价值观使用“左侧”高于“右侧”的格式来表述，既然是“高于”而不是“取代”，也就意味着“左侧”和“右侧”内容其实都应该考虑，只是‘左侧比‘右侧’要‘高’。很多人都说敏捷不需要流程，不需要文档，不需要做计划，答案当然不是，敏捷从来没说不能有这些。而且在实践中没有这些你也不可能做一个项目。

在敏捷开发中，软件系统被划分成多个迭代，每个迭代的成果都会经过软件开发的基本流程，最后提交可运行的功能。简单来说，就是比增量开发模型更细粒度的更小规模的迭代。

敏捷宣言演绎出一套12条的支持原则：

(1) 快速交付有价值的产品来使客户满意。  
(2) 欢迎需求变更。  
(3) 可工作软件平凡交付（以周为单位）(4) 可工作软件是进度的首要测量方式。  
(5) 可持续开发，能保持恒定的节奏。  
(6) 业务人员和开发人员指甲剪的紧密的、每日的合作。  
(7) 集中办公：面对面的交流是沟通的最好形势。  
(8) 项目围绕激励的个体来构建，个体应给予信任。

(9) 持续关注技术卓越和良好设计。

(10) 简洁是最重要的。

(11) 最好的构架、需求和设计出于自组织团队。

(12) 团队定期对工作进行反省，然后相应地对自己的行为进行调整[28]。

敏捷有超过 14 种方法，而各种方法分为两个部分。一部分是轻量级的方法（可以简单地理解为服务于单个团队的方法），另一部分是服务于多个敏捷团队的方法。在轻量级方法中，又可以从方法解决的问题这个角度将它们分为两类，其中，Scrum、Kanban 都是生产产品的框架，用于产品开发或工作管理。而XP（极限编程）、FDD（特征驱动开发）则是工程实践类的方法。敏捷之伞的另外一部分是服务于多个团队的方法，根据不同的项目规模和团队之间工作的耦合度，有多个方法来协调多个敏捷团队的协同工作（如 SAFe、Scrum-of-Scrums、LeSS 等）。

Scrum：作为最受欢迎，使用最为广泛的敏捷方法，接下来的第四章节中将仔细介绍 Scrum框架。

Kanban：Kanban 是一种源于丰田精益化管理的方法，它是仅次于Scrum 的另外一种敏捷软件开发的框架方法。它有以下特点：流程可视化，限制WIP（Work InProgress，在制品数量），度量生产迭代（没有固定时长的迭代）。相对于 Scrum更适于开发新产品，Kanban 则更加适合于运营维护团队实施敏捷时使用。

XP：XP（极限编程, eXtreme Programming,）是最著名的敏捷方法。它由一组简单且互相依赖的实践组成。这些实践结合在一起形成一个整体大于部分的集合。

FDD：FDD（Feature-Driven Development，特性驱动开发）是一个模型驱动的快速迭代开发过程，适用于需求经常变动的小型软件项目，简单、实用。

Scrum-of-Scrums：SoS（Scrum of Scrums）是一种管理大型 Scrum 团队的技术（团队多于12人，被划分为 $5 \sim 1 0$ 人一组的Scrum小组）。每一个小组都选出一名代表成员去参加所在团队的每日会议（也叫作 Scrum ofScrums 会议）。根据不同团队的需求，这些代表可以是工程师或 ScrumMaster。通过 Scrum of Scrums 会议达到小组之间的信息同步，解决问题的目的。

综合知名度、影响力和普及程度等方面，Scrum 可谓是目前最流行的敏捷开发方法[29]。Scrum 源自于橄榄球运动的一个专业术语，表示“并列争球”[30]。那最出色的团队表现就一个像在球场上的配合一样[31]。

# 2.2.2 Scrum 敏捷开发框架

自从 Scrum 框架创建以来，Scrum 已经在全球范围内的各个领域得到了应用。根据 2017 年 Scrum 联盟的最新调查报告，Scrum 的应用已经遍及 IT、金融／银行、健康、咨询服务、保险、政府、通信、教育、娱乐、制造、零售、科研、旅游和医疗等领域。在组织中，更是涉及 IT、产品、运营、销售、市场、教育和人力部门。

Scrum并不是一种过程或一项技术，而是一个框架，在此框架之下可以使用各种不同的过程和技术。Scrum框架由Scrum 的角色、事件、工件和规则组成。

研发团队(Team) 、敏捷教练（Scrum Master）、产品负责人(Product Owner)是Scrum 团队中中的三个关键角色[32]。

（1）开发团队(Team)

开发团队一般由 10 人以内组成，但也有 15 人的情况[33]，团队成员基于冲刺达成一致的目标成功，团队工作是定义达成冲刺目标所需活动，决定谁来完成什么工作，以及接下来在冲刺中开展什么任务。

（2）敏捷教练（Scrum Master，SM）

他是一位项目团队的仆人式领导或引导者，其角色是在开展每个冲刺的时候支持团队。敏捷教练引导每日敏捷，以及冲刺规划和冲刺后会议；他确保团队成员能够胜任开发任务，协调团队内外的沟通，控制产品开发进度，把握产品开发质量以及引导Scrum流程。

（3）产品负责人(Product Owner)

作为确保团队做出正确产品以便帮公司得到最高投资回报的产品负责人，在Scrum 团队中负责“做什么”的问题。不同公司，不同部门，不同团队的产品负责人的具体职责不尽相同。但是，从总体上来说，产品负责人的工作包括：愿景和边界。产品负责人的工作包括两个方向：提出正确的解决方案和确保解决方案被正确执行。

Scrum 通过 Sprint 迭代来实现软件开发，一个Sprint 的主要活动包括计划会、每日站会、评审会和回顾会[34]。

# （1）Sprint 计划会

Sprint 计划会议是由固定的迭代时间周期（时间盒）限定的。Sprint 会议主要明确两个事情：一是确定当前冲刺能做什么？产品负责人讲解产品待办列表以及迭代目标。开发团队理解完目标后，根据以往迭代的经验对这个 Sprint 中的能力预测，决定选择产品待办列表项的哪几项。二是如何完成所选的工作？开发团队来分解任务工作，即将产品待办列表转化成可工作的产品增量所需要的工作。在Sprint 计划会议的最后，开发团队规划出在Sprint 内所要做的工作，通常以4个小时或8个小时为一个规模。团队开发人员领取各自要做的工作。

# （2）Scrum 每日站会

提起 Scrum，很多人可能第一反应会是“每天都站着开会”。站会是Scrum最有名的一项活动。每日站会就是团队的脉搏，健康的脉搏稳定，持续而又轻快。甚至很多不具备实践敏捷的组织和项目，都会使用每日站会这个活动来促进沟通。每日 Scrum 站会一般限定 10-15 分钟，不可太长，会耽误当天工作。每日站会优化了开发团队达成目标的可能性。

# （3）Sprint 评审会

在 Sprint 评审会议中，Scrum 团队讨论在这次Sprint 中所完成的工作的表现和可以改进的地方以及改进方案。会议时间长度通常根据 Sprint 时间长度来调整，一般不建议超过2个小时。

# （4）Sprint 回顾会

Scrum 鼓励变化，鼓励尝试；最终目的是找到适应环境的实践；当然如果环境变了，实践就要做出相应的调整。回顾会议是Scrum 检视与调整的一个重要环节。在这个会议上，团队对自己的开发过程进行了改进，并确定了什么样的调整能让下一步 Sprint 更高效、结果更满意、工作更简单。就像我们频繁地迭代和交付是为了快速地获得外部用户的反馈，进而帮助我们做产品需求的调整一样，每个迭代的回顾会议就是想更快地得到大家对团队工作问题和改进点的反馈，帮助团队内部的工作效能和能力成长不断改进。

# 2.2.3 Scrum 敏捷开发在软件项目进度管理中的优势

据调查报告显示，敏捷正在从它兴起的软件产品部门快速渗透到公司的其他部门，甚至是渗透到其他行业。而从敏捷项目地域分布的角度来看，在美国已经有将近一半的项目在使用敏捷的方法，而在中国这个比例还低于 $3 \%$ 。我们有理由相信，敏捷方法的应用在中国将会越来越广泛。

开发模型没有好坏之分，只是对于某些软件尤其是互联网快速发展的现代软件开发来说，传统的开发模型无法解决项目管理上的问题。主要是需求不明确和技术不确定的特点，导致的进度延期的问题。需求不明确指的是：虽然对要做一个怎样的产品有规划，但是并不明确和确定所有功能的细节；并且随着产品的开发，极有可能对产品功能不断地改变以适应最终用户的需求。这种情况经常发生在对全新概念的产品的开发过程中。技术的不确定性指的是：技术的发展日新月异，对于所定义功能的可实现性面临着多重不确定性的因素。

Scrum 敏捷框架下的软件需求开发也是增量迭代的开发过程，但是不同的是它欢迎需求的变化，可以随时响应客户带来的需求资料[35]。它注重团队协作和持续的用户参与和反馈，因此能够有效应对快速变化的需求、快速交付高质量软件[36]。敏捷开发强调个人和团队协作、通过短期的迭代，快速地交付和展示产品效果，让客户持续参与产品开发，通过客户不断反馈和开发团队快速地响应变化进行项目的良性管理。

SP 项目开发前期一直采取增量开发的项目管理方法，在系统开发中出现了很多问题，导致项目进度延期非常严重，开发效率低下，开发速度无法跟上业务的增长需求。在敏捷开发中，软件项目被分割成多个子项目，这样就将项目风险分散在子项目中，而子项目更少的需求内容也让项目进度更加可控[37]。

与传统的项目管理方法沟通较少相比，敏捷项目管理方法从项目启动就开始十分重视沟通，对变化的需求可以迅速做出反应，比较适合当前的软件开发项目。而且敏捷开发能够通过一次次的部分软件产品的交付,降低软件开发的复杂性,而且很好的控制需求变更,从而减低项目进度延期的风险,提高项目成功率]。

# 2.3 文献述评

通过大量的文献整理及分析，对项目进度管理概念、项目进度管理方法、关键链项目管理方法、敏捷开发理论、Scrum 框架等进行了梳理。项目进度管理是指围绕项目需求和项目进度目标，编制项目进度计划，并在项目实施的过程进行监督和控制[38]。这个定义指明项目进度管理主要分项目进度计划和项目进度控制两大部分。

通常的项目进度计划，首先通过WBS工作任务分解将整个项目分解成若干个部分，确定项目活动所需的时间和资源，确定各个活动之间的依赖关系，确定顺序，建立网络图，然后根据网络图确定关键路径。在项目实施过程中，通过网络图和时间进度表动态地对项目实际进展情况进行监控，主要是关键路径上的活动，跟踪项目当前的进度状态，看是否与进度计划表相一致，如果晚于计划的进度，那么就要进行进度控制。项目进度控制主要是针对进度跟踪发现的异常情况，对造成进度异常的原因进行分析，并采取措施弥补进度的损失，调整到正常状态后，使其恢复到计划状态。

但是面对软件开发项目需求难以明确、技术不明确且频繁变更的特点，一般的项目进度管理方法明显不再通用和适合。基于关键链的项目进度管理方法，在非关键链路径的任务节点之前，关键链上的任务节点之后增加一定的缓冲时间。这样就能够在一定程度上避免因为在关键链上的资源缺乏导致的项目延期。但是这样还不够，面对难以明确且频繁变更的软件开发需求，还需要敏捷开发模型，敏捷开发方法是基于迭代和增量流程的软件开发方法论，通过控制一个个迭代的需求变更等，来控制每个迭代的进度，最终达到控制整个项目的进度的目的。

由此可见，敏捷开发框架结合关键链是比较适合软件项目开发进度管理的方案，本人将在 SP软件项目中应用该方案进行改造项目进度管理，解决 SP 项目前期在软件开发中，尤其是在项目进度管理方面存在各种问题，来改善SP项目进度管理现状，为公司其他项目的进度管理提供模板，提高公司整体研发效率，提高公司营收，也为类似业务为导向、进度管理不佳的企业提供一种有效的进度管理参考案例。

# 3 SP 软件项目开发进度管理的现状与问题分析

# 3.1 SP软件项目概述

# 3.1.1 项目背景

SP软件项目所在企业是基于早期的为药企组织药物临床试验服务的传统企业发展起来的，属于其子公司，该公司主要为药企、医生、患者提供一个医疗健康管理平台，公司目前经营三条业务线：

（1）医生研究解决方案，即 PRS，PRS 经营着最大的肿瘤临床试验现场管理组织。其中的 SMO 业务贡献大部分 PRS 收入，主要是支持制药公司从I期到IV期临床试验的药物研发过程，也为上市后的创新药物提供真实世界研究（RWS）服务。

（2）供货商与支付方解决方案，即PPS，PPS 业务的主要是向保险公司提供的健康管理及索偿管理服务。

（3）药品福利管理，即 PBM，PBM 经营着最大的私营特药药房，主要售卖治疗肿瘤及其他危重疾病的特药以及为患者提供增值药剂师服务，包括药品配送、不良反应指导、患者随访。

企业自 2014 年成立以来，发展迅速，已经具备上市能力，从企业的招股书中得到从近三年企业的收益情况，从中可以看到公司业务发展迅速，是很具备发展潜力的企业。

首先看近三年企业的损益情况，如表3.1 企业综合损益表所示，2019年、2020年三个财政年度和 2021 年第一季度企业分别实现营业收入（第一行所示）分别为10.39 亿元、27 亿元， 34.74 亿元。2019 到 2020 年企业的营收增长了将近 2 倍，2021 年虽然收到疫情的影响，但是营收也增长了近 $30 \%$ ，与同行业相比，势头很好。

表 3.1 企业综合损益表  
Table 3.1 Comprehensive profit and loss of enterprise   

<table><tr><td>收入或开支\年份</td><td>2019年</td><td>2020年</td><td>2021年</td></tr><tr><td>营业收入</td><td>10.39亿元</td><td>28亿元</td><td>34.74亿元</td></tr><tr><td>研发开支</td><td>2485万元</td><td>4574万元</td><td>5920万元</td></tr><tr><td>行政开支</td><td>2.19亿元</td><td>3.61亿元</td><td>6.70亿元</td></tr></table>

从招股书中很明显的看到，公司最大的费用开支来自于行政开支，2019年至2021年，分别为2.19亿元、3.61亿元和6.70亿元。这样说明公司的自动化办公不完善，人员办公效率低，这与公司新成立时间不久有一定的关系，同时也说明了在互联网时代，公司对于办公软件、运营软件这个可以提高效率的工具不是很重视。因为从表中可以看到另外一行比较典型的数据是研发的费用开支，2019年-2021 年研发支出仅为 2485 万元、4574 万元和 5920 万元。由此可见，做好内部办公电子化，提高效率，降低人力行政成本已经势在必行。

另外，如表 3.2 所示三大收入来源收入及占总收入的百分比，每一行代表每条业务线收入总数和企业总收入的占比，可以看出三大收入来源中来自药品福利管理（PBM）的收入分别占总收入的 $8 3 . 1 \%$ 、 $9 1 . 9 \%$ 及 $9 0 . 3 \%$ ，可以说PBM业务支撑着整个企业的现金流。

但是，思派健康药品福利管理（PBM）虽然在三大业务的营收占比最高，毛利率却最低，如表3.3所示，2019 年至2021年，PBM的毛利率分别为 $5 . 6 \%$ 、 $5 . 5 \%$ 、$5 . 9 \%$ 。这其中有很多原因，主要原因是PBM作为特药药房，销售的特效肿瘤药物高度依赖少数供应商，导致在采购价格方面缺乏话语权，所以导致毛利较低，还有近几年国家医保系统的完善，很多被纳入医保后可议价的能力变得更低。因此利用技术手段打造高效平台，提高公司整体运营能力，扩大用户量，提高平台影响力，是在医药行业获取价格管控话语权的有效手段，同时提高软件研发能力尽快将所有药房接入国家双通道医保系统中来增加销售渠道和销售量。

表 3.2 三大收入来源收入及占总收入的百分比表  
Table 3.2 Income from the three major sources of income and its percentage in total income   
表 3.3 三大收入来源毛利及占总收入的百分比表  

<table><tr><td>收入来源\年份</td><td>2019年</td><td>2020年</td><td>2021年</td></tr><tr><td>PRS</td><td>1.73195亿元116.7%</td><td>1.85652亿元16.9%</td><td>2.44857亿元17.0%</td></tr><tr><td>PBM</td><td>8.636亿元183.1%</td><td>24.82亿元191.9%</td><td>31.3648亿元190.3%</td></tr><tr><td>PPS</td><td>0.2216亿元10.2%</td><td>3.1989亿元|1.2%</td><td>9.2589亿元12.7%</td></tr><tr><td>总计约</td><td>10.39亿元</td><td>28亿元</td><td>34.74亿元</td></tr></table>

Table 3.3 Gross profit of the three major sources of income and its percentage in total income   

<table><tr><td>收入来源丨年份</td><td>2019年</td><td>2020年</td><td>2021年</td></tr><tr><td>PRS</td><td>3.2996 亿元|19.1%</td><td>3.9399亿元16.9%</td><td>4.4859亿元|18.3%</td></tr><tr><td>PBM</td><td>4.7932亿元15.6%</td><td>13.5775亿元191.9%</td><td>18.5444亿元15.9%</td></tr><tr><td>PPS</td><td>0.0876亿元139.5%</td><td>1.1686 亿元|1.2%</td><td>5.3146亿元157.4%</td></tr><tr><td>总计约</td><td>8.1804亿元17.9%</td><td>18.686亿元16.9%</td><td>28.3449亿元18.2%</td></tr></table>

SP 软件项目属于 PBM 业务线，该软件项目主要为全国 100 多家私营特药药房提供软件服务。在 2016 年之前的药店经营，一直用的外采的海典药店收银系统以及其他系统，但是虽然业务的发展，外采系统的弊端暴露出来，首先成本高，一个系统每年需要花费几百万甚至千万，其次，外采系统很难做到定制。最后最重要的是企业要想挖掘自身的资源和数据变得非常困难，因为外采的各个系统无法有效将所有数据进行汇总和分析。比如对于最基础的财务、运营以及患者管理等报表都无法系统支持，只能通过药店人员进行手工记账，然后再录入系统的方式，直接影响工作效率，增加了人力成本。除此之外，数据在不同的外采系统里无法汇总分析，对于PBM业务的进一步发展影响巨大。

2016企业成立软件研发团队，但当时只是十几个人的团队，无法支持整个公司的业务需求，只是做了收银系统，以及对接外采系统。

随着2020年，企业成立了数据中台部门，开启自主研发三条业务线自有系统的新征程。部门的目标不仅仅为支持各个业务线的需求进行软件开发和维护，更是将各个业务的数据逐步整合到一起，进一步地，通过技术平台及数据，提高公司整体的营运能力。

PBM 业务线的自有系统围绕特药药房的业务提供软件支持，其中主要包含ERP系统、收银系统、患者随访系统以及一些后台运营系统，这些项目统称为 SP软件系统。

# 3.1.2 项目目标和范围

SP 软件项目总体目标是建设 SP 软件系统，支持公司全部药店切换到自主研发的新系统，建设强大的技术平台、数据平台，让数据赋能运营，大大提高运营能力，同时降本增效，节约行政、人力等成本，提高效率。前期的项目规划将整个项目分为五个阶段：

（1）第一阶段：ERP 系统上线，保证所有门店切换到新 ERP 系统。（2）第二阶段：小程序系统，增加小程序端的销售和配送系统，为门店配送  
提供系统支持。（3）第三阶段：后台运营系统，为运营人员提供权限配置、审核功能、数据  
报表等功能。（4）财务管理系统：自主研发 ERP 系统与公司采用的第三方财务系统打通，  
为财务人员提供便利。（5）患者随访系统：主要对于购买特药药品的患者进行真实世界随访。

SP 软件系统主要采用微服务的架构，系统总体功能模块架构，如图3.1所示。

![](images/904a11494b0f7acc9cddf863f7b278da8e74a6d695450d64e073a116c39472ff.jpg)  
图 3.1 系统总体功能架构图  
Fig.3.1 Overall functional architecture of the systems

# 3.2 SP软件项目组织结构分析

# 3.2.1 组织结构概述

SP软件项目所在的数据运营中台部门，设立了一位总经理，下设 2个部门经理，共将近200多人。2个部门分别是产品运营部，研发部。产品运营部主要负责三个业务线的需求收集、需求整理、产品规划设计以及产品上线后的运维工作。研发部则主要负责三个业务线需求的如软件开发，质量控制，以及上线后的系统维护。

产品运营部，划分为产品部、视觉部，产品部主要负责需求收集、需求整理、产品规划等。视觉部主要针对产品原型等进行视觉设计和交互设计。

研发部，划分为后端部、前端部、测试部、运维部，项目管理部，公司系统均为前后端分离项目，后端部主要负责后端系统开发维护。前端部主要负责前端系统开发维护，测试部主要负责系统测试，质量管控。运维部主要负责前后端系统基础环境配置、网络配置、服务器运行管理等。项目经理部主要人员是项目经理，负责跟进项目，联络和协调各个部门的工作，主要是沟通协调和产品部门的工作。

整体的数据运营中台组织架构如图 3.2所示。

![](images/50d4808fede6b2079c51d20aff201787507a88b2e04734f8dfc73ff1cd6d3532.jpg)  
图 3.2 数据运营中台组织架构图  
Fig.3.2 Organizational structure of data operation center

数据运营中台部门整体的组织架构决定了SP项目的组织结构为弱矩阵式的组织结构，如图3.3所示。

![](images/1be6b292dc7b54e935a8bed310d41bf762f34d3d038cf7662cb2557167424cc8.jpg)  
图 3.3 SP 软件开发项目组织结构图

Fig.3.3 SP software development project organization structure在项目前期，参与SP 项目的人员一共33人,人员分配如下：

研发部经理：1人。研发部经理主要负责研发部项目人员划分，组织结构，公司整体的系统架构统筹。

产品运营部经理：1 人。负责公司整体的产品架构统筹，负责产品部门的组织结构，负责收集需求，并协调各个业务方之间的需求。

产品部团队：产品经理 3 人。产品经理主要从业务方了解收集需求、最后整理需求文档，给出初步的功能设计。

视觉部团队：设计人员 1 人，在产品经理给到的初步功能设计上进行视觉和交互设计。

项目管理部团队：项目经理 1 人。项目管理部设置在研发部门下，项目经理相当于作为研发部的发言人，与产品运营部进行沟通，并协调沟通研发部个团队资源。

后端部团队：后端开发人员11人。后端开发人员包括公共基础系统开发人员和业务系统开发人员，基础系统开发人员主要为各个业务系统提供基础依赖，比如用户管理系统、支付系统、消息系统等等，是各个系统都需要依赖的公共基础系统；业务系统开发人员则专注于用户使用的前台业务系统，和前端开发人员完成一个应用的开发。

前端部团队：前端开发人员 6 人。前端开发人员主要负责创建 WEB 页面或APP等前端界面，与后端开发人员配合完成应用开发

测试部团队：测试人员：8 人。测试人员负责系统测试，应用程序质量的管控。

运维部团队：运维人员 1 人。运维人员主要负责前端和后端系统基础环境配置、网络配置、以及保证服务器正常运行。

在这种弱矩阵组织里，项目经理跟踪项目进展、汇报项目进度，来协调项目，小型简单的项目自然可以应对，但是对于SP项目这种大型且复杂多样的软件开发项目，弱矩阵组织结构显然是不行的，项目经理无法直接指挥团队或者下决策。

另外各个团队各自为阵营，互不支持的情况也时有发生。为了体现需求响应速度，产品经理从业务方收集来的需求，稍加整理后，直接转述给到研发部门，没有专门细致剖析业务的需求，设计新功能，也没有整体的考虑和规划产品。一个问题是新产品的设计与用户的需求可能不一致，另一个问题就是和其他产品经理的需求或者旧有的产品逻辑产生冲突，导致在评审、研发和测试阶段都不停的暴露出问题，不仅影响了项目进度，而且不停的修改和沟通问题加剧了各个部门的紧张关系，大家相互推诿，更加不利于项目进度推进。

# 3.2.2 组织结构特点

《PMBOK 指南》中将项目组织结构划分为三种类型，分别是职能型、项目型、矩阵型，职能型组织结构适用于主要由一个部门完成的项目。项目型组织结构中的部门完全是按照项目进行设置，适用于进度、成本、质量等指标有严格要求的项目。矩阵形组织结构结合职能型组织和项目型组织结构的特点，适用于公司或跨职能部门的项目，管理规范，分工明确。

SP 软件项目为矩阵型组织结构，部门划分是按职能的划分， 例如产品部、后端部、测试部。项目管理部在研发部下面，有多个项目经理，会有一个或多个项目在项目经理手下，并且多个部门组成的团队来进行项目开发。

弱矩阵型的项目结构的优点是：：一方面，多项目可以共享各个部门的资源，人力资源得到有效利用；另一方面，在实现项目目标的同时，还有利于贯彻公司的整体方针，减少资源浪费。

矩阵型的项目结构的也有非常明显的问题：首先，项目经理负责多个项目沟通协调，当项目多的时候会使项目经理产生力不从心的状况；其次，多项目资源共享往往会造成项目之间的冲突。这种组织结构所带来的时间和精力上的消耗，更有可能拖慢业务发展而非推动它向前发展[39]。

# 3.2.3 组织结构存在问题

结合 SP 软件系统情况和弱矩阵型组织结构的特点，SP 软件开发项目前期的组织结构存在的问题，主要为以下几个方面：

（1）跨部门的协作，会因为各部门的契合度不高，导致效率触底。而 SP 项目每一个阶段开发多少都会调整人员安排，所以SP项目更适合组织结构。（2）每个项目跨多个职能部门，项目经理在同时参与多个项目的时候，协调的工作安排比较困难，时常顾此失彼。（3）各个项目资源共享，当项目来自不同业务方时，往往会引起项目之间严重的冲突，最后导致多个业务部门都不满意的状况；

随着业务的快速发展，需求越来越多，变更也越来越频繁，但基于上述弱矩阵组织结构存在的问题，目前的组织结构是不能灵活响应业务日趋增长的需求。另外，整个数据运营中台部门的成本费用由三个业务线均摊，资源冲突的情况下，需求消化慢，开发时间长，完成度差，导致各个业务方非常不满意，也不愿意承担这部分开支，团队整体士气受到影响，工作积极性不高，也直接影响项目进度。

# 3.3 SP软件项目开发模型分析

# 3.3.1 开发模型概述

传统的软件开发模型常用的有、快速原型模型（Rapid Prototype Model）、增量模型（Incremental Model）、瀑布模型（Waterfall Model 等。

瀑布模型（Waterfall Model）是出现最早的软件开发模型，它给出了一个固定的顺序，将活动从之前的阶段一步一步过渡到下一个阶段，就像流水一样往下泻，最终开发出来软件产品并投入使用[40]。

快速原型模型（Rapid Prototype Model）的是根据客户的需求描述，设计一个原型，然后让给客户展示，并让客户对原型进行评价，然后修改原型，这样反复2-3 次，通过逐步调整原型直至满足客户的要求，然后开发出客户满意的软件产品。

增量模型（Incremental Model）就是将一个大型的软件，按照软件的特性和功能分组件来开发，也就是迭代，每个迭代要求能产出一个完整可用的软件组件。但需要特别注意的是将软件产品分解为增量组件时，新构件集成到现有系统时，形成的产品必须是可用的。

SP 软件项目项目总体目标下，前期的项目计划分五个阶段，采用的就是增量开发模型。第一阶段保证 ERP 系统上线，保证所有门店切换到新系统；第二阶段增加小程序端销售和配送系统，为门店配送提供方便使用的系统；第三阶段建设健全后台运营系统，为运营人员提供便利；第四阶段财务管理系统，为财务人员提供方便；第五阶段患者随访系统，为购买特药的患者随访，为医学部和药企提供真实世界研究数据。每一阶段虽有前后依赖关系，整体还是按照顺序进行，但相对独立，能够在依赖系统开发完成的基础上，再次单独完整进行分析、设计、编码、测试以及发布上线。

# 3.3.2 开发模型特点

增量开发模型的特点，首先，相对于瀑布模型，它将待开发的软件系统模块化，分批次地提交产品，使用户可以在更短的时间内看到产品；其次，分批次进行开发，从一定程度上降低了项目整体开发风险，一个开发周期内的错误不会影响到整个项目；最后，相对于瀑布模型和快速原型模型，开发比较灵活，当需求变化时，能够一定程度上响应变化，做出调整和变动。

# 3.3.3 开发模型存在问题

增量开发模型也存在缺陷，在 SP 软件开发项目中主要表现为三点：

第一，由于每一个组件都是在已有的软件架构中逐渐合并的，因此新加入的组件容易对已构建的系统部分造成破坏，导致之前做好系统可能需要重新调整，甚至推倒重来。

第二，需求的变化不可避免，增量开发能够支持变化，但是也容易走入边做边改的境况，一旦控制不好，项目会因为不断地出现延期的情况，在SP软件项目开发过程中，由于前期需求产品缺乏整体规划，考虑不周、设计规划缺乏全盘系统考虑等可能导致整体延期率为 $100 \%$ 。

第三，对于业务需求变化响应和接受的速度很快，但是相当于开发过程中接受到新需求，需要重新评审、设计方案，既要保证开发中的功能不受，又要保证新功能完成，这几乎是不可能的，因此只能项目延期。然而现实情况SP项目在开发中一定都会出现很多次的需求修改，整体进度受到影响，整个产品的交付时间也只能不断后推，最后进度管理很可能失控。

因此 SP 软件开发项目需要一种新的开发模型，尽量避免在开发中需求变更影响项目进度，需要一种即能积极响应需求变化，又能够快速交付产品、项目进度

可控的开发模型。

# 3.4 SP 软件项目进度管理现状

# 3.4.1 项目工作分解结构

基于增量式开发模型，SP 软件开发项目分为五个阶段，每一阶段都经历从立项到上线验收的流程。

工作分解结构（Work Breakdown Structure，WBS），主要是将一个项目分解成易于管理的几个部分或几个细目[41]。

SP系统的工作分解是按照软件开发流程进行分解的，如图3.4所示，为SP软件开发项目每个阶段要经历的过程。

![](images/c1dd7e3f02329db254c8d5a73803d33a8840080bdffd51c8353a6520ddfa63fd.jpg)  
图 3.4 SP 项目工作分解结构  
Fig.3.4 Work breakdown structure of project

目前SP项目的工作分解结构是按照软件系统开发阶段的过程来划分的，系统工程量大，迫于业务的压力，必须要给一个上线时间，属于倒排期的状况，因此工作分解都是以目标时间为准倒序排期，每个任务的时间都很紧张，一旦一个地方出现延迟，那么整个项目就延期，这个从项目早期实际执行情况可以看出，所有的增量迭代开发几乎都没有按照计划时间交付，最后总会延迟。

# 3.4.2 项目进度计划

SP 软件开发项目，在增量开发模型基础上，项目总体分为五个阶段，分别是五个系统，即 ERP 系统、小程序系统、后台运营系统、财务管理系统、患者随访系统。每个阶段项目分别进行项目进度的计划，按照图 3.4 SP 系统工作分解结构模型，分别对每个阶段项目进行活动分解、活动排序、各项活动持续时间估计。

最后通过 Excel 表格来绘制各个阶段项目的进度计划表，每个阶段项目计划表汇总得到SP软件开发项目整体的进度计划表，如表3.4所示。

表 3.4 项目进度计划表  
Table 3.4 Project schedule   

<table><tr><td>序号</td><td>任务名称</td><td>开始日期</td><td>结束日期</td></tr><tr><td>1</td><td>SP软件开发项目</td><td>2020年3月2日</td><td>2022年1月5日</td></tr><tr><td>2</td><td>ERP 系统</td><td>2020年3月2日</td><td>2020年9月1日</td></tr><tr><td>3</td><td>需求阶段</td><td>2020年3月2日</td><td>2020年3月23日</td></tr><tr><td>4</td><td>设计阶段</td><td>2020年3月24日</td><td>2020年5月4日</td></tr><tr><td>5</td><td>开发阶段</td><td>2020年5月5日</td><td>2020年7月15日</td></tr><tr><td>6</td><td>测试阶段</td><td>2020年5月5日</td><td>2020年8月26日</td></tr><tr><td>7</td><td>发布验收阶段</td><td>2020年8月27日</td><td>2020年9月1日</td></tr><tr><td>8</td><td>小程序系统</td><td>2020年9月2日</td><td>2020年11月27日</td></tr><tr><td>9</td><td>需求阶段</td><td>2020年9月2日</td><td>2020年9月10日</td></tr><tr><td>10</td><td>设计阶段</td><td>2020年9月11日</td><td>2020年9月25日</td></tr><tr><td>11</td><td>开发阶段</td><td>2020年9月25日</td><td>2020年11月5日</td></tr><tr><td>12</td><td>测试阶段</td><td>2020年9月28日</td><td>2020年11月24日</td></tr><tr><td>13</td><td>发布验收阶段</td><td>2020年11月25日</td><td>2020年11月27日</td></tr><tr><td>14</td><td>后台运营系统</td><td>2020年11月30日</td><td>2021年3月19日</td></tr><tr><td>15</td><td>需求阶段</td><td>2020年11月30日</td><td>2020年12月11日</td></tr><tr><td>16</td><td>设计阶段</td><td>2020年12月14日</td><td>2020年12月24日</td></tr><tr><td>17</td><td>开发阶段</td><td>2020年12月25日</td><td>2021年2月16日</td></tr><tr><td>18</td><td>测试阶段</td><td>2020年12月25日</td><td>2021年3月16日</td></tr><tr><td>19</td><td>发布验收阶段</td><td>2021年3月17日</td><td>2021年3月19日</td></tr><tr><td>20</td><td>财务管理系统</td><td>2021年3月22日</td><td>2021年6月16日</td></tr><tr><td>21</td><td>需求阶段</td><td>2021年3月22日</td><td>2021年3月31日</td></tr><tr><td>22</td><td>设计阶段</td><td>2021年4月1日</td><td>2021年4月9日</td></tr><tr><td>23</td><td>开发阶段</td><td>2021年4月9日</td><td>2021年5月28日</td></tr><tr><td>24</td><td>测试阶段</td><td>2021年4月12日</td><td>2021年6月11日</td></tr><tr><td>25</td><td>发布验收阶段</td><td>2021年6月14日</td><td>2021年6月16日</td></tr><tr><td>26</td><td>患者随访系统</td><td>2021年6月17日</td><td>2022年1月5日</td></tr></table>

<table><tr><td>27</td><td>需求阶段</td><td>2021年6月17日</td><td>2021年6月30日</td></tr><tr><td>28</td><td>设计阶段</td><td>2021年7月1日</td><td>2021年7月16日</td></tr><tr><td>29</td><td>开发阶段</td><td>2021年7月19日</td><td>2021年12月2日</td></tr><tr><td>30</td><td>测试阶段</td><td>2021年7月19日</td><td>2021年12月30日</td></tr><tr><td>31</td><td>发布验收阶段</td><td>2021年12月31日</td><td>2022年1月5日</td></tr></table>

从项目整体进度计划表中看出，阶段一（ERP 系统）项目持续 6 个月，阶段二（小程序系统）项目持续 4 个月，阶段三（后台运营系统）项目持续 3 个月，阶段四（财务管理系统）项目持续 3 个月，阶段四（患者随访系统）项目持续 6个月。整个项目大约持续 22 个月，2020 年的 3 月初开始，一直到 2022年的 1 月初为止。但是缺点也很明显，就是任务分解太粗犷，整体的技术方案无法确定，在做新的系统时发现旧的设计不能满足新功能的情况，很容易陷入做完后经常重构的情况，比如 ERP 系统对应的销售系统、采购系统已经都经历重构。

# 3.4.3 项目进度控制

项目进度计划编制完成，如何控制进度是保证项目能按照计划按时完成的重要内容。SP 项目会采用关键路径法分析项目进度计划，估算项目最短工期。并且在项目实施中，着重关注关键路径上的活动，来尽量保证进度正常。如果需要缩短工期，优化关键路径上的活动也是最有效的。SP 项目找到关键路径的方法通常经过以下几个步骤：分解活动、活动排序、估计工时、定位关键路径。

但是使用关键路径法也有缺点，首先，关键路径法没有重视人力资源情况的约束，加之由于学生综合症（Student Syndrome）、帕金森定律（Parkinson’s Law）和完工不报告现象的普遍存在，项目开始的进度计划常常和人力资源情况相互矛盾，造成项目延期的情况。其次，使用关键路径法的初衷是为了减少项目开发过程中的风险，使各项任务都能够尽早的开始，但是在实际项目中，有些工作起步较早，不但不能为项目争取利益，反而使工程进度打乱，使项目整体管理较为混乱，进度失控。

除了运用关键路径法，还制定了进度跟踪措施，具体的控制措施包括：

(1)项目经理按照项目进度计划进行项目进度跟踪；  
(2)建立了进度报告制度，包括周报和每月会议；  
(3)遵守项目控制流程，SP 软件开发项目控制流程如图 3.6所示。

![](images/0a8b87f9dc83d39aba8183729e7092af43fb2521820384ebc64d68424fa71865.jpg)  
图 3.5 SP 软件开发项目控制流程图  
Fig.3.5 SP software development project control process

从图 3.5 得出，项目的进度状况进行周期性汇报，一般是每周五开周会汇报确认，项目经理在通过周报方式发个各个部门领导。每月向各个部门，包括业务方领导进行一次进度汇报。但是这样的项目控制措施流程，缺乏有效监督制度，周会流于形式，尤其在项目进度紧张的时候，开发人员紧张开发，觉得开会反而耽误进度，进度无法跟踪，也s 为在项目后期的进度延迟埋下了隐患。而月度汇报更是一个研发对业务方的单向的汇报，业务方大部分情况只关注结果，不关注过程。

从图 3.5 所示的 SP 项目开发进度计划中，可以粗略计算出该项目原计划工期是22个月，但从阶段一ERP系统项目开始，因为进度控制不利等原因，每个阶段项目都有延期。业务方自然很不满意，也不愿意增加更多资源在研发，因此研发只能日复一日，年复一年的加班赶工，熬夜通宵，做不完的需求仿佛大山一样压得整个部门喘不过来气，整个团队气氛低压，研发人员工作热情逐渐降低，开发效率见底。同时，业务方对于整个数据中台部门的能力产生了怀疑和不信任感，一度向上级投诉反馈，整个部门承受着极大的压力。

# 3.5 SP 软件项目进度管理问题分析

SP 软件开发项目过程中，进度管理存在很多的问题：

# 3.5.1 进度计划制定不科学

在编制项目计划和资源配置时未考虑约束资源带来的影响进行分析，在估算时默认资源充足，所有资源不会出现短缺或产生瓶颈的情况。问题具体表现在这几个方面：

（1）在制定进度计划时未重视资源的约束，往往会在开发时发生资源缺少或冲突。如并未考虑人员离职、请假的情况，如果安排别的人做这部分工作，可能需要一些时间去熟悉项目，而且如果新安排的人员不了解相关业务或能力较差，还要安排老人为其提供帮助。如果没有资源可以安排或者遇到任务无法分解的情况，项目进度肯定收到影响，更加无法确保项目能够顺利实施。

（2）当项目在执行中出现问题或改变项目范围时，不及时调整人员，致使一些活动不能按时完成，从而影响了项目的整体进度。另外，数据运营中台刚刚成立，团队不稳定性高，人员流动性强，出现人员从项目离开等情况时，工作交接需要一段时间，会导致项目的实际进度产生落后。

# 3.5.2 进度管理和控制不完善

不同于普通产品，软件研发中产品不可见，项目管理人员要及时做好进度管理工作。在 SP 项目的早期开发者中系统时，没有实施监督项目成员的条件和制度，导致项目问题和延期，例如成员工作拖延等，主要表现为以下几点：

（1）帕金森定律（Parkinson’s Law）。帕金森定律简单说就是：一个事儿多的人20分钟可以寄出一叠明信片,但闲着的老太太能花一整天[42]。这样的现象在工作中也常常出现，同样一个人高负荷下，其工作效率比在负荷不满的状态下高很多。究其原因，主要是因为时间充裕，导致他们在观念上放慢了工作节奏，把所有的时间都用掉了，从而导致工作时间明显增多。在软件项目开发过程中，往往在开发初期人员紧迫感，工作都比较轻松，但是越往后就发现问题过多，难以完成就加班加点，工作任务直到临近结束时间才提交，或发生了提前完工不报告的情况。这样造成的后果就是，一旦在工程过程中发计划外的紧急情况，进度就会延迟，从这个角度来说，制定这一阶段的进度计划和监控工作就变得格外重要。

（2）学生综合症（Student Syndrome）。学生综合症指得是老师给学生布置作业的时候，学生要求老师给，更多的时间来完成成作业，老师同意了，但实际上，学生第一周净玩儿了，第二周才开始写作业，但是可能完不成，只能要求老师在给点儿时间[43]。在实际工作中，开发人员也会出现类似的问题，希望获得足够的时间来完成任务，领导同意后，他们就认为时间很宽裕，前期不紧张，后期开始做发现做不完了，当反复形成了这样的局面，就必然导致项目延后。

（3）缺乏对进度状况进行实时跟踪工具。虽然每周五下午进行进度汇报，但是由于缺乏有效的进度管理工具，周期回报的形式进行进度状况统计，数据不具有实时性，可能一个问题已经耽误好几天了，才在周会上发现了或者整个项目的问题最后才发现，增加了项目的风险。

# 3.5.3 需求变更造成的进度延迟问题

软件项目需求变化是常态，需求变更产生的问题主要体现为两个方面：

一方面，在对需求变更进行评估时，管理人员基本上单独评估，很少和项目成员进行交流与沟通，或者沟通的时候漏掉了可能影响部分的项目成员，导致评估不准确；

另一方面，就算评估准确，由于项目成员都在各自任务上，需求变更导致额外的工作量，无法合理安排资源，如果强行插入只能使项目整体延期。

在软件开发中，需求变化往往难以预测的，因此在软件项目研发的整个过程中必须做好需求分析和跟踪工作，这样才能确保项目按时完成，也能保证产品满足客户的需求。当需求改变后，变更计划制定好后，及时向所有成员传达变更计划，如果没有，则收到变更的成员按照之前的计划进行工作，就会浪费项目资源和时间，影响羡慕进度。

# 3.5.4 项目进度管理问题原因分析

SP 软件开发项目过程中进度管理问题的成因分析，如下：

（1）项目组织结构不合理。SP 软件开发项目弱矩阵式的组织结构并不太适合项目。1）项目缺乏一个整体负责人，因此缺乏各种制度和流程的制定人，会造成项目管理的混乱，尤其是进度管理。2）项目经理只是一个协调者，不是管理者，项目经理不能直接要求成员按照自己的安排去完成任务，就变的比较困难，大部分情况主要依赖各部门职能经理，项目协调不顺畅，也可能会造成项目的延期。3）弱矩阵式的组织结构项目资源共享造成的资源冲突问题，一旦有高优先级的需求进来，当前进行的项目就可能存在延期风险。4）公司业务发展非常快，业务需求多而且迫切，需求变更非常频繁，弱矩阵的组织结构就经常产生资源冲突，进度延后也成为家常便饭。

5）人力资源不足，数据运营中台为公司三条业务线提供支持，本身人员缺乏，而且业务方对于刚成立的部门缺乏信任，也不愿意增加的投入。

（2）项目开发模型不适应。具体表现为以下两个方面：一方面，增量开发模型是各个构件逐渐并入已有的软件体系结构，在SP 项目开发过程中，ERP系统完成后，进行小程序系统开发，那销售管理系统就需要做调整，需要整体兼容两个系统，对于整个 SP 项目来说，调整之前的项目增加的工作量，就有延期的风险。另一方面，增量开发模型一定程度上允许需求变更，但是对于SP项目频繁的变更也是无可奈何，一旦陷入边做边改的境况，项目进度必然收到影响。

（3）项目进度计划制定不科学。主要体现在工期估算方法不科学。项目工时  
估计是项目进度计划的重要部分，工时估计不准确直接影响了整个项目的工期。  
SP软件项目工期估算不科学表现在以下几个方面：1）在项目初期估算项目工时时，SP 项目并没有用科学的数学推导或者经验丰富的专家，项目成员完全凭自己过去的经验。2）SP 项目初期制定进度计划时，迫于业务的压力，必须要给一个上线时间，属于倒排期的状况，因此工作分解都是以目标时间来贴近，当然分解粒度就不够细致，最后估计时间往往不准确，与实际偏离较大。3）项目中的各个活动密切关联，一旦一个活动产生延迟，很可能影响其他活动，最后导致整个项目的延迟。4）增量式的开发模型，开发周期比较长，任务项目越是后期发现更多问题，需要返工的工作量就越是巨大，直接影响项目进度。（4）缺乏有效的进度监控，尤其是缺乏合适的软件工具。1）项目进度跟踪几乎全靠项目经理组织项目成员进行周会汇报，一周的工作内容汇报中难免有遗漏，后期发现问题后再汇报已为时已晚。2）项目工作结构分解过于草率，不能及时发现进度延迟的情况，及时补救更正。3）项目进度计划表不够灵活，而且只在项目经理或者职能经理手里，项目成员就鲜能关注进度偏差。（5）缺少需求变更管理。业务发展快速，随之项目的需求也经常改变，而需  
求变化后，SP项目开发时未做好需求变更管理工作，主要表现在如下几个方面：1）变更的流程过于简单、评审比较仓促。有时候为了不耽误目前的开发时间，变更由负责人决定，很可能会导致重要内容会遗漏。2）未采取有效措施控制需求变更。SP 项目包含的系统，目前涉及到多个业务方使用，一些业务方的需求可能与其他的相悖，或者与产品设计不统一，此时并没有严格控制需求，一味地接收，导致同一个功能反复更改。

（6）其他方面的原因。在开发方面，由于项目周期比较长，且解决线上问题的时间没有留出时间，而是临时安排工程师查问题解决问题，往往总是会消耗 1天变天的项目时间。在测试方面，测试人员有限，不停的奔波于各个项目的测试，对于基础的自动化测试和压力测试缺乏方案。

通过以上原因分析，SP 软件开发项目在早期的开发中，进度管理方便能力较弱，采用的管理方法也比较简单、落后、不科学，要想保质保量的完成项目，必须改变目前的方法，采用新的有效的方法。

# 4 SP 软件项目开发进度管理的改造方案

# 4.1 项目组织结构的改造

根据 3.2.3 节中总结的 SP 项目前期在组织架构上的问题，主要表现为弱矩阵组织结构中项目跨多个职能部门，跨部门效率低，项目之间资源冲突等问题，公司正在采取项目式组织结构的特点来优化组织架构，建立适合研发部门的组织结构。

# 4.1.1 项目型的组织结构改造

项目型组织结构，按照项目设置部门，这种组织方式下项目经理或者负责人有足够的权力控制协调整个项目的资源[44]。项目型组织结构有很明显的特点：

（1）项目经理或负责人对项目负责，可以调动整个项目的资源。（2）只关注当前项目，决策速度快，能够对客户的要求作出快速响应。（3）项目成员在一个部门下更默契，沟通交流更简单、快速。单一用项目型组织结构也会存在一定的缺陷：（1）项目团队之间资源不能共享，可能造成资源不能充分利用。（2）项目型组织临时组织，项目结束，项目成员解散，就会让项目成员心理上缺乏安全感。而这些缺陷在公司组织结构调整的时候也都考虑进去了，一定程度上克服了项目型组织结构的缺陷，具体措施在项目组织改造中详述。

# 4.1.2 项目组织的改造

调整后的整个数据运营中台部门分为四个项目组，一个公共研发组，另外三个研发组分别对应公司三条业务线，SP 项主要对接 PBM 业务线的需求。基于调整后的部门整体架构，SP项目改造后的组织结构如图4.1所示。

![](images/2537652300e59a046e148d8fee4775b94eb20b55f22bbb5a3099ad461c467e9b.jpg)  
图 4.1 改造后的 SP 项目组织结构  
Fig.4.1 Modified SP project organization structure

项目设置一个整体的负责人，管理团队工作，包括团队人员结构调整和任命；制定项目管理策略，如项目沟通管理、评审管理，进度管理等策略的编制；周期性向业务方汇报项目工作。

一个流程经理，主要来辅助项目负责人，协助项目管理策略的制定和跟进检查，协助一些行政性的工作，如组织沟通会议，为团队成员提供服务等。

整个项目团队分为两个组，A 组主要负责 ERP 系统、小程序系统和后台运营系统，B 组主要负责患者随访系统和财务管理系统。每个小组产品经理各1位，视觉设计人员个 1 位，前端开发人员各 4 位，后端开发人员各 4 位，测试人员各 4位，运维人员各 1 位。两个小组负责人，负责处理自己所负责系统的事务，推动系统开发前进。两组资源共享，两组人员也不是完全分开的，当有较大规模的需求，一个团队无法消化的时候，A 和 B 组会合并开发，或灵活调配人员，而当小需求或迭代补丁需求较多时，则各自分开开发项目。

针对项目型组织结构的缺陷，改造组织结构是也做了充分考虑和安排，首先各个项目之间资源不能共享问题和公司的宏观政策无法贯彻的问题，是不存在的，因为各项目组都同在一个数据运营中台部门，项目之间人员借调，调换具有一定灵活性，另外单独的公共研发组，不仅负责公共基础项目的开发，为其他三个项目组提供基础组件支持，而且主要负责支持推行公司的政策，政策的实行只存在推行时间有先后问题，不存在不执行的情况。其次，项目结束后团队解散导致项目成员缺乏安全感问题也不存在，公司三条业务线是公司的三个支柱业务，目前发展前景良好，业务对于软件系统的需求也非常多，所以项目会长期存在和维护，团队也相对比较稳定。

# 4.2 运用 Scrum敏捷框架改造项目开发模型

SP 项目面临的典型问题是项目的范围、资源会经常被调整，计划总是赶不上变化。基于敏捷以下几个特点来解决这些问题：

（1）敏捷开发强调小周期迭代交付；  
（2） 敏捷开发注重沟通，每天站会，问题能尽早暴露，降低风险；  
（3） 敏捷开发可以快速响应变更；  
（4）敏捷开发市面上已经有很多免费专业的软件，可以很方便地管理项目。

# 4.2.1 项目团队角色定义

基于 Scrum 敏捷软件开发方法，对SP 项目团队的角色定义如下：

第一个角色：产品负责人（Product Owner） 由产品经理担任，是项目团队和公司内部业务方或者第三方公司人员的联系人。总体来说产品负责人的工作包括两个方向：提出正确的解决方案和确保解决方案被正确制造。产品负责人的具体职责如下：

（1）提供产品愿景和边界，不仅要提出产品的愿景，更要确保把产品的愿景  
介绍给开发团队，提供约束条件。（2）代表客户和业务，对接业务，了解业务诉求。（3）拥有产品列表，故事优先级的唯一决定者，因为产品负责人直接接触需  
求方，更清楚需求方的诉求和产品的具体使用场景。（4）设立故事的接收标准。（5）和项目相关团队、干系人沟通。

第二个角色：敏捷教练（Scrum Master） 由每个小组的组长担任。敏捷教练是Scrum 流程的捍卫者和布道者。敏捷教练是一位服务型领导，具体职责如下：

（1）教练，指导 Scrum团队。（2）Scrum 专家，Scrum 团队的过程专家，引导 Scrum 的流程。（3）推土机，推动一切阻碍开发团队工作的问题。（4）保护伞，保护开发团队免受外部的干扰。（5）服务型领导，区别于传统的命令式领导，敏捷教练和团队中的其他成员是平级的，不会过问团队成员的绩效，他主要确保能够满足团队最高优先级的需要。

第三个角色：Scrum 研发团队（Scrum Team） 包含设计人员、软件开发工程师、测试工程师、敏捷教练、项目经理和产品负责人，通常由 7 到 9 名成员，但面对大型的项目和需求，也可能扩展到 15个人左右。Scrum 的研发团队是跨职能的，在 Scrum 的每个冲刺当中，开发团队为了实现计划里的功能，他们必须完成所有的相关工作，包括产品设计，开发和测试。因此他们必须具备完成这些工作的所有技能。区别于传统开发方法里的“只负责自己那一部分工作”，作为一个整体，团队对功能的实现负责。因此这样就赋予 Scrum 研发团队前所未有的权利和使命。Scrum 研发团队主要有以下职责：

（1）在冲刺执行期间，开发团队完成创造性的工作，包括设计，开发，测试，最终提供可发布的版本功能。（2）每日站会，团队通过每日站会来实现自我的检视和调整，以实现Sprint目标。（3）梳理产品列表，帮助产品负责人梳理产品列表，细化产品列表条目，估算和排列故事优先级。（4）Sprint 冲刺计划，在每个 Sprint 之初，参与计划会议。在会议上，根据产品经理提供的背景信息，对产品列表里的条目工作量进行估算，并在敏捷教练的指导下，与产品负责人共同制订冲刺目标。（5）回顾检视，在每次 Sprint 结束后，研发团队会参加冲刺评审会议和冲刺回顾会议。在会议上，Scrum 团队对自己的流程和技术进行了检视和调整，以进一步提升团队利用Scrum交付商业价值的能力。

第四个角色：架构师，负责技术选型，系统整体的技术架构设计和业务架构设计，技术指导，帮助开发人员完成工作。

第五个角色：软件开发工程师，负责开发新的功能编码，修复编码缺陷，并对所负责部分的功能进行单元测试和功能试验等。

第五个角色：软件测试工程师，制定测试计划，编写测试用例，对软件开发工程师开发的功能，按照测试计划进行测试。

第六个角色：项目经理（Program Manager） 与其他角色几乎全身投入到冲刺中相比，项目经理可能同时在多个项目里，负责整个SP 项目团队的流程，进度管理，协调资源，汇报状态。

# 4.2.2 项目开发模型改造

按照敏捷开发的思路，只需要对当前迭代的所有故事点进行详细的规划，不需要详细考虑对整个项目进行之后的工作，这样既能是工作简单，又能很好的控制项目进度和质量。

运用 Scrum 敏捷框架，SP 项目团队重新确立了软件研发的项目开发流程，整个研发周期以会议为节点，分为以下几个阶段：

阶段一：用户故事选择  
阶段二：计划会议，需求评审  
阶段三：新功能开发和测试  
阶段四：评审会议  
阶段五：产品正式发布  
阶段六：回顾会议

这个周期结合了增量开发模型和敏捷开发模型的优点，改造的项目开发周期具有以下几个特点：

（1）总体来说，阶段划分上与传统开发模型没有太大区别，但每个项目阶段都有重合，而不是纯粹的串连关系，比如开发阶段和测试阶段，开发人员完成一个故事点或新功能，测试人员就可以对该功能点进行测试，并不需要等到迭代中所有功能完成提测后才进行测试，这样也为整个项目进度增加了一定的缓冲时间。

（2）固定周期可以方便的度量项目开发速度，以更短的周期交付可用的软件，也有利于安排之后的工作和会议。

（3）团队中的每个人都对产品质量负责，也避免了传统开发中所有问题集中在项目阶段靠后的测试环节，问题积攒太多，没有时间解决，最后导致项目进度延迟的问题。

# 4.3 运用 Scrum敏捷框架改造项目进度管理

# 4.3.1 用户故事描述需求

Scrum敏捷框架中，通过用户视角来描述具体需求的问题，用户故事常用的格式是：作为 $<$ 角色 $>$ ，我想要 $<$ 功能 $>$ ，以便于<价值> [45]。在计划阶段，无需知道需求的太多的细节，因为需求的特定细节很可能随着时间而改变，因此，在离真正实现需求还很遥远的时候关注需求的特定细节很可能产生浪费。

用户故事就是需求澄清过程中的助记词。它是一个计划工具，根据它的优先级和估算来安排计划[46]。SP项目在召开计划会议1周前，研发负责人和产品负责人对用户故事进行故事点（即规模）的预估，原则上一个用户故事不超过 20个故事点（大约10个工作日），超过就需要进行细化拆分成多个用户故事。

# 4.3.2 Sprint 冲刺做项目分解

SP 项目之前的项目结构分解，粒度过大，造成开发周期长、软件质量差，进度延迟的问题。Scrum 中按 Sprint （所谓 Sprint 就是 Scrum 里面对迭代的称呼）分期发布的机制，固定 3 周一个周期。固定的 Sprint 周期有以下好处：

（1）对于整个团队来说，有规律的节奏感可以帮助团队清楚开发速度。（2）固定的 Sprint 长度便于度量开发速度。（3）如果 Sprint 周期不是固定的，Sprint 计划会、评审会、回顾会的时间也都不能确定，不规律的安排也很不利于后期工作和会议的安排，尤其是涉及到多个部门同时参加的会议。

# 4.3.3 建立 Scrum 的开发流程

Scrum 项目在时间上会被划分为多个Sprint，每个Sprint 一开始项目团队都会将优先级较高的用户故事从产品列表移入 Sprint 列表。然后，团队开始在这些用户故事上工作。在 Sprint 结束的时候，团队提交潜在可发布的增量。接下来，下一个Sprint。使用Scrum 框架来开发，会经历如下过程：

Sprint 开始前的工作：准备好产品列表。

Sprint 当中的工作：

（1）计划：从产品列表中挑选出几个优先级较高的用户故事。（2）工作于这两个故事（召开每日站会以便团队随时进行检视和调整）。（3）在冲刺结束前完成这些功能，提交潜在可发布增量（也许立即发布，也许以后发布，什么时候发布取决于产品发布策略）。（4）召开评审会议。（5）召开回顾会议。进行下一个 Sprint……。SP 项目一个完整的 Sprint，由几个关键的会议串联起来：（1）Sprint 计划会议。3 周的 Sprint 冲刺周期，在召开 Sprint 计划会议之前大概 1 周左右，研发负责人和需求负责人要确定下一个迭代可能要做的用户故事。在 Sprint 计划会议前，会将将要产生的用户故事公布到任务看板上，至少提前一天，团队成员在工作有空闲的情况下，可以先熟悉下需求。

计划会议主要议程有以下 3项:

1）产品负责人讲解用户故事。  
2）研发和产品在评审和讨论之后，共同确定优先级和任务范围。  
3）研发人员认领自己任务，进行分解并估算工时。

（2）每日站会。SP 项目团队弹性办公，上午9 点到9 点半，一般会议会再早上9点四十分开始，会议进行 10-20 分钟结束。站会时会有一个看板，配合站会的开展，相对于电子看板，物理看板板让团队每个人都的参与操作。任务的执行情况分为：未开始、实现中、待测试、测试中、待发布、已发布。

每日站会尽量的保持简单，以下是一个可以使用的会议议程范例：

1）上个工作日我完成了什么？

2）今天我计划做什么？

3）任务进行中有什么障碍？

开发团队借由每日站会来检视完成目标的进度，并监视完成 Sprint 待办列表的工作进度趋势。。

（3）Sprint评审会议。Sprint 评审会议一般定于一个 Sprint 的倒数第二个工作日，根据当次 Sprint 所发布的需求组，产品经理可以在测试环境对当前迭代功能进行验收。评审本次 Sprint 的故事点，检查是否已达到 Sprint 的目标。会议过程中，如果产品负责人想要改变某个功能，可以提需求变更或者加入到下一个 Sprint 产品列表中去。遇到比较重要的功能，会邀请业务方用户来亲自体验，从而收集用户的反馈，达到检验和调整的目的。

（4）Sprint 回顾会议。回顾会议一般定在Sprint 的最后一个工作日，为Scrum再次提供了检验和调整的机会。回顾会议就是成员对团队工作问题指出和改进方案的提出，帮助团队之后更好的工作。

回顾会议开起来可以很简单，但是想开好其实并不容易。需要能够调动气氛，促进团队畅所欲言，让团队认为安全，最终收集到更多的有待改进的建议和具体的解决方案及下一步计划。SP 项目开展 Scrum 初期的回顾会议大家积极性不高，基本上流于形式，后期通过茶话会的形势，大家坐在一起一边吃下午茶一边发表自己的意见，气氛非常活跃。会后敏捷教练会把会议讨论的内容用影像的形式记录下来，分享给大家，整个团队都会更有成就感，而且更愿意去履行他们在会议上承诺的职责和流程。

# 4.4 Scrum 敏捷框架结合关键链项目管理的进度管理优化

# 4.4.1 项目进度计划优化

按照3.4节中 SP 项目之前的进度计划，每个迭代至少要进行 3 个月，而且这个工期估算不准确，原因在于三个方面：第一，由于业务需求方要求时间紧，而工作量大复杂，工期的估算不会让所有成员参与，存在高估了项目成员的能力的情况；第二，由于项目周期较长，无法考虑项目开发中会产生人员变动的情况，当人员变动时，项目进度多少都会受到影响。第三，实施周期长，当项目在实施中出现问题或项目范围发生变化后，项目进度就也会受到影响，新实施的 Scrum架构很好的解决这些问题，使得进度计划更科学：

（1）在 Sprint 计划会上，研发人员认领自己的故事，进行任务分解，工作量估算，就不存在高估项目成员能力的情况。（2）项目周期缩短至 3 周，如果有人员变动的情况，会在Sprint 开始之前进行调整，几乎不会影响Sprint的进度。（3）实施周期缩短，业务方的紧急需求大部分可以放在下一个迭代，既不影响当前迭代的进度，也快速响应处理了用户需求。

# 4.4.2 项目进度控制优化

项目进度控制对项目进展情况的监控，主要是在项目确定的各个里程碑或控制点上，对照计划，确定实际规模、工作量、成本、进度等情况。对工作进展的适当了解，有助于及时发现与计划的偏离并纠偏。

第 3.4.3 节中介绍 SP 项目的几次迭代都产生了延期，这与没有好的进度跟踪策略和控制的工具有很大的关系。SP 项目改造后，使用了TAPD（Tencent AgileProduct Development）腾讯敏捷研发协作平台，帮助研发团队有效地管理需求、资源、进度和质量，提高研发效率和产品质量，具体使用主要涉及几个模块：

“需求”：一般有产品负责人以用户故事的方式创建。

“迭代”：3 周一个迭代，在计划会议之前 1 周左右，研发负责人和需求负责人，将要做的用户故事加入到下一个迭代中。

“缺陷”：一般由测试人员创建，有测试中或线上的问题，会被记录且分配相应的开发人员来处理。

TAPD 中还有许多报表统计，如需求统计，进度跟踪（燃尽图），工时报告、缺陷统计等。

下面介绍基于 Scrum 敏捷框架对项目进度控制的优化，主要从工作结构分解，和采纳看板、燃尽图等跟踪进度，并利用关键链项目管理法来改善项目制约条件。

（1）工作分解结构(WBS)。工作分解是基础，但是Scrum敏捷框架下，是对于用户故事的分解，而且只对当次的迭代进行分解，无需考虑更远的需求。

2 看板（Kanban）。这里的看板就是在4.3.3小节Scrum 开发流程里介绍得站会里的白板，看板上列出所有的故事点，团队成员每天早晨对自己的工作进行介绍，每个人都清楚整个团队的工作情况，对于更好的安排手里的工作，或者需要其它成员参与的工作，非常简单便利。

（3）燃尽图(Burn-down Chart)。燃尽图可以直观获取进度和项目剩余的总工作量，也可以跟踪任务进度、监控项目进度是否偏离计划。SP项目团队采用的是TAPD 敏捷研发协作云平台，它非常便捷，可以根据团队实际情况进行定制化设置，团队成员都可以在平台上操作自己的任务，清楚别人的任务，还提供有效检查工作的图表，可以导出整个团队在整个迭代中的表现等。如图4.2所示是一个三周的工时燃尽图，横轴是时间，纵轴是剩余的工作量（还未完成的工时），橘色线是理想状态的燃尽曲线，绿色线是实际的燃尽曲线。如果如果橘色线在绿色线下方，则表明实际进度比理想进度超前；反之，绿色线在橘色线上方，则表示实际的进度落后于理想进度。理想的燃尽图，是两条曲线距离不远。

![](images/306bcf83ed267e45e2ec3a315db0fbbbbb70b3478f171a2c338440075cc0a357.jpg)  
图 4.2 工时燃尽图  
Fig.4.2 Man hour burnout diagram

# 4.4.3 关键链项目管理方法完善进度管理

关键链项目管理方法（CCPM，Critical Chain Project Management）是约束理论(TOC,Theory of Constraint)在项目管理领域的一种实现[47]。在第 3.4.3 小节中用到的关键路径法，但是其缺乏考虑资源约束对项目进度造成了很多问题。

对于在Scrum敏捷开发框架下,对于复杂的，影响因素多的用户故事且无法拆分为更小故事点的情况下，要保证当次迭代能在三周内交付，更加考研团队在项目进度管理上的能力。运用关键链方法可以为类似困难大的迭代更好的控制项目进度。

本文采用关键链模型为：

（1）识别关键路径。

（2）识别资源约束，确认关键链。如图 4.3 所示，根据关键路径法得出A-D-E-H 为一条项目的关键路径。但是C、E、G 存在共用的资源的情况下，比如需要同一个人来完成，那么在时间上这三个活动只能一个一个执行，那么这样的关键路径就对项目进度管理产生了误导，导致项目因为计划制定的不合理导致不能按计划完成。但是根据关键链的思想，考虑资源约束，则A-D-C-E-G-H 是个关键链，需要关注每个活动。

![](images/a78ab28123c3f75010440b3dd547e5071ff71979c8e6ed0e947ff32a15e947a2.jpg)  
图4.3关键链  
Fig.4.3 Critical chain

（3）设置缓冲。在软件开发项目中，项目成员在做活动时间估算时，通常会预留一些安全时间。关键链法会去除安全时间，但在关键链的最后加入额外时间,即设置项目缓冲（Project Buffer,PB)。另外，在非关键链汇入关键链的连接处设置接驳缓冲（Feed Buffer, FB）来来保证关键链上的任务不受非关键链上任务延误的影响。资源缓冲(Resource Buffer,RB)在共用资源的活动后面加入缓冲，降低共用资源引起的风险。图4.3 加入缓冲后即为如图 4.4所示。

![](images/74044880d68a96cf68c6e77db6aed4632a70eb5d1e5ff1458e632fc9f596e85b.jpg)  
图4.4 缓冲机制  
Fig.4.4 Buffer mechanism

下面以 SP 项目一次典型的 Sprint 冲刺为例，使用关键链管理方法。需求内容为：患者随访系统的医学报表需求。该需求功能复杂，要求高，且不具备再拆分故事点的情况，因为整体报表要展示数据要出自一个数据源，且业务方希望看到完整的报表呈现。

用户故事描述：作为特殊药品随访管理人员，我想要医学报表，以便于为药企提供之前协议中需要的数据报告，为创新药品提供真实世界研究数据。具体的需求功能包括：某时间段内某药品，随访患者年龄分布柱形图、随访患者性别分布饼图、前十AE(即不良反应)按患者数分布柱形图、前十AE按人次分布柱形图，某AE 严重程度分布饼图、某 AE处理指导意见分布饼图、某 AE 结局分布饼图，开始发生 AE 的时间分布箱型图。如表4.1 为传统方式预估的任务工时安排表，由于项目成员在估算任务时间时加入了大量安全时间，总工时 288。

表 4.1 医学报表任务工时预估表  
Table 4.1 Medical Report Task Man Hour Estimation   

<table><tr><td>序号</td><td>任务</td><td>前置任务</td><td>工时</td></tr><tr><td>1</td><td>医学报表</td><td></td><td>288</td></tr><tr><td>2</td><td>需求分析</td><td></td><td>12</td></tr><tr><td>3</td><td>需求调研</td><td></td><td>8</td></tr><tr><td>4</td><td>需求评审</td><td>3</td><td>4</td></tr><tr><td>5</td><td>设计阶段</td><td></td><td>40</td></tr><tr><td>6</td><td>原型设计</td><td>4</td><td>16</td></tr><tr><td>7</td><td>视觉交互设计</td><td>6號</td><td>16</td></tr><tr><td>8</td><td>产品评审</td><td>7</td><td>8</td></tr><tr><td>9</td><td>开发阶段</td><td></td><td>136</td></tr><tr><td>10</td><td>技术方案设计，数仓表结构设计</td><td>8</td><td>16</td></tr><tr><td>11</td><td>数据清洗同步到数据仓库</td><td>10</td><td>16</td></tr><tr><td>13</td><td>后端：随访患者年龄分布柱形图和随访患者性 别分布饼图接口</td><td>11</td><td>8</td></tr><tr><td>14</td><td>后端：前十AE 按患者数分布柱形图接口</td><td>11</td><td>8</td></tr><tr><td>15</td><td>后端：前十AE 按人次分布柱形图、严重程度、 指导意见、结局分布饼图接口</td><td>11</td><td>8</td></tr><tr><td>16</td><td>后端：开始发生AE 的时间分布箱型图接口</td><td>11</td><td>8</td></tr><tr><td>17</td><td>前端：随访患者年龄分布柱形图和随访患者性 别分布饼图调试测试</td><td>13</td><td>8</td></tr><tr><td>18</td><td>前端：前十AE 按患者数分布柱形图接口调试</td><td>14</td><td>8</td></tr></table>

<table><tr><td></td><td>测试</td><td></td><td></td></tr><tr><td>19</td><td>前端：前十AE 按人次分布柱形图、严重程度、 指导意见、结局分布饼图调试测试</td><td>15</td><td>16</td></tr><tr><td>20</td><td>前端：开始发生AE 的时间分布箱型图调试测 试</td><td>16</td><td>8</td></tr><tr><td>21</td><td>功能测试</td><td>20、24</td><td>16</td></tr><tr><td>22</td><td>测试阶段</td><td></td><td>84</td></tr><tr><td>23</td><td>用例编写</td><td>8</td><td>16</td></tr><tr><td>24</td><td>用例评审</td><td>23</td><td>8號</td></tr><tr><td>25</td><td>患者年龄分布柱形图和随访患者性别分布 饼图调试测试</td><td>21</td><td>8</td></tr><tr><td>26</td><td>AE 按患者数分布柱形图接口调试测试</td><td>21</td><td>8</td></tr><tr><td>27</td><td>AE 按人次分布柱形图、严重程度、指导意 见、结局分布饼图调试测试</td><td>21</td><td>16</td></tr><tr><td>28</td><td>发生AE 的时间分布箱型图调试测试</td><td>21</td><td>8</td></tr><tr><td>29</td><td>环境测试</td><td>28</td><td>4</td></tr><tr><td>30</td><td>发布验收</td><td></td><td>8</td></tr><tr><td>31</td><td>发布计划</td><td>29</td><td>2</td></tr><tr><td>32</td><td>发布系统</td><td>31</td><td>2</td></tr><tr><td>33</td><td>系统验收</td><td>32</td><td>4</td></tr></table>

对该次迭代需求使用关键链管理方法，包含以下步骤：

（1）制作网络图，识别项目的关键路径。主要任务包括活动定义、工期估算、活动排序、画出网络图。如表 4.2所示，我们采用 $5 0 \%$ 法消除安全时间后得到的医学报表功能的任务工时估算表，总工时 144小时。

表 4.2 消除安全时间工时估算表  
Table 4.2 Estimate of Eliminating Safety Time and Man Hour   

<table><tr><td>序号</td><td>任务</td><td>前置任务</td><td>工时</td></tr><tr><td>1</td><td>医学报表</td><td></td><td>144</td></tr><tr><td>2</td><td>需求分析</td><td></td><td>6</td></tr><tr><td>3</td><td>需求调研</td><td></td><td>4</td></tr><tr><td>4</td><td>需求评审</td><td>3</td><td>2</td></tr></table>

<table><tr><td>8</td><td></td><td></td><td></td><td></td><td>记</td><td></td><td></td><td>亿</td><td></td><td></td><td>6 测试</td><td>8</td><td>Ⅱ</td><td></td><td>5</td><td></td><td></td><td></td><td>Ⅱ</td><td></td><td>6 8</td><td></td></tr><tr><td rowspan="2">发生 AE 的时间分布箱型图调试测试</td><td>见、结局分布饼图调试测试 AE 按人次分布柱形图、严重程度、指导意</td><td>AE 按患者数分布柱形图接口调试测试</td><td>饼图调试测试</td><td>患者年龄分布柱形图和随访患者性别分布</td><td>用例评审</td><td>用例编写</td><td>测试阶段</td><td>功能测试</td><td>前端：开始发生 AE 的时间分布箱型图调试测</td><td>指导意见、结局分布饼图调试测试 前端：前十AE 按人次分布柱形图、严重程度、</td><td>前端：前十AE 按患者数分布柱形图接口调试</td><td>别分布饼图调试测试 前端：随访患者年龄分布柱形图和随访患者性</td><td>后端：开始发生AE 的时间分布箱型图接口</td><td>指导意见、结局分布饼图接口</td><td>后端：前十AE按人次分布柱形图、严重程度、</td><td>后端：前十AE 按患者数分布柱形图接口</td><td>别分布饼图接口 后端：随访患者年龄分布柱形图和随访患者性</td><td>数据清洗同步到数据仓库</td><td>技术方案设计，数仓表结构设计</td><td></td><td>开发阶段 产品评审</td><td>视觉交互设计</td><td>设计阶段 原型设计</td></tr><tr><td>乙</td><td></td><td></td><td></td><td></td><td>8 8</td><td></td><td>20、24</td><td>9</td><td></td><td></td><td></td><td>Ⅱ</td><td></td><td>Ⅱ</td><td></td><td></td><td></td><td>8</td><td></td><td>9 8</td><td></td><td></td></tr><tr><td>→</td><td>8</td><td></td><td></td><td></td><td></td><td></td><td></td><td>8</td><td></td><td>8</td><td></td><td></td><td>→</td><td></td><td>→</td><td></td><td>8</td><td>8</td><td>8</td><td></td><td>8</td><td></td></tr></table>

<table><tr><td>29</td><td>环境测试</td><td>28</td><td>2</td></tr><tr><td>30</td><td>发布验收</td><td></td><td>4</td></tr><tr><td>31</td><td>发布计划</td><td>29</td><td>1</td></tr><tr><td>32</td><td>发布系统</td><td>31</td><td>1</td></tr><tr><td>33</td><td>系统验收</td><td>32</td><td>2</td></tr></table>

根据计划表画出网络图，如图 4.5所示。

![](images/d62a9d7d473b8a1ce07ab358b92c88e60cd09f3e1772cc351a5352d38004fb5b.jpg)  
图 4.5 关键链网络图  
Fig.4.5 Critical chain network diagram

（2）识别关键链。根据关键链的定义，满足资源约束的关键路径就是关键链。如图 4.5 所示，为网络图，通过计算，最长的一条路径为 78 个小时，关键链为:3-4-6-7-8-10-11-15-19-21-27-29-31-32-33。

（3）设置缓冲。

1）设置项目缓冲(Project Buffer, PB) 。

消除安全时间后，项目整体时间是明显不够的，项目很大可能会延期，根据$50 \%$ 完工概率法，在关键链的末尾设项目缓冲以应对不确定风险。设置项目缓冲为39 个小时。

# 2）设置接驳缓冲(Feed Buffer, FB)

医学报表项目上，共设置了 7 个接驳缓冲，具体设置情况见表4.2。

表 4.2 接驳缓冲  
Table 4.2 Feed Bugger   

<table><tr><td>缓冲</td><td>对应非关键活动</td><td>对应时间长度</td><td>缓冲长度</td></tr><tr><td>FB-17</td><td>13、17</td><td>8</td><td>4</td></tr><tr><td>FB-18</td><td>14、18</td><td>8</td><td>4</td></tr><tr><td>FB-20</td><td>16、20</td><td>8</td><td>4</td></tr><tr><td>FB-24</td><td>23、24</td><td>20</td><td>10</td></tr><tr><td>FB-25</td><td>25</td><td>4</td><td>2</td></tr></table>

<table><tr><td>FB-26</td><td>26</td><td>4</td><td>2</td></tr><tr><td>FB-28</td><td>28</td><td>4</td><td>2</td></tr></table>

# 3）资源缓冲(Resource Buffer,RB)

该次开发不需要设置资源缓冲，因为各个阶段之间相互独立，分别会安排不同的人员来完成，没有人力资源冲突的问题。。

最后加入接入缓冲和项目缓冲后的网络图，如图4.6 所示，整个开发时间为78个小时，即月

![](images/9aa926262da8e19ad3cdfc42b4d1dd43fc117ede2931f649c108e1e679d1d08d.jpg)  
图4.6 加入缓冲后关键链网络图

Fig.4.6 Critical chain network diagram after adding buffer

加入缓冲后，整体的医学报表功能进度计划，如表4.3 所示。从3月1日到3月 22 日，一个Sprint 地迭代将医学报表功能完整完成。

表 4.3 医学报表进度计划表  
Table 4.3 Medical Report Schedule   

<table><tr><td>序号</td><td>任务</td><td>前置任务开始时间</td><td></td><td>完成时间</td><td>负责人</td></tr><tr><td>1</td><td>医学报表</td><td></td><td>2022/3/1</td><td>2022/3/17小组组长</td><td></td></tr><tr><td>2</td><td>需求分析</td><td></td><td>2022/3/1</td><td>2022/3/1</td><td>产品经理</td></tr><tr><td>3</td><td>需求调研</td><td></td><td>2022/3/1</td><td>2022/3/1</td><td>产品经理</td></tr><tr><td>4</td><td>需求评审</td><td>3</td><td>2022/3/1</td><td>2022/3/1</td><td>产品经理</td></tr><tr><td>5</td><td>设计阶段</td><td></td><td>2022/3/2</td><td>2022/3/4</td><td>设计人员</td></tr><tr><td>6</td><td>原型设计</td><td>4</td><td>2022/3/2</td><td>2022/3/2</td><td>产品经理</td></tr><tr><td>7</td><td>视觉交互设计</td><td>6</td><td>2022/3/3</td><td>2022/3/3</td><td>设计人员</td></tr><tr><td>8</td><td>产品评审</td><td>7</td><td>2022/3/4</td><td>2022/3/4</td><td>产品经理</td></tr><tr><td>9</td><td>开发阶段</td><td></td><td>2022/3/4</td><td>2022/3/14开发人员</td><td></td></tr><tr><td>10</td><td>技术方案设计，数仓表结构设计</td><td>8</td><td>2022/3/4</td><td>2022/3/7</td><td>后端开发</td></tr><tr><td>11</td><td>数据清洗同步到数据仓库</td><td>10</td><td>2022/3/7</td><td>2022/3/8</td><td>后端开发</td></tr><tr><td>13</td><td>后端：随访患者年龄分布柱形图</td><td>11</td><td>2022/3/9</td><td>2022/3/9</td><td>后端开发A</td></tr></table>

<table><tr><td></td><td>和随访患者性别分布饼图接口</td><td></td><td></td><td></td><td></td></tr><tr><td>14</td><td>后端：前十AE 按患者数分布柱 形图接口</td><td>11</td><td>2022/3/9</td><td>2022/3/9</td><td>后端开发B</td></tr><tr><td></td><td>后端：前十AE 按人次分布柱形 15图、严重程度、指导意见、结局分布饼 图接口 后端：开始发生 AE 的时间分布</td><td>11</td><td>2022/3/9</td><td>2022/3/9</td><td>后端开发C</td></tr><tr><td>16 17</td><td>箱型图接口 前端：随访患者年龄分布柱形图</td><td>11</td><td>2022/3/9</td><td>2022/3/9</td><td>后端开发D</td></tr><tr><td></td><td>和随访患者性别分布饼图调试测试 接驳缓冲</td><td>13</td><td>2022/3/10 2022/3/10</td><td></td><td>2022/3/10 前端开发A</td></tr><tr><td>18</td><td>前端：前十AE 按患者数分布柱 形图接口调试测试</td><td>14</td><td>2022/3/10</td><td>2022/3/10</td><td>2022/3/10 前端开发B</td></tr><tr><td></td><td>接驳缓冲 前端：前十AE 按人次分布柱形 19图、严重程度、指导意见、结局分布饼 图调试测试</td><td>15</td><td>2022/3/10 2022/3/10</td><td>2022/3/10</td><td>2022/3/10前端开发C</td></tr><tr><td>20</td><td>前端：开始发生 AE 的时间分布 箱型图调试测试</td><td>16</td><td>2022/3/10</td><td></td><td>2022/3/10前端开发D</td></tr><tr><td>21</td><td>接驳缓冲 功能测试</td><td>20、24</td><td>2022/3/10 2022/3/11</td><td>2022/3/10</td><td>2022/3/11前后端人员</td></tr><tr><td>22 23</td><td>测试阶段 用例编写</td><td>8</td><td>2022/3/4 2022/3/4</td><td>2022/3/16测试人员 2022/3/7</td><td>测试人员</td></tr><tr><td>24</td><td>用例评审</td><td>23</td><td>2022/3/8</td><td>2022/3/8</td><td>测试和开发</td></tr><tr><td></td><td>接驳缓冲</td><td></td><td>2022/3/9</td><td>2022/3/10</td><td>人员</td></tr><tr><td></td><td>患者年龄分布柱形图和随访患</td><td>21</td><td>2022/3/14</td><td></td><td></td></tr><tr><td>25</td><td>者性别分布饼图调试测试</td><td></td><td></td><td></td><td>2022/3/14测试人员A</td></tr><tr><td>26</td><td>AE 按患者数分布柱形图接口 调试测试</td><td>21</td><td>2022/3/14</td><td></td><td>2022/3/14测试人员B</td></tr></table>

<table><tr><td>27</td><td>AE 按人次分布柱形图、严重程 度、指导意见、结局分布饼图调试测试</td><td>21</td><td>2022/3/14</td><td></td><td>2022/3/14测试人员C</td></tr><tr><td>28</td><td>发生 AE 的时间分布箱型图调 试测试</td><td>21</td><td>2022/3/14</td><td></td><td>2022/3/14测试人员D</td></tr><tr><td></td><td>接驳缓冲</td><td></td><td>2022/3/14</td><td>2022/3/14</td><td></td></tr><tr><td>29</td><td>环境测试</td><td>28</td><td>2022/3/15</td><td>2022/3/15测试人员</td><td></td></tr><tr><td>30</td><td>发布验收</td><td></td><td>2022/3/15</td><td>2022/3/15团队成员</td><td></td></tr><tr><td>31</td><td>发布计划</td><td>29</td><td>2022/3/15</td><td>2022/3/15开发人员</td><td></td></tr><tr><td>32</td><td>发布系统</td><td>31</td><td>2022/3/15</td><td>2022/3/15开发人员</td><td></td></tr><tr><td>33</td><td>系统验收</td><td>32</td><td>2022/3/15</td><td>2022/3/15产品经理</td><td></td></tr><tr><td></td><td>项目缓冲</td><td></td><td>2022/3/16</td><td>2022/3/22</td><td></td></tr></table>

对于关键链的监控，结合敏捷框架，运用燃尽图动态实时监控整个迭代进度，及时发现关键链的动态变化，尽早做出措施预防。

# 4.4.4 需求变更管理

在 3.5.4节中提到由于需求变更导致项目延期的问题，在于没有成熟的流程来处理需求变更。在项目实施中，需求变更请求提交得越晚，造成项目延期的风险就越大，研发和测试时间被挤压又造成产品质量下降。

需求变更一般分为两种情况：

（1）项目实施期间，资源发生了变化，需要变更，比如团队人员调整导致了人力资源不足等。（2）业务提出来新需求，发现正在开发的产品不能满足，需要修改变更。由于软件项目变化快的特性和互联网信息瞬息万变，业务的需求也比过去更加频繁地发生变化，提交到研发部门的变更要求也越来越多。

随着公司业务的快速发展，PBM 业务线的飞速扩展，项目接收到越来越频繁的变更的请求，继续使用传统的开发流程的团队来说已举步维艰。然而实施 Scrum敏捷框架可以在一定程度上缓解这个挑战，虽然实施敏捷后大部分需求变更会被安排到下一个迭代，但是面对非常重要的需求还是需要进行变更处理，而且如果是更加重大的变更，可能会影响整个项目后续的迭代，正规的变更流程是必不可少的：

（1）判断问题类型：简单的问题就可以临时加入到迭代中，复杂的需要正式

的变更管理；

（2）Scrum团队讨论，包含：核实问题，变更影响范围，变更的技术方案；（3）对于团队无法解决的问题升级到上级部门，由业务领导和研发领导一起讨论，并决定是否变更和变更方案；（4）产品经理代表业务方提需求变更申请，团队按照确定的方案执行。

# 5 SP 软件项目开发进度管理改造方案的实施效果

# 5.1 新旧项目组织结构对比

如图 5.1 是新旧项目组织结构对比图，可以看出新的项目组织结构在项目责任人、解决资源冲突、项目经理工作以及团队沟通等方面都优于旧的。

![](images/3f481a600c7eee0319e75d91eb22bcb779fc9d45448029735e0db8739f5f8385.jpg)  
图 5.1 弱矩阵型组织结构和项目型组织结构对比图

Fig.5.1 Comparison between weak matrix organization structure and project organization structure

# 5.2 新旧项目开发模型对比

如图5.2 是新旧项目开发模型对比图，Scrum 敏捷开发模型在 SP 项目上的开发时间、开发周期、产品质量和需求变更方面体现了优越性。

![](images/cc688d9315d8dbfae70f738c60515d8d7f3d7b3425c10906f15ad9f40c71bf75.jpg)  
图 5.2 增量开发模型和 Scrum 敏捷开发模型对比图

Fig.5.2 Comparison between incremental development model and scrum agile development model

# 5.3 新旧项目进度管理对比

# 5.3.1 进度计划制定对比

如图5.3是新旧项目进度计划情况对比图，可以看出改造后的项目进度计划在工作分解、工期估算、项目约束条件考虑和缓冲机制考虑上更加的详细和科学。

![](images/88bd3b45028ac6c5ec2d4f2e465f7334e1f9724b530a21168bbeb356440dadef.jpg)  
图 5.3 新旧项目进度计划情况对比图

# 5.3.2 进度控制对比

如图5.4是新旧项目进度控制情况对比图，改造后的进度获取和进度跟踪，通过管理工具来管理更加方便快捷。另外在传统项目里大部分项目会使用微软的Project 软件，但是对于软件类，尤其是对于不太复杂的项目来说并不友善，首先Project 软件购买费用高且管理成本高，需要花费大量时间和精力来维护，其次，Project 不能管理需求，然后TAPD可以做从业务需求到项目上线以及线上 BUG的管理，最后 Project 协作性不好，基本上由项目经理来操作，而TAPD上每个人针对自己的部分操作，所有人都可见。

![](images/105992b0fa022bdc056d75f5cb80db0d8e9636d2dc7be3bdf1bb6fa74d85aaf2.jpg)  
图 5.4 新旧项目进度控制情况对比图

Fig.5.4 Comparison of progress control between new and old projects

# 5.4 Scrum 敏捷方法和关键链方法的成效

SP 软件开发项目前期，由于不适应的项目组织结构、不灵活的开发模型以及进度计划和进度控制措施不完善，导致了项目进度大量延迟、甚至进度失控。延期导致来自业务方的压力也不断增大，对系统投诉的情况越来越多，项目组成员在项目开发工作的同时还需要抽出大量精力来应对业务方对于项目进度和质量的各种问题。相对而言，解决各种问题浪费的时间又影响了项目进度的正常推行，形成了恶性的循环。更有业务发展快，需求变更频繁，也进一步导致项目进度安排的混乱。在重重的压力之下，大刀阔斧的改革是 SP项目能够回归正规的必经之路。

从项目整体的改造来讲，运用 Scrum 敏捷框架，加速了对业务方需求的响应和产出，有节奏地提高了项目团队的开发效率，保证了项目进度，也保证了项目质量。快速的交付结果也让业务对项目团队的认可度提高，增加了业务收益回报。运用关键链方法，项目进度计划制定更加科学合理，项目控制更加有效，保证了项目正常按计划实施交付。目前 SP 项目有条不紊的进行，系统正常稳定允许，提高了整个运营中台部门的话语权，增强了业务使用方对系统的信心，提高了业务推广销售力度，增加了整个公司的运营收入。

下面从需求完成情况，进度延期情况、业务方的评价等三个方面来具体说明改造方案的成效。

# 5.4.1 需求完成情况

# 表 5.1 需求完成情况表

Table 5.1 Requirements Completion   

<table><tr><td>年份\需 求指标</td><td>初始数量</td><td>新增数量</td><td>完成数量</td><td>初始规模</td><td>新增规模</td><td>完成规模</td></tr><tr><td>2020</td><td>84</td><td>312</td><td>166</td><td>1764</td><td>6552</td><td>3486</td></tr><tr><td>2021</td><td>230</td><td>384</td><td>366</td><td>4830</td><td>8064</td><td>7686</td></tr><tr><td>2022 前4个月</td><td>248</td><td>60</td><td>146</td><td>5208</td><td>2220</td><td>5526</td></tr></table>

如表5.1所示。2020 年SP项目各系统总的初始需求数量为 84 个，2020年业务方总新增需求数量为 312 个，整体研发团队完成需求数量 166 个。从规模上看（一个规模定义为 4 个工时），初始为 1764 规模，新增 6552 规模，研发团队完成规模为 3486。而 2021 年，初始需求量为 230，新增需求量 384，团队完成需求量 366 个，初始需求规模 4830，新增需求规模 8064，消耗需求规模 7686。2022年前 4 个月，初始需求量 248，新增需求量 60，消耗需求量 146，初始需求规模5208，新增需求规模2220，消耗需求规模 5526。

SP 项目从 2021 年初开始逐步实施 Scrum 敏捷开发方法，整体的团队人员数量没有明显变化的情况下，从需求完成的数据上看，2021 年团队效率提升一倍。

# 5.4.2 进度延期和加班情况

2020年由于项目整体组织架构、开发模型等的弊端，项目进度状况非常糟糕，延期率达 $80 \%$ 以上。2021年在逐步实施敏捷结合关键链方法后，延期率得到很好改善，降至 $30 \%$ 。根据 2022 年前 4 个月的统计，整体项目还未出现延期情况，由于项目一般会在后半年需求量和紧急程度增加，后半年可能会有延期情况，但已经有较大改善。

团队的加班情况也能体现项目进度计划和控制是否科学合理，据团队成员周末及节假日加班天数统计，可以看出项目进度计划和控制得到了很大改善。在2020年，总加班天数 722 天，人均 24 天，可以看出几乎每人每月有超过 3天的加班时间。这一情况在 2021 年得到改善，2021 年，团队成员总加班天数485天，人均约16 天。在 2022 年前 4 个月，团队周末节假日加班总天数 0，可以说团队加班情况有了飞跃式的改善。

# 5.4.3 项目质量情况

项目质量情况可以通过统计每个需求的缺陷数量变化来体现，如表 5.2所示，可以看出每个需求的缺陷数量得到明显改善，这表明了SP项目需求完成质量得到显著提高。

表 5.2 平均每个需求缺陷数量统计表  
Table 5.2 Statistics Of Average Number Of Defects Per Demand   

<table><tr><td>年份\缺陷</td><td>完成需求数量</td><td>缺陷数量</td><td>平均每个需求缺陷数量</td></tr><tr><td>2020年</td><td>166</td><td>2835</td><td>17</td></tr><tr><td>2021年</td><td>366</td><td>2020</td><td>5.5</td></tr><tr><td>2022年前4个月</td><td>146</td><td>667</td><td>4.6</td></tr></table>

# 5.4.4 业务部门的评价

业务方对研发部门的认可和评价，可以直接体现项目的价值和完成度。评价主要包括两个方面：

一个方面是运营中台部门在每年年底会组织业务领导对研发部门的总体绩效进行评级。

在 2020 年底业务部门对 SP 研发团队的评级为 D，可以说非常不满意。一是面是新系统上线有很多改变，对药房门店工作人员日常工作提出挑战，业务员需要适应适应新系统；二则主要还是研发团队开发效率低，不能及时响应业务的需求；三则是系统不稳定，导致的了 3次线上重大事故。

而在 2021 年底业务部门给出的评级为B。一是药房门店经过一年的适应，也逐渐熟练操作系统；二是经过研发产品一年时间对系统的优化和改进，系统更加稳定、使用更为便捷；三是经过项目进度管理的改造，项目团队能积极响应业务需求，快速交付需求，提高了业务方对研发工作的认可度。

另一个方面就是业务部门对研发部门的投诉情况，投诉越多也就直接说明了研发部门工作没有到位。在 2020 年接到 10 个投诉，包括收银系统、采销存系统和随访系统等，投诉主要集中在未积极响应业务诉求，还有就是系统上线后不满足业务要求。而 2021 年总接收到业务 2 个投诉，主要是系统不能满足业务实际使用，说明系统的产品规划还有很多待改进的地方。

# 6 结论与展望

# 6.1 结论

本论文以 SP软件开发项目的进度管理为研究对象，介绍了公司背景，公司的发展主要以业务为导向，业务发展快，随之软件项目需求变化快，进度延期，研发速度无法支持业务的快速发展。详细分析了SP 项目背景和前期开发中的问题，主要包括软件开发方法，实际状况和遇到的问题，从组织架构、开发模型、进度管理、变更管理等各方面，运用理论分析研究和案例分析相结合的方法，形成一套结构严谨、覆盖全面的进度控制方法。

通过研究国内外项目进度管理相关的文献资料作为理论依据，再将理论研究运用到具体实际案例中，通过 SP 软件开发项目在项目进度管理的改造方案，深入探讨了 Scrum 敏捷框架如何对软件开发的活动进行有效的指导，凸显了敏捷项目管理相对于传统增量项目管理方式的优势，体现了敏捷方式以人为本、欢迎变化的特点。

通过对SP软件开发项目，患者随访系统医学报表迭代的具体的迭代开发实例，展示了在敏捷迭代中，在软件开发全生命周期中如何应用关键链方法来改进项目开发进度管理，解决了项目进度管理中人的行为和资源制约导致项目延期的问题，为项目的顺利进行提供了可靠的方法和实践，为后续的项目开发提供借鉴。

SP 软件开发项目目前已经在后期的项目开发中实施了以上的各项改进措施，并且取得了很好的效果。本次对 SP 软件开发项目的进度管理案例的研究，也可以运用到类似业务导向、需求变化快的企业，来就解决项目进度管理问题，避免或减少项目延期的情况，提高用户体验和满意度。

# 6.2 不足与展望

软件项目开发是一项复杂且多变的工程，受到多方因素的影响，项目进度的控制也并非易事，本文存在一些局限性：

（1）进度管理改造方案模型适用于 SP 软件项目开发进度管理，对于其他的团队可能有些问题不存在，或者存在其它的问题，问题都不一样，一定要结合团队文化、项目情况、管理目标、团队现状等方面做出相应的调整，不能生搬硬套。

（2）对于关键链设置缓冲的方法并不具有广泛性。在关键链项目管理方法具体案例中，医学报表需求中，项目计划是设置缓冲长度采用 $50 \%$ 法，是由于报表数据整理分析比较复杂，留足够的时间进行数据校对，但对于对于其他的需求都采用 $50 \%$ 的方法并不一定合理，需要根据项目实际情况采用合理的方法。

总而言之，本人根据 SP 软件项目开发中进度管理存在的问题，结合 Scrum 敏捷开发框架和关键链项目管理方法等，给出符合SP 软件开项目进度管理上的解决方案。但 Scrum 敏捷框架本身也存在局限性，框架和实际应用也是一个逐步完善的过程，通过进一步在实际项目中的应用逐步优化。

# 参考文献

[1] 杰克．吉多，詹姆斯．克莱门斯著,张金成译．成功的项目管理[M].北京：电子工业出  
版社，2007，3-7．[2] 康路晨.PMP 备考宝典：路晨带你去通关[M].北京: 电子工业出版社, 2021, 133.[3] 斯琴高娃．关于项目进度计划与控制策略的研究[J]．经济研究导刊，2020（07）：  
183-185[4] 格雷戈里 T.豪根著, 北京广联达慧中软件技术有限公司译.项目计划与进度管理[M] .  
北京：机械工业出版社，2005，9-11.[5] (美) 哈罗德.科兹纳著 杨爱华 王丽珍等译 项目管理 计划、进度和控制的系统方法  
(第 10 版)，电子工业出版社，2013-09.[6] 赵建霖. A 软件项目进度管理的改善研究[D]. 四川:电子科技大学，2020.[7] 卢向南．项目计划与控制[M]．北京：机械工业出版社，2009，26[8] 刘伊生．工程项目计划与控制[M]．北京：中国建筑工业出版社，2008, 10[9] D.H. Chang, H.S. Jin, M.H. Kim. Critical path identification in the context of a workflow[J].  
Information & Software Technology, 2002, 44(7): 405-417[10] 项目管理协会. 项目管理知识体系指南（PMBOK 指南)第六版 [M].北京：电子工业  
出版社，2018，176[11] J. Kamburowski. New validations of PERT times[J]. Omega, 1997, 25(3): 323-328[12] 郭瑞霞. 基于关键链的 GIS 项目进度管理研究[D]. 中国科学院学,2020.[13] Goldratt E M.Critical Chain [M]. Great Barrington:The North River Press，1997.[14] 艾利.高德拉特. 突破项目管理的瓶颈-关键链(罗嘉颖译)[M].北京：电子工业出版社，  
2006.[15] Z.D. Radovilsky. A quantitative approach to estimate the size of the time buffer in the  
theory of constraints[J]. International Journal of Production Economics, 1998, 55(2): 113-119[16] P. Barber, C. Tomkins, A. Graves. Decentralised site management — a case study[J].  
International Journal of Project Management, 1999, 17(2): 113-120[17] Leach L P. Critical Chain Project Management Improves Project Performance[J]. Project  
Management Journal, 1999, 30(2): 39-51[18] K.T. Yeo, J.H. Ning. Integrating supply chain and critical chain concepts in  
engineer-procure-construct (EPC) projects[J]. International Journal of Project Management,2002,  
20(4): 253-262[19] Newblod R C.Project management in the fast lane-applying thetheory of constraints［M］.Boca Raton:TheSt.Lucie Press，1998:55-57[20] 杨健，张晓丽．基于绩效偏差阈值的军用软件项目挣值管理研究[J]．软件导刊，2013（06）：1-3．[21] 马萍, 樊燕燕, 魏兴华,等. 基于改进灰色关键链的缓冲设置方法研究[J]. 工程管理学  
报, 2020, 34(1)：5.[22] 刘剑. 加强软件项目管理 提高软件开发质量[J]. 数字石油和化工, 2006(3):4.[23] 贾 文 潇 . 软 件 项 目 管 理 中 的 进 度 管 理 [J]. 电 子 测 试 ,2016(10):145-145,111.

DOI:10.3969/j.issn.1000-8519.2016.10.079.

[24] 杨蕾，郑江. 天天学敏捷：Scrum 团队转型记. 北京：清华大学出版社，2019：35-46

[25] 斯里格，布罗德里克．软件项目管理与敏捷方法（李晓丽，李虎，赵华译）．北京：机械工业出版社，2010：5，11．18.

[]26 罗伯特·G·库珀.新产品开发流程管理-以市场为驱动（刘立，师津锦，于兆鹏译）[M].北京：电子工业出版社，2019，127-128

[27] Poppendieck M，Poppendieck T．Lean Software Development：An Agile Toolkit．Boston：Addison-Wesley Professional Publisher，2006，：24-25

[28] Jack Milunsky. State of Agile[EB/OL]. http://agilesoftwaredevelopment.com/ blog/jackmilunsky/state-agile. 2009

[29] 陈华恩. Scrum 敏捷方法在湖南翼方软件项目管理中的应用研究[D]. 湖南:湖南大学,2014.

[30] Ken S 著.李国彪,孙媛译.Scrum 敏捷项目管理实践[M].北京:清华大学出社,2009,192-193.

[31] 孙杰成,颜锦奎. Scrum 敏捷开发方法在跨境电商平台的实践[J]. 计算机技术与发展,2018,28(1):159-163. DOI:10.3969/j.issn.1673-629X.2018.01.034.

[32] 李超. Scrum 在 K 公司 M 团队项目管理中的应用[D]. 四川:西南科技大学,2018.

[33] 王成飞. Scrum 方法在万维公司软件开发过程管控中的应用研究[D]. 甘肃:兰州理工大学,2018.

[34] Scott W.Ambler. Agile Requirements Change Management [EB/OL]. http://www.agilemodeling.com/essays/changeManagement.htm

[35] 贾智 泉. 普惠 公司 敏捷 项目管 理的 研究 [D]. 湖北: 华中 科技 大学,2016.DOI:10.7666/d.D01075494.

[36] 王政涛. 基于敏捷开发方法的 Z 公司 U 产品开发策略研究[D]. 东南大学, 2015.

[37] 刘雄华. 基于关键链的软件项目进度管理[D]. 天津大学,2016.

[38] 马修·斯凯尔顿,曼纽尔·派斯著.石雪峰,董越,雷涛译.高效能团队模式：支持软件快速交付的组织架构[M].北京:电子工业出版社,2021,5-9.

[39] 洪江龙. 基于数据挖掘的本科专业评估管理信息系统应用研究[D]. 上海:上海交通大学,2010.

[40] 刘伊生．工程项目计划与控制[M]．北京：中国建筑工业出版社，2008, 11-12

[41] 刘金霞. X 科技公司软件开发进度管理研究——以 TR-069 软件开发项目为例[D]. 兰州大学, 2011.

[42] 银洋. DR 公司 STAR--MES 软件开发项目进度管理研究[D]. 东北大学, 2016.

[43] 吕阳. 多项目背景下 BMCC 网络建设项目人力资源管理研究[D]. 北京:北京交通大学,2019.

[44] 沈晓华. 基于关键链的 L 公司多项目进度管理研究[D]. 苏州大学.

[45] 罗伯特·C.马丁著.鄢倩，徐进译.敏捷软件开发[M].北京：清华大学出版社，2021.1，4-14.

[46] 梅刚. D 公司研发项目的进度控制和质量管理研究[D]. 四川:电子科技大学,2017.

# 独创性声明

本人声明所呈交的学位论文是本人在导师指导下进行的研究工作和取得的研究成果，除了文中特别加以标注和致谢之处外，论文中不包含其他人已经发表或撰写过的研究成果，也不包含为获得北京交通大学或其他教育机构的学位或证书而使用过的材料。与我一同工作的同志对本研究所做的任何贡献均已在论文中作了明确的说明并表示了谢意。

学位论义作者签名：茶香签字日期：2022年5月20日

# 学位论文数据集

表1.1:数据集页  

<table><tr><td>关键词*</td><td>密级*</td><td>中图分类号</td><td>UDC</td><td>论文资助</td></tr><tr><td>项目进度管理； Scrum敏捷框架; 关键链</td><td>公开</td><td></td><td></td><td></td></tr><tr><td colspan="2">学位授予单位名称*</td><td>学位授予单位代 码*</td><td>学位类别*</td><td>学位级别*</td></tr><tr><td colspan="2">北京交通大学</td><td>10004</td><td>管理学</td><td>硕士</td></tr><tr><td colspan="2">论文题名*</td><td colspan="2">并列题名*</td><td>论文语种*</td></tr><tr><td colspan="2">SP软件项目开发进度管理研究</td><td colspan="2"></td><td>中文</td></tr><tr><td>作者姓名*</td><td colspan="2">秦香宇</td><td>学号</td><td>20140419</td></tr><tr><td colspan="2">培养单位名称*</td><td>培养单位代码*</td><td>培养单位地址</td><td>邮编</td></tr><tr><td colspan="2">北京交通大学</td><td>10004</td><td>北京市海淀区西直 门外上园村3号</td><td>100044</td></tr><tr><td colspan="2">专业学位*</td><td>研究方向*</td><td>学制*</td><td>学位授予年*</td></tr><tr><td colspan="2">工程管理</td><td>信息管理</td><td>2年</td><td>2022</td></tr><tr><td>论文提交日期*</td><td colspan="4"></td></tr><tr><td>导师姓名*</td><td colspan="2">2022年5月30日</td><td>职称*</td><td>教授</td></tr><tr><td>评阅人</td><td colspan="2">顾元勋 答辩委员会主席*</td><td colspan="2">答辩委员会成员</td></tr><tr><td></td><td colspan="2">秦秋莉</td><td colspan="2">陈学冬、万辉</td></tr><tr><td colspan="6">电子版论文提交格式文本（）图像（）视频（）音频（）多媒体（）其他（）</td></tr><tr><td colspan="2">推荐格式：application/msword；application/pdf 电子版论文出版(发布）者</td><td colspan="2">电子版论文出版（发布）地</td><td></td></tr><tr><td></td><td></td><td colspan="2"></td><td>权限声明</td></tr><tr><td>论文总页数*</td><td colspan="4">59</td></tr><tr><td colspan="6">共33项，其中带*为必填数据，为21项。</td></tr></table>