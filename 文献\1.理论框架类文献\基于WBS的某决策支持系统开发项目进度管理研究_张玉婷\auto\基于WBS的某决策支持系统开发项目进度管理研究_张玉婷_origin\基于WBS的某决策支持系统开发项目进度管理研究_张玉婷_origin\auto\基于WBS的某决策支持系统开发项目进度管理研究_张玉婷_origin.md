# 基于 WBS 的某决策支持系统开发项目进度管理研究

张玉婷1，2 杨镜宇3

( 1. 国防大学研究生院，北京 100091;2. 海军参谋部机要局，北京 100841;3. 国防大学联合作战学院，北京 100091)

摘要: 为保证某决策支持系统开发项目能够在有序管控下运行，并按期按质实现系统交付使用，首先，对项目进度管理相关概念进行概述; 其次，基于 WBS 制订进度管理计划; 最后，基于三点估算法和蒙特卡洛方法对工期进行估算仿真。 结果显示，工期估算结果具有一定的可行性，该项目进度管理计划能够为管理人员提供决策参考。

关键词: 决策支持系统; 项目进度管理; 蒙特卡洛法; 三点估算法

# 0 引言

项目是为创造独特的产品 服务或成果而进行的临时性工作，是组织创造价值、 实现效益的重要形式［1］ 基于项目管理理论，对项目活动进行整合和管理，如项目的进度、 成本、 质量等，有助于提高项目管理效率、 缩短项目工期、 节省项目成本。 而项目进度管理是项目管理的重要内容。 通过分析项目中各工序间的逻辑关系，确定科学的施工顺序; 通过编制进度计划和资源供应计划，在质量、 费用目标协调的基础上实现工期目标。 目前，常用的项目进度管理方法包括甘特图 ( Gantt Chart，GC) 关键路径法 ( Critical PathMethod，CPM) 、 图示评审技术 ( Graphical Evalua-tion Review Technique， GERT ) 风 险 评 审 技 术( Venture Evaluation Review Technique，VERT) 、 计划评审技术 ( Program Evaluation and Review Tech-nique，PERT) 、 关 键 链 项 目 管 理 ( Critical ChainProject Management，CCPM) 等［2-3］ 。

随着科学技术的进步、 网络信息技术的发展，决策支持系统 ( Decision Support System，DSS) 已从传统的单模型、 人工协调模式发展为多模型、计算机自组织协调运行的辅助决策模式。 本文所研究的某决策支持系统以管理科学 系统工程为理论基础，以仿真模拟技术为技术手段，能够为管理人员提供决策参考 该系统涉及与多种设备多个平台的对接，以及各类复杂数据的处理、 数据库和模型库的管理等过程，若进度管理过程出现问题，则会影响项目总体进度，甚至会出现级联效应，使项目面临严重风险

基于此，本文针对该系统开发项目进度管理问题进行分析，制订了项目进度计划并进行工期估算仿真，主要包括创建项目 WBS 设置目标和里程碑 绘制网络计划图以及利用三点估算法和蒙特卡洛法对工期进行估算仿真等。

# 1 经典 PERT 方法概述

Malcolm 等［4］于 20 世纪 50 年代末提出了经典PERT 网络方法 该方法中各项工作间的逻辑关系明确，工作持续时间为随机变量。 设 $^ { a }$ 、 b 、 $m$ 分别表示某项工作的最乐观时间、 最悲观时间、 最可能时间，以 μ = a + 4m + b 表示工序持续时间期望值，以 $\sigma ^ { 2 } = \frac { ( b - a ) ^ { 2 } } { 3 6 }$ 表示工序持续时间方差。

经典PERT 方法的假设条件包括: 各工序持续时间服从 $\beta$ 分布，且相互独立; 对于工序 i，用 $\mu _ { i }$ 和 $\boldsymbol { \sigma } _ { i } ^ { 2 }$ 表示其不确定性; 网络计划图中的关键路径只有1 条，且关键路径上的工序数量足够大，并能满足中心极限定理，其他路径成为关键路径的概率可忽略; 网络计划中某条路径的总工期服从正态分布。在以上假设的基础上确定关键路径，并得到某路径上项目总工期期望值 $T _ { e }$ 和方差 $\sigma _ { e } ^ { 2 }$ ，公式如下

$$
T _ { e } \ = \ \sum _ { i = 1 } ^ { k } \ ( \ \frac { a _ { i } \ + 4 m _ { i } \ + \ b _ { i } } { \sigma } )
$$

$$
\sigma _ { e } ^ { 2 } \ = \ \sum _ { i = 1 } ^ { k } \left( { \frac { b _ { i } \ - \ a _ { i } } { \sigma } } \right) ^ { 2 }
$$

式中， $T _ { e }$ 越大，说明路径越关键， $T _ { e }$ 为最大值时的路径为关键路径; $\sigma _ { e } ^ { 2 }$ 越小，说明该路径工期期望值可靠度越高; 反之，可靠度越低、 工期不确定性越大 根据大数定理，项目工期为正态分布，得到标准偏移值 $z$ ，即

$$
z ~ = ~ { \frac { T - T _ { e } } { \sigma } }
$$

因此， $T \sim N ~ \left( ~ T _ { \mathrm { e } } \right) ~ \sigma _ { e } ^ { 2 } )$ 。 其概率密度函数 $f ( T )$ 为项目计划工期 $T$ 的完工概率，计算公式如下

$$
f ( \ T ) = { \frac { 1 } { \sqrt { 2 \pi } \sigma _ { e } } } e ^ { { \frac { ( T - T _ { e } ) ^ { 2 } } { 2 \sigma _ { e } ^ { 2 } } } }
$$

# 2 某决策支持系统开发项目进度计划编制

# 2.1 项目工作分解结构

项目工 作 分 解 结 构 ( Work Breakdown Struc-ture，WBS) 是指将项目可交付物和项目工作按照实施顺序划分为相对独立的、 便于管理的工作单元 通过对项目全部工作范围进行逐层分解，以有效控制项目工期［13］ 创建项目 WBS 应遵循的原则如下［14］:

( 1) 分层数量应适宜。 层级越多，项目工作越详细，但项目管理难度越大; 层级过少，项目工作难以表述清楚。

( 2) 分解工作单元时，需厘清各工作单元间的逻辑关系。

( 3) 在一张 WBS 图中，每个工作单元仅出现一次 。

( 4) 同一层级工作单元应具备可比性。

基于此，创建某决策支持系统开发项目 WBS图，如图1 所示。

![](images/9f92ac98615d2ad6ea29fce3d9027a0d683bbc54ce5392da95fb0b50591bbbfc.jpg)  
图1 某决策支持系统开发项目 WBS 图

# 2. 2 项目里程碑

某决策支持系统开发项目生命周期管理包括需求分析、 方案设计、 系统设计、 编码和测试、集成系统测试、 验收发布、 项目总结 7 个阶段。整个项目管理过程复杂且漫长，各阶段在时间维度呈串行分布，因此，需要设置项目建设的关键性节点对项目进展进行标识，以保证项目按时完工 某决策支持系统开发项目里程碑节点见表1

表1 某决策支持系统开发项目里程碑节点  

<table><tr><td>阶段</td><td></td><td></td><td></td><td></td><td></td><td></td><td>x月×日×月×日×月×日×月×日×月×日×月×日×月×日</td></tr><tr><td>需求分析</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>方案设计</td><td></td><td>△</td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>系统设计</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>编码和测试</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>集成系统 测试</td><td></td><td></td><td></td><td></td><td>▲</td><td></td><td></td></tr><tr><td>验收发布</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>项目总结</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr></table>

# 2. 3 项目组织管理

在项目进度管理中，合理规划人力资源类别和工作量、 制订人力资源计划十分重要。 根据项目 WBS，设置项目管理第一责任人 项目负责人系统分析员、 程序员、 工程师等岗位。 其中，项目管理第一责任人主要负责追踪项目整体进度，调配项目负责人员及确定各组分工，牵头项目评审工作，协调其他难题等; 项目负责人主要负责与项目管理第一责任人沟通、 汇报工作情况，直接负责项目管理，负责里程碑评审及文档审核等;系统分析员主要参与项目早期筹划和项目评审等;程序员主要负责软件设计、 软件编码、 集成与测试等; 工程师主要负责软件需求分析、 软件质量保证 撰写工作日记等 某决策支持系统人力资源计划表见表2。

表2 某决策支持系统人力资源计划表  

<table><tr><td>工作类别</td><td>工作内容</td><td>人资资源配置</td></tr><tr><td rowspan="4">1.需求分析</td><td>1.1客户需求获取</td><td>项目负责人、系统分析员</td></tr><tr><td>1.2产品需求说明</td><td>项目负责人、系统分析员</td></tr><tr><td>1.3需求评审</td><td>系统分析员、工程师</td></tr><tr><td>1.4客户确认需求</td><td>系统分析员、工程师</td></tr></table>

( 续)

<table><tr><td>工作类别</td><td>工作内容 1.5技术方案编评</td><td>人资资源配置</td></tr><tr><td>1.需求分析</td><td>1.6确认工作量和 时间</td><td>系统分析员、工程师 项目负责人、系统分析 员、工程师</td></tr><tr><td rowspan="3">2.方案设计</td><td>2.1总体设计</td><td>项目负责人、系统分析 员、工程师</td></tr><tr><td>2.2概要设计</td><td>系统分析员、工程师</td></tr><tr><td>2.3设计评审</td><td>系统分析员、工程师</td></tr><tr><td rowspan="9">3.系统设计</td><td>3.1界面模块设计</td><td>程序员</td></tr><tr><td>3.2接口模块设计</td><td>程序员</td></tr><tr><td>3.3管理模块设计</td><td>程序员</td></tr><tr><td>3.4安全系统设计</td><td>程序员</td></tr><tr><td>3.5用例设计</td><td>程序员、工程师</td></tr><tr><td>3.6设计规格说明 4.1界面模块一阶</td><td>系统分析员、工程师</td></tr><tr><td>段编码</td><td>程序员</td></tr><tr><td>4.2 界面模块二阶 段编码</td><td>程序员</td></tr><tr><td>4.3界面管理交互</td><td>程序员、工程师</td></tr><tr><td rowspan="14"></td><td>4.4界面模块单元</td><td></td></tr><tr><td>测试 4.5数据模块一阶</td><td>系统分析员、程序员</td></tr><tr><td>段编码 4.6数据模块二阶</td><td>程序员</td></tr><tr><td>段编码</td><td>程序员</td></tr><tr><td>4.7数据管理交互 4.8数据模块单元</td><td>程序员、工程师</td></tr><tr><td>4.编码和测试|测试 4.9 接口模块一阶</td><td>工程师</td></tr><tr><td>段编码</td><td>工程师</td></tr><tr><td>4.10 接口模块二阶 段编码</td><td>工程师</td></tr><tr><td>4.11接口模块单元 测试</td><td>系统分析员、工程师</td></tr><tr><td>4.12 管理模块一阶 段编码</td><td>程序员</td></tr><tr><td>4.13 管理模块二阶 段编码</td><td>程序员</td></tr><tr><td>4.14 管理模块三阶 段编码</td><td>程序员</td></tr></table>

( 续)

( 续)

<table><tr><td>工作类别</td><td>工作内容</td><td>人资资源配置</td></tr><tr><td rowspan="3">4.编码和测试</td><td>4.15管理模块单元 测试</td><td>系统分析员、工程师</td></tr><tr><td>4.16 安全模块编码</td><td>程序员</td></tr><tr><td>4.17 安全模块单元 测试</td><td>系统分析员、工程师</td></tr><tr><td rowspan="2">5.集成系统测试</td><td>5.1代码修正</td><td>程序员、工程师</td></tr><tr><td>5.2集成测试与试 运行</td><td>系统分析员、工程师</td></tr><tr><td rowspan="2">6.验收发布</td><td>6.1产品发布</td><td>项目负责人、系统分析 员、工程师</td></tr><tr><td>6.2验收和维护</td><td>项目负责人、系统分析 员、工程师</td></tr><tr><td>7.项目总结</td><td>7.1项目总结</td><td>项目负责人、系统分析 员、工程师、程序员</td></tr></table>

# 2. 4 项目网络计划图

项目网络计划图可以直观展示项目中各项工作进度及工作之间的关系，通过网络分析确定关键路径等［15］。 网络计划图一般由工作、 事项、 线路三个部分构成。 本文采用 PERT 三点估计法对工序持续时间进行估算 为编制某决策支持系统开发项目网络计划图，首先，确定项目中各项工作工期和工作间的逻辑关系 ( 表 3) ; 其次，根据项目活动紧前和紧后关系，绘制某决策支持系统开发项目进度网络计划图，如图 2 所示。 由计算可知，该项目路径共84 条。

表3 项目活动逻辑关系表  

<table><tr><td>工作编号</td><td>工作名称</td><td>工期/d</td><td>紧前工作</td></tr><tr><td>1-2</td><td>客户需求获取</td><td>7</td><td></td></tr><tr><td>2-3</td><td>产品需求说明</td><td>5</td><td>1-2</td></tr><tr><td>2-4</td><td>需求评审</td><td>8</td><td>1-2</td></tr><tr><td>3-5</td><td>客户确认需求</td><td>5</td><td>2-3</td></tr></table>

<table><tr><td>工作编号</td><td>工作名称</td><td>工期/d</td><td>紧前工作</td></tr><tr><td>4-5</td><td>技术方案编评</td><td>15</td><td>2-4</td></tr><tr><td>5-6</td><td>确认工作量和时间</td><td>5</td><td>3-5,4-5</td></tr><tr><td>6-7</td><td>总体设计</td><td>14</td><td>5-6</td></tr><tr><td>7-8</td><td>概要设计</td><td>35</td><td>6-7</td></tr><tr><td>8-9</td><td>设计评审</td><td>15</td><td>7-8</td></tr><tr><td>9-10</td><td>界面模块设计</td><td>5</td><td>8-9</td></tr><tr><td>9-11</td><td>管理模块设计</td><td>10</td><td>8-9</td></tr><tr><td>9-12</td><td>用例设计</td><td>25</td><td>8-9</td></tr><tr><td>10-12</td><td>接口模块设计</td><td>7</td><td>9-10</td></tr><tr><td>11 -12</td><td>安全系统设计</td><td>8</td><td>9-11</td></tr><tr><td>12-13</td><td>设计规格说明</td><td>15</td><td>10-12,11-12,9-12</td></tr><tr><td>13-14</td><td>界面模块一阶段编码</td><td>30</td><td>12-13</td></tr><tr><td>13-16</td><td>数据模块一阶段编码</td><td>60</td><td>12-13</td></tr><tr><td>13-18</td><td>接口模块一阶段编码</td><td>30</td><td>12-13</td></tr><tr><td>13-20</td><td>管理模块一阶段编码</td><td>35</td><td>12-13</td></tr><tr><td>13-23</td><td>安全模块编码</td><td>35</td><td>12-13</td></tr><tr><td>14 -15</td><td>界面模块二阶段编码</td><td>40</td><td>13-14</td></tr><tr><td>15-21</td><td>界面管理交互</td><td>20</td><td>14-15</td></tr><tr><td>15-24</td><td>界面模块单元测试</td><td>10</td><td>14-15</td></tr><tr><td>16-17</td><td>数据模块二阶段编码</td><td>80</td><td>13-16</td></tr><tr><td>17-21</td><td>数据管理交互</td><td>30</td><td>16-17</td></tr><tr><td>17-24</td><td>数据模块单元测试</td><td>15</td><td>16-17</td></tr><tr><td>18-19</td><td>接口模块二阶段编码</td><td>45</td><td>13-18</td></tr><tr><td>19-24</td><td>接口模块单元测试</td><td>15</td><td>18-19</td></tr><tr><td>20-21</td><td>管理模块二阶段编码</td><td>50</td><td>13-20</td></tr><tr><td>21-22</td><td>管理模块三阶段编码</td><td>70</td><td>15-21, 17-21, 20-21</td></tr><tr><td>22-24</td><td>管理模块单元测试</td><td>25</td><td>21-22</td></tr><tr><td>23-24</td><td>安全模块单元测试</td><td>15</td><td>13-23</td></tr><tr><td>24-25</td><td>代码修正</td><td>45</td><td>15-24，17 -24，19-24,</td></tr><tr><td>25-26</td><td>集成测试与试运行</td><td></td><td>22 -24,23-24</td></tr><tr><td>26-27</td><td>产品发布</td><td>20 7</td><td>24-25 25-26</td></tr><tr><td>26-27</td><td>验收和维护</td><td></td><td>25-26</td></tr><tr><td></td><td></td><td>10</td><td></td></tr><tr><td>27-28</td><td>项目总结</td><td>7</td><td>26-27</td></tr></table>

![](images/1dd449fd85a7f6b6ef433d1967918837b747d96ab1bbf5ab7c3337eae2a3c616.jpg)  
图2 某决策支持系统开发项目进度网络计划图

计算每个工作的最早开始时间和最晚开始时间，当两者数值相等时所构成的路径即为关键路径。 基于此，计算得到该项目关键路径为: $1 \longrightarrow$ $2 {  } 4 {  } 5 {  } 6 {  } 7 {  } 8 {  } 9 {  } 1 2 {  } 1 3 {  } 1 6 {  } 1 7 {  } 2 1 {  }$ $2 2 { \longrightarrow } 2 4 { \longrightarrow } 2 5 { \longrightarrow } 2 6 { \longrightarrow } 2 7 { \longrightarrow } 2 8$ 即1.1 客户需求获取 1.3 需求评审 $ 1 . 5$ 技术方案编评 $ 1 . 6$ 确认工作量和时间 $ 2 . 1$ 总体设计 $ 2 . 2$ 概要设计 $ 2 . 3$ 设计评审 $ 3 . 5$ 用例设计 $ 3 , 6$ 设计规格说明 $ 4 . 5$ 数据模块一阶段编码 $ 4 . 6$ 数据模块二阶段编码 4.7 数据管理交互 ${  } 4 . ~ 1 4$ 管理模块三阶段编码 4.15 管理模块单元测试 $ 5 . 1$ 代码修正 $ 5 . 2$ 集成测试与试运行 $ 6 . 2$ 验收和维护 $ 7 . 1$ 项目总结，项目工期为 486d。 在图 2 中，以粗箭线表示关键路径 。

# 3 基于 MC 的某决策支持系统开发项目工期仿真模拟

由于该项目中工作数量多，且各工作之间存在关联依赖关系，因此，在识别关键路径的基础上，进一步分析工序与环境的关系、 工序间的资源和时间冲突［16-21］等不确定性因素，对项目进度计划进行优化。

本文采用蒙特卡洛方法 ( Monte Carlo，MC) ［22］对关键路径工期估算进行仿真模拟 MC 的核心思想是采用随机抽样的方法进行统计模拟试验，得到统计特征值。 在制订项目进度计划时，首先，构建概率模型; 其次，对概率模型进行抽样试验，在此基础上进行工期估计; 最后，得到仿真试验预测结论。 本文采用水晶球 ( Crystal Ball) 软件进行进度管理计划分析，具体步骤如下:

( 1) 基于关键路径，采用蒙特卡洛方法构建项目进度计划仿真模型，见表4

表4 基于 PERT 的网络进度计划仿真模型  

<table><tr><td>工作 编号</td><td>工作内容</td><td>工期 B/d</td><td>最早开始 时间C</td><td>最早结束 时间D</td></tr><tr><td>1-2</td><td>客户需求获取</td><td>7</td><td>0</td><td>= C2 + B2</td></tr><tr><td>2-3</td><td>产品需求说明</td><td>5</td><td>= D2</td><td>= C3 + B3</td></tr></table>

( 续)   

<table><tr><td></td><td></td><td>工期</td><td>最早开始</td><td>最早结束</td></tr><tr><td>工作 编号</td><td>工作内容</td><td>B/d</td><td>时间C</td><td>时间D</td></tr><tr><td></td><td></td><td></td><td>= D2</td><td></td></tr><tr><td>2-4</td><td>需求评审</td><td>8</td><td></td><td>= C4 + B4</td></tr><tr><td>3-5</td><td>客户确认需求</td><td>5</td><td>= D3</td><td>= C5 + B5</td></tr><tr><td>4-5</td><td>技术方案编评</td><td>15</td><td>= D4 = MAX （D5，</td><td>= C6 + B6</td></tr><tr><td>5-6</td><td>确认工作量和时间</td><td>5</td><td>D6</td><td>= C7 + B7</td></tr><tr><td>6-7</td><td>总体设计</td><td>14</td><td>= D7</td><td>= C8+ B8</td></tr><tr><td>7-8</td><td>概要设计</td><td>35</td><td>= D8</td><td>= C9 + B9</td></tr><tr><td>8-9</td><td>设计评审</td><td>15</td><td>= D9</td><td>= C10 + B10</td></tr><tr><td>9-10</td><td>界面模块设计</td><td>5</td><td>= D10</td><td>= C11 + B11</td></tr><tr><td>9-11</td><td>管理模块设计</td><td>10</td><td>= D10</td><td>= C12 + B12</td></tr><tr><td>9-12</td><td>用例设计</td><td>25</td><td>= D10</td><td>= C13 + B13</td></tr><tr><td>10-12</td><td>接口模块设计</td><td>7</td><td>= D11 = D12</td><td>= C14 + B14</td></tr><tr><td>11- 12</td><td>安全系统设计</td><td>8</td><td>= MAX（D10,</td><td> = C15 + B15</td></tr><tr><td>12-13</td><td>设计规格说明</td><td>15</td><td>D11， D12)</td><td>= C16 + B16</td></tr><tr><td></td><td>13-14界面模块一阶段编码 13-16数据模块一阶段编码</td><td>30 60</td><td>= D16 = D16</td><td>= C17 + B17 = C18 + B18</td></tr><tr><td></td><td>13-18接口模块一阶段编码</td><td>30</td><td>= D16</td><td>= C19 + B19</td></tr><tr><td></td><td>13-20管理模块一阶段编码</td><td>35</td><td>= D16</td><td>= C20 + B20</td></tr><tr><td>13-23</td><td>安全模块编码</td><td>35</td><td>= D16</td><td>= C21 + B21</td></tr><tr><td></td><td>14-15界面模块二阶段编码</td><td>40</td><td>= D17</td><td> = C22 + B22</td></tr><tr><td>15-21</td><td>界面管理交互</td><td>20</td><td>= D22</td><td>= C23 + B23</td></tr><tr><td>15-24</td><td>界面模块单元测试</td><td></td><td>= D17</td><td>= C24 + B24</td></tr><tr><td></td><td>16-17数据模块二阶段编码</td><td>10</td><td>= D18</td><td></td></tr><tr><td>17-21</td><td>数据管理交互</td><td>80 30</td><td>= D25</td><td>= C25 + B25</td></tr><tr><td></td><td></td><td></td><td>= D25</td><td> = C26 + B26</td></tr><tr><td>17 -24</td><td>数据模块单元测试</td><td>15</td><td></td><td>= C27 + B27</td></tr><tr><td></td><td>18-19接口模块二阶段编码</td><td>45</td><td>= D19 = D28</td><td>= C28 + B28</td></tr><tr><td>19-24</td><td>接口模块单元测试</td><td>15</td><td>= D20</td><td>= C29 + B29 = C30 + B30</td></tr><tr><td></td><td>20-21管理模块二阶段编码</td><td>50</td><td>= MAX （D26,</td><td></td></tr><tr><td></td><td>21-22|管理模块三阶段编码</td><td>70</td><td>D23）</td><td>= C31 + B31</td></tr><tr><td>22 -24</td><td>管理模块单元测试</td><td>25</td><td>= D31 = D21</td><td>= C32 + B32 = C33 + B33</td></tr><tr><td>23-24</td><td>安全模块单元测试</td><td>15</td><td>= MAX （D32,</td><td></td></tr><tr><td>24-25</td><td>代码修正</td><td>45</td><td>D27，D24, D33，D29）</td><td>= C34 + B34</td></tr><tr><td>25 -26 26-27</td><td>集成测试与试运行</td><td>20</td><td>= D34</td><td>= C35 + B35 = C36 + B36</td></tr><tr><td>26-27</td><td>产品发布 验收和维护</td><td>7 10</td><td>= D35 = D35</td><td> = C37 + B37</td></tr><tr><td></td><td></td><td></td><td>= MAX（D36,</td><td></td></tr><tr><td>27-28</td><td>项目总结</td><td>7</td><td>D37）</td><td>= C38 + B38</td></tr></table>

( 2) 运用水晶球软件，设置项目工作持续时间服从三角分布，设 B2-B38 为假设单元，设 D38为预测单元，即项目最早完成时间

( 3) 采用 MC 抽样方式进行仿真，设置仿真次数为10 000，得到项目工期仿真图，如图 3 所示，敏感度分析结果如图4 所示

![](images/fd081d3beebef06666900d23ecadd5d0961b6d497b6c70b9d3dde5c8d3e0e2bb.jpg)  
图3 项目工期仿真图 ( 截图)

![](images/3a1fa4893a630a18b3e60d5d6ad88f39c5e3bd4e179dba4939758072b3250a66.jpg)  
图4 敏感度分析结果 ( 截图)

由图4 可知，当完工概率为 $9 5 \%$ 时，项目工期为471d，而传统PERT 方法对应的完工概率为 $9 5 \%$ 时的项目工期为 486d，结果相差较小，说明以486d 作为项目工期具有一定的可行性和可靠性

# 4 结语

本文采用 WBS 分解某决策支持系统开发项目工作单元，明确项目目标和里程碑节点，并根据项目实际情况制订人力资源分配方案 通过三点估算法和 MC 仿真实验方法对项目工期进行估算，明确关键路径，制订科学合理的项目进度管理计划，实现了项目进度优化，为管理人员提供了决策依据。

# 参考文献

［1］ 李强. G 公司运维软件开发项目的进度管理研究 ［D］ . 成都: 电子科技大学 . 2022.  
［2］ MOELLER. Operation planning with VERT ［J］ . Operation Re-search. 1981，29 ( 4) : 676-697.  
［3］ 王卓甫 . 工程项目管理 ［M］ . 北京: 中国水利水电出版社，2007.  
［4］ MALCOLM D G，ROSEBOOM J H. Application of a techniquefor research and development evaluation ［J］ . Opns. Res. 1959( 7) : 646-669.  
［5］ 万伟，蔡晨，王长峰 . 在单资源约束项目中的关键链管理［J］ . 中国管理科学，2003 ( 2) : 71-76.  
［6］ 张俊光，宋喜伟，贾赛可，等 . 基于梯形模糊数的项目缓冲确定方法研究 ［J］ . 管理工程学报，2015，29 ( 2) :223-228.  
［7］ 郭小马 . 帕金森定律 ［J］ . 企业管理，1999 ( 3) : 55-56.  
［8］ 单汨源，龙颖. 一种关键链缓冲机制改进方法及其应用研究 ［J］ . 项目管理技术，2006，4 ( 3) : 32-35.  
［9］ 褚春超. 缓冲估计与关键链项目管理 ［J］ . 计算机集成制造系统，2008，14 ( 5) : 1029-1035.  
［10］ 周阳，丰景春. 基于排队论的关键链缓冲区研究 ［J］. 科技进步与对策，2008，25 ( 2) : 174-176.  
［11］ 贾静. 关键链项目管理方法中设置缓冲的新思路 ［J］. 项目管理技术，2011，9 ( 3) : 30-33.  
［12］ GOLDRATT E M. Critical chain ［M］ . Great Barrington: TheNorth River Press，1997.  
［13］ 谭跃进. 公共管理与项目管理 ［M］ . 长沙: 国防科技大学出版社，2006.  
［14］ Project Management Institute. 项目管理知识体系指南 ( PM-$\mathrm { B O K } ^ { \textregistered }$ 指南) ［M］ . 6 版 . 北京: 电子工业出版社，2018.  
［15］ 卢向南 . 项目计划与控制 ［M］ . 北京: 机械工业出版社，2006.  
［16］ 陈赟，张营慧，朱文喜. 基于不确定性因素的柔性关键链缓冲设置方法 ［J］. 长沙理工大学学报 ( 自然科学版) ，

2017，14 ( 3) : 61-67.  
［17］ KHALILISMAIL W L，SHAMAL A O. Schedule risk analysisusing monte carlo simulation for residential projects ［J］ . ZancoJournal of Pure and Applied Sciences，2019，31 ( 5) : 90-103.  
［18］ 李雪，何正文，王能民. 不确定环境下基于时间、 费用及鲁棒性权衡的多目标项目调度优化 ［J］ . 运筹与管理，2019，28 ( 1) : 6-16.  
［19］ RIPON K，RUHUL A，SARKER D L. Resourcec on strainedprojects cheduling with uncertain activity durations ［J］． Com-puters ＆ Industrial Engineering，2017，112 ( 3) : 537-550.  
［20］ 耿苏杰，王秀利. 双重时间不确定性资源受限项目的预防调度研究 ［J］ . 工业工程与管理，2016，21 ( 4) : 86-92.  
［21］ 王仁超，欧阳斌，褚春超. 工程网络计划蒙特卡洛仿真及进度风险分析 ［J］. 计算机仿真， $2 0 0 4 ( 4 ) : 1 4 3 \mathrm { - } 1 4 7 .$ .  
［22］ 尹增谦，管景峰，张晓宏，等. 蒙特卡罗方法及应用 ［J］．物理与工程，2002 ( 3) : 45-49. PMT

# 收稿日期: 2023-04-17

# 作者简介:

张玉婷 ( 通信作者) ( 1991—) ，女，博士研究生，研究方向:联合作战体系建设发展  
杨镜宇 (1971—) ，男，正高级工程师，博士研究生导师，研究方向: 联合作战体系仿真分析与评估