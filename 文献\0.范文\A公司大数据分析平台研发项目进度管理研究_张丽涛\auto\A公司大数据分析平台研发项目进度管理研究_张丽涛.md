密级：公开

# 硕士学位论文

![](images/56bef4ac53b985c6326cbc6833fc91c9deb78056fa79dabb798ec4ca59826336.jpg)

题目：A公司大数据分析平台研发项目进度管理研究

学姓 号： 2019180221名： 张丽涛学科专业： 工程管理培养方式： 非全日制导学 师： 刘丹院： 经济管理学院

密级：公开

# 北京郵電大學

硕士学位论文（专业学位）

题目：A公司大数据分析平台研发项目进度管理研究

学 号： 2019180221

姓 名： 张丽涛

学科专业： 工程管理

培养方式： 非全日制

导 师： 刘丹

学 院： 经济管理学院

# BEIJING UNIVERSITY OF POSTS AND TELECOMMUNICATIONS

Thesis for Master Degree (Professional Degree)

Title: Progress Management Study of Company A'S Big Data Analytics Platform R&D Project

Student No: 2019180221

Name: Zhang LiTao

Major: Engineering Management

Form: Part Time

Supervisor: Liu Dan

Institute:  School of Economics and Management

# 答辩委员会名单

<table><tr><td>职务</td><td>姓名</td><td>职称</td><td>工作单位</td></tr><tr><td>主席</td><td>刘克选</td><td>教授</td><td>北京邮电大学</td></tr><tr><td>委员</td><td>刘丹</td><td>教授</td><td>北京邮电大学</td></tr><tr><td>委员</td><td>张永泽</td><td>高级国际商务师</td><td>北京邮电大学</td></tr><tr><td>委员</td><td>刘楠祺</td><td>高级经济师</td><td>中国国际医药卫生公司</td></tr><tr><td>秘书</td><td>夏东政</td><td>中级工程师</td><td>北京邮电大学</td></tr><tr><td>答辩日期</td><td colspan="3">2023年9月8日</td></tr></table>

# A公司大数据分析平台研发项目进度管理研究

# 摘要

随着企业数字化转型的不断推进和数字经济的持续增长，数字化转型已成为不可阻挡的趋势。在这一转型过程中，大数据平台作为关键的底座平台直接影响着企业数字化转型的成功与否。随着业务场景的多样化，大数据平台研发项目变得更加复杂，这一复杂性给项目进度管理带来了巨大挑战，因此对大数据平台研发项目的进度管理研究显得尤为重要。

A公司是一家企业级大数据平台软件厂商，专注于为企业提供全面的数字化转型服务，以满足其业务需求。大数据分析平台作为该公司的核心研发项目，目前面临严重的项目进度管理问题，可能导致项目延误。这个项目规模庞大，包含17个子系统，且存在的进度问题有突出的代表性，具备高度的研究价值。

论文以A公司大数据分析平台研发项目进度管理作为研究对象，以研发管理、项目进度管理和IPD方法为理论基础，通过对A公司大数据平台开发项目的现状进行调研，提炼出项目进度管理现存的问题，并对关键问题进行深入的原因分析。通过引入IPD方法，从研发项目组织结构与研发管理流程两个维度提出了项目进度管理优化方案。最后，将优化方案进行了实施，评估效果验证了基于IPD方法搭建研发项目管理体系对项目进度管理具有科学性与可行性。

本研究通过改进大数据分析平台研发项目的进度管理问题，优化了A公司研发项目管理体系，使其更加适应复杂、多变的项目环境。研究结论对于其它数字化转型领域企业的研发项目进度管理具有一定的参考和借鉴意义。

关键词：IPD研发管理项目进度管理大数据分析平台

# PROGRESS MANAGEMENT STUDY OF COMPANY A'S BIG DATA ANALYTICS PLATFORM R&D PROJECT

# ABSTRACT

With the continuous promotion of enterprise digital transformation and the continuous growth of the digital economy, digital transformation has become an unstoppable trend. In this transformation process, big data platform as a key pedestal platform directly affects the success of enterprise digital transformation. With the diversification of business scenarios, big data platform R&D projects have become more complex, and this complexity has brought great challenges to the project schedule management, so the study on the schedule management of big data platform R&D projects is particularly important.

Company A is an enterprise-level big data platform software vendor that focuses on providing comprehensive digital transformation services for enterprises to meet their business needs. The big data analytics platform, as the company's core R&D project, is currently facing serious project schedule management issues that may lead to project delays. This project is large in scale, contains 17 subsystems, and has outstandingly representative schedule problems, which possess high research value.

The thesis takes the progress management of Company A's big data analytics platform R&D project as the research object, takes R&D management, project schedule management and IPD methodology as the theoretical basis, and through researching the current status quo of Company A's big data platform development project, refines the existing problems of project schedule management, and carries out in-depth analysis of the causes of the key problems. Through the introduction of IPD method, a project schedule management optimization plan is proposed from the dimensions of R&D project organization structure and R&D management process. Finally, the optimization scheme is implemented, and the evaluation verifies that the R&D project management system based on the IPD method is scientific and feasible for project schedule management.

This study optimizes the R&D project management system of Company A by improving the schedule management problem of the R&D project of big data analytics platform, which makes it more adaptable to the complex and changing project environment. The conclusions of the study have certain reference and reference significance for the schedule management of R&D projects in other enterprises in the field of digital transformation.

KEY WORDS: ipd, r&d management, project schedule management, big data analytics platform

# 目录

# 第一章绪论.

1.1研究背景..  
1.2研究意义.  
1.3研究内容. 2  
1.4研究方法. .3  
第二章 基础概念与献综述 .5  
2.1 基础概念.. ..5  
2.1.1研发管理.  
2.1.2项目进度管理.. .6  
2.1.3集成产品开发(IPD).. ..8  
2.2 文献综述… …..1.3  
2.2.1 研发管理相关研究  
2.2.2研发项目进度管理相关研究. ..15  
2.2.3 IPD相关研究. .16  
2.3本章小结. .17  
第三章A公司大数据分析平台研发项进度管理现状 ..18  
3.1大数据分析平台研发项目介绍.. ..1  
3.1.1项目背景与目标.. .18  
3.1.2项目子系统简介.. .18  
3.1.3项目特点21  
3.2进度管理问题调研.. ..23  
3.2.1调研对象. ..24  
3.2.2 数据分析 ..25  
3.3现存问题与成因分析... ..26  
3.3.1 组织结构问题. ...6  
3.3.2 研发管理流程问题. ..29  
3.4本章小结 ..31  
第四章A公司大数据分析平台研发项目进度管理优化策略. ..32  
4.1IPD方法的可行性论证. ..32  
4.2IPD项目进度改进策略， ..33  
4.3组织结构改进设计.. ..34  
4.3.1市场与产品管理委员会. ..35  
4.3.2产品线集成产品管理团队..  
4.3.3 产品开发团队37

# 4.3.4技术开发团队.

..40  
4.4研发管理流程改进设计. ..41  
4.4.1引入产品市场管理流程. ..41  
4.4.2 重构产品开发流程. .44  
4.4.3引入技术开发流程.  
4.5 本章小结 54  
第五章 A公.数据分析平台研发项目进度管理效果评价..55  
5.1大数据分析平台研发项目进度改进路径 ..55  
5.1.1项目里程碑.. ..55  
5.1.2工作分解结构. ..56  
5.1.3项目活动清单. ….56  
5.1.4项目网络图与关键路径. ….0  
5.2项目进度控制保障措施.  
5.2.1基于绩效管理与激励.. .1  
5.2.2基于角色和分工 ….64  
5.2.3基于决策评审点 .  
5.3组织结构改进效果分析. ..5  
5.3.1 打破部门壁垒..  
5.3.2促进公司研发项目创新发展. …65  
5.3.3提高了设计共用性.. .  
5.4研发管理流程改进效果分析 .7  
5.4.1提升产品生命周期内的风险管控 .7  
5.4.2提升产品研发规划质量... .7  
5.4.3提升研发管理流程的适应性. ….68  
5.5本章小结. .8  
第六章结语..  
6.1总结. .9  
6.2展望. ..70  
参考文献. ..71

# 第一章绪论

# 1.1研究背景

数字化经历了从计算机到互联网再到人工智能和区块链的演进发展后，数据的获取和处理变得更加容易和快速。大数据和数据分析技术的发展使得企业和组织可以更好地利用数据做出决策和预测。近期，信通院发布的《中国数字经济发展研究报告（2023年）》表明，2022年，我国数字经济推进取得了新的突破。数字经济的规模达到了50.2万亿元，同比名义增长 $1 0 . 3 \%$ ，连续11年高于同期GDP名义增长，占我国GDP比重达到了 $4 1 . 5 \%$ ，数字经济作为我国国民经济的“稳定器”和“加速器”的作用更加凸显[。国家刚刚成立的大数据局专门将统筹管理数据资源的整合共享和开发利用，加快推进数字中国的建设。因此，不管是从国家政策的支持来看，还是从信息技术的进步来看，数字化转型已经成为企业发展的必然趋势。

在中国数字化转型浪潮中，项目进度管理研究具有极其重要的意义。数字化转型项目通常涉及复杂的技术和系统集成，涉及多个业务部门和技术团队之间的密切合作，而且带有一定的风险性，例如信息安全、隐私保护等。这些情况会导致项目进度管理中的问题进一步放大，例如项目目标不明确、沟通不畅、信息传递延误和风险得不到有效管控等，最终导致影响项目进度和交付。随着数字化转型的推进，许多企业已经意识到，仅有技术和战略规划是不够的，还需要有效的项目进度管理来确保项目按时交付。国内的一些企业已经开始采用敏捷项目管理.IPD等新的项目管理方法，以应对数字化转型项目的快速变化和不确定性。

当前，A公司已然意识到改进产品研发管理问题的紧迫性，并决定引进先进的研发管理方法，重构公司研发管理体系，以期能够改进项目管理中存在的问题，优化新产品的研发效率，提高产品的研发质量，使新产品能够被快速推向市场，缓解公司在项目上的交付压力，提升公司在数字化转型领域的市场地位。

# 1.2研究意义

本论文通过对A公司研发项目进度管理的现状进行系统分析，并结合IPD方法完成了大数据分析平台研发项目管理体系搭建和进度计划的制定与实施，总结出了基于IPD的项目进度管理应用方法，对于A公司以及数字化转型领域企业的项目进度管理具有一定的参考和借鉴意义。

第一，丰富和拓展A公司项目管理方法体系，解决A公司目前存在的项目进度管理问题，总结并积累项目进度管理经验。从而缩短新产品研发周期，降低项目风险以及提升产品研发质量，进而实现增强公司市场竞争力的目的。

第二，促进数字化转型领域企业研发项目进度管理方法的更新和优化，使项目进度管理方法更加适应复杂、多变的项目环境。

# 1.3研究内容

第一章绪论。本章阐明了论文的研究背景、研究意义，以及研究方法、技术路线和论文内容框架。

第二章基础概念与文献综述。主要阐述研发管理、项目进度管理和IPD的基础概念，并对论文研究相关文献进行研究与综述。

第三章A公司大数据分析平台研发项目进度管理现状。本章首先对大数据分析平台研发项目进行了简要介绍，然后通过项目调研的方式提炼出了A公司项目进度管理的关键问题，并对其进行了深入分析，包括问题的成因以及对项目进度造成的影响，为后续章节提出针对性改进方案提供了事实依据。

第四章A公司大数据分析平台研发项目进度管理优化策略。主要阐述针对A公司大数据分析平台研发项目进度管理问题进行改进的策略与设计，主要包括两个方面：组织结构改进设计、研发管理流程改进设计。

第五章A公司大数据分析平台研发项目进度管理效果评估。本章将前一章节提出的改进方案进行了实施验证，设置了项目进度控制保障措施，并从组织结构和研发管理流程两个维度进行了项目进度改进效果的分析。

第六章结语。总结本论文的研究内容，提出改进方向。

论文框架如图1-1所示。

![](images/99ce8309bdf91cd8be698ef6234da34383455f298d273d7dd800172e6385fa52.jpg)  
图1-1论文框架

# 1.4研究方法

论文采用了三种研究方法：（1）文献研究法。通过查阅、收集众多论文、期刊、项目案例、书籍和A公司以往项目文档等文献资料，探知并吸收前人的科研成果，积淀研究所需的理论基础和参考依据。（2）调查研究法。在了解A公司大数据分析平台项目背景并识别项目相关方之后，通过访问、座谈等方式调查A公司在引进IPD前后各相关方在项目中的角色与分工，以及项目管理问题与改进。然后对收集到的信息进行整理和提炼，作为论文研究的事实依据。（3）案例研究法。以大数据分析平台研发项目为研究对象，搭建基于IPD的研发项目管理体系，并在新的管理体系下完成该大数据分析平台研发项目的项目进度计划的制定。

首先，通过文献研究法研究相关理论基础，包括研发管理项目进度管理等理论的研究。了解本文研究所涉及理论的核心思想及方法，例如IPD组织架构管理体系和项目进度计划制定等，为后续研究打好理论基础。

其次，通过实地调查了解A公司以往研发项目的实施情况，整理并提炼出影响项目进度的关键问题，并分析其造成的影响以及形成的原因。

之后，论证IPD对A公司项目进度管理改进的适用性，并结合IPD提出针对A公司研发项目管理体系的改进思路和优化设计。

最后，以A公司大数据分析平台研发项目为实施对象，研究并验证优化后的设计对于项目进度的改进效果，并根据改进效果得出研究结论。论文的技术路线如图1-2所示。

![](images/2ffa47bcedaf01175452cdd7fe6aa915f65dfc3762d4677f5f573792de2617c8.jpg)  
图1-2技术路线图

# 第二章基础概念与文献综述

# 2.1基础概念

# 2.1.1研发管理

研发管理涉及在构建坚实的研发体系结构的基础上，通过信息平台来协调和管理多方面的研发活动，包括团队建设、流程设计、绩效管理、风险管理、成本控制、项目管理和知识管理等方面。

研发管理是现代组织成功的关键因素之一，其基本原则涵盖了在管理研究与开发活动时应考虑的核心理念和方法。包括（1）研发活动应与组织的战略目标和愿景保持一致。研发项目和投资应该有助于实现组织的长期战略，确保资源的合理分配。（2）研发管理应鼓励创新，不断寻找新的方法、技术和市场机会。同时，要保持灵活性，能够适应变化的需求和新的发现。（3）设定明确的绩效指标和目标，以评估研发项目的成功和效率。通过监控和持续改进，确保项目在时间和预算范围内交付高质量的成果。（4）培养跨职能团队的协作，鼓励不同领域的专家共同解决问题。有效的沟通和团队合作有助于知识分享和创新。（5）识别、评估和管理潜在的风险，包括技术风险、市场风险和法规风险。采取措施降低风险，并有应对计划以应对意外情况。（6）确保资源（包括资金、人力资源和技术设备）得到最佳利用，以提高研发效率和效果。（7）采用有效的项目管理方法，规划、执行和监督研发项目。项目管理有助于确保项目按计划推进，遵循时间表并实现目标。（8）收集、组织和分享研发过程中产生的知识和经验。知识管理可以促进学习和持续改进，避免重复工作。（9）研发活动应以客户需求和市场需求为导向。理解客户的期望，并设计产品或服务以满足这些期望。（10）不断寻求提高研发过程和结果的机会。采取反馈，借鉴经验教训，并定期进行回顾，以实现持续改进。

研发管理的首要任务是确立研发体系结构，随后搭建高水平的研发团队，精心设计高效的研发流程，并充分利用适用的研发信息平台，以支持研发团队的卓越工作。通过绩效管理激发研发团队的积极性，运用风险管理来掌控潜在研发风险，以成本管理确保研发在成本预算范围内完成任务，并通过项目管理来保障研发项目的平稳推进。此外，知识管理有助于智慧的互联和知识的有序积累，进一步增强研发团队的竞争力。

研发管理的团队搭建是确保研究与开发（R&D）项目成功的关键步骤之一。一个强大、高效的研发团队能够推动创新、提高效率，并在竞争激烈的市场中取得优势。在研发团队搭建过程中，需要考虑以下内容：首先，一个成功的研发团队通常由具有不同背景和技能的成员组成。多样性有助于创新，因为不同的观点和经验可以带来不同的思考方式。同时，跨功能性团队可以更好地应对复杂的问题，因为它们汇集了不同领域的专业知识。其次，每个团队成员都应该明确其在项目中的角色和职责。这有助于避免混淆和冲突，确保每个人都知道他们的任务是什么以及如何与团队其他成员协作。再次，建立开放和积极的沟通渠道，鼓励成员分享想法、经验和问题。促进协作，确保信息能够顺畅流通，有助于解决团队内外的挑战。最后，建立明确的绩效评估机制，以识别和奖励卓越的表现。激励机制可以包括奖金、晋升机会、专业发展计划或其他形式的奖励，以激发员工的积极性。

研发管理流程设计是确保研究与开发（R&D）项目高效、有序进行的关键要素。一个明晰、合理的研发流程可以提高项目的质量、控制成本，并确保项目按计划完成。研发管理流程的设计需要关注以下内容：（1）项目立项和规划。确定项目的业务目标、技术目标和范围。这包括明确项目的愿景、目标、范围和可交付成果，以及项目的时间表和预算。（2）需求分析和定义。定义项目的详细需求，包括功能要求、性能指标、质量标准和可行性研究。需求分析可以帮助确保项目满足客户和市场的期望。（3）概念设计和研究。在项目的早期阶段，进行概念设计和研究，以确定最佳的技术方案和方法。这包括概念验证、技术评估和原型开发。（4）详细设计和开发。根据需求和概念设计，进行详细设计，并开始开发产品或解决方案。这包括制定详细的设计规范、编码、测试和验证。（5）质量控制和测试。进行质量控制和测试活动，以确保产品符合规格并达到质量标准。这包括功能测试、性能测试、安全性测试等。（6）验证和验收。验证是确认产品是否符合规格和需求的过程，而验收是确认产品是否满足最终用户的实际需求和操作环境。（7）文档和知识管理。管理和记录项目中生成的文档、数据和知识。这有助于组织积累经验教训，并确保知识共享。（8）风险管理和问题解决。持续监控项目的风险，采取措施来减轻潜在风险的影响。及时解决项目中出现的问题，以防止它们影响项目进展。（9）发布和部署。准备产品或解决方案以进行发布和部署。这包括培训、文档化、支持计划和上线部署。（10）项目关闭和总结。项目完成后，进行项目关闭和总结。评估项目的绩效，记录经验教训，并确定未来改进的机会。

# 2.1.2项目进度管理

随着项目管理领域的不断演进和企业实践经验的不断积累，项目进度管理理论和方法也在不断发展。项目进度管理是项目管理的一个重要领域[2]，起源于美国，其发展可以追溯到上世纪50年代和60年代。旨在规划、控制和监督项目的时间进度，以确保项目能够按时完成，并在规定的时间范围内交付。

项目进度管理的目标是确保项目按时交付，满足项目目标和需求。为实现该目标,需要进行一系列管理工作，包括：定义活动并形成活动清单，制定详细的进度计划，确定关键路径和关键活动，对进度进行控制，及时识别和应对进度风险等。

定义活动是项目进度管理计划制定和控制的基本单元，是将项目范围和工作分解结构（WBS）转化为项目可交付成果而采取的具体的进度管理行动过程[3]。定义活动的目的是将项目的工作包拆分为可执行的进度活动[4，并能够被分配给相应的团队成员。这有助于更好地组织和安排项目工作，以实项目的目标和交付要求。定义活动具体工作内容参见表2-1。

表2-1定义活动阶段工作汇总表  

<table><tr><td>序号</td><td>工作名称</td><td>工作内容</td></tr><tr><td>1</td><td>回顾项目范围</td><td>回顾项目的范围说明书和工作分解结构（WBS）。了解项目的各个工作包 及其层次结构，确保对项目范围的理解准确。</td></tr><tr><td>2</td><td>识别可管理活动</td><td>活动应该是可以被分配、计划和跟踪的工作单元，具有明确的可交付成果 或结果。</td></tr><tr><td>3</td><td>划分活动层次结构</td><td>根据活动之间的逻辑关系，将活动组织成一个层次结构。这有助于更好地 理解和管理活动之间的依赖关系。</td></tr><tr><td>4</td><td>估算活动持续时长</td><td>基于专家判断、历史数据、以往项目经验等方法，结合PERT工具进行估算。</td></tr><tr><td>5</td><td>定义项目里程碑</td><td>识别项目中的里程碑点，标记项目的重要阶段或需要完成的重要事件。</td></tr><tr><td>6</td><td>活动清单</td><td>将定义的活动记录在活动清单中，并为每个活动记录相关的属性信息。这 样的文档可以作为进度管理的参考和依据。</td></tr></table>

项目进度计划是项目管理的核心内容，制定详细的项目进度计划是做好进度控制的基础[5]。一般情况下，项目都有严格的时间限制，想要对项目时间进行合理的监控和管理，在项目早期阶段，就需要明确项目的执行时间表、任务安排和关键路径。进度计划的制定涉及确定活动依赖关系、先后顺序和活动持续时间等项目管理活动。该阶段具体工作内容参见表2-2。

表2-2制定计划阶段工作汇总表  

<table><tr><td>序号</td><td>工作名称</td><td>工作内容</td></tr><tr><td>一</td><td>定义活动的先后 顺序</td><td>根据活动之间的逻辑关系，确定它们的先后顺序。常见的逻辑关系包括：开 始-完成（FS）、完成-完成（FF）、开始-开始（SS）和完成-开始（SF）等。通 过这些关系，确定活动之间的依赖性，确保它们按正确的顺序执行。</td></tr><tr><td>2</td><td>绘制项目网络图</td><td>使用项目管理软件或手动绘制项目网络图，基于活动和它们的先后顺序以 及持续时间定义活动路径。项目网络图清晰地展示了项目活动之间的关系 和时间安排，帮助团队理解项目的工期和关键路径。</td></tr><tr><td>3</td><td>识别关键路径</td><td>通过分析项目网络图，确定项目中的关键路径，即影响项目总工期的最长 路径。关键路径上的活动不能延误，否则将对整个项目的工期产生影响。 关键路径分析帮助项目团队识别出关键任务和潜在的风险点。</td></tr><tr><td>4</td><td>工期优化</td><td>工期优化是在基于网络计划所计算的工期未达到要求时的一种处理方式， 其目标是通过压缩计算工期，以达到既定的工期目标，或在特定的限制条 件下追求最短工期[。主要是在识别出关键路径的基础之上进行工期压 缩，压缩关键路径。</td></tr></table>

项目进度控制和保障是确保项目按时交付的关键要素，其重要性不容忽视。项目进度控制与保障不仅仅关系到项目的交付时间，更涉及到项目的质量、成本和利益相关者的满意度。通过合理的进度控制和保障，项目管理团队可以避免项目延期、资源浪费和不必要的压力，更高效的进行项目研发的工作，在保证项目质量的同时减少项目成本[7]。

项目进度控制与保障的三大关键措施：绩效管理、决策评审和角色分工。

（1）绩效管理是项目进度控制与保障的核心环节之一。通过设定明确的绩效指标和目标，项目团队能够更好地衡量项目进展，并根据实际情况做出相应的调整。定期的绩效评估和比较有助于识别项目中的问题和瓶颈，从而采取及时的措施来解决。

(2）决策评审是确保项目顺利推进的重要机制。通过定期召开决策评审会议，项目管理团队可以对项目的进展情况进行全面的审查和讨论。这有助于及早发现问题，避免项目延误，并确保项目目标与战略保持一致。

(3）明确的角色分工对于项目进度的控制与保障至关重要。明确的角色和责任有助于确保每个团队成员都知道自已的职责，避免资源浪费和任务重叠。合理的角色分工也能够提高项目管理的效率，使团队协作更加紧密。

# 2.1.3集成产品开发（IPD)

IPD是指集成产品开发（Integrated Product Development）的缩写，它是一种先进而成熟的产品开发模式，包括了一系列的理念和方法。IPD 的核心思想体现在以下七个方面：

（1）产品开发属于一种投资行为。一般来来说，企业更倾向于从技术的角度来思考产品和技术开发。而IPD提出了投资产品开发的理念，强调首先应该从投资的角度来思考产品和技术的开发[8]。这意味着企业需要考虑投资的风险和回报，以及产品或技术的市场潜力。通过这种方式，我们可以更加全面地评估产品或技术的可行性，并做出更明智的决策。通过进行投资决策评审，我们可以确保我们的投资决策是基于充分的信息和分析，从而降低投资风险，提高项目成功的概率[9]。

(2）以市场为导向的创新。IPD强调产品创新的关键在于以市场为基础。这表示在进行创新之前，必须进行彻底的市场分析，制定明确的产品和技术计划。只有在这些基础上进行创新，才能保障产品取得成功。为了达到此目标，并确保新产品符合公司的战略发展要求，需要展开一系列市场管理步骤，其中涵盖需求获取、需求评估、竞争评估、市场定位，以及产品和技术规划等环节。这些活动的目的是充分了解市场需求、竞争情况以及产品定位[I10]，从而确保新产品的立项研发能够符合公司的战略发展方向。

(3）采用基于平台的异步开发模式和重用策略。在开发和设计集成产品时，最佳做法是以一个共同的产品平台作为基础进行开发。可以按照最终产品、平台、模块/组件和关键技术进行分层、分时地异步开发[]。这样可以提高开发效率，减少重复工作，并且能够更好地保持产品之间的一致性。同时，还可以在开发过程中灵活地进行调整和改进，以满足市场需求和技术发展的变化。帮助团队更好地协同工作，提高产品的质量和竞争力。

在不牺牲差异性的前提下尽可能增加对公共模块/组件(CBB)的重用[12]。可以采取拆分功能、灵活配置和模块化开发等策略，以满足不同用户的需求，提高代码的重用性和可维护性。

(4）技术开发与产品开发相分离。在异步开发模式中，关键点在于将技术开发与产品开发分隔开来。具体来说，在进行产品开发之前，需要先进行技术开发，将可以被不同的团队或企业共享和试用的技术模块或平台开发出来，再应用于具体的产品或服务中[13]。这种技术转化的过程需要专业的团队来进行研发和实施，以确保技术的稳定性和可用性。

(5）跨部门协同。由于产品开发的复杂性以及部门之间存在协同障碍的问题，需要寻找一种更加高效和协同的方式来推动产品开发。一种可行的解决方案是引入跨部门团队，将不同部门的成员组成一个团队，共同负责产品的开发和推进。建立跨部门团队的主要目标是促进沟通、协调和决策的高效性[14]。通过跨部门的合作，我们可以充分利用不同部门的专业知识和资源，打破部门之间的壁垒，促进信息的流动和交流。

(6）结构化并行开发流程。产品开发可以被系统地规范和管理。为有效掌控庞大复杂的产品开发，企业需建立定义规范、层次清晰的开发流程。这个流程可以帮助企业更好地组织和协调各个环节，确保项目的顺利进行。设计结构化流程时，需要在非结构化和过于结构化之间寻求平衡，以确保产品开发在质量和效率方面取得理想成果，并且不会限制创新的发展。允许研发管理人员根据公司战略和项目需求对流程进行裁剪，保证流程能够适应实际应用环境[15]。为了在产品开发中提高效率和质量，应该在可能的情况下并行进行各项技术和职能活动，并尽早地纳入各领域的专业人员参与，以缩减开发周期并确保产品品质。

（7）产品线与能力线并重。随着企业产品范围不断扩展和新产品涌现，研发组织需要建立横向的产品线组织（即产品线研发组织，进一步可扩展为产品线经营组织）和纵向的能力线（资源部门）。强化产品线研发组织的市场导向、自主权和对产品开发项目的领导作用至关重要。同时，加强能力线的资源构建，提升各领域的专业素养。支持产品研发团队在整个流程中的协同合作，实现产品线的跨部门协作。最终达到企业产品竞争力提升以及市场响应快速的效果。

IPD框架可进一步简化理解为两种评审模式、三大业务流程、四大组织、五个主要业务决策评审点、六个开发阶段和七个技术决策评审点。

（1）两种评审模式包括业务决策评审模式和技术决策评审模式。

业务决策评审关注项目的商业目标和战略方向。在评审会议中，业务相关的利益相关者和决策者将对项目的业务需求、市场定位、产品规划、商业模式等进行评审。评审的目的是确保项目的业务决策与组织的战略目标一致，并且在商业上具有可行性和可持续性。业务决策评审还可以检查项目的收益模型、市场竞争力、目标用户群等方面，以验证项目的商业前景和可行性。

技术决策评审关注项目的技术实施和技术方案选择。在评审会议中，技术相关的团队成员和专家将对项目的技术方案、系统架构、技术选型、开发方法、代码质量等进行评审。评审的目的是确保项目的技术决策与项目的目标和要求相匹配，并且在技术上具有可行性和可实现性。技术决策评审还可以检查项目的可扩展性、安全性、可维护性等方面，以确保项目在技术上的稳定性和可持续性。

(2）三大业务流程包括市场管理流程、产品开发流程和技术开发流程。

市场管理流程涉及对市场需求和竞争环境进行分析，以确定产品的市场定位和市场策略。主要步骤包括市场调研、需求分析、目标市场的识别和定位等。在市场管理流程中，团队将制定营销计划、市场推广策略和销售计划，以确保产品在市场中获得竞争优势。

产品开发流程是IPD过程中的核心流程，它涵盖了从概念到最终产品的开发和交付的全过程。主要步骤包括产品规划、概念开发、需求分析、产品设计、原型制作等。团队将根据市场需求和用户反馈，逐步开发产品的功能和性能，并进行反复迭代和优化。

技术开发流程专注于产品的技术方面，包括技术选型、系统架构设计、软件开发、CBB管理等。在技术开发流程中，团队会根据产品需求和技术要求，进行技术研发和实施，以确保产品的可靠性、安全性和性能。这包括确定适合产品的技术方案，进行开发和集成工作，并进行相关的测试和验证。

（3）四大组织包括综合项目管理团队（IPMT）、项目管理团队（PMT）、产品开发团队（PDT）和技术开发团队（TDT）。

IPMT是负责整体项目管理和决策的团队，由高级管理人员、项目经理和项目相关的重要领域的代表组成。负责项目的整体规划、控制、监督和决策，协调各个团队的工作，处理高级问题和风险，与高层管理层沟通和汇报项目进展。以确保项目按时、按预算、按质量要求完成，达成项目目标。IPMT从市场投资的角度对项目进行考察，判断项目未来的盈利情况，实时中止市场前景差的项目，以保障公司资源能够投入到回报率高的项目上[16]。

PMT是负责项目计划、执行和控制的团队，由项目经理和项目各个领域的代表组成。负责制定详细的项目计划，监督任务的执行和进度，处理项目中的问题和变化。确保项目按计划进行，及时识别并解决问题，协调不同领域的工作。

PDT是负责产品开发和交付的团队。由工程师、设计师、开发人员、测试人员和项目经理等专业人员组成。负责根据产品规划和需求进行技术开发、系统设计、软硬件开发、集成测试和产品验证等活动，包括产品从概念到生命周期终止的整个生命周期的产品开发过程。

TDT是负责技术研发和技术创新的团队。这个团队通常由研发工程师、技术专家、科学家和创新团队组成。TDT负责进行技术调研、新技术的探索和实验、技术验证和技术路线图的制定，以支持产品的技术创新和不断改进。

(4）五个主要业务决策评审点包括立项评审（CharterDCP）、概念决策评审（CDCP）、计划决策评审（PDCP）、可获得性决策评审（ADCP）和生命周期终止决策评审（EDCP）。

Charter是 IPD 过程的起点，它确定了项目的目标、范围和时间表。Charter评审为后续决策评审提供了基础和指导，确保项目的目标与业务需求一致。

CDCP评审是项目概念阶段结束时进行的决策评审。由PDT团队正式向IPMT进行初始的项目计划和业务计划汇报，在这个评审中，将审查和评估产品的概念设计，包括产品概念、技术路线和方案等方面，IPMT团队基于PDT团队的汇报来决策项目是否继续，若概念评审无法通过，则终止项目，不再浪费资源[17]。

PDCP涉及制定详细的项目计划，包括工作分解结构（WBS）、资源分配、进度安排和风险管理策略等。PDCP评审确保项目有清晰的计划和组织，以便有效地进行产品开发。

可获得性决策评审是IPD过程中的一个重要里程碑评审点。在这个评审中，团队将审查和评估产品的可获得性和供应链管理，以确保产品在市场上的可用性和生产准备。ADCP评审涉及供应链管理、生产准备、质量控制和供应链风险管理等方面的考虑。

EDCP发生在产品开发的晚期阶段，评估产品的当前状态、市场需求、竞争情况和盈利能力等。EDCP评审决定是否终止产品的生命周期，例如产品不再具有竞争力或盈利能力。

（5）六个开发阶段包括概念阶段、计划阶段、开发阶段、验证阶段、发布阶段和生命周期阶段。

概念阶段是确定产品的概念和目标。这包括识别市场需求、确定产品特性和功能，定义产品包需求。在此阶段，重点是确定产品的商业可行性和技术可行性，并进行概念决策评审（CDCP）。

计划阶段是制定详细项目计划的阶段。团队会制定项目的工作分解结构（WBS）、资源分配、时间表和风险管理计划等。PDCP（计划决策评审）通常在此阶段进行，以确保项目有清晰的计划和组织。

开发阶段是实际进行产品开发的阶段。团队将根据概念阶段的需求和设计规范，进行产品的设计、编码、测试和集成。这个阶段涉及到软件开发、硬件开发或其他相关开发工作。

在验证阶段，团队对产品进行测试和验证，以确保其符合设计规范和要求。这包括功能测试、性能测试、安全测试等。团队将检查产品是否满足用户需求，并进行必要的修复和调整。

发布阶段是将产品推向市场的阶段。团队会准备产品的发布和营销计划，并进行市场推广和销售活动。在此阶段，团队将准备产品文档、培训材料，安装和配置产品，以便顺利交付给客户。

产品进入销售和交付环节后，直到产品最终终止销售和维护，统称为产品的生命周期阶段，包括产品的运营、维护、升级和退役等。

(6）七个技术决策评审点包括TR1评审点、TR2评审点、TR3评审点、TR4评审点、TR4A评审点、TR5评审点和TR6评审点。

TR1是在概念阶段对产品需求包进行的评审。主要关注产品需求的完整性，以确定产品的技术路线和市场定位是否符合产品需求规格。

TR2是指产品总体设计评审。在将产品需求规格映射至产品总体设计时，需特别注意其全面性。同时，确保产品总体设计能够涵盖并适用于各模块，如硬件和软件模块，以指导随后的IPD流程及其子流程的产品开发。通过TR2评审，能够找出产品总体设计的问题和限制，并评估相关风险。在评审过程中，还可以制定规避措施和应急计划，以降低风险的影响。

TR3是对各模块概要设计的评审。透过TR3评审，确保产品整体设计在各模块的概要设计中得到准确而全面的呈现。确保各模块的概要设计具备充分的指导功能，以促进随后的模块详细设计、产品开发计划以及业务计划的顺畅制定和优化。同时还需要关注基于共用模块的重用计划执行情况。

TR4是在各个子系统开发完成并经系统联调后，进行系统集成测试之前的评审。旨在保障产品总体设计在各模块中所分解的相关需求已在子系统详细设计中得到具体体现和实现。须对每个子系统进行详细设计审查和模块级测试结果的验证，换言之，每个子系统均需经过一次TR4评审。及时发现和解决子系统中存在的问题，确保整个系统的稳定性和可靠性。

TR4A评估产品技术方案的技术成熟度。确定产品是否达到了预期的技术水平，并且是否满足用户的需求和期望。评估的结果将为产品的后续开发和改进提供重要的参考依据。在评估过程中，需要充分考虑产品的可行性、可持续性和可扩展性等因素，以确保产品能够在市场上取得成功。同时，评估还需要考虑到竞争对手的产品和市场趋势，以便在产品设计和定位上做出合理的调整。

TR5是对产品整体的设计完成度和稳定性的评估，包括评估产品性能、可靠性、稳定性等。评估试制过程中产品是否充分达到预设的功能和性能标准，产品需求规格是否得到满足，并且功能和性能问题是否已被发现和解决。

TR6 指系统验证测试技术评审，对产品满足发布要求进行的评审，包括系统验证测试结果，外部测评及客户Beta测试结果的评审。

# 2.2文献综述

# 2.2.1研发管理相关研究

研发管理理论是关于组织如何有效管理和指导研发活动的一套原则、概念和方法体系。它涉及到组织内部和外部的资源调配、人员协作、技术创新、研发项目管理[18]等方面，以实现创新和研发活动的有效运作和成果产出。

国外研发管理的理论研究发展较早，处在领先地位[19]，往前可以追溯到20世纪初，大致可以划分为4个主要的发展阶段：早期阶段、技术导向阶段、组织创新阶段和整合性阶段。其中，整合性阶段是研发管理理论发展的高级阶段，该阶段强调将研发管理与其他管理学科融合，如技术战略创新与组织管理、项目组织与资源管控等。在这一阶段，研发管理更加关注跨职能协作和整体项目管理，强调整合各方资源、知识和利益相关者，以实现项目的协同和高效研发。

相比于国外，国内研发管理理论的发展相对较晚，且研究主要集中在国内实践相关的领域，多以企业为研究对象，关注企业内部创新和研发活动的管理实践。随着全球化和信息交流的加强，国内企业也在不断借鉴和吸收国外的经验和理论成果，发展和完善自身的研发管理体系，并且取得了一定研究成果。

以下是笔者针对本文的研究主题进行的相关文献研究。

20世纪末期，Robert提出了一种新的研发管理办法，他将项目研发活动进行阶段划分，并且在每个研发阶段中设置了一个决策评审点和研发质量控制入口，每个阶段的工作都经过跨职能设计，形成了一种新的研发管理体系——门径管理体系[20]。

21世纪初，美国PRTM公司在出版的《产品及生命周期优化法》（简称：PACE）[21]一书中提出，产品研发应该关注七个核心要素：阶段性决策评审、跨职能核心团队、结构化的研发流程、技术管理、开发工具与技术、产品战略规划以及管道管理[2²]，将企业的研发管理所涉及到的各方面内容进行了整合，达到了优化企业研发管理体系的效果[23]。

IBM是全球最先将PACE方法付诸实践的公司之一，IBM在实践过程结合自身情况对PACE方法加以改造，最终形成了IPD（集成产品开发）方法[24，并且在产品研发管理领域取得了很好效果。在此之后，全球多家高科技公司也陆续采用了IPD方法，都取得了不错的效果。研究数据表明，目前国外研发公司 $9 2 \%$ 以上都采用了基于IPD 架构的产品研发管理体系[25]。由此可见，IPD 方法在国外的应用已经非常普遍。

华为是国内最先开始实施IPD的公司之一，并在很短时间内就取得了长足进步。例如，基于IPD方法，华为在核心部件重用率方面得到了提升，缩短了新产品开发周期，利润也大幅增加[26]。基于华为的示范效应，国内掀起了IPD企业应用研究的热潮。丁丽娟（2019)进行了基于IPD的研发管理体系设计的研究探索，为云计算企业的研发管理体系建设提供了成功案例，并提出企业应该使用适于自身的研发管理流程和落地措施[27]。杨志凌（2020）基于对所在公司的大量调研，分析和诊断了所在公司存在的组织结构问题和新产品开发流程问题。他结合集成产品开发管理的核心思想，为该公司提出了富有针对性的改进方案，帮助公司提高了新产品的研发效率[28]。李耀（2020）通过对SGS、PACE、IPD、LPD这四种主流研发管理理论进行对比分析后，认为IPD的适应性更强，且管理范围更加全面，理论更加成熟，最终采用IPD理论作为W公司产品研发管理改进设计的基础，帮助公司解决了产品研发管理过程中存在的问题[29]。

# 2.2.2研发项目进度管理相关研究

项目进度管理的目标是确保项目按时交付可接受的成果。它包括对项目时间和进度的规划、监控和控制。主要研究内容有：制定进度计划，其中包括定义活动、确定活动顺序、估算活动持续时间；以及在项目计划实施过程中监控和控制进度。

在国外，项目进度管理是项目管理领域的核心议题之一，许多著名的学术机构和研究中心致力于对项目进度管理进行深入研究。研究者们通常采用系统性的方法，结合数学模型、案例研究和实证研究，探索如何更好地管理项目进度，以提高项目的成功率和效率。主要研究方向包括进度计划编制、进度控制、资源优化、进度风险管理、进度管理工具和技术等。1959 年由Morgan Walker 和 JamesKelley在Dupont公司首次提出的关键路径法[30]被广泛应用于大型工程项目、产品研发、事件规划等领域的进度管理,推动了项目管理实务的发展，是项目管理的重要工具之一。20世纪50年代末，为美国波利斯导弹计划开发的项目管理工具PERT,即程序评审与评估技术，PERT为大型复杂项目的网络进度管理提供了处理时间不确定性的方法与工具，推进了项目进度风险管理的发展[31]。

在国内，随着国内项目管理实践的不断发展，对项目进度管理的研究逐渐兴起。国内学者主要关注项目进度计划编制、进度控制与监控、资源调配与优化、进度风险管理等方面。研究者们往往结合国情和本土项目管理实践，探索适合中国企业和项目的进度管理策略和方法。韩鹏凯（2004）认为选择项目进度控制方法需要从项目的实践经验出发，根据具体的项目实施内容、实施阶段等进行[32]。杨爱华等（2008）指出，在项目进度管理中，项目里程碑即是项目管理阶段中设立的检查点和决策点，设立里程碑的目的就是为了更便利地在项目管理过程中进行检查和追踪项目的进展情况[33]。杜桂伏（2011）表示，项目进度控制是工程类项目管理的重中之重，为了达到项目进度目标，不仅需要重视进度控制的技术方案，还需要加强进度控制的组织管理[34]。姜欣廷（2021）提出项目在关键路径上的管理质量，不仅能够影响项目交付的及时性，同时也影响项目建设的质量[35]。

在当前市场经济环境下，企业需要在最短的时间内开发出符合消费者需求的产品，并在技术和质量上保持领先地位。因此，产品研发进度管理成为企业竞争力的关键。本研究旨在通过对文献的综述和分析，找出当前主题下的理论和方法，从而为企业的产品研发项目进度管理提供有力支持。

在与此相关的文献中，有几篇论文提供了关于产品研发项目进度管理的研究结果和方法。

首先，杜军在"HY公司产品研发项目进度管理改善研究”一文中通过5W（what,why,where,when,who）原因分析方法，分析了导致HY公司产品研发项目进度延误的原因，并找出了四个不足之处：任务估时不准确、项目物料资源计划不全、活动排序不合理、进度控制不足。接着，论文比较了不同的项目管理工具的优劣势，并提出适合HY公司产品研发的项目管理理论与方法，如三点估时法[36]、关键控制链法等。最后，通过项目进度管理的实施与应用，验证了这些理论方法在实际企业项目管理中的有效性。

其次，曾世华在“QS公司阿车研发项目的进度管理研究”一文中针对QS公司在阿根廷机车研发项目中遇到的问题，提出了优化项目里程碑、主要节点和工作分解结构、运用三点估算法和甘特图等方法，改进了进度计划的编制和监控方法[37]。通过这些措施，该公司取得了良好的应用效果，提高了研发项目的管理能力。

另外，贾锋歌在“QH公司产品研发项目进度管理方案优化研究”一文中研究了QH公司在新品科研进度管理中存在的问题，并提出了产品研发项目进度管理优化的方法。该研究综合运用多种管理理论和方法，构建了重度矩阵式的新品研发项目组织架构模式[38]，优化了项目进度计划和进度控制，从而提升了产品研发项目的管理能力。

还有两篇论文分别针对H公司和A公司的产品研发项目进度管理进行了研究。这些论文主要通过分析企业在新产品研发过程中的管理问题，运用项目管理理论和方法，提出了优化方案和方法，以实现新产品研发进度的科学规划和有效管理。

# 2.2.3IPD相关研究

随着科研项目的复杂性和规模增加，如何有效地管理研发项目成为一个重要的问题。IPD作为一种集成的产品开发管理模式，被广泛应用于管理研发项目。本研究的目的是通过综合分析相关文献，理解IPD对研发项目进度管理的影响，发现研究的不足，并提出改进的方向和方法。

陆波在“基于IPD流程的研发类项目管理信息化研究”一文中基于某项目的特点，根据IPD流程对各个阶段进行详细的分析，并建立了项目管理模型。文章的主要贡献是提出了一种基于IPD流程的研发类项目管理信息化框架[39]。

王文轩在“基于IPD的A公司金融卡芯片研发进度管理研究”一文中以A公司金融卡芯片研发项目为研究对象，通过分析现有研发项目管理中影响研发进度的原因，提出了A公司基于IPD的项目进度管理体系优化方案[40]。通过优化后的项目进度管理，A公司在计划执行和进度控制方面取得了明显的效果。

马飞等在“基于IPD的体系化研发管理平台研究”一文中讨论了新产品研发项目管理面临的困境，提出了基于IPD的研发管理体系实现平台的重要性。通过分析基于IPD的研发管理体系实现平台的产品全生命周期管理[41，探讨了该平台在企业研发管理中的作用。

韩冰在"基于产品设计的研发项目管理体系研究”一文中通过以燃烧器产品研发项目为例，研究了研发项目的进度计划制定和进度控制。通过明确项目目标、工期估计和解决进度问题[42]的措施等，优化了研发项目进度，加快了产品上市速度。

王光翔在“基于IPD的N公司新产品研发管理改进研究”一文中针对N公司的新产品研发管理存在的问题，运用IPD相关理论知识进行研究。通过分析N公司现行的研发管理实际，提出了改进研发管理的策略方案，并对改进方案的实施进行了阐述和评估，为N公司搭建了更加高效、完善的新产品研发管理体系[43]。

通过对以上论文的综述分析，可以得出以下结论：

基于IPD的研发项目管理模式在研发项目进度管理方面具有重要的影响。这些论文提出了一系列基于IPD的项目管理模型、体系化研发管理平台以及改进方案，通过优化研发流程、组织结构、进度控制等方面的管理，达到缩短研发时间、降低成本、提高产品质量的目标。然而，这些论文中还存在一些问题，例如对于IPD在不同领域和企业的适用性和效果还需要进一步研究。

综上所述，基于IPD的研发项目进度管理研究在实践中取得了一定的成果，但仍然存在一些不足之处。进一步的研究可以从不同行业、不同规模的企业中进行案例分析，深入探讨IPD的实施策略和效果评估，以更好地指导研发项目进度管理的实践。

# 2.3本章小结

本章首先介绍了本文研究所用到的重要基础概念：研发管理基础概念、项目进度管理基础概念和IPD基础概念。并对相关文献进行了综述研究，为接下来论文的研究提供理论与方法的支撑。

# 第三章A 公司大数据分析平台研发项目进度管理现状

本章将对A公司大数据分析平台研发项目进度管理现存的问题进行调研，基于调研数据分析提取项目进度管理的关键问题，并对进行深入的原因分析。

# 3.1大数据分析平台研发项目介绍

# 3.1.1项目背景与目标

随着全球经济进入数字化时代，企业数字化转型已经成为一种不可逆转的趋势[44]。而且已然成为推动经济社会发展的核心驱动力，并在"十四五"规划和2035远景目标中得到了重要的关注。在数字化发展的浪潮下，企业正迅速向前推进，致力于构建数字经济的新优势。通过应用数字化技术来重新塑造和转型自己的业务。企业数字化转型对企业带来的价值主要体现在以下八个方面：提高员工生产力、实现数据驱动的业务价值提升、优化客户体验、提供安全便捷的移动应用、推动业务流程自动化、开拓新的数字业务收益、实现企业流程的敏捷创新以及优化供应链。

大数据分析平台是一款针对企业和行业数字化转型需求设计的高性能、敏捷、智能化大数据平台。其平台设计以"数据驱动”为中心，通过不断构建核心数据能力，帮助企业实现“数据智能化、协同一体化、应用场景化、创新敏捷化、模式生态化”，进而支持企业以更快速、高效、低成本的方式进行业务创新，增强企业架构。其平台设计理念如图3-1所示。

![](images/f65253d5193cf8c8a44c80f1c17933e1b0c7187843fb8b1171c04c5edc41ffa3.jpg)  
图3-1平台设计理念图

# 3.1.2项目子系统简介

A公司大数据分析平台研发项目共涉及十七个子系统的研发工作，本节将对

这十七个子系统分别进行简要的介绍。

（1）开发框架子系统。在微服务和领域驱动设计（DDD）框架广泛应用的背景下，考虑到大数据分析平台产品线包含众多子系统，为了更好地实现各个子系统的开发框架的一致性，以及使功能开发更加敏捷，以确保各系统在研发过程中能够专注于核心功能逻辑。因此，将大数据分析平台的通用和公共部分抽象出来，并交由开发框架来实现。随后，各应用子系统可以根据业务功能的需求自由组合和使用这些通用部分。

(2）统一配置子系统。统一配置子系统是一款以"元数据，数据驱动"为核心理念的产品，其基本设计思想是实现元数据的多租户支持。它为用户提供了一系列功能，包括命名空间管理、模型配置、版本管理以及系统监控等。这些功能旨在增强企业间数据的隔离能力，并为企业数据的生产领域管理者提供了有力的支持。

(3）工作台子系统。大数据分析平台作为一个平台级产品，涵盖众多子系统，其业务流程相对复杂。为了提升其易用性，引入了工作台子系统的设计。该子系统的目标是为整个平台提供一统入口、统一用户认证、一致的界面风格以及导航功能，以简化平台的操作。通过工作台子系统，用户能够方便地查看各个子系统的消息通知、待办事项，并能够快速处理业务，从而提高工作效率。此外，工作台还根据不同用户层级和功能需求实施了权限隔离，为用户提供高效且安全的办公体验。

(4）流式计算引擎子系统。流式计算引擎是一款高性能、分布式的计算作业执行引擎，能够同时支持流式和批处理计算。它内置了丰富的算子，包括输入、转换、分析、统计、输出以及自定义算子，同时提供了图形化作业编排的功能。该引擎支持多种执行框架，包括Flink和Spark等。此外，它还兼容多种数据源类型，满足流式和批处理混合的业务需求。流式计算引擎具备全链路监控功能，包括任务执行监控、任务调度监控以及任务引擎监控。最重要的是，它支持可视化和图形化操作，用户只需通过简单的拖拽方式就可以轻松定义计算模型。

(5）数据采集子系统。数据采集子系统是一款高性能的数据采集工具，采用云边架构，并支持主流数据传输协议，可实现离线和实时数据采集。该工具具备高效多样的采集执行器，不仅能够采集关系型数据库数据，还能应对 $2 0 +$ 种异构数据源，包括文件、物联网、指标、HTTP等多种数据类型。它能够适应批量、实时、全量、增量以及周期性采集等多种业务场景，为企业提供了数据整合的能力。

(6）数据路由子系统。数据路由子系统是一个高性能、分布式的数据汇聚和分发工具，基于流批一体架构构建。它为用户提供了多种能力，包括数据同步、转发、输出、清洗、转换、质量校验、订阅等，同时支持多个路由之间的数据传输。该子系统的设计注重简洁、高效和级联特性，还支持能力插件的扩展，以及数据一致性校验和数据错误管理等功能。

(7）实时数仓引擎子系统。实时数仓引擎是一款高性能、低延时的数仓计算引擎，旨在为用户提供广泛的实时数据处理功能，包括实时数据处理、查询、分析以及预测等。这一功能集合有助于企业更敏捷地感知和应对数据，为企业管理者提供有力的决策支持。实时数仓引擎的独特之处在于它降低了传统数仓建设的难度，实现了数仓的自动化和智能化构建。它整合了数据采集、汇总和存储功能，同时提供了实时采集、查询、分析、预测以及实时存储等多方面的实时处理能力。这使得它能够管理多种数仓模型，包括宽表模型、Cube模型、树结构模型和图模型等。此外，它还支持数据事务和数据一致性检查，确保了数据的准确性和完整性。

(8）统一查询引擎子系统。统一查询引擎是一款高性能、低延时的数据查询引擎，为用户提供海量数据的实时查询、离线查询、事务处理等功能，支持基于物理模型和逻辑模型两种模型的数据查询计算操作，能够加强企业对数据的感知能力，为企业经营、生产领域管理者提供决策支持。

(9）元数据管理子系统。元数据管理子系统支持从多种数据源中提取元数据，并提供血缘分析、影响分析等功能。能够帮助企业快速整理数据资产，深入了解数据的源头和变化过程，为数据标准的建设以及数据质量的提升提供能力支持。(10）数据模型子系统。数据模型子系统是一款图形化建模工具，支持多种模型的构建和管理，包括物理模型、逻辑模型、分析模型、业务模型等。它为数据标准、数据质量、数据采集、数据路由、实时数仓、数据服务以及数据可视化等提供了强大的数据模型支持。数据模型的创建可以通过手动方式完成，也可以通过拉取元数据和数据标准进行自动生成。这一子系统的功能旨在帮助用户更有效地构建和管理数据模型，以满足各种数据需求。

（11）数据标准子系统。数据标准子系统建立了一个完整的数据标准体系，实现了数据的标准化，包括一致的数据定义、数据分类、记录格式、转换和编码等方面。通过确立规范的数据标准，确保数据的一致性，促进底层数据的顺畅交流，并提高数据的可用性，后续数据管理提供坚实的标准基础。

(12）数据质量子系统。数据质量子系统是一种基于规则的工具，专注于对大数据存储和实时数据流进行质量检验。该子系统具备多维度配置数据质量规则的能力，包括完整性、准确性、有效性、一致性、唯一性、及时性、安全等。通过有机整合质量评估、质量审计、质量修复以及质量报告等流程，形成一个闭环的数据质量管理体系，全面提升了数据的质量水平。

（13）数据服务子系统。数据服务子系统采用微服务架构，以敏捷化和低代码为特点，旨在提供数据API的生成和全生命周期管理工具。它还具备数据API的发布、数据脱敏、访问控制、以及流量控制等功能。该子系统专注于数据API的查询逻辑，而不涉及运行环境等基础设施问题。它支持弹性扩展、动态扩缩容以及自动化API创建等能力，从而满足了多样化的数据服务需求。

(14）数据资产子系统。数据资产子系统是一款对数据资产进行全生命周期管理的工具，支持定义、盘点、规划无序的数据资源和数据产品。为平台提供统一的数据资产门户，帮助数据使用者快速了解和获取业务需要的高质量数据资产，充分释放数据在业务系统中的价值。

(15）主数据管理子系统。主数据管理子系统是一个综合性主数据管理平台，提供了主数据的全生命周期管理，包括采集、申请、变更、校验、审批、生效、失效、分发等功能。它的目标是实现主数据的统一标准、规范流程、及时分发和高效共享，确保信息系统之间的互联互通，全面提升企业的数据治理和数据服务水平。通过这一平台，业务可以获得全面、可靠、及时和准确的主数据服务支持。

(16）指标管理子系统。指标管理子系统涵盖了指标的定义、建模和固化等环节，旨在实现对指标的统一管理。它对企业建设指标体系、优化指标数据流程以及有效实施指标管理提供了有力支持。通过将分散在各个系统中的各类指标集中整合，并按照统一标准进行规范，该工具清晰地呈现用户整体指标概览以及各指标的统计方法、数据来源、计算标准等细节，使整个指标体系一目了然。这有助于规范企业对指标的应用，统一指标定义，便于未来的指标共享和应用，同时避免了多方操作引发的指标混乱和数据不一致等问题。

(17)数据安全子系统。数据安全子系统，以数据为核心，致力于构建综合的安全管理与防护体系，覆盖数据的整个生命周期。该子系统专注于数据风险的预防与合规监管，根据不同的业务场景和数据生命周期的各个阶段，基于数据的发现和分类分级，旨在实现数据流转的监控和风险评估。通过综合应用多种数据安全技术，包括数据访问控制、数据存储保护和用户身份验证等，该子系统构建了一个平台化的数据安全防护体系，为数据安全提供了可靠的保障。数据安全子系统基于A公司现有的产品生态，强化了数据安全管理能力。同时，它向外部提供安全SDK，供第三方应用程序调用，以实现全局统一的分类分级、数据脱敏、数据加解密、安全审计等功能。这一综合性的安全解决方案有助于确保数据在各个阶段的生命周期内得到充分的保护和管理。

# 3.1.3项目特点

该项目的技术开发难度较高，项目涉及的大数据技术组件繁多，几乎覆盖了整个大数据生态技术环境，例如数据存储方面有键值数据库Redis、列式数据库

HBase、文档数据库MongoDB、图数据库Neo4j、常用关系数据库、对象存储MINIO、时序数据库InfluxDB、分布式文件存储HDFS、常用消息中间件、常用分布式数据仓库（Hive、ClickHouse）等，数据计算方面有离线计算（MapReduce、Spark）、流式计算（Flink、SparkstructuredStreaming）、资源调度引擎（Yarn、K8s）、查询组件（SparkSQL、Flink SQL、Elasticsearch）等，任务调度方面有Oozie、Airflow等，运维方面有Ganglia、Grafana、Ambari、docker、K8s等。这些组件是大数据分析平台研发所涉及到组件中的一部分，本文中不做完全的列举。繁多的技术组件为大数据分析平台的技术管理提出了更高的要求，同时也给项目进度增添了很多不确定因素。

大数据分析平台研发项目的技术方案开发比较困难。本项目的研发工作包含了很多技术解决方案的攻坚工作，比如说数据实时采集技术方案、高性能离线采集技术方案、多源异构数据源的元数据采集技术方案（实时采集、离线全量采集、离线增量采集）、高并发服务技术方案、分布式事务技术方案、系统高可用技术方案、插件化技术方案、数据血缘分析和影响分析技术方案等等，这些技术方案的攻坚工作是项目进度能否按计划推进的重要影响因素，甚至于决定了项目能否按时保质的完成。

大数据分析平台研发项目的技术架构具有多样性的特点。整个设计需要使用到很多先进的架构思想，项目人员对于这些架构思想了理解，对于保证产品迭代方向、团队人员统一思想、项目进度正常推进等方面有着非常重要的影响。这些产品设计架构主要包括湖仓一体架构、存算分离架构、低代码架构、云原生架构、流批一体架构、微服务架构、私有元架构、混合云架构等等。

大数据分析平台中的“大数据”具有“大量化”、“快速化”、“多样化”的属性特征。首先，中国社会正处于数据爆炸的时代，我国数据量年均增长速度超过 $50 \%$ ,这里的大量化指的就是是数据量大，基于此，大数据分析平台设计提供了对于海量数据的高效存储、快速计算能力。其次，大数据的处理速度至关重要，在许多业务场景中，数据的生成到消耗的时间窗口非常短暂，可供决策的时间也十分有限，这与传统的数据挖掘技术存在显著区别。最后，大数据分析平台面临多种数据形式，包含结构化和非结构化数据。其中涵盖文本、图像、视频、音频、日志、时序、对象等数据类型。这些数据呈现多样性，存储格式方面也极其丰富，包括CSV、JSON、XML、Parquet等多种不同形式。

大数据分析平台研发项目的子系统间存在复杂的依赖关系。复杂的子系统依赖关系导致整体项目的开发任务依赖也会非常复杂，这就使项目开发任务的管理变的非常困难。可能会导致项目开发任务频繁阻断的情况出现，想要解决这个问题，不仅需要规范的、具有前瞻性的研发规划，还需要标准化研发管理体系的支

撑。子系统间的关系如图3-2所示。

![](images/880f9fa7f336ccfb91dcb3d76c9011c8eddbdb5a0f0629b989c605993b9218b6.jpg)  
图3-2子系统关系示意图

大数据分析平台的研发涉及多个部门的协同合作。主要包括产品规划部门、后端研发部门、前端研发部门、用户体验部门、质量管理部门、数据科学部门、市场交付部门、安全部门、测试部门、运维部门、人事部门、产品营销部门等等。

大数据分析平台研发项目的研发周期相对较短，项目时间非常紧张。随着大数据技术的快速发展，大数据产品层出不穷，迭代速度也逐渐加快，一个产品版本的售卖窗口期被不断压缩，为了能够将大数据分析平台尽快推出市场售卖，以期能够快速抢占市场份额，大数据分析平台的项目进度规划非常紧凑，首个版本的研发周期仅为14个月。然而在此之前，A公司在大数据类型的研发项目实施中，一个仅包含四个子系统的产品想要迭代一个小版本，也要经历三个月的研发才能完成交付，可想而知大数据分析平台项目的研发时间非常紧张，想要在如此短的时间内达成项目目标，需要对公司项目进度管理进行系统化改进。

# 3.2进度管理问题调研

鉴于A公司以往研发项目多次出现项目延期交付的原因，因此本次调研的焦点集中在项目进度管理领域，通过调研收集影响A公司研发项目进度的具体问题，并从这些具体问题中识别提炼出关键问题，为更有针对性的改善A公司

研发项目进度管理问题提供事实依据。

# 3.2.1调研对象

本次调研采用项目资料调查、负责人访谈、相关部门访谈和专项讨论会的方式进行。调研对象涉及项目管理部门、产品部门、技术研发部门、质量管理部门、销售部门、市场管理部门、交付与服务部门等。调研范围覆盖了项目管理、产品设计、组织结构、研发流程、质量管理、产品需求管理、产品销售与营销和产品文档管理等方面。另外，笔者针对本次调研制定了详细的时间计划，具体内容见表3-1所示。

表3-1调研计划表  

<table><tr><td>序号</td><td>时间</td><td>调研部门</td><td>调研角色</td></tr><tr><td rowspan="3">1</td><td>2022.7.13 10:00-11:00</td><td>项目管理部门</td><td>部门经理</td></tr><tr><td>2022.7.13 11:00-12:00</td><td>项目管理部门</td><td>交付项目经理</td></tr><tr><td>2022.7.13 14:00-15:00</td><td>项目管理部门</td><td>公司内部产品研发项目经理</td></tr><tr><td rowspan="4">2</td><td>2022.7.14 10:00-11:00</td><td>产品规划部</td><td>部门经理</td></tr><tr><td>2022.7.14 11:00-12:00</td><td>产品规划部</td><td>资深产品经理</td></tr><tr><td>2022.7.14 14:00-15:00</td><td>产品规划部</td><td>高级产品经理</td></tr><tr><td>2022.7.14 15:00-16:00</td><td>产品规划部</td><td>初级产品经理</td></tr><tr><td rowspan="4">3</td><td>2022.7.15 11:00-12:00</td><td>交付服务部</td><td>部门经理</td></tr><tr><td>2022.7.15 14:00-15:00</td><td>交付服务部</td><td>项目交付经理</td></tr><tr><td>2022.7.15 15:00-16:00</td><td>交付服务部</td><td>技术实施经理</td></tr><tr><td>2022.7.15 16:00-17:00</td><td>交付服务部</td><td>产品经理</td></tr><tr><td rowspan="6">4</td><td>2022.7.1811:00-12:00</td><td>技术研发部</td><td>部门总监</td></tr><tr><td>2022.7.18 14:00-15:00</td><td>技术研发部</td><td>技术经理</td></tr><tr><td>2022.7.18 15:00-16:00</td><td>技术研发部</td><td>资深研发工程师</td></tr><tr><td>2022.7.18 16:00-17:00</td><td>技术研发部</td><td>高级研发工程</td></tr><tr><td></td><td></td><td></td></tr><tr><td>2022.7.18 17:00-18:00</td><td>技术研发部</td><td>初级研发工程是</td></tr></table>

表3-1调研计划表（续上表）  

<table><tr><td rowspan="7">5</td><td>2022.7.19 10:00-11:00</td><td>质量管理部</td><td>部门经理</td></tr><tr><td>2022.7.19 11:00-12:00</td><td>质量管理部</td><td>资深测试工程师</td></tr><tr><td>2022.7.1913:30-14:30</td><td>质量管理部</td><td>高级测试工程师</td></tr><tr><td>2022.7.19 14:30-15:30</td><td>质量管理部</td><td>初级测试工程师</td></tr><tr><td>2022.7.19 15:30-16:30</td><td>质量管理部</td><td>安全测试工程师</td></tr><tr><td>2022.7.19 16:30-17:30</td><td>质量管理部</td><td>文档管理员</td></tr><tr><td>2022.7.2010:00-11:00</td><td>销售部门</td><td>部门经理</td></tr><tr><td rowspan="2">6</td><td>2022.7.20 11:00-12:00</td><td>销售部门</td><td>销售专员</td></tr><tr><td>2022.7.2014:00-15:30</td><td>市场部门</td><td>部门经理</td></tr><tr><td>7 8</td><td>2022.7.2016:00-17:30</td><td>财务部</td><td>部门经理</td></tr></table>

在理想状态下，整个调研过程计划花费六个工作日完成，但由于调研工作周期较长，涉及相关方比较多，且大多是公司业务运转环节的核心人员，会频繁的出现访谈安排与相关人员工作出现冲突的情况，因此整个调研工作实际持续了将近三周时间。从2022年7月13号到8月1号期间，总共调研了公司的8个部门，调研总人数超过35人，专项讨论会举行场次7场，输出调研记录11份。

# 3.2.2数据分析

经过汇总整理，整个调研阶段共收集了131个问题，其中与项目进度密切相关的问题有93个，主要涉及沟通协作、责任归属、研发流程、产品质量等方面。具体见表3-2所示。

表3-2问题提及次数统计表  

<table><tr><td>序号</td><td>问题分类</td><td>提及次数</td></tr><tr><td>1</td><td>沟通协作</td><td>31</td></tr><tr><td>2</td><td>责任归属</td><td>29</td></tr><tr><td>3</td><td>研发流程</td><td>29</td></tr><tr><td>4</td><td>产品质量</td><td>26</td></tr><tr><td>5</td><td>风险管控</td><td>20</td></tr><tr><td>6</td><td>资源浪费</td><td>17</td></tr><tr><td>7</td><td>产品规划</td><td>17</td></tr><tr><td>8</td><td>技术规划</td><td>13</td></tr></table>

通过分析问题相互间的关系与影响，结合问题提及次数，从收集的问题中进

一步提取了优先级较高的影响A公司研发项目进度的8个关键问题，详情见表3-3所示。

表3-3问题优先级划分表  

<table><tr><td>序号</td><td>问题描述</td><td>优先级</td></tr><tr><td>1</td><td>跨部门沟通障碍</td><td>高</td></tr><tr><td>2</td><td>项目相关方利益不一致</td><td>高</td></tr><tr><td>3</td><td>权责不清</td><td>高</td></tr><tr><td>4</td><td>重复造轮子</td><td>高</td></tr><tr><td>5</td><td>缺乏市场调研</td><td>高</td></tr><tr><td>6</td><td>缺乏技术规划</td><td>高</td></tr><tr><td>7</td><td>缺乏有效的决策评审设计</td><td>高</td></tr><tr><td>8</td><td>缺乏系统性的阶段管理设计</td><td>高</td></tr></table>

# 3.3现存问题与成因分析

在前文研究的基础之上，论文结合IPD和研发管理的理论基础以及相关研究，将影响A公司项目进度的关键问题分别归属到IPD的组织结构和研发管理流程两个领域，具体的领域划分参见表3-4。

表3-4问题领域划分表  

<table><tr><td>问题描述</td><td>问题领域</td></tr><tr><td>跨部门沟通障碍</td><td>组织结构</td></tr><tr><td>项目相关方利益不一致</td><td>组织结构</td></tr><tr><td>权责不清</td><td>组织结构</td></tr><tr><td>重复造轮子</td><td>组织结构</td></tr><tr><td>缺乏市场调研</td><td>研发管理流程</td></tr><tr><td>缺乏技术规划</td><td>研发管理流程</td></tr><tr><td>缺乏有效的决策评审点设计</td><td>研发管理流程</td></tr><tr><td>缺乏系统性流程阶段管理划分</td><td>研发管理流程</td></tr></table>

接下来，本节将针对每一个问题进行深入的分析，研究问题的影响范围以及形成原因。

# 3.3.1组织结构问题

（1）跨部门沟通障碍。在A公司中，项目的各阶段的工作通常由不同部门负责，例如设计、开发、测试和市场推广等。一个项目的实施需要多个部门合作完成，然而，这些部门之间的沟通并不总是顺畅的。在项目研发过程中，跨部门沟通存在障碍会带来以下不利影响。首先，跨部门沟通障碍会导致信息滞后。不同部门间信息流通存在时滞，这使得项目团队未能迅速获得准确及时的情报。结果，决策陷入延误，项目进展难以把握。然后，跨部门沟通障碍会导致问题忽视。由于部门之间沟通困难，造成问题或风险被忽视。一个部门没有意识到另一个部门遇到的问题，从而导致问题在项目实施中被放大。最后，跨部门沟通障碍会导致决策困难。跨部门沟通困难会导致项目决策变得困难。如果不同部门之间无法有效共享信息和意见，项目经理会面临难以做出正确决策的问题。

例如，在项目研发过程中，产品经理、开发工程师和测试工程师三方之间的沟通是非常频繁的，三方的沟通质量很大程度决定了产品的研发效率和质量。理论上来说，产品经理在完成产品设计之后，输出产品的设计文档，开发部门根据产品设计文档进行代码开发，测试部门根据产品设计文档进行测试方案设计和测试用例编写。但从实际情况来说，项目进度总是紧张的，项目研发不会等待产品经理给出详实的产品设计文档之后才启动研发和测试工作，一般会采取边开发边补充的方式，不断完善设计文档，因此开发工程师的工作效率与质量以及测试工程师的工作效率与质量很大程度上依赖于同产品经理的沟通输出。而如果三方的沟通存在障碍，就会对整个项目的推进带来不利影响。

导致问题存在的根本原因也是显而易见的，主要包括以下几个方面：跨部门合作意味着涉及不同领域和职能的人员需要共同合作。然而，不同部门之间的专业术语和工作流程常常存在差异，导致沟通信息不对称。这种情况下，可能会出现误解和偏差，进而影响项目团队对项目目标和任务的共识，甚至产生冲突。不同部门之间有不同的团队文化和工作习惯。文化差异会导致沟通风格的不匹配，增加信息传递的难度。同时，缺乏相互信任也是一个问题。如果团队成员不相信其他部门的承诺和能力，就会出现信息保留和互相指责的情况，从而影响合作和决策的效率。跨部门项目往往涉及多个职能领域，需要协调不同部门的资源和优先级。在资源分配、时间安排和优先级制定方面，容易产生拖延、冲突和不必要的浪费。

（2）项目相关方利益不一致。A公司大数据分析平台研发项目中涉及到多个部门，从显性的角度来看是典型的合作关系，但从隐性的角度来看，各个部门之间的目标和利益并不完全相同，甚至存在冲突，这牵涉到公司的资源分配、职工的晋升发展和绩效考核等问题。在项目研发过程中，项目相关方利益不一致的情况会带来以下不利影响：

利益不一致会引起目标冲突。项目的目标通常是为了实现组织的战略目标，而员工所在的部门往往会优先考虑本部门的利益，例如部门绩效，如果部门绩效目标与项目绩效目标方向不一致，就会导致项目目标与部门利益之间出现冲突。利益不一致会引起资源争夺。在资源有限的情况下，不同项目和部门之间会争夺有限的资源[45]，这会导致项目相关方合作变的困难，影响项目进度和质量。

利益不一致会起因竞争性优先级。项目团队中的员工通常情况下会将自身部门的工作优先考虑，在这种情况下，对项目的投入就会受到影响，导致项目进度延误。

导致项目相关方利益不一致的原因有很多。不同相关方有不同的资源需求和优先级，导致项目管理团队难以平衡资源分配。例如，财务部门可能更关注成本控制，而市场部门可能更关注项目的快速推出，这会导致资源的分配冲突。不同相关方对项目的进度有不同的期望。例如，销售部门可能希望项目尽快完成以实现预期收益，而开发团队可能需要更多时间来保证产品质量。这会导致项目进度的冲突和压力。不同的相关方对项目风险的认知和容忍度不同。一些利益相关方可能愿意承担更多的风险，而另一些可能更加谨慎。这会影响项目风险管理和决策。项目成功的评估标准因利益相关方而异。

（3）权责不清。在A公司的项目研发过程中，存在权责不清的问题。成员之间关于任务分工、决策权限以及责任范围的界定不明确，边界不清楚。在项目研发过程中，项目团队权责不清的问题会带来很多不利的影响。例如：质量问题，缺乏负责人来监督代码审查、测试和验证过程，导致质量问题的出现。没有人监督质量控制流程，导致缺陷和错误的增加。决策困难，不确定谁有权做出特定决策，导致决策延迟或不一致。责任逃避，若责任不明确，会造成任务下发困难，甚至成员会试图逃避责任或将错误归咎于其他人。资源浪费，成员在不清楚自己职责的情况下进行工作，会浪费时间和资源在不必要的任务上。

在A公司石油客户的项目中，产品交付服务部门为客户提供产品版本升级服务，将产品版本升级到最新版本。产品交付服务部门的两位同事在经历了三天的努力之后完成了客户环境升级，但新版本在客户生产环境中出现质量问题，并且问题非常明显的在测试环境完成了复现，最终无奈又进行了客户环境的回滚，延误了项目交付时间。事情发生后，客户非常不满意，投诉至公司领导层。公司也最终给出响应，将最大责任归到了写出bug的研发工程师身上，虽然他只是个应届生。这个事情有很多问题值得反思，为什么带有严重缺陷的产品能够完成产品版本发布流程？为什么在问题出现后，责任能够穿透砸到一个初级工程师的头上？研发管理人员和测试人员为什么没有及时发现问题，并将问题扼杀在研发阶段？而且问题出现后，责任归属为什么没有按照制度或者流程规范作为依据，确定责任归属？最后却成了谁写的bug谁负责。

究其原因，项目在实施过程中，缺乏明确的项目角色定义。项目团队成员的角色和职责没有得到明确定义，成员不完全清楚自己在项目中扮演的角色以及应该承担的职责范围。团队组织结构过于复杂。在A公司大数据平台类项目中，项目团队由来自不同部门和团队的人员组成。导致跨部门协调和权责划分变得复杂，进而导致团队权责不清的问题。

（4）重复造轮子。公司在项目研发中缺乏通用化、标准化、模块化设计，缺乏对共用构建模块（CBB）的规划、开发、应用及维护，那些本可以在不同产品、系统之间共用的产品能力和技术能力在不同产品和系统中被重复研发，增加了大量的重复劳动。这种情况会导致项目资源浪费、项目进度延误、项目质量无法保证、技术债务累计等问题。当前，A公司的CBB能力建设就存在很多不足。首先，缺乏通用的业务开发框架。用户权限体系和数据安全体系建设混乱，不同的产品都有自己设计方案，导致在客户环境无法集成兼容，只能重新开发一套。其次，缺乏CBB组件的统一管理。公司没有建立统一管理的组件库，各个团队在研发设计阶段并不清楚公司都有哪些CBB能力积累，也没有可查询或者咨询的入口。最后，缺乏公司技术路线图规划。各部门产品的技术框架五花八门，版本选择各种各样，给CBB能力提取造成巨大挑战等等。

归根到底，公司缺少一个技术团队，统一负责CBB 组件库的建设与管理，规范各产品线的技术要求，为业务开发团队提供统一的技术规范、成熟的技术路线图和共享的CBB组件工具。

# 3.3.2研发管理流程问题

（1）缺乏市场调研。A公司几乎所有的项目都存在缺乏市场调研的问题，且该问题已经对项目进度管理产生了严重影响。首先，缺乏市场调研导致项目方向不明确。在没有充分了解市场需求和竞争状况的情况下，公司研发团队产生了误导性的假设，导致项目方向不明确。这也导致团队在研发过程中频繁调整方向，浪费大量的时间和资源。其次，缺乏市场调研导致产品定位出现错误。公司研发团队在没有进行充分的市场调研情况下，产生了错误的产品定位，导致产品无法满足实际用户的需求，最终导致产品在研发完成之后，在市场上得不到认可，在多个项目竞标中丢标。此外，缺乏市场调研导致项目进度不稳定。市场调研可以帮助研发团队提前预测市场变化和趋势，从而做出相应的调整和应对措施。A公司由于在研发过程中缺乏市场调研，研发团队在项目中会遇到意想不到的市场挑战，导致项目进度的不稳定和延误。

出现这些问题的根本原因是A公司在产品研发的整个流程中，没有以产品研发为目标的市场管理环节。A公司的市场管理部门只服务于产品销售，并不参与产品研发。这就导致在产品设计和开发中，严重依赖产品经理对市场的把握，如果产品经理忽略或者没有获取到足够的市场信息，研发团队就无法准确把握用户的需求和市场趋势，也就无法研发出具有市场竞争力的产品。

（2）缺乏技术规划。随着科技的迅猛发展，新产品不断涌现，企业为了保持竞争力和创新能力，不得不加大新产品研发的投入。然而，在新产品研发过程中，缺乏充分的技术规划往往会对项目进度管理带来负面影响。A公司也面临着这样的问题，为公司研发项目的进度管理带来了很多不利影响。首先，缺乏技术规划导致项目进度延误。在公司产品研发过程中，技术问题的解决往往涉及多个环节，在没有提前规划好技术路线的情况下，多数研发团队会陷入盲目的试错和摸索中，浪费了大量时间和资源，导致项目进度延误。其次，缺乏技术规划导致项目风险管理不足。缺乏技术规划的项目对于风险的存在和可能性缺乏充分的认识，因而在风险发生时无法迅速应对，进而影响了项目进度和质量。再次，缺乏技术规划导致产品质量得不到保障。技术规划不足可能导致研发过程中忽视关键的技术挑战和质量控制，没有明确的技术方向，团队会忽略细节，从而导致产品的质量问题和漏洞。最后，缺乏技术规划导致创新受限，技术规划有助于明确项目的技术愿景和创新目标。缺乏技术规划则会使团队陷入短期目标的追逐，而忽视了长期的技术创新和发展。

根据对公司以往项目的调研分析，可以得出，导致公司项目缺乏技术规划的原因主要包括时间压力、缺少人才储备和项目管理层的认知不足。

在竞争激烈的市场中，项目团队常常受到时间紧迫的压力。为了尽快推出产品，他们会忽略技术规划的重要性，导致后续问题的积累。

在一些新兴领域，缺乏成熟的技术方案和标准，相关研究人才储备不足。在这种情况下，团队难以制定详细的技术规划，比如人工智能领域的大模型。

项目管理层对技术规划的重要性认识不足，忽视了制定和执行这些规划。他们更关注短期的成果，而不是长远的技术发展。

（3）缺乏有效的决策评审机制。一个典型的案例是A公司的石油大数据产品研发项目，在项目进行过程中，由于缺乏有效的决策评审设计，项目进度管理出现了明显的问题。第一，项目进展缓慢。团队内部在项目的不同阶段都没有设计何时、如何进行决策评审，导致多项任务无法顺利衔接，项目进展缓慢。第二，项目决策和调整不及时。由于缺乏评审，项目的进展缺乏透明度，上层管理层无法准确了解项目的实际情况，难以做出及时的决策和调整。最终，这种缺乏决策评审的情况使得项目在一次次调整延误中失去了宝贵的市场机会。

针对这个问题，笔者进行了深入分析后认为，导致问题存在的原因主要有以下三个方面：首先，A公司的研发管理流程阶段划分比较粗，在过于简单的流程之中很难将有效的决策评审设计加入进去。其次，A公司针对先进的研发管理流程研究不足，缺乏相关人才储备，没能及时发现并推动问题的解决。最后，研发管理流程的改进，牵涉面较广，不仅涉及公司研发组织结构调整，而且设计多个项目研发管理相关软件系统的改进升级，革新阻碍较大。

（4）缺乏系统性的阶段管理。A公司的研发管理流程仅简单的分为产品设计、产品开发和产品测试阶段，而且各个阶段应该达到的阶段目标并没有得到充分明确的定义。这会带来很多不利影响。首先，缺少系统性的阶段管理设计会导致公司项目研发流程出现混乱。例如，在一个阶段的工作还没有完成的情况下，就开始了下一个阶段，导致任务交叉和信息传递不畅。这种不规范的流程会导致团队无法高效合作，影响项目的整体进度和质量。其次，公司项目的质量和成本会受到影响。缺乏系统性的阶段管理设计意味着团队无法及时发现和解决问题，这会导致问题在后期被放大，进而影响项目的质量。此外，由于流程阶段设计不清晰，项目的成本控制也可能受到困难。没有明确的阶段划分和评估，难以准确预测项目在各阶段所需的资源和预算，从而可能造成资金浪费和不必要的开支。相比于产品研发，公司更重视项目交付，公司对外售卖的产品多数是从交付项目中孵化而来，而项目交付是以客户为主，项目管理流程更需要灵活性，缺乏明确的指导和标准，从而使得项目阶段管理变得模糊不清。

# 3.4本章小结

本章首先对大数据分析平台研发项目进行了简要介绍，然后通过项目调研的方式提炼出了A公司项目进度管理的关键问题，并对其进行了深入分析，包括问题的成因以及对项目进度造成的影响，为后续章节提出针对性改进方案提供了事实依据。

# 第四章A 公司大数据分析平台研发项目进度管理优化策略

本章将针对大数据分析平台研发项目进度管理问题进行具体改进方案的研究。

# 4.1 IPD 方法的可行性论证

随着企业竞争日益激烈和技术不断进步，企业研发项目管理正面临着前所未有的挑战。在这一背景下，集成产品开发（IPD）方法为企业研发项目管理提供了新的思路和方法，因而逐渐受到关注。

IPD方法在实践中的成熟度表现为多学科协同、全生命周期管理和信息技术应用的集成。这种方法能够有效应对项目的复杂性，促进团队合作，降低风险，并提升项目研发的质量和效率。同时，IPD方法的理论先进性体现在其强调跨领域合作、打破学科壁垒以及关注产品全生命周期等方面，有助于企业在快速变化的市场环境中保持竞争力。IPD方法通过促进团队合作、明确项目目标、优化资源利用、强化市场导向、确保技术规划、提供决策支持以及规范阶段管理等方面，有效地应对了现阶段企业研发项目管理中的诸多挑战。结合A公司大数据分析平台研发项目的现存的进度管理问题和IPD方法特点两个方面，本文进行了进一步的分析，具体分析见表4-1所示。

表4-1IPD方法适用性分析  

<table><tr><td>序号</td><td>分类</td><td>关键问题</td><td>适用性分析</td></tr><tr><td>1</td><td>组织结构</td><td>跨部门沟 通障碍</td><td>IPD方法强调不同部门的合作与沟通，将各专业领域的知识和经验融 入项目，促进了跨部门间信息共享和交流。通过明确的沟通渠道和协 作机制，IPD能够减少信息滞后、问题忽视和决策困难的问题，促进 高效的团队合作。</td></tr><tr><td>2</td><td>组织结构</td><td>项目相关 方利益不 一致</td><td>IPD方法鼓励项目相关方合作，突出共同利益。通过明确的项目目 标、阶段评审和决策流程，IPD可以使各方的利益更加一致。合理的 资源分配和绩效考核机制也能平衡各部门的关注点，从而减少目标冲 突、资源争夺以及竞争性优先级问题的出现。</td></tr><tr><td>3</td><td>组织结构</td><td>权责不清</td><td>IPD方法强调在项目启动阶段就明确了角色、职责和权限，确保每个 团队成员都清楚自已的任务和责任。这有助于避免质量问题、决策困 难和责任逃避。IPD通过明确的项目管理架构，促进团队成员的有效 协同和协调。</td></tr></table>

表4-1IPD方法适用性分析（续上表）  

<table><tr><td>4</td><td>组织结构</td><td>重复造轮 子</td><td>IPD方法强调模块化和标准化设计，通过共用构建模块（CBB）来减 少重复开发。通过建立统一管理的组件库，团队可以更好地利用已有 的技术和能力，从而避免重复研发，提高资源利用效率。</td></tr><tr><td>5</td><td>研发管理 流程</td><td>缺乏市场 调研</td><td>IPD方法强调市场导向，将市场需求纳入项目的早期阶段。通过与市 场部门的紧密合作，IPD能够确保项目从开始阶段就充分了解市场需 求，从而更好地满足客户的期望，减少产品定位错误。</td></tr><tr><td>6</td><td>研发管理 流程</td><td>缺乏技术 规划</td><td>IPD方法将技术规划作为项目的一部分，强调技术方案的明确性和合 理性。通过全生命周期的技术规划，IPD有助于解决技术债务、确保 产品质量，从而提高项目的技术水平和竞争力。</td></tr><tr><td>7</td><td>研发管理 流程</td><td>缺乏有效 的决策评 审设计</td><td>IPD方法明确了决策评审的时间点和流程，确保项目在不同阶段都能 够接受适时的评审和调整。这有助于项目管理层准确了解项目进展， 做出及时的决策，避免项目延误和目标偏离。</td></tr><tr><td>8</td><td>研发管理 流程</td><td>缺乏系统 性的阶段 管理设计任务交叉和信息不畅等问题。</td><td>IPD方法强调阶段管理，明确不同阶段的目标和任务。通过阶段评 审，IPD可以确保项目在不同阶段都有明确的目标和成果交付，避免</td></tr></table>

综上所述，集成产品开发（IPD）方法的多学科协同、全生命周期管理和明确的角色与流程设计，使其能够解决A公司在组织结构与研发管理流程两个领域存在的项目进度管理问题。由此可以得出，IPD对于A公司项目进度管理改进来说具有极高的适用性。

# 4.2IPD项目进度改进策略

接下来，本文将从组织结构和研发管理流程两个领域，对A公司的项目管理体系进行改进设计。

首先，通过改进公司研发管理组织结构，改进组织结构领域存在的问题，具体改进措施包括：(1)成立跨部门团队。采用产品线组织管理模式重构公司研发项目管理组织结构。(2)定义团队职责。分解IPD理论中的跨部门团队职责，结合公司实际情况，对跨部门团队的团队职责进行再定义，明确团队权力、责任和团队之间的协作关系。(3)定义团队人员构成与角色职责。基于项目研发活动和重新定义的团队职责，明确团队人员角色的权力和职责，避免出现权责不清的问题。

然后，通过改进公司研发管理流程的设计，改进研发管理流程领域存在的问题，具体改进措施包括：（1)引入市场管理流程。将IPD理论中的市场管理流程引入到公司研发管理流程之中，解决公司产品研发缺乏市场调研的问题。(2)重新定义产品开发流程。将IPD理论中的业务决策评审、技术决策评审和产品研发阶段管理设计引入到公司研发管理流程之中，针对性的解决公司产品研发过程中存在的缺乏有效决策评审点设计以及缺乏系统性流程阶段划分的问题。(3)引入技术开发流程。将IPD理论中的技术开发流程引入到公司研发管理流程之中，结合对组织结构的跨部门团队设计，建立专业的跨部门技术开发团队，针对性解决公司产品研发重复造轮子以及缺乏技术规划的问题。(4)流程阶段定义。根据公司实际情况，对IPD理论中的市场管理流程、产品开发流程、技术开发流程进行裁剪，使其更适应公司项目实施环境。对流程中的各阶段流程活动进行明确的定义，规范流程活动，明确阶段目标。

# 4.3组织结构改进设计

通过对A公司项目进度管理中存在的问题进行分析，发现公司的研发管理组织架构体系存在较多问题，如产品缺乏规划，团队协作困难，缺少CBB库建设与管理等。基于以上问题的分析结果，A公司结合公司研发管理现状和管理经验，在IPD的核心框架基础之上进行了研发管理体系的重新设计，并以此进行公司项目研发管理组织结构的重构，建立产品线管理模式，增设MPC、PL-IPMT、PDT和TDT团队，产品线中具体产品和通用组件的研发管理通过在PDT、TDT团队下建立扩展组来负责完成。四个团队各司其职，相互协同，共同对大数据分析平台的市场成功负责。大数据分析平台项目研发管理组织结构设计如图4-1所示

![](images/74c15e761a52f52cffe5fc85ef30a12d95e965428d8dbcda348c45670cc3e92e.jpg)  
图4-1项目组织结构示意图

新的研发管理组织架构是基于IPD理论设计而来，并且结合了公司环境进行了本地化设计。

第一、MPC是高层决策团队，类似与IPD 理论中的IPMT设计，MPC直接领导PDT、PL-IPMT、TDT团队。

第二、PL-IPMT主要考虑的是产品线的管理与发展问题，需要说明的是产品市场管理流程由PL-IPMT团队进行管控，PL-IPMT团队更关注市场。PL-IPMT直接向MPC汇报，协助MPC完成重要决策工作，与PDT和TDT团队协同完成项目目标。

第三、PDT团队在负责产品开发的同时，也负责产品研发阶段的项目管理工作，PDT更关注市场需求的落地。PDT团队直接向MPC汇报，与PL-IPMT和TDT团队协同完成项目目标。

第四、TDT团队负责技术开发和CBB的管理工作，直接向MPC汇报，与PDT和PL-IPMT团队协同完成项目目标。

# 4.3.1市场与产品管理委员会

设立市场与产品管理委员会（简称：MPC）作为公司级产品开发投资决策和管理机构，MPC主要对产品市场规划和产品管理负责，MPC成员应包括市场、财务、研发、交付与服务、行政以及经营等各领域的高层管理人员或专家。MPC团队的详细职责设计见表4-2所示。

表4-2MPC团队职责设计表  

<table><tr><td>序号</td><td>职责描述</td></tr><tr><td>1</td><td>依据市场调研团队输出的商业计划书或MRD等文档，对公司的市场产品立项进行决策评 审，并签发工作任务书（立项Charter）。</td></tr><tr><td>2</td><td>依据技术规划团队输出的商业计划书或技术规划书等文档，对公司的公共组件或技术研究的 立项进行决策评审，并签发工作任务书（立项Charter）。</td></tr><tr><td>3</td><td>根据产品、公共组件及解决方案的开发需要，任命产品线集成产品管理团队PL-IPMT，产品 开发团队PDT，技术开发团队TDT。</td></tr><tr><td>4</td><td>批准和签发对PL-IPMT、PDT、TDT的考核方案和结果。</td></tr><tr><td>5</td><td>依据产品开发团队的工作结果，对产品进行PDCP、ADCP等决策评审，签发评审决议。</td></tr><tr><td>6</td><td>对PDT或SPDT提交的变更申请（PCR）进行审批。</td></tr></table>

# 4.3.2产品线集成产品管理团队

产品线集成产品管理团队（简称：PL-IPMT）是一个跨职能团队，专注于产品线的管理和发展。主要任务是协调和管理产品线的策略制定、营销和销售，确保产品能够满足市场需求并顺利推向市场，以实现产品线的整体成功和市场竞争力。PL-IPMT团队职责定义和角色职责定义见表4-3和4-4所示。

表4-3PL-IPMT团队职责定义表  

<table><tr><td>序号</td><td>职责描述</td></tr><tr><td>1</td><td>产品线策略制定。PL-IPMT团队负责规划产品线的战略方向与目标。运用市场研究、竞争分 析和技术评估等方法，洞察市场需求与趋势，明确产品线的定位、目标市场以及核心特点。 确保产品线策略与公司总体战略保持一致，并与MPC团队共同制订产品线的发展走向。</td></tr><tr><td>2</td><td>产品线协调与整合。PL-IPMT 团队协调和整合产品线内各个产品的开发和营销活动。他们与 产品开发团队、技术开发团队和市场管理团队合作，确保产品线内的产品在技术、功能和定 位上的协调一致。PL-IPMT团队通过有效的沟通和协作，促进产品线内产品之间的相互支持 和协同作用。</td></tr><tr><td>3</td><td>产品线管理和运营。PL-IPMT 团队负责产品线的管理和运营。他们跟踪产品线的销售情况、 市场份额和客户反馈等关键指标，监测产品线的绩效和市场表现。进行数据分析，识别潜在 的改进机会和市场趋势，为产品线的持续优化和未来发展提供建议和决策支持。</td></tr><tr><td>4</td><td>跨部门协调与沟通。PL-IPMT团队在不同部门之间起到桥梁和协调的作用。他们与市场管理 团队、产品开发团队、销售团队等部门保持紧密联系，促进信息共享和沟通。PL-IPMT团队 通过定期会议、工作坊和汇报会议等形式，确保团队成员之间的有效协作和信息流通。</td></tr><tr><td>5</td><td>产品线营销与推广。PL-IPMT团队与市场管理团队合作，制定产品线的推广和营销策略。他 们参与市场推广计划和资源的协商，确保产品线在市场上获得良好的曝光度和销售机会。 PL-IPMT团队提供关于产品线特点和竞争优势的关键信息，以支持市场管理团队的市场营销 活动，并协助制定市场推广计划和销售策略。</td></tr><tr><td>6</td><td>定义产品功能和规格。PL-IPMT与产品开发团队以及技术开发团队合作，将市场需求转化为 产品的功能和规格要求。通过与各利益相关方以及客户的沟通，确定产品的关键特性和功 能，以满足市场需求。</td></tr><tr><td>7</td><td>制定产品开发计划。PL-IPMT负责制定产品开发的计划和时间表。与产品开发团队（PDT） 合作，确保项目的里程碑和交付时间得到合理安排，并监督项目进展。</td></tr><tr><td>8</td><td>进行市场竞争分析。PL-IPMT进行市场竞争分析，了解竞争对手的产品特点、定价策略和市 场份额，制定产品的差异化策略和竞争优势，以增加产品的市场占有率。</td></tr><tr><td>9</td><td>管理项目进度和预算。PL-IPMT负责监督项目进展，确保项目按计划进行。与财务团队合 作，管理项目预算和资源分配，确保项目的可控性和成本效益。</td></tr><tr><td>10</td><td>检测市场反馈和用户需求。PL-IPMT收集市场反馈和用户需求。他们通过市场调研、用户调 查和客户反馈，了解产品的市场表现和改进机会，以便及时进行产品调整和优化。</td></tr></table>

表4-4角色职责定义表  

<table><tr><td>角色</td><td>资源团队/职位</td><td>主要职责</td></tr><tr><td>产品线经理</td><td>项目管理部/产 品规划部/市场</td><td>产品线经理是PL-IPMT团队的核心角色。负责整个产品线的管理 和发展。产品线经理需要具有全局视角，了解市场需求、竞争情 况和公司战略，并制定产品线策略和目标。与其他团队成员密切</td></tr><tr><td>市场代表</td><td>市场管理部</td><td>代表市场需求和客户观点，提供市场调研、竞争分析和市场定位 等信息，以支持产品线策略的制定。市场管理团队代表还负责制 定产品推广和营销策略，确保产品线能够在市场上获得成功。</td></tr><tr><td>PDT代表</td><td>PDT经理</td><td>代表产品设计和开发方面的观点，与产品线经理合作，将业务功能 可行性和创新引入产品线的开发过程中。负责将市场需求转化为产 品功能和设计要求，并确保产品线在技术层面上具备竞争力。</td></tr><tr><td>TDT代表</td><td>TDT经理</td><td>代表技术设计和开发方面的观点，与产品线经理合作，提供技术 研究、技术评估、技术验证、技术架构设计等支持，开发技术解 决方案，制定技术策划与路线图。</td></tr><tr><td>销售代表</td><td>销售部</td><td>负责与市场管理团队和产品开发团队紧密合作，了解市场需求和客户 反馈，并提供销售数据和见解。销售代表在PL-IPMT团队中提供市场 反馈和销售趋势分析，以支持产品线策略和产品开发决策。</td></tr><tr><td>数据分析师</td><td>产品运营部</td><td>负责与市场管理团队和产品开发团队紧密合作，了解市场需求和客 户反馈，并提供销售数据和见解。销售代表在PL-IPMT团队中提供 市场反馈和销售趋势分析，以支持产品线策略和产品开发决策。</td></tr><tr><td>客户服务代表</td><td>交付服务部</td><td>客户服务代表关注产品线的售后支持和客户满意度。他们与市场 管理团队代表合作，收集和分析客户反馈，解决客户问题，并提 供客户服务和支持的策略建议。</td></tr></table>

# 4.3.3产品开发团队

产品线设立跨部门产品开发团队（简称：PDT）作为具体的产品设计、研发和制造团队。该团队是IPD模式中最核心的团队，团队人员组成覆盖了众多部门，包括项目管理、产品、研发、测试、质量、交付服务、市场、财务、人事等部门。PDT的主要职责包括根据PL-IPMT的需求和规格进行产品设计和开发、制定工程计划和进度、解决技术难题、进行原型制作和测试、确保产品质量等。PDT的目标是按时交付高质量的产品，并确保产品设计与规格的一致性。PDT团队内部还可以分为核心组和扩展组。

MPC授权PDT经理组建PDT核心组，对产品开发按项目进行管理，PDT经理即为项目经理。核心组成员负责协调各自负责领域的资源，按照项目计划完成所负责领域的任务，达成项目目标。

PDT授权开发代表、测试代表组建扩展组，支持产品的开发和验证过程。扩展组按照项目的方式组织开发和测试的资源进行开发和验证工作。

PDT团队职责定义和角色职责定义如表4-5和4-6所示。

表4-5PDT团队职责定义表  

<table><tr><td>序号</td><td>职责描述</td></tr><tr><td>1</td><td>产品设计与规划。PDT负责产品的设计和规划，将市场需求和PL-IPMT确定的产品规格转 化为实际的产品设计。考虑产品的功能、性能、外观、用户体验和可落地性等方面，以确保 产品能够满足市场需求并符合技术可行性要求。</td></tr><tr><td>2</td><td>技术开发与原型制作。PDT负责产品的技术开发和原型制作。利用各种工具和技术，将产品 的设计转化为实际的原型或样品，以验证产品的功能和性能，并进行技术验证和风险评估。</td></tr><tr><td>3</td><td>项目管理与协调。PDT负责产品开发项目的管理与协调。制定项目计划、设置里程碑和交付 时间，并确保项目按计划进行。与其他团队密切合作，协调资源、解决问题和管理项目风 险，以确保产品的及时交付。</td></tr><tr><td>4</td><td>技术可行性评估。PDT负责评估产品的技术可行性和可落地性。研究和分析产品的技术要 求，评估所需的技术能力和资源，以确保产品能够在可行的技术范围内进行开发和生产。</td></tr><tr><td>5</td><td>质量控制与测试。PDT负责产品的质量控制和测试。制定质量标准和测试计划，进行产品的 功能测试、性能测试、可靠性测试、系统集成测试和安全测试，以确保产品符合质量要求和 市场期望。</td></tr><tr><td>6</td><td>创新与改进。PDT持续进行技术创新和产品改进。密切关注新技术和市场趋势，提出创新的 想法和解决方案，以增加产品的竞争力和不断改进产品的功能和性能。</td></tr><tr><td>7</td><td>与其他团队协作。PDT需要与其他团队密切合作，例如PL-IPMT、TDT等，以确保产品开 发的成功。 (1）与PL-IPMT的协作。PDT与PL-IPMT密切合作，完成对市场的洞察和产品策略的制 定，理解产品的战略目标和市场需求。共同制定产品规格和设计要求，并确保产品的设计与 PL-IPMT的产品方向和市场推广要求保持一致。PDT需要与PL-IPMT保持沟通和协商，及 时调整产品设计和开发计划，以适应市场的变化和需求。 (2）与TDT的协作。PDT与技术开发团队（TDT）之间的密切合作是产品开发过程的核心。 PDT与TDT共同解决技术挑战，评估和选择合适的技术方案，确保产品的功能和性能得以 实现。共同制定产品开发的技术路线图和时间表，并通过持续的沟通和协调，确保技术开发 与产品需求相匹配。</td></tr></table>

表4-6角色职责定义表  

<table><tr><td>角色名称</td><td>资源部门/职位</td><td>主要职责</td></tr><tr><td>PDT经理</td><td>项目管理部/产品规 划部/技术业务群</td><td>（1）按照与MPC签订的项目合同书中做出的承诺，对项目的 成功负责； (2）建立、管理和指导PDT核心团队； (3）制定和管理跨职能部门的产品包计划： (4）管理产品包的盈亏； (5）管理项目变更控制； (6）管理项目风险；</td></tr><tr><td>市场代表 客户服务代 表</td><td>市场管理部 交付服务部</td><td>（1）进行本领域与项目相关活动的有效管理，并从本部门其他 专家处获得所需支持。 (2）制订详尽的本领域项目计划； (3）遵循项目计划和MPC承诺，从本部门获取所需资源并进行 有效管理； (4）提供有关本领域决策评审的建议； (5）与扩展团队成员一同审视项目状态，解决涉及本领域的问 题； (6）提出涉及本领域的风险和问题，并推动解决方案的制定； (7）监控外围团队成员的工作进展，确保按时完成项目计划中</td></tr><tr><td>PQA</td><td>质量管理部</td><td>的任务； (1）遵循公司和业务领域的质量政策，达成产品质量目标； (2)制定并监控产品质量计划； (3）引导并审计过程活动； （4）统筹各个领域的质量保证活动和质量问题；负责执行技术 评审流程，组织技术评审会议； (5）编写产品质量月报；</td></tr><tr><td>产品经理</td><td>产品规划部</td><td>(6）提出涉及本领域的风险和问题，并推动解决方案的制定： （1）组织对业务需求的分析和分解，将业务需求转化为产品开 发需求； (2）进行产品的整体设计、详细设计以及产品管理； (3）对研发成果进行验收； （4）提出涉及本领域的风险和问题，并推动解决方案的制定；</td></tr></table>

表4-6角色职责定义表（续上表）  

<table><tr><td>角色名称</td><td>资源部门/职位</td><td>主要职责</td></tr><tr><td>架构师</td><td>架构师</td><td>(1）负责产品的技术解决方案选型和系统总体架构设计： (2)提供技术咨询和支持、开展技术验证和实验、解决技术难题等； (3)编写项目技术规范与标准，并提供技术培训与知识分享； (4）指导产品概要设计； (5）负责产品的技术合作和专利规划；</td></tr><tr><td>开发代表</td><td>开发经理</td><td>(6）提出涉及本领域的风险和问题，并推动解决方案的制定； （1）带领研发工程师完成开发任务； (2)与架构师一起制定产品专利发掘计划和组织专利交底书撰写； (3）提出涉及本领域的风险和问题，并推动解决方案的制定；</td></tr><tr><td>测试代表</td><td>测试经理</td><td>(1）对产品开发的结果组织测试人员进行测试和验证； (2）提出涉及本领域的风险和问题，并推动解决方案的制定；</td></tr></table>

# 4.3.4技术开发团队

公司设立跨部门技术开发团队（简称：TDT）来支持各产品线在产品开发过程中的技术需求和创新。该团队通常由研发工程师、测试工程师、架构师、科学家、技术专家和研究人员组成。TDT的主要职责包括开发CBB组件、研究新技术和趋势、提供技术咨询和支持、开展技术验证和实验、解决技术难题等。TDT的目标是为产品开发团队提供支持，确保产品具备先进的技术竞争力。TDT团队内部还可以分为核心组和扩展组。

MPC授权TDT经理组建TDT核心组，对技术开发立项按项目进行管理，TDT经理为项目经理。核心组成员负责协调各自负责领域的资源，按照项目计划完成所负责领域的任务，达成项目目标。

TDT经理授权开发代表、测试代表组建扩展组，支持技术开发和验证过程。扩展组按照项目的方式组织开发和测试的资源进行开发和验证工作。

TDT团队的职责定义如表4-7所示。

表4-7TDT团队职责定义表  

<table><tr><td>序号</td><td>职责描述</td></tr><tr><td>1</td><td>技术策略与路线图。TDT负责制定技术策略和路线图，以支持产品的技术开发和实施。与 PL-IPMT和PDT合作，了解产品需求和市场要求，并确定适合的技术方案和开发计划。 TDT考虑技术可行性、资源需求和时间限制，制定技术开发的目标和计划</td></tr></table>

表4-7TDT团队职责定义表（续上表）  

<table><tr><td>序号</td><td>职责描述</td></tr><tr><td>2</td><td>技术研究与评估。TDT进行技术研究和评估，以确定最适合产品需求的技术解决方案。跟踪 技术趋势和创新，评估各种技术选项的优缺点，并为产品的技术决策提供专业的建议。TDT 还进行原型制作和实验验证，以验证技术的可行性和性能。</td></tr><tr><td>3</td><td>技术开发与编码。TDT负责产品的技术开发和编码工作。根据产品规格和设计要求，进行软 件开发、系统集成等工作，确保产品的功能和性能得以实现。TDT采用适当的开发方法和工 具，进行编码、测试和调试，以确保产品的质量和稳定性。</td></tr><tr><td>4</td><td>技术验证与风险评估。TDT进行技术验证和风险评估，以确保产品的技术可行性和可靠性。 他们进行系统测试、性能测试、安全性评估等工作，验证产品是否符合技术规范和市场需 求。对技术开发过程中的风险进行评估，并制定对应的风险管理计划；</td></tr><tr><td>5</td><td>技术支持与故障排除。TDT提供技术支持和故障排除，以解决产品开发和部署过程中的技术 问题。与PDT和其他团队紧密合作，解决技术难题、优化性能，并提供技术培训和支持，以 确保产品的顺利交付和运行。</td></tr><tr><td>6</td><td>技术创新与改进。TDT致力于技术创新和持续改进。关注新兴技术和行业趋势，提出创新的 想法和解决方案，以增强产品的竞争力和创新性。TDT参与研发项目，推动技术的进一步改 进和突破，寻找新的技术应用和优化方案，以满足不断变化的市场需求。</td></tr><tr><td>7</td><td>技术规范与标准。TDT制定技术规范和标准，以确保产品的技术质量和一致性。遵循行业标 准和最佳实践，确保产品的技术符合安全性、可靠性和兼容性等方面的要求。</td></tr><tr><td>8</td><td>技术培训与知识共享。TDT进行技术培训和知识分享，以提升研发团队的技术能力和专业知 识。组织内部培训，推广公司的技术规范、标准和技术路线，分享最新的技术趋势和开发工 具，公司研发团队的学习和成长。</td></tr><tr><td>9</td><td>CBB库建设与管理。TDT通过与各产品线合作，参与产品的规划设计，抽象提取通用能力 并回流至CBB库。研发通用CBB组件，统一管理公司CBB库，为公司各产品线提供CBB 能力支撑。</td></tr></table>

# 4.4研发管理流程改进设计

# 4.4.1引入产品市场管理流程

在传统的业务运营当中，研发和市场部门在企业中往往相对独立地运作，导致产品开发与市场需求之间的脱节。然而，随着市场竞争的加剧，将市场需求融入研发过程变得至关重要。通过引入产品市场管理流程，企业可以更好地理解市场需求、预测趋势，并将这些信息直接融入研发策略和计划中。大数据分析平台

# 产品市场管理流程设计如图4-2所示。

![](images/21c2946e8056771e14c77386140dc8205c1ee7cb74243219e17e4ae6982efb99.jpg)  
图4-2产品市场管理流程示意图

产品市场管理流程的核心思想是将市场管理流程与公司战略有机融合，以产品线业务计划为核心，以统一的方法制定公司、产品线、细分市场和产品包业务计划，打通市场和研发，使研发以市场为导向。使用基于客户需求的结构化流程/分析工具体系，贯穿始终的投资组合决策分析，融合公司各职能规划的市场导向的业务计划，跨部门团队运作产品线。产品市场管理流程阶段划分如表4-8所示。

表4-8产品市场管理流程定义表  

<table><tr><td>序号</td><td>阶段</td><td>详细内容</td></tr><tr><td>1</td><td>了解市场</td><td>（1）了解数字化转型进程当前现状。 (2）了解数字化转型、大数据产业的发展趋势，输出对公司业务有重大影响的趋 势、机会、威胁。 (3）了解数字经济、大数据产业的政治环境、社会环境、产业环境、技术环境，输 出对公司业务有重大影响的趋势、机会、威胁。 (4）通过了解各个细分市场和客户，我们能够洞察市场的发展趋势以及客户的需 求，输出对公司业务有重大影响的趋势、机会、威胁以及客户痛点与需求。 (5）分析行业竞争态势和竞品发展情况，输出竞争对手未来发展规划、优势、劣 势。 (6）进行行业规模评估，为目标行业选择提供数据支持。</td></tr><tr><td>2</td><td>市场细分</td><td>(7)进行自身分析，输出自身能力短板、优势、能力储备。 公司深耕的细分市场主要包括油气（中石油、中海油、中石化），数字政府，国企， 央企（招商局、航天科工、华润），大出行（整车、车联网、轨道交通），产业园 区，司法，能源电力（国家电网、南方电网，国家电投、中国华电、中国电建、中 广核），金融，智能制造，工业大数据等细分市场。</td></tr></table>

表4-8产品市场管理流程定义表（续上表）  

<table><tr><td></td><td></td><td></td></tr><tr><td>序号</td><td>阶段</td><td>详细内容 (1）分析各细市场的内外在因素，了解各细分市场都有哪些具有竞争力的解决方</td></tr><tr><td>3</td><td>组合分析</td><td>案和解决方案提供商，洞察公司在各细分市场的优势、劣势、机会与威胁 (2）结合细分市场规模、发展趋势、竞争态势、我司优劣势，制定我司市场战 略，输出市场战略地图，并提取我司重点目标客户 结合市场信息与细分市场组合分析结果，制定产品线的业务市场SP/BP规划， 主要活动内容包括： (1）制定产品及服务市场策略。</td></tr><tr><td>4</td><td>业务市场 SP/BP规划</td><td>(2）确定产品平台定位 （3）制定产品与服务售卖模式 (4）进行产品版本规格设计 (5）制定产品与服务定价规则 （6）规划产品与子系统生命周期 (7）汇总业务成本 （8）制定市场营销计划 (9)规划产品交付策略与计划 (10)制定关键时间点计划</td></tr><tr><td>5</td><td>产品线 SP/BP规划</td><td>产品线SP/BP规划的主要活动包括： (1）产品整体架构与规划设计 (2）总体路标规划和子系统路标规划 (3）产品研发成本预估 （4）解决方案开发规划与成本预估 （5）产品交付与服务开发成本预估</td></tr><tr><td>6</td><td>财务预算评估</td><td>(6）关键风险及规避措施 签单额、收入、市场营销成本、销售人力成本、商务差旅费用、研发成本、交 付成本：整体利润率、产品毛利率。</td></tr><tr><td>7</td><td>关键风险识别 与规避措施</td><td>(1）社会环境风险（疫情） (2）产品研发风险 (3）产品交付风险 (4）人力风险</td></tr></table>

# 4.4.2重构产品开发流程

![](images/0c4a30500a9fed9942d589baf97cb11d890f04c45bdfbb4fe7df4f6b298ac3ee.jpg)  
图4-3产品开发流程示意图

新的产品开发流程是在IPD理论的基础之上设计而来，但结合A公司的实际项目研发环境进行了简化设计。第一，简化了阶段划分。将“概念阶段”和“计划阶段”合并为“概念与计划阶段”，将“开发阶段”和“验证阶段”合并为“开发与验证阶段”。第二，简化了业务决策评审。将“CDCP"和“PDCP”的评审工作合并到“PDCP”的评审阶段进行。第三，简化了技术决策评审。将“TR4”和“TR4A”的评审工作合并到"TR4”的评审阶段进行。

经过以上的简化处理，为整个产品开发流程带来了诸多优化。第一，使整个流程变的更加简单明了，有助于团队成员更好地理解和执行流程，减少混淆。第二，能够减少阶段切换和等待时间，有助于加速产品的开发和上市时间。第三，可以减少阶段性的成果要求，减少重复步骤。第四，概念和计划合并为一个阶段，可以更集中地思考产品的愿景、目标和计划，有助于更好的定义开发方向。第五，决策评审的合并，能够将原来处于不同评审阶段的工作，集中到一个评审阶段之中，有助于项目开发计划的异步开发设计，提高项目活动的并行度。

简化后的产品开发流程分为四个阶段，分别是概念与计划阶段、开发与验证阶段、发布阶段和生命周期阶段。笔者对每个阶段进行了详细的设计定义，具体如下。

（1）概念与计划阶段设计。在该阶段，将对产品的业务需求进行分析和确认，论证需求的技术实现可行性，明确产品包需求、产品概念及产品规格，并确定产品技术方案。按照制定的产品规格和技术方案，分解产品子系统和模块进行概要设计，制定产品集成方案和测试方案，并完成具体的产品业务计划和产品开发计划。阶段流程设计如表4-9。

表4-9概念与计划阶段流程定义表  

<table><tr><td>序号</td><td>活动</td><td>详细内容</td></tr><tr><td>1</td><td>项目启动会</td><td>下发并讲解产品Charter任务书；项目组织结构介绍：PDT成员介绍及任命； TDT成员介绍及任命：下发项目计划书；产品开发流程培训。</td></tr><tr><td>2</td><td>定义产品 包需求</td><td>确定市场需求（产品市场策略、细分市场需求、核心项目需求、目标市场准入 需求）；确定系统功能需求（业务场景、业务流程、业务功能、安装部署、升 级）；确定非功能需求（运行环境需求、业务性能需求、资料需求、服务需</td></tr><tr><td>3</td><td>录入业务 需求</td><td>求、资质和知识产权需求、DFX领域需求）。 将业务需求录入到需求管理系统，方面后期需求的统一管理以及与产品开发需 求的关联管理。</td></tr><tr><td>4</td><td>知识产权 保护</td><td>产品经理发起专利侵权风险分析申请，并提供材料，知识产权代表组织进行专 利检索和分析，输出专利侵权风险分析报告。</td></tr><tr><td>5</td><td>TRI评审</td><td>(1)产品概念和用户需求定义是否符合目标客户预期？ (1)是否已明确产品的营销亮点和营销计划？ (2）产品概念和包需求是否符合产品市场定位？ (3）是否已对产品概念和商标名称等进行知识产权检索？ (4）产品包需求是否包含了全部内部需求（DFX需求）？ (5)是否输出了产品包需求文档并评审通过？ (6）是否已完成业务需求的录入？ (7)产品包需求是否满足产品交付和售后服务的要求？ (8)产品包需求是否满足系统可运维的要求？ (9)产品包需求内容是否可被测试？ (10）是否已完成输出专利侵权风险分析报告文档？ (11）是否已创建项目文档工作目录及配置库，并完成初始设置？ (12)阶段输出的文档是否都已完整归档到文档库并打上基线标签？ (13)产品包需求是否明确了需要满足的质量标准和需要进行的认证等？</td></tr><tr><td>6</td><td>产品概要 设计</td><td>(14)文档的输出过程和评审过程是否规范？ 概要设计承接产品包需求，对产品的部件及模块划分、系统功能和非功能展开 设计，概要设计能够为下一阶段的详细设计和技术方案设计提供指导。概要设 计主要包括功能架构图、角色、用例、核心业务场景、关键业务流程、部署架 构、系统运行原理、子系统间关系等。</td></tr></table>

表4-9概念与计划阶段流程定义表（续上表）  

<table><tr><td>序号</td><td>活动</td><td>详细内容</td></tr><tr><td>7</td><td>技术方案 设计</td><td>技术经理或架构师依据产品概要设计和DFX需求，对产品的系统架构进行设 计，规划设计产品技术路线实现的总体性技术方案。技术方案作为产品实现的 基础，需要在对系统架构进行设计的同时进行可行性论证和验证。</td></tr><tr><td>8</td><td>包括CBB 选择和立 项方案</td><td>基于产品包需求、技术方案、产品概要设计，结合公司CBB能力，进行CBB 组件的选择或者立项方案开发。</td></tr><tr><td>9</td><td>确定产品 规格列表</td><td>包括产品功能清单和非功能特性</td></tr><tr><td>10</td><td>知识产权 保护</td><td>产品经理和架构师依据专利侵权风险分析报告设计和制定相应的规避措施，并 由知识产权代表进行评审 (1)产品概要设计是否覆盖了产品需求包的所有功能需求？</td></tr><tr><td>11</td><td>TR2评审</td><td>(2)产品概要设计和技术方案是否覆盖了产品需求包的所有非功能需求？ (3）产品概要设计和技术方案是否覆盖了产品需求包的所有设计需求？ （4）是否已完成产品概要设计并评审通过？ (5）是否已完成技术方案设计并评审通过？ （6）是否已完成产品选用已有CBB和开发新CBB的评估？ (7）是否已完成产品规格列表并评审通过？ （8）是否已完成制定专利侵权风险的规避措施？ (9)阶段输出的文档是否都已完整归档到文档库并打上基线标签？ (10）阶段活动是否满足过程规范要求？</td></tr><tr><td>12</td><td>规划产品 版本设计</td><td>产品版本功能规划</td></tr><tr><td>13</td><td>进行产品 详细设计</td><td>完成需求规格说明书的开发，内容包括功能的输入、输出、操作、约束、影 响、前置、后置、数据需求等；完成产品原型开发。 （1）明确集成构件组成、构件集成顺序、集成分工等。</td></tr><tr><td>14</td><td>制定产品 集成方案</td><td>(2）集成环境定义：包括开发环境、测试环境（单体测试、集成测试、验收测 试、预发布）、正式环境 (3）系统平台设计：系统运行环境设计方案 （4）产品集成计划：包括集成组件、开始时间、结束时间、负责人、执行内容</td></tr></table>

表4-9概念与计划阶段流程定义表（续上表）  

<table><tr><td>序号</td><td>活动</td><td>详细内容</td></tr><tr><td>15</td><td>制定产品 测试方案</td><td>内容包括测试范围、测试环境、测试人员、测试方法、整体测试计划、子系统 测试计划、测试依赖等。</td></tr><tr><td>16</td><td>制定产品 业务计划</td><td>完成产品发版计划、质量保障计划、国产化适配计划、研发策略与计划（产品 路标与规划、系统研发计划、产品文档撰写计划等）、交付与服务策略等计划 的制定。</td></tr><tr><td>17</td><td>制定市场 业务计划</td><td>产品营销计划（产品营销方案、营销资源及成本预算、宣传活动及计划） 产品销售计划（销售目标分解、销售人员计划、销售激励和奖励措施）</td></tr><tr><td>18</td><td>知识产权 保护</td><td>技术经理负责完成产品专利交底书和技术秘密交底书的撰写计划。</td></tr><tr><td>19</td><td>TR3评审</td><td>(1）是否已完成产品详细设计并评审通过？ (2）产品概念和关键特性是否满足市场开发和推广的需要？ (3）是否已完成产品集成方案并评审通过？ (4）是否已完成产品测试方案和计划并评审通过？ (5）产品研发计划中的测试时间和投入是否已充分经过评估和沟通？ (6）是否已完成产品业务计划的制定并评审通过？ (7）是否已完成市场业务计划的制定并评审通过？ (8）当前产品及研发的开发计划及关键时间节点是否满足市场要求？ (9）是否已完成产品营销计划，完成产品的销售预测及盈利分析？ (10)对产品研发成本的估算是否全面和完整，估算途径和方法是否合理？ （11）对产品总体收入及成本的估算是否完整及合理，销售预测和盈利分析是否 符合财务要求？ (12）是否已制定明确的专利交底书和技术秘密交底书撰写计划？ (13）阶段输出的文档是否都已完整归档到文档库并进行基线？ (14）是否为软件开发创建了配置库？ (15)阶段输出的交付件是否齐全，过程是否符合已定义的规范？ (16）项目计划及WBS分解过程是否完整和规范？</td></tr><tr><td>20</td><td>PDCP汇报 材料开发</td><td>对概念与计划阶段的输出物和项目数据进行汇总汇报，主要包括产品概述、产 品竞争分析与产品策略、系统整体设计、领域策略和计划、财务分析、TR评 审结论、风险及问题等方面。</td></tr></table>

表4-9概念与计划阶段流程定义表（续上表）  

<table><tr><td>序号</td><td>活动</td><td>详细内容</td></tr><tr><td>21</td><td>PDCP概 念、计划 决策评审</td><td>（1）产品概念是否已定义清晰，产品是否有足够的业务发展潜力，市场空间？ (2）产品的设计是否是可实现的？ (3)产品是否有足够的竞争力，能及时推向市场并盈利？ （4）明确营销策略，制定产品营销计划？</td></tr><tr><td>22</td><td>签署项目 合同书</td><td>(5)产品开发的成本预算是否可控？ 基于PDCP的评审结果和产品PDCP材料包，项目管理人员输出产品开发的项 目合同书，作为产品开发工作执行和业绩考核的基准；</td></tr></table>

(2）开发与验证阶段设计。进入该阶段之后，在签订的项目合同书基础上，PDT核心组开始按照获得资源的承诺，成立PDT扩展组，按照产品的版本和规格特性开发计划，启动相应的子项目来进行特定版本或特性的开发与验证。子项目组按照分配的需求和产品设计方案，对需求内容进行详细设计和实现，并通过代码评审和单元测试等工程活动来保证所实现模块的质量，按照产品集成方案和测试方案，进行产品集成和系统集成测试，测试的范围包括对产品的功能、性能、安全、安装部署、运营维护等领域的开发需求和设计实现。设定通过系统测试的产品版本，已基本达到设计要求，可以进行验证和按要求发布候选版本，支持在试点项目上使用，或者对客户小批量销售使用。在通过了代表客户角度进行的系统验收测试和必要的外部认证测试后，还需要经过在试点项目上使用结果的验收，评估认为产品可以满足用户要求后，才能认为具备了可获得的能力，可以进行ADCP决策评审。该阶段流程设计如表4-10。

表4-10开发与验证阶段流程定义表  

<table><tr><td>序号</td><td>活动</td><td>详细内容</td></tr><tr><td>1</td><td>下发产品开发需求</td><td>将需求进行分解，并分配到多个子项目组中进行开发实现，最大程度 实现并行开发设计，并将产品需求录入需求管理系统，链接上TR1阶</td></tr><tr><td>2</td><td>测试用例开发与评审</td><td>段完成录入的业务需求 完成测试用例开发并通过评审</td></tr><tr><td>3</td><td>软件详细设计与实现</td><td>数据库设计、接口设计、代码开发</td></tr><tr><td>4</td><td>代码评审与单元测试</td><td>进行代码review，出具评审报告；进行单元测试覆盖率评估。</td></tr><tr><td>5</td><td>代码静态分析</td><td>通过代码静态分析工具检查代码规范，输出检测报告。</td></tr></table>

表4-10开发与验证阶段流程定义表（续上表）  

<table><tr><td>序号</td><td>活动</td><td>详细内容</td></tr><tr><td>6</td><td>TR4评审</td><td>(1)产品的实现方案是否与设计一致? (2）定义的功能规格是否都已实现？ (3）定义的非功能规格是否都已实现？ (4)DFX需求是否都已实现？ (5）定义的软件需求规格是否都已正确实现？ （6）是否已完成代码静态分析和检查？</td></tr><tr><td></td><td></td><td>（11）是否已按照集成方案输出产品安装包和安装部署手册？ (12）所有代码实现是否满足容器化适配要求？ (13）代码调试日志格式和输出是否满足规范要求？ (14）是否已按照运维要求实现了产品监控指标接口？ (15)提供的产品安装包和手册是否已达到可集成的要求？ (16）代码安全扫描结果是否已达到安全测试要求？ (17)版本是否已通过接收测试？ (18)软件代码分支是否规范进行了配置管理？ (19)开发阶段输出的文档是否已完整归档？ (20）开发过程规范性是否符合要求？</td></tr><tr><td>7</td><td>子系统测试</td><td>(21）开发计划及任务、需求、缺陷等是否已正确的跟踪处理？ 测试人员对各子系统进行单独测试，包括功能测试、性能测试、稳定 性测试，提报缺陷并跟踪缺陷修复，最终出具子系统测试报告；</td></tr><tr><td>8</td><td>系统集成测试</td><td>测试人员在大数据分析平台集成测试环境进行功能测试、性能测试、稳 定性测试、安全测试，提报缺陷并跟踪缺陷修复，最终出具集成测试报 告：</td></tr><tr><td>9</td><td>产品文档开发</td><td>包括产品白皮书、产品操作手册、版本说明书、运维手册、安装部署 方案、安装部署手册、运维验收报告、标准培训视频与文档等</td></tr></table>

表4-10开发与验证阶段流程定义表（续上表）  

<table><tr><td>序号</td><td>活动</td><td>详细内容</td></tr><tr><td>10</td><td>TR5评审</td><td>（1）是否已完成产品操作手册或用户手册文档？ (2)是否已完成版本说明书，需要申请软著的版本是否已提交申请？ (3）是否确定产品的试点验证计划，设置Beta测试需要达成的目标？ (4）产品功能规格是否已全部测试通过？ (5)产品非功能规格已全部测试通过？ (6）是否已完成性能测试并建立性能基线？ (7）是否已完成License控制方案的测试？ (8）是否未遗留致命级别的缺陷，满足遗留缺陷标准？</td></tr><tr><td></td><td></td><td>(15）测试过程的文档是否已完整归档到了产品文档库？ (16)发布的版本安装包及文档是否已正确归档？ (17)产品是否达到可试点使用的质量标准？ (18)产品集成过程的规范性是否符合要求？ 针对产品功能、性能、稳定性、安全、缺陷等特性进行验收，并出具产品</td></tr><tr><td>11</td><td>项目验收</td><td>验收报告。</td></tr><tr><td>12</td><td>输出可交付产品包</td><td>包括产品部署程序、产品文档、交付方案、运维文档等 完成灯塔项目验证，输出试用报告。</td></tr><tr><td>13</td><td>Beta测试 外部测试</td><td>通过外部评测机构测试，获取证书，例如赛宝评测、塞西评测、鲲鹏适配</td></tr><tr><td>14</td><td></td><td>测试、麒麟适配测试等。</td></tr></table>

表4-10开发与验证阶段流程定义表（续上表）  

<table><tr><td>序号</td><td>活动</td><td>详细内容</td></tr><tr><td>15</td><td>TR6评审</td><td>(1）产品的全部系统规格是否都已正确实现，满足定义的业务场景的使 用要求？ (2）是否已完成信创适配测试并符合要求？ (3）发布版本的系统验证测试报告是否已被批准? (4)遗留缺陷是否满足产品发布的质量标准的要求？ (5）产品Beta测试是否达到了预期的质量目标？ (6)产品包需求已全部实现，产品版本说明书已评审通过？ (7)是否已完成产品的验收测试并通过了试点用户或模拟生产环境的验 证? (8)发布的版本内容是否与计划一致，版本号定义是否规范？</td></tr><tr><td>16</td><td>ADCP评审</td><td>(12）发布版本是否满足质量目标控制要求？ (13）版本验证过程的规范性是否符合要求？ (14）发布给交付和市场的文档是否都已按规范进行了评审？ (1）验证开发的产品是不是满足最初的设计要求； (2）产品是否已准备好发布，包括对产品的业务展望，发布质量，发布</td></tr></table>

(3）发布阶段设计。ADCP决策评审通过的产品版本，才可以正式对市场发布进行销售，发布阶段需要按照上市要求准备市场相关的文档和产品支持材料，市场部门按计划组织和策划相关的上市宣传和营销活动。在产品发布阶段活动中，完成产品发布检查后，即可发布产品GA（一般可获得）通告。市场部门按计划在GA后开展上市宣传和营销活动。该阶段工作流程设计如表4-11。

表4-11发布阶段流程定义表  

<table><tr><td>序号</td><td>活动</td><td>详细内容</td></tr><tr><td>一</td><td>产品市场材料准备</td><td>汇总产品市场材料，包括产品白皮书、解决方案白皮书、版本说 明书、产品手册、报价表、功能列表、交付方案、销售PPT、产品 标准PPT等市场材料，并进行发布前评审。</td></tr><tr><td>2</td><td>产品发布检查</td><td>包括市场材料评审、各阶段完成情况检查、遗留问题解决情况检 查、营销和销售活动准备情况检查等。</td></tr><tr><td>3</td><td>发布产品GA通告</td><td>包括邮件通告，官网信息更新等。</td></tr><tr><td>4</td><td>开展产品上市宣传及营 销活动</td><td>举行线上、线下产品发布会，参加大数据相关技术论坛活动等</td></tr></table>

（4）生命周期阶段设计。产品GA后即进入生命周期阶段，PDT核心组需要继续管理和支持产品GA版本的销售和交付工作，市场代表和交付代表负责按计划组织各自领域的工作，完成产品的销售目标和交付支持。在产品销售期和维护期内，PDT核心组还需要解决产品在销售和交付过程中反馈的用户需求和用户问题，根据市场和交付的需要，组织开发和发布相应的修订版本和补丁版本。交付代表还需要按照公司要求代表PDT支持与服务产品线的相关工作。当产品版本不再支持进行销售时，需要PDT核心组和销售部门沟通制定产品EOM计划，由MPC审批后，按销售部门要求进行EOM公告。当产品版本不再进行缺陷修改和维护支持时，需要PDT核心组和交付部门沟通制定产品EOS计划，由MPC审批后，按交付部门要求进行EOS公告。在产品完成EOM和EOS后，在公司服务政策要求的时间限制内，PDT可以申请产品EOL，在EDCP决策评审通过后，发布产品EOL公告。该阶段工作流程设计如表4-12。

表4-12 生命周期阶段流程定义表  

<table><tr><td>编号</td><td>活动</td><td>详细内容</td></tr><tr><td>1</td><td>产品市场技术支持</td><td>售前支持，包括产品演示、产品介绍、poc支持等等。</td></tr><tr><td>2</td><td>产品交付技术支持</td><td>售后技术支持，包括产品培训、问题处理、定制化设计等等。</td></tr><tr><td>3</td><td>修订版本或补丁版本的开 发与发布</td><td>基于客户需求和缺陷反馈，进行补丁版本的产品研发工作。</td></tr><tr><td>4</td><td>分支代码合并</td><td>从众多的分支版本中提取产品功能，合并代码到代码主分支中。</td></tr><tr><td>5</td><td>产品EOM评审及公告</td><td>向MPC汇报并申请某个产品版本停止销售，经MPC审核通过后发布 停售时间点公告，在该时间点以后，该产品版本不再进行销售。</td></tr></table>

表4-12生命周期阶段流程定义表（续上表）  

<table><tr><td>编号</td><td>活动</td><td>详细内容</td></tr><tr><td>6</td><td>产品EOS评审及公告</td><td>向MPC汇报并申请某个产品版本停止服务和支持，经MPC审 核通过后发布停服时间点公告，在该时间点以后，不再对购买该 版本产品的客户提供服务和支持，客户需在该时间点之前升级到 新版本。</td></tr><tr><td>7</td><td>产品EDCP决策评审及公 告</td><td>EOM和EOS完成之后，向MPC汇报并申请产品版本EOL（生 命周期结束），停止该产品版本的一切开发、营销和销售活动， 并不再提供支持和维护。</td></tr></table>

# 4.4.3引入技术开发流程

![](images/f043467e3c76d6a593f8fb6a50c95a060a9871a0c813113a3fee4d38cba58887.jpg)  
图4-4技术开发流程示意图

通过对比产品开发流程和技术开发流程的示意图，可以发现总体上两个流程的设计上非常相似，不同点在于技术开发流程中裁减掉了发布阶段和和生命周期阶段，增加了迁移阶段，并将迁移的结束点设定为生命周期的结束点。在A公司技术开发流程的设计中，技术开发的最终成果将用于产品开发之中，因此技术开发流程的设计需要考虑与产品开发流程的衔接问题，这个问题主要体现在技术开发的成果迁移上，如图4-5所示：

![](images/4ae3b90590b715acca8ac2a883cdd9ca4406553962c320ffce906cdb07bbd9e0.jpg)  
图4-5技术成果迁移流程示意图

从图中可以看到，技术开发流程的ADCP评审点是迁移阶段的起始点，在使用技术开发成果的产品开发中，第一个到达的ADCP评审点，就是技术开发流程的技术开发项目的结束点。第一个到达的EDCP评审点，就是技术开发流程的生命周期结束点。

# 4.5本章小结

本章首先论证了IPD在大数据分析平台研发项目进度管理改进中的适用性，然后在此基础之上，提出了基于IPD方法的项目进度管理改进思路，并按照预定思路，对A公司的研发管理组织结构和研发管理流程进行了系统性的优化改进设计，实现了对大数据分析平台研发项目管理体系的改进。

# 第五章A 公司大数据分析平台研发项目进度管理效果评价

如前文所述，已经完成了A公司大数据分析平台研发项目进度管理问题的改进设计，本章，论文将对改进方案进行事实验证，分析项目进度管理的改进效果。

# 5.1大数据分析平台研发项目进度改进路径

首先，PDT团队根据项目立项Charter任务书，完成项目里程碑计划的制作，并且根据IPD产品开发流程进行项目工作分解，识别WBS工作包，并确定工作包之间的紧前紧后关系，估算工作包持续时间，汇总形成初版的项目活动清单，并完成初版网络图的编制工作。

然后，在初版网络图的基础之上，结合IPD的并行开发流程思想，对工作包的紧前紧后工作、工作包持续时间进行进一步调优，形成优化后的项目活动清单和网络图。最后，基于项目网络图识别项目的关键路径。

# 5.1.1项目里程碑

本项目的项目里程碑点是根据IPD结构化流程中的技术检查点和业务检查点进行设计，共设计了9个里程碑点。此设计具有非常强的通用性，公司不同类型的项目，只要是基于最新设计的项目研发管理体系进行项目进度管理，都可参照使用，只需调整里程碑点完成要求即可。而且里程碑点即是检查点和业务评审点的设计，能够方便项目进度计划的制定、理解和执行。大数据分析平台研发项目里程碑列表如表5-1所示。

表5-1里程碑列表  

<table><tr><td>里程碑点</td><td>计划完成时间</td><td>完成要求描述</td></tr><tr><td>TR1</td><td>2022-08-20</td><td>完成产品包需求汇总和确认。</td></tr><tr><td>TR2</td><td>2022-09-30</td><td>完成产品概要设计、技术方案设计、CBB选择和立项方案、输出产品规格列 表。</td></tr><tr><td>TR3</td><td>2022-12-20</td><td>完成产品版本规划、产品详细设计、产品集成方案、产品测试方案、产 品业务计划、市场业务计划。</td></tr><tr><td>PDCP</td><td>2022-12-30</td><td>撰写汇报PPT，通过PDCP业务决策评审。</td></tr><tr><td>TR4</td><td>2023-07-30</td><td>完成软件详细设计和代码开发工作，并通过代码评审与静态代码质量检测。</td></tr><tr><td>TR5</td><td>2023-09-15</td><td>完成子系统单独测试和系统集成测试，完成产品文档开发。</td></tr><tr><td>TR6</td><td>2023-9-30</td><td>完成产品验收和灯塔项目试用验收。</td></tr></table>

表5-1里程碑列表（续上表）  

<table><tr><td>里程碑点</td><td>计划完成时间</td><td>完成要求描述</td></tr><tr><td>ADCP</td><td>2023-10-20</td><td>撰写汇报PPT，通过ADCP业务决策评审。</td></tr><tr><td>GA</td><td>2023-10-30</td><td>完成产品发布检查，召开产品发布会，发布大数据分析平台第一个可售卖版 本。</td></tr></table>

# 5.1.2工作分解结构

项目工作分解结构（简称：WBS）以产品开发流程的各个阶段为基础进行划分，与里程碑点设计保持了统一，各阶段工作包编号也与各个阶段的名称进行对应，例如TR1阶段的编号为1，TR2阶段的编号为2，PDCP的编号为D1,ADCP 的编号为D2。这样的设计能够方便团队成员对工作计划的理解以及团队成员之间的沟通，如图5-1所示。

![](images/3e0a21b112603a803721d5d350b4191170c32fa9cc5a7bb5c60a69234647714a.jpg)  
图5-1工作分解结构示意图

# 5.1.3项目活动清单

项目活动清单主内容包括活动编号、WBS编码、工作名称、期望时间t、乐观时间a、悲观时间 $\mathbf { b }$ 、最可能时间 $\pmb { \mathrm { c } }$ 、紧前工作和紧后工作。需要说明的是PDT经理组织PDT核心组成员估算各自领域内活动的a、b、c时间，然后，应用三点估算法来计算项目活动的预期工期t。

$$
\mathbf { t } = \left( \mathbf { a } + 4 \mathbf { c } + \mathbf { b } \right) / 6
$$

大数据分析平台项目活动清单如表5-2所示。

表5-2项目活动清单  

<table><tr><td>编</td><td>WBS</td><td>工作名称</td><td>期望</td><td>乐观时</td><td>悲观时</td><td>最可能</td><td>紧前工作</td><td>紧后工作</td></tr><tr><td>号</td><td>编号</td><td></td><td>时间t</td><td>间a</td><td>间b</td><td>时间c</td><td></td><td></td></tr><tr><td>1</td><td>1</td><td>TR1阶段</td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>2</td><td>1.1</td><td>确定产品包需求</td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>3</td><td>1.1.1</td><td>确定市场需求</td><td>12</td><td>8 7</td><td>16 20</td><td>12</td><td>1.1.1</td><td>1.1.2，1.1.3</td></tr><tr><td>4</td><td>1.1.2</td><td>确定功能需求 确定非功能需求</td><td>12 7</td><td>3</td><td>10</td><td>11</td><td>1.1.1</td><td>2.1.1,1.2</td></tr><tr><td>5 6</td><td>1.1.3</td><td>专利侵权风险分析</td><td></td><td>5</td><td>10</td><td>7 6</td><td>1.1.2,1.1.3</td><td>2.1.1,1.2</td></tr><tr><td>7</td><td>1.2</td><td>TR2阶段</td><td>6</td><td></td><td></td><td></td><td></td><td>2.5</td></tr><tr><td></td><td>2</td><td>产品概要设计</td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>8</td><td>2.1</td><td>总体应用架构设计</td><td>7</td><td>4</td><td>10</td><td>7</td><td>1.1.2,1.1.3</td><td></td></tr><tr><td>9</td><td>2.1.1</td><td>子系统概要设计</td><td>16</td><td>10</td><td>25</td><td>15</td><td>2.1.1</td><td>2.1.2</td></tr><tr><td>10</td><td>2.1.2</td><td>概要设计评审</td><td></td><td>1</td><td>5</td><td></td><td>2.1.2</td><td>2.1.3</td></tr><tr><td>11</td><td>2.1.3</td><td>技术方案设计</td><td>3</td><td></td><td></td><td>3</td><td></td><td>2.2.1,2.3.1</td></tr><tr><td>12</td><td>2.2</td><td>总体技术方案设计</td><td></td><td>4</td><td></td><td></td><td></td><td></td></tr><tr><td>13 14</td><td>2.2.1</td><td>子系统技术方案设计</td><td>6</td><td>6</td><td>10</td><td>6</td><td>2.1.3 2.2.1</td><td>2.2.2,2.2.3</td></tr><tr><td></td><td>2.2.2</td><td></td><td>8</td><td></td><td>12</td><td>8</td><td></td><td>2.2.3</td></tr><tr><td>15</td><td>2.2.3</td><td>技术可行性论证</td><td>10</td><td>6</td><td>18</td><td>10</td><td>2.22</td><td>2.2.4</td></tr><tr><td>16</td><td>2.2.4</td><td>技术方案评审 CBB方案开发</td><td>3</td><td>1</td><td>5</td><td>3</td><td>2.2.2, 2.2.3</td><td>3.1</td></tr><tr><td>17 18</td><td>2.3 2.3.1</td><td>系统分析和CBB</td><td>4</td><td>3</td><td>6</td><td>4</td><td>2.1.3</td><td>2.3.2</td></tr><tr><td></td><td></td><td>能力抽象</td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>19</td><td>2.3.2</td><td>CBB方案设计与评审</td><td>5</td><td>3</td><td>7</td><td>5</td><td>2.3.1</td><td>2.4.1,2.4.2</td></tr><tr><td>20</td><td>2.4</td><td>确定产品规格列表</td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>21</td><td>2.4.1</td><td>确定产品功能清单</td><td>3</td><td>2</td><td>8</td><td>4</td><td>2.3.2</td><td>3.1</td></tr><tr><td>22</td><td>2.4.2</td><td>确定产品非功能特性</td><td>3</td><td>2</td><td>8</td><td>4</td><td>2.3.2</td><td>3.1</td></tr><tr><td>23</td><td>2.5</td><td>制定专利侵权风险 规避方案</td><td>6</td><td>5</td><td>10</td><td>6</td><td>1.2</td><td>3.7</td></tr></table>

表5-2项目活动清单（续上表）  

<table><tr><td>编</td><td>WBS</td><td>工作名称</td><td>期望</td><td>乐观时</td><td>悲观时</td><td>最可能</td><td>紧前工作</td><td>紧后工作</td></tr><tr><td>号</td><td>编号</td><td></td><td>时间t</td><td>间a</td><td>间b</td><td>时间c</td><td></td><td></td></tr><tr><td>24 25</td><td>3 3.1</td><td>TR3阶段 产品版本规划</td><td>3</td><td>1</td><td>4</td><td>3</td><td>2.2.4,2.4.1,</td><td>3.2.1, 3.2.2, 3.3,</td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td>2.4.2</td><td>3.4, 3.5.1, 3.6.1, 3.6.2</td></tr><tr><td>26</td><td>3.2</td><td>产品详细设计</td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>27</td><td>3.2.1</td><td>子系统需求规格说 明书开发</td><td>20</td><td>15</td><td>30</td><td>20</td><td>3.1</td><td>3.2.3</td></tr><tr><td>28</td><td>3.2.2</td><td>子系统原型开发</td><td>20</td><td>15</td><td>30</td><td>20</td><td>3.1</td><td>3.2.3</td></tr><tr><td>29</td><td>3.2.3</td><td>需求规格说明书与 原形评审</td><td>6</td><td>3</td><td>9</td><td>6</td><td>3.2.1, 3.2.2</td><td>3.2.4</td></tr><tr><td>30</td><td>3.2.4</td><td>UI、UE设计</td><td>15</td><td>10</td><td>20</td><td>15</td><td>3.2.3</td><td>D1.1</td></tr><tr><td>31</td><td>3.3</td><td>产品集成方案开发 与评审</td><td>6</td><td>4</td><td>15</td><td>6</td><td>3.1</td><td>D1.1</td></tr><tr><td>32</td><td>3.4</td><td>产品测试方案开发 与评审</td><td>4</td><td>2</td><td>6</td><td>4</td><td>3.1</td><td>D1.1</td></tr><tr><td>33</td><td>3.5</td><td>制定产品业务计划</td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>34</td><td>3.5.1</td><td>产品版本发布计划</td><td>2</td><td>1</td><td>4</td><td>2</td><td>3.1</td><td>3.5.2</td></tr><tr><td>35</td><td>3.5.2</td><td>子系统开发计划</td><td>8</td><td>5</td><td>12</td><td>8</td><td>3.5.1</td><td>3.5.3, 3.5.4</td></tr><tr><td>36</td><td>3.5.3</td><td>产品文档撰写计划</td><td>1</td><td>1</td><td>2</td><td>1</td><td>3.5.2</td><td>3.5.5</td></tr><tr><td>37</td><td>3.5.4</td><td>国产化适配计划</td><td>1</td><td>1</td><td>1</td><td>1</td><td>3.5.2</td><td>3.5.5</td></tr><tr><td>38</td><td>3.5.5</td><td>产品业务计划评审</td><td>1</td><td>1</td><td>2</td><td>1</td><td>3.5.3,3.5.4</td><td>D1.1</td></tr><tr><td>39</td><td>3.6</td><td>制定市场业务计划</td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>40</td><td>3.6.1</td><td>制定产品营销计划</td><td>3</td><td>2</td><td>4</td><td>3</td><td>3.1</td><td>3.6.3</td></tr><tr><td>41</td><td>3.6.2</td><td>制定产品销售计划</td><td>3</td><td>2</td><td>4</td><td>3</td><td>3.1</td><td>3.6.3</td></tr><tr><td>42</td><td>3.6.3</td><td>市场业务计划评审</td><td>1</td><td>1</td><td>2</td><td>1</td><td>3.6.1,3.6.2</td><td>D1.1</td></tr></table>

表5-2项目活动清单（续上表）  

<table><tr><td>编</td><td>WBS</td><td>工作名称</td><td>期望 时间t</td><td>乐观时</td><td>悲观时</td><td>最可能</td><td>紧前工作</td><td>紧后工作</td></tr><tr><td>号 43</td><td>编号 3.7</td><td>制定产品专利和技 术秘密交底书撰写</td><td>2</td><td>间a 1</td><td>间b 3</td><td>时间c 2</td><td>2.5</td><td>D1.1</td></tr><tr><td></td><td>D1</td><td>计划 PDCP汇报</td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>44 45</td><td>D1.1</td><td>撰写PDCP汇报 PPT</td><td>2</td><td>1</td><td>3</td><td>2</td><td>3.2.4，3.3, 3.4,3.5.5,</td><td>D1.2</td></tr><tr><td></td><td></td><td>PDCP评审会</td><td></td><td>1</td><td></td><td></td><td>3.6.3,3.7</td><td></td></tr><tr><td>46 47</td><td>D1.2 4</td><td>TR4阶段</td><td>1</td><td></td><td>2</td><td>1</td><td>D1.1</td><td>4.1</td></tr><tr><td>48</td><td>4.1</td><td>需求分解与分配</td><td>2</td><td>1</td><td>3</td><td>2</td><td>D1.2</td><td>4.2.1,4.3,4.4</td></tr><tr><td>49</td><td>4.2</td><td>子系统详细设计与</td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>50</td><td>4.2.1</td><td>开发 数据库设计</td><td>3</td><td>2</td><td></td><td>3</td><td>4.1</td><td></td></tr><tr><td>51</td><td>4.2.2</td><td>接口设计</td><td>2</td><td>1</td><td>4 4</td><td>2</td><td>4.2.1</td><td>4.2.2 4.2.3</td></tr><tr><td>52</td><td>4.2.3</td><td>系统详细设计评审</td><td>2</td><td>1</td><td>3</td><td>2</td><td>4.2.2</td><td>4.2.4</td></tr><tr><td>53</td><td>4.2.4</td><td>前后端代码开发与</td><td>90</td><td>70</td><td>110</td><td>90</td><td>4.2.3</td><td>4.2.6</td></tr><tr><td>54</td><td>4.2.5</td><td>自测 前后端联调</td><td>90</td><td>70</td><td>110</td><td>90</td><td>4.2.3</td><td>4.2.6</td></tr><tr><td>55</td><td>4.2.6</td><td>代码质量评审</td><td>10</td><td>6</td><td>12</td><td>10</td><td>4.2.4,4.2.5</td><td>5.1</td></tr><tr><td>56</td><td>4.3</td><td>测试用例开发与评审</td><td>15</td><td>10</td><td>25</td><td>15</td><td>4.1</td><td>5.1</td></tr><tr><td>57</td><td>4.4</td><td>产品市场文档开发</td><td>7</td><td>5</td><td>10</td><td>7</td><td>4.1</td><td>5.7</td></tr><tr><td>58</td><td></td><td>TR5阶段</td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>59</td><td>5</td><td>功能测试</td><td>15</td><td>10</td><td>20</td><td>15</td><td>4.2.6,4.3</td><td>5.2,5.3,5.4</td></tr><tr><td>60</td><td>5.1 5.2</td><td>性能测试</td><td>15</td><td>10</td><td>20</td><td>15</td><td>5.1</td><td>5.5</td></tr><tr><td>61</td><td>5.3</td><td>稳定性测试</td><td>15</td><td></td><td></td><td>15</td><td></td><td></td></tr><tr><td>62</td><td>5.4</td><td>集成测试</td><td>15</td><td>10 10</td><td>20 20</td><td>15</td><td>5.1 5.1</td><td>5.5 5.5</td></tr></table>

表5-2项目活动清单（续上表）  

<table><tr><td>编 号</td><td>WBS 编号</td><td>工作名称</td><td>期望 时间t</td><td>乐观时 间a</td><td>悲观时 间b</td><td>最可能 时间c</td><td>紧前工作</td><td>紧后工作</td></tr><tr><td>63</td><td>5.5</td><td>安全测试</td><td>15</td><td>10</td><td>20</td><td>15</td><td>5.2,5.3, 5.4</td><td>5.6</td></tr><tr><td>64</td><td>5.6</td><td>测试报告评审</td><td>5</td><td>4</td><td>8</td><td>5</td><td>5.5</td><td>6.1</td></tr><tr><td>65</td><td>5.7</td><td>产品技术文档开发</td><td>3</td><td>2</td><td>4</td><td>3</td><td>4.4</td><td>6.2</td></tr><tr><td>66</td><td>6</td><td>TR6阶段</td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>67</td><td>6.1</td><td>产品开发成果验收</td><td>5</td><td>4</td><td>8</td><td>5</td><td>5.6</td><td>6.2</td></tr><tr><td>68</td><td>6.2</td><td>输出可交付产品包</td><td>2</td><td>1</td><td>3</td><td>2</td><td>5.7,6.1</td><td>6.3</td></tr><tr><td>69</td><td>6.3</td><td>灯塔项目测试</td><td>15</td><td>10</td><td>20</td><td>15</td><td>6.2</td><td>D2.1</td></tr><tr><td>70</td><td>D2$</td><td>ADCP汇报</td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>71</td><td>D2.1</td><td>撰写汇报PPT</td><td>2</td><td>1</td><td>3</td><td>2</td><td>6.3</td><td>D2.2</td></tr><tr><td>72</td><td>D2.2</td><td>ADCP评审会</td><td>1</td><td>1</td><td>2</td><td>1</td><td>D2.1</td><td>7.1</td></tr><tr><td>73</td><td>7</td><td>GA发版</td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>74</td><td>7.1</td><td>产品发布检查</td><td>2</td><td>1</td><td>3</td><td>2</td><td>D2.2</td><td>7.2</td></tr><tr><td>75</td><td>7.2</td><td>产品发布会</td><td>2</td><td>1</td><td>3</td><td>2</td><td>7.1</td><td></td></tr></table>

# 5.1.4项目网络图与关键路径

活动清单中的工作包之间存在着比较复杂的依赖和制约关系，并且存在很大的调优空间，虽然在活动清单中定义出了紧前紧后工作关系，但由于活动较多，关系复杂，导致项目管理人员无法看清，更不用说去进一步的进行调优，因此还需要绘制出项目网络图，基于网络图去调优，最终再通过优化后的网络图识别项目的关键工作和关键路径，把握制约项目进度的主要矛盾，为项目进度管理实施提供依据。大数据分析平台的项目网络图如图5-2所示。

![](images/177ffdc3c64bce46e8484314b5d769fd9b2f532c85545e37204937527c17ebb0.jpg)  
图5-2项目网络示意图

图中加粗的箭线是项目的关键路径，因此本项目的关键路径即为：

A—E—G—H—I—K—M—S—W—Y—Z—n—t—u—v—w —x—y—(A)—(E)—(I)—(L)— (M)—(O)—(Q)—(R)—(S) —(T)—(U) —(M)。

# 5.2项目进度控制保障措施

项目进度控制是项目管理中至关重要的一部分，它涉及到对项目的进展和执行过程进行监控、调整和管理，以确保项目按时完成、在预算内达到预期目标。大数据分析平台研发项目的进度控制保障措施从类型上可以分为4类，即基于绩效管理与激励、基于角色与分工和基于决策评审点。

# 5.2.1基于绩效管理与激励

在项目进度控制中，绩效管理与激励是不可忽视的因素，在确保项目顺利推进和团队保持高效的过程中扮演着重要角色。绩效管理是指对项目团队成员的工作绩效进行评估、监控和反馈的过程。通过绩效管理，项目经理能够了解每个团队成员的工作表现，及时发现问题和瓶颈，并采取相应的措施来解决[46]。这有助于保持项目进度的稳定和准确性。绩效考核通过激励、评估和奖励机制，能够推动团队成员在IPD框架下更积极地参与合作，为项目进度管理提供动力和支持。同时，基于IPD的项目进度管理要求跨职能的协同合作，而绩效考核可以激发跨职能合作的意愿，形成双向的积极影响。大数据分析平台研发项目的绩效管理与激励设置包括以下五个方面的内容：

（1）考核形式。大数据分析平台研发项目的绩效考核分为项目阶段考核与基于季度/年度考核两种形式。PDT经理、TDT经理、PDT核心组成员、TDT核心组成员、PDT扩展组成员、TDT扩展组成员同时参与以上两种形式的考核。项目阶段评估基于项目成员的绩效承诺，对阶段性成果进行评价。主要依据项目研发管理流程中的决策评审节点进行评估。评估结果和建议将作为职能部门主管绩效考核的重要参考。季度/年度考核是根据公司统一安排的评估时间，用于对员工进行绩效评估。职能部门领导参考项目阶段考核的输出，同时结合员工部门日常工作的成果输出，按季度或年度对员工进行考核。

(2）考核对象及考核内容。考核对象包括职能部门经理、PL-IPMT成员、PDT成员和TDT成员。具体的考核内容如下：

职能部门经理：评估的焦点在于对PDT和TDT团队的支持程度，评估会从数量和质量两个方面展开，最终的评估结果将与其年度绩效评价相结合。

PL-IPMT成员：绩效评估建立在个人承诺的基础上，包括对项目目标的支持，评估结果反映了个人承诺的履行情况。

PDT成员：绩效评估建立在个人承诺的基础上，包括对项目目标的支持，评估结果反映了个人承诺的履行情况。

TDT成员：绩效评估建立在个人承诺的基础上，包括对项目目标的支持，评估结果反映了个人承诺的履行情况。

(3）绩效管理流程。大数据分析平台研发项目的绩效管理流程涵盖了绩效计划、绩效执行与指导、绩效评估以及沟通反馈这三个流程阶段，通过持续的监测、评估和改进，建立起一种正向循环，以不断提升绩效和业务结果。这种循环在项目管理中尤其重要，它可以帮助团队持续改进并逐步实现更高水平的绩效。

![](images/759bd6a4c7b3a4aa707e00f059ce01b85368b2068074695e33fee4e16a7d6dd6.jpg)  
图5-3绩效管理流程示意图

首先，绩效计划阶段是承诺绩效目标的阶段。在这个阶段，部门主管、项目主管与员工会结合当前的工作重点进行充分沟通，共同制定员工的绩效目标和工作改进方向，为每个团队成员设定明确的工作目标和预期结果，确保他们了解自已的职责和任务。

其次，在绩效执行与辅导阶段，当员工个人绩效计划确定之后，部门主管需要辅导员工达成绩效目标，通过定期的进度报告、工作成果评估和沟通，项目经理可以对团队成员的工作绩效进行监控和测量，收集相关绩效指标数据。该阶段部门主管需要在部门内建立双向沟通制度，项目主管需要在项目组中建立双向沟通制度，主要包括周/月例会制度、工作周报、工作日报等。准确记录对员工工作进行评估的参考依据。必要时可以调整绩效考核指标，以保障项目进度计划实施的良好氛围。

最后，在绩效评分与沟通反馈阶段，项目主管根据收集到的信息，在项目阶段考核中对员工进行客观评价。部门主管根据收集到的多方面信息，在季度/年度考核中对员工进行客观评价。并就考核结果与员工进行沟通，鼓励优点，指出改进的地方，并在必要时提供培训和支持，同时可以作为下一个绩效考核期考核计划制定的参考。

(4）绩效结果阶段。在该阶段将对员工的绩效进行评分，并根据评分划分绩效等级，最后根据绩效等级实施奖惩措施。

大数据分析平台研发项目的指标评分采取5分制规则，不同性质的指标采取不同的计算方式。详细内容如表5-3所示。

表5-3绩效评分规则列表  

<table><tr><td>完成情况</td><td>定量指标评分</td><td>定性指标评分</td></tr><tr><td>实际值&lt;门槛值</td><td></td><td>0</td></tr><tr><td>门槛值&lt;实际值≤目标值</td><td>（（实际值-门槛值）/（目标值-门槛值））*（3-1）+1</td><td>[1.00,3.00]</td></tr><tr><td>目标值&lt;实际值</td><td>（（实际值-目标值）/（挑战值-目标值））*（5-3) +3，最高5分封顶</td><td>[3.00,5.00]</td></tr></table>

指标得分 $=$ 指标评分 $^ *$ 相应权重

绩效得分 $= \sum$ 指标得分。

完成绩效评分后，将根据评分与绩效等级的对应规则进行绩效等级划分，大数据分析平台研发项目的绩效得分与绩效等级对应规则参见表5-4。

表5-4绩效评分规则列表  

<table><tr><td>绩效得分(X)</td><td>绩效等级</td></tr><tr><td>4.50≤X≤5.00</td><td>S</td></tr><tr><td>3.50&lt;X&lt;4.50</td><td>A</td></tr><tr><td>2.50≤X≤3.50</td><td>B</td></tr><tr><td>1.00≤X&lt;2.50</td><td>C</td></tr><tr><td>X&lt;1.00</td><td>D</td></tr></table>

基于绩效评估结果，建立奖惩机制，将绩效评估结果与员工的年终绩效奖金、项目奖金、薪资与职级调整等等挂钩，鼓励团队成员的出色表现，或者在存在问

题时采取必要的纠正措施。

(5）激励是推动团队成员付出更多努力的重要手段。有效的激励能够提高团队成员的工作动力和投入，从而有助于项目的顺利推进。激励方法可以包括以下几点：

首先，设立奖金、奖品或其他形式的奖励，以表彰出色的工作绩效和贡献。

其次，提供职业发展机会。例如，提供培训、晋升和发展机会，让团队成员看到自己在项目中的成长和前景。

再次，给予员工认可与赞赏。及时公开赞扬团队成员的优秀工作，让他们感受到自己的价值和重要性。

最后，通过团队文化建设，营造积极向上的团队文化，让团队成员感受到合作、支持和归属感。

需要注意的是，绩效管理和激励都应该建立在公平和透明的基础之上。团队成员应该清楚了解绩效评估的标准和方法，激励措施应当公平对待每个人，以避免不公正和不满情绪的产生。

# 5.2.2基于角色和分工

基于角色和分工的项目进度控制是一种重要的项目管理方法，它将项目成员的角色、责任和分工明确化，以促进任务的有效推进和项目进度的控制。在项目中，每个成员的角色和职责需要清晰明确。基于角色的进度控制强调将不同的任务和工作分配给合适的角色，确保每个成员知道自己的任务和责任。项目进度控制需要明确每个成员的责任范围，确保每个人明白自己在项目进度中的作用。这有助于避免任务滞后和责任不清的情况。

PDT经理负责整体的项目进度控制和监管，协调各团队成员的进度和任务。

进度管理员负责收集、整理和维护项目进度数据，生成报告和图表，以便PDT经理进行分析和决策。

团队成员负责按时完成自己的任务，及时更新项目管理系统中的任务进度，以便PDT经理监控项目进展。

# 5.2.3基于决策评审点

基于决策评审点的项目进度控制是一种系统性的方法，它强调及时评估、调整和沟通，以确保项目按计划进行，最终实现项目目标。这种方法有助于增强项目的可控性、适应性和成功交付的可能性。如前所述，在产品研发管理流程中设立了业务决策评审和技术决策评审两类决策审查节点，在每个决策评审点，团队会对项目的目标进行评估，检查进度是否与计划一致，是否达到了预期的目标。如果存在偏差或问题，可以在此时调整项目计划，确保项目在后续阶段能够更好地执行。决策评审点提供了识别风险和问题的机会。团队可以在这些节点上检查项目的健康状况，发现潜在风险，并及时采取措施进行管理，避免问题在后续阶段扩大影响。

# 5.3组织结构改进效果分析

A公司引入IPD方法进行项目研发组织结构的改进设计，为公司的项目进度管理带来了积极影响，具体如下：

首次，改进影响项目进度的组织结构难题。借助构建跨部门项目团队，明确定义团队职责、成员构成以及团队合作关系等，针对性的对A公司项目研发过程中出现的跨部门沟通障碍、项目利益相关方的分歧、权责不明确以及重复开发的问题进行改进。

然后，提升项目进度的整体协同效率。A公司以往项目的研发管理常常存在各部门之间信息传递不畅、合作不充分的问题，导致项目进度受阻。而基于IPD方法建立跨部门的集成团队，打破了传统组织的界限，促使各个专业领域的人员能够更紧密地协同工作。在这种集成的团队结构下，各专业领域的知识和经验可以更充分地交流和共享，问题得以更迅速地解决，从而推动项目进度的顺利进行。

最后，够促进团队的创新。跨部门团队MPC、PL-IPMT、PDT和TDT的成员构成包括不同领域的专家，形成一个多元化的团队。这样的团队结构有助于各专业领域之间的知识交流和经验分享，从而激发创新的灵感。在项目进度改进方面，创新意味着更高效的方法和更快速的解决方案，有助于项目进度的提升。

# 5.3.1打破部门壁垒

基于IPD理论建立的研发管理组织结构强调团队成员的全面参与和共同决策。在以往项目的组织结构中，决策往往由高层管理人员独立制定，项目团队成员的意见难以得到充分尊重。然而，IPD方法鼓励团队成员在项目决策中发挥主动作用，从而增强了团队的归属感和责任感。团队成员的全面参与和共同决策使得问题能够更快速地得到解决，推动了协同效率的提升。

达成了资源整合的目的。大数据分析平台项目团队整合了不同部门的人员、技术、信息等资源，释放了更大的协同效应，这比传统的部门分工合作方式更高效。通过优化资源在项目不同阶段和任务上的配置，提高了项目进度计划的可行性。

突破了部门的局限性。跨部门产品线团队的建立，突破了部门的局限视角，从项目整体利益出发考虑问题，避免了部门利益的约束，从不同视角发现项目进度的潜在风险，并快速决策应对这些风险。

# 5.3.2促进公司研发项目创新发展

新的研发管理组织结构设计强调多学科融合，促进知识交流。A公司以往项目的组织结构往往在不同部门之间形成壁垒，导致知识孤立，限制了创新的可能性。然而，IPD方法将不同领域的专家融合到一个团队中，打破了这种界限。各个领域的专家可以共同交流经验和知识，激发创新的思维。在项目进程中，这种跨学科的知识交流为问题的解决提供了更多的可能性，从而促进了创新的发展。

鼓励跨职能合作，创造多元化的思维碰撞。新的研发组织结构将不同职能的人员集成到一个团队中，使得不同背景和角度的人能够紧密合作。这种多元化的团队结构为创新创造了更多的机会。不同职能的人员在问题解决过程中会带来不同的思考方式和方法，从而产生新颖的创意。这种思维碰撞有助于团队从传统的思维模式中突围，创造出更具创新性的解决方案。

# 5.3.3提高了设计共用性

A公司原本是没有独立的CBB建设与管理团队的，这造成了前文提到的“重复造轮子"的现象出现。而在大数据分析平台项目中，项目团队设计开发的CBB数量达到了6个，包括统一部署子系统、流式计算引擎、统一查询引擎、AI引擎、知识图谱引擎和全文检索引擎，这些CBB不仅可以用于支持大数据分析平台项目，也可以支持公司其它产品项目的研发。MPC任命由TDT团队负责CBB的研发与统一管理工作，TDT团队参与大数据分析平台的设计过程，与产品开发团队共同进行CBB能力的识别、选择、抽象提取和设计，并全权负责CBB组件的代码研发与管理工作。这种融合IPD 设计共用性思想的组织架构设计，对整体项目进度管理产生了诸多改进的效果：

第一，缩短设计周期。使用可复用设计，大幅减少重复工作，加快了设计进度。

第二，减少开发时间和成本。CBB可以直接应用于新项目，无需从头开始开发。大数据分析平台设计中的AI引擎、知识图谱引擎和全文检索引擎便是抽取于A公司的知识智能平台的CBB组件，不用从头开始构建这些组件能力，实现功能模块、子系统和技术在不同产品之间的共享和重用，缩短了开发时间，降低了单一产品的研发成本[47]。

第三，提升了项目质量。在产品设计方面，注重产品整体的设计，CBB组件的设计考虑了全局因素，从而增强了整体设计的质量，降低了因设计瑕疵而导致的返工。在产品开发方面，CBB组件通常具备更高的质量标准，广泛的应用也意味着它们在各种环境中经受了考验，因此有助于提升整个项目的品质水准。

第四，降低了研发风险。降低了产品设计风险，CBB组件基于模块化和标准化设计，变更更加可控，不会引发系统性风险。降低了开发风险，CBB已经在其他项目中经过测试，因此它们通常是可靠和稳定的。通过使用这些经过验证的组件，降低了引入错误和故障的风险，有助于确保项目按时交付。

# 5.4研发管理流程改进效果分析

A公司引入IPD方法进行研发管理流程的改进设计，为公司的项目进度管理带来了积极影响，具体如下：

首先，改进影响项目进度的研发管理流程问题。通过引入市场管理流程、重构公司产品开发流程和引入技术开发流程的研发流程改进措施，改进A公司项目研发过程中出现的缺乏市场调研、缺乏技术规划、缺乏有效的决策评审设计、缺乏系统性研发流程阶段划分和缺乏协调和整合机制的问题。

此外，改进项目全生命周期的规划和管理。公司以往的项目研发管理常常偏向于项目执行阶段，忽视了项目前期规划的重要性，进而影响了项目的整体进度。然而在新的研发管理流程的设计中，开始阶段就注重全面规划，早期识别潜在问题，制定相应策略。这种前瞻性的规划能够有效减少后期因规划不周导致的延误，从而明显提升项目进度的稳定性。

# 5.4.1提升产品生命周期内的风险管控

提升产品生命周期内的风险管控。基于IPD的研发流程优化强调风险管理，注重在项目全生命周期内识别和应对风险。IPD方法通过在早期阶段就开展风险评估和管理，能够帮助团队提前识别可能的风险，并采取相应措施进行应对。这种风险管理的方法有助于降低项目风险，避免可能的延误和成本超支，从而提高了项目全生命周期的管理质量。

# 5.4.2提升产品研发规划质量

公司以往的项目一般是在执行阶段投入更多的资源，而在规划阶段则显得相对匆忙。新的研发管理流程设计则强调早期规划，注重项目开始阶段的全面规划。这种规划包括了项目目标、范围、风险等多个方面，能够帮助团队提前识别问题，避免后期的调整和变更，从而提高项目的规划质量和管理效率。

做好产品规划的前提是需要做好产品的市场管理。A公司原本就设有市场管理部门，只不过市场管理部门以往主要服务于产品销售，并不参与产品研发。之前的产品在设计开发之前，并没有进行充分的市场调研，导致了产品缺乏明确和具有前瞻性的研发规划。在大数据分析平台项目中，运用IPD流程管理理念，将市场管理流程引入到研发管理流程之中，通过将市场管理流程与产品开发流程紧密结合，在项目立项之前进行充分的市场洞察，深入了解市场需求、客户期望、竞争局面以及市场动向，从而引导产品研发团队进行科学的研发策划。这样的举措有助于研发团队在项目规划初期就确立明确的目标，制定有效的策略，合理分配资源，为后续项目进度管理提供了重要的支持和指引。

# 5.4.3提升研发管理流程的适应性

大数据分析平台项目的研发管理流程中引入了DCP决策评审和TR决策评审的设计，将这两种评审模式与项目流程阶段设计和研发活动紧密结合，在项目关键环节设置评审点，可以帮助团队及时总结阶段工作，从进度、目标、规划、质量和风险等各方面回顾阶段工作情况，识别阶段工作中存在的问题，并针对存在问题，提出改进措施，避免了项目问题的积压。这种及时的反馈和调整能力使得研发项目管理流程更具适应性，有助于确保项目在不同阶段都朝着正确的方向发展，并提高项目的成功率，保证项目按时交付。

# 5.5本章小结

本章将前一章节提出的项目进度管理改进方案进行了实施验证，设置了项目进度控制保障措施，并从组织结构和研发管理流程两个维度进行了项目进度改进效果的分析。

# 第六章 结语

本章将对前文所述研究内容进行总结，强调论文所揭示的重要发现和结果。此处将突出IPD在A公司大数据分析平台研发项目进度管理中的影响，以及相应的成果和挑战。

# 6.1总结

以IPD方法为理论基础，着眼于以IPD为核心的A公司研发项目管理体系改进，本文对A公司大数据分析平台研发项目进度管理难题进行了深入研究和分析，随后提出了针对该公司研发进度管理改进的解决方案。本文主要完成的研究内容如下：

首先，对A公司大数据分析平台研发项目进度管理现状进行调研，提炼出项目进度管理的关键问题，并针对每个问题进行了深入分析，为后续运用IPD理论进行项目进度管理优化设计提供事实依据。

其次，论证得出IPD对于A公司项目进度管理改进来说具有极高的适用性的结论。之后运用IPD理论进行项目进度管理的优化设计，涵盖以下四个方面：1）优化项目组织结构：设立跨部门管理团队MPC、PL-IPMT、PDT和TDT，明确团队的职责权限、成员构成以及各成员的角色职能；2）整合产品市场管理流程至研发管理流程中；3）重构产品开发流程：明确产品开发流程的各个阶段划分和活动内容，确立阶段完成的标准和目标；4）将技术开发流程纳入研发管理流程之中。通过构建以IPD为核心的研发管理框架，以期改善项目进度管理问题。

再次，针对前文提出的改进方案进行实施验证，开展里程碑识别、编制工作分解结构、估算活动持续时间并编制项目活动清单、绘制项目网络图和识别项目关键路径的工作。然后为项目进度计划的执行设置多种控制保障措施，包括设置绩效管理与激励、设置项目进度管理员角色、设置业务决策评审点和技术决策评审点等控制保障措施，保障项目进度计划的顺利实施，实现IPD 理论在大数据分析平台研发项目进度管理的实施落地。

最后，对大数据分析平台研发项目进度管理的改进方案实施效果进行分析，主要从组织结构改进和研发管理流程两个维度进行分析。

基于以上完成的研究内容，得出本文的研究结论如下：

(1）基于IPD的研发管理组织结构优化对项目进度改进效果明显。通过成立跨部门项目团队，并对项目组织结构进行系统化定义，改进了以往项目中存在的组织结构问题，打破部门壁垒，提高了产品设计共用性。基于IPD方法，提升了项目团队的协同效率，促进了公司研发项目创新发展，显著提高了项目进度管理的创新性和可控性。

(2）基于IPD的研发管理流程优化对项目进度改进效果明显。基于IPD方法，本文对A公司大数据分析平台研发项目的研发管理流程问题进行了针对性的改进设计，在完成问题改进的同时，提升了项目生命周期内的风险管控，提升了项目规划的质量以及研发管理流程的适应性，显著降低了项目进度管理的复杂性，提升了项目进度管理的稳定性和效率。

(3）综合以上两个结论，可以得出：基于IPD搭建研发项目管理体系是对项目进度管理进行改进的有效手段。

# 6.2展望

鉴于我对项目进度管理以及IPD方法的理解还不够全面，仍有许多方面需要深入学习。因此，在本文的研究内容中存在一些待改进之处，特别是以下三个方面：

第一，需求管理作为影响项目进度的关键因素，在本文的研究中并未被涉及，如何在多元、协作、集成的环境下，更好的定义、管理和满足项目需求，如何设计需求管理的过程和方法，可以作为之后的研究方向。

第二，在基于IPD方法的绩效管理方面还存在许多需要进一步改进的内容，例如部门绩效管理与项目绩效管理结合实施的设计需要进一步细化，例如如何更合理的进行绩效管理的指标设计，如何有效使用绩效管理工具促进IPD方法在项目管理中落地实施等。

第三，研发管理流程可以进一步改进，比如阶段输出物模板定义，在固化阶段活动的基础之上，通过固定模板，补充阶段完成标准。

参考文献  
[1]中国信息通讯研究院．中国数字经济发展研究报告[R]．北京：中国信息通讯研究院，2023：3-4.  
[2]张宇.基于关键链的项目进度管理方法研究[D]·大连：大连理工大学，2023.  
[3]（美）项目管理协会著.项目管理知识体系指南(PMBOK指南)第6版[M]·王勇，张斌，译．北京：电子工业出版社，2018.  
[4]罗亮．核电设计进度计划中定义活动的分析研究[J]·科技视界，2019(07)：255-256.  
[5]王长峰等．IT项目管理案例与分析[M]·北京：机械工业出版社，2008.  
[6]李铭．濮阳市住建局双代号网络图绘制及优化系统设计与实现[D]·成都：电子科技大学，2012.  
[7]恽洁．QSY公司海外基地仓储信息系统项目进度管理研究[J]．铁路采购与物流，2019(02)：36-38.  
[8]孙延岭，李建华，蓝彦．IPD在国有企业的实施介绍[J]．科技管理研究，2013， 33(14).  
[9]田雪松．基于IPD模式的G公司产品开发管理流程优化[D]·广州：广东工业大学，2022：39-41  
[10] 李阳阳．CM通信公司集成产品开发关键流程研究[D]·北京：北京交通大学，2018：21.  
[11]汉捷咨询．什么是IPD 咨询？[EB/OL] . http://www.higet.com.cn/ServerStd_1814.html, 2021-03-10.  
[12]张连营，杨丽，高源．IPD 模式在中国成功实施的关键影响因素分析[J·项目管理技术，2013，11(06).  
[13] 梁鸣.集成产品开发(IPD)探讨[J].科技管理研究，2010，30(17)：120-122.  
[14]胡志军．光电子器件公司基于集成产品开发（IPD）的产品开发管理研究[D]·南昌：南昌大学，2014.  
[15]葛星，黄鹏.流程管理理论设计工具实践[M]·北京：清华大学出版社，2011：129.  
[16] Lientz B. P, Larssen L. Risk Management for IT Projects: How to Deal withOver 150 Issues and Risks[M] · Butterworth-Heinemann, 2006.  
[17]王淑清.TC公司产品项目研发管理的应用研究[D]·天津：天津大学，2008：23.  
[18] Kelley J E. Critical-path planning and scheduling[J] · Operations Research,1959,9(3): 296-320.  
[19]商宝强．计划评审技术在海洋工程项目进度风险评估中的应用——以K平台项目为例[J]．项目管理技术，2023，21(04)：150-153.  
[20] 韩鹏凯．浅谈电力基建工程项目进度管理[J]．电力建设，2004(12)：42-44.  
[21]杨爱华，刘禄韬．合理设置项目里程碑的五个依据[J]．项目管理技术，2008(08): 67-69.  
[22] 杜桂伏．论工程项目进度控制[J]．中国城市经济，2011(23)：283-284.  
[23] 姜欣廷，任若楠．数据中心暖通系统建设交付关键路径分析[J].数字通信世界，2021(09)：35-37.  
[24] 杜军.HY公司产品研发项目进度管理改善研究[D]·广州：华南理工大学，2017.  
[25]曾世华.QS公司阿车研发项目的进度管理研究[D]·大连：大连理工大学，2017.  
[26]贾锋歌．QH公司产品研发项目进度管理方案优化研究[D]·西安：西北大学，2017.  
[27]陆波．基于IPD流程的研发类项目管理信息化研究[J]·科技创新导报，2021, 18(19): 85-87.  
[28]王文轩．基于IPD的A公司金融卡芯片研发进度管理研究[D]·北京：北京交通大学，2019.  
[29]马飞，刘德智，李毅斌等．基于IPD 的体系化研发管理平台研究[J]·现代制造工程，2013(07)： $1 2 \mathbf { - } 1 5 \mathbf { + } 2 6$ .  
[30] 韩冰．基于产品设计的研发项目管理体系研究D]·北京：首都经济贸易大学，2017.  
[31]王光翔．基于IPD的N公司新产品研发管理改进研究[D]·杭州：浙江理工大学，2018.  
[32] 沈凤斌．北方股份：基于项目制的研发管理[J]．企业管理，2018(05)：30-32.  
[33]李茜．国外产品创新研发流程综述[J]．现代企业，2018，12：59-60.  
[34]保罗·特罗特．创新管理和新产品开发（第2版）[M]．北京：中国人民大学出版社，2005：45-49.  
[35] Faysal , Khalaf. Time and Cost Based Product DevelopmentProcess[D] · Detroit Michigan: WaNe State University, 2005.  
[36]张彦卿．研发管理的流程设计与优化[J]·上海信息化，2006：571．□  
[37]杨青，唐尔玲．研发项目产品与流程架构的跨领域集成与优化[J·系统工程理论与实践，2014(06)：167-174.  
[38]王宗彦．基于IPD的系列产品快速设计系列研究[J]·《中北大学学报（自然科学版）》，2012，16(4)：638-641.  
[39] Ching-Hsiang Chen, Chien-Yi Huang. The synergy of QFD and TRIZ for solving

EMC problems in electrical products-a case study for the Notebook PC[J] . Journal of Industrial and Production Engineering, 2015, Vol. 32(5): 311-330.

[40] Stevens M. Project Management Pathways. Association for Project

Management[M]. APM Publishing Limited, 2002.  
[41]丁丽娟.基于IPD 的研发管理体系设计[D]·南京：南京大学，2019：4-5.  
[42]杨志凌．基于IPD的Y公司新产品研发管理改进研究[D]·广州：华南理工大学，2020：21-58.  
[43]李耀．基于IPD的W公司产品研发管理改进研究D]·桂林：广西师范大学，2020：23-25.  
[44]张伟，刘英为.数字化转型对跨国企业创新绩效的机制研究[J].宏观经济研究,2023(06):86-100.  
[45] 王春．关于制造型企业实施项目化管理的几点思考[J·杭氧科技，2017(2)：5.  
[46] 刘文杰.水利工程财务管理的风险问题与策略[J].财经界,2023(19):90-92.  
[47] 罗伯特，G.库伯.新产品开发流程管理[M]·北京：机械工业出版社，2003.