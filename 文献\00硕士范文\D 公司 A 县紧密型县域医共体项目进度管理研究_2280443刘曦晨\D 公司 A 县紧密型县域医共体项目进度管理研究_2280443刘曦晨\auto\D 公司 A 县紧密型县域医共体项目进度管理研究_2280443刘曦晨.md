# 学 位 论 文

# D 公司 A 县紧密型县域医共体项目进度管理研究

作 者 姓 名 ： 刘曦晨  
作 者 学 号 ： 2280443  
指 导 教 师 ： 张川 教授东北大学工商管理学院  
申请学位级别：硕士 学 科 类 别 ： 管理学  
学科专业名称：工商管理（专业学位）  
论文提交日期：2024 年 11 月 28 日 论文答辩日期： 2024 年 12 月 8 日  
学位授予日期：2025 年 1 月 答辩委员会主席： 郭伏 教授  
评 阅 人 ： 郭伏 教授

# 东 北 大 学

2024 年 12 月

# A Thesis for the Degree of Master of Business Administration

# Study on Project Progress Management of Tightly Integrated County-level Medical Alliance in A County of D Company

By Liu Xi Chen

Supervisor: <PERSON> Chuan Professor

# Northeastern University December 2024

# 独创性声明

本人声明，所呈交的学位论文是在导师的指导下完成的。论文中取得的研究成果除加以标注和致谢的地方外，不包含其他人已经发表或撰写过的研究成果，也不包括本人为获得其他学位而使用过的材料。与我一同工作的同志对本研究所做的任何贡献均已在论文中作了明确的说明并表示谢意。

学位论文作者签名：

日 期：2024 年 12 月 16 日

# 学位论文版权使用授权书

本学位论文作者和指导教师完全了解东北大学有关保留、使用学位论文的规定：即学校有权保留并向国家有关部门或机构送交论文的复印件和磁盘，允许论文被查阅和借阅。本人同意东北大学可以将学位论文的全部或部分内容编入有关数据库进行检索、交流。

作者和导师同意网上交流的时间为作者获得学位后：

半年 □ 一年□ 一年半□ 两年□学位论文作者签名： 导师签名：签字日期：2024 年 12 月 16 日 签字日期：2024 年 12 月 16 日

# 摘要

随着信息技术在医疗领域的广泛应用，县域医疗信息化建设成为提升基层医疗服务效率和质量的重要途径。D 公司在 A 县启动的紧密型县域医共体的软件开发项目，旨在通过开发和实施一套集成化的医疗信息管理系统，以支持县域医疗机构之间的信息共享和资源整合。本文围绕 D 公司 A 县紧密型县域医共体项目的进度管理展开研究，探讨了该项目在进度管理方面的策略、方法以及实际效果，为医疗信息化项目的实施提供了理论支持和实践经验。D 公司负责该系统的需求分析、设计、开发、测试等工作，涉及的进度管理任务涵盖项目的全过程。论文详细阐述了进度管理的理论框架和实际应用，确保项目按计划推进和达到预期目标的关键环节。

研究回顾了进度管理的基本理论，包括项目进度计划理论、进度控制理论等，并结合项目的特点，探讨了如何通过科学的进度管理方法来保证项目的顺利推进。在进度计划制定方面，项目分解为多个阶段和活动，并制定里程碑和时间表，这里涉及活动之间的依赖关系、资源需求和风险因素，从而确保项目各阶段的顺利衔接和协调推进。在进度控制方面，论文探讨了进度监控与调整的策略。通过建立实时进度监控系统，跟踪项目的实际进展情况，并与计划进度进行对比，能够及时发现并解决进度偏差问题。研究提出了相关技术来调整优先级，以确保项目能够在预定时间内完成。

通过对 D 公司 A 县紧密型县域医共体项目的进度管理研究，合理的进度计划和及时的调整措施不仅提高了项目的执行效率，还有效保障了项目目标的实现，为D公司的其他医疗信息化项目的管理提供了的参考。

关键词：项目管理；进度管理；软件开发；关键链技术

# Abstract

With the wide application of information technology in the medical field, the construction of county medical information systems has become an important way to improve the efficiency and quality of primary medical services. Tight county-level medical community project initiated by Company D in County A aims to support information sharing and resource integration among county medical institutions through the development and implementation of an integrated medical information management system. This paper focuses on the progress management of D Company's software development project for a close-knit county medical community in County A. It discusses the project's strategies, methods and actual results in terms of progress management, providing theoretical support and practical experience for the implementation of medical informatisation projects. D Company is responsible for the system's requirements analysis, design, development and testing, and the progress management tasks involved cover the entire project process. The paper details the theoretical framework and practical application of progress management, the key link to ensuring that the project progresses as planned and achieves its desired goals.

The research reviews the basic theories of progress management, including project schedule planning theory and progress control theory, and discusses how to ensure the smooth progress of the project through scientific progress management methods based on the characteristics of the project. In terms of schedule planning, the project is broken down into multiple stages and tasks, and milestones and timetables are set. This involves the interdependence of tasks, resource requirements and risk factors, so as to ensure the smooth connection and coordinated progress of each stage of the project. In terms of progress control, the paper discusses the strategies for progress monitoring and adjustment. By establishing a real-time progress monitoring system to track the actual progress of the project and compare it with the planned progress, deviations in progress can be identified and resolved in a timely manner. The study proposed relevant technologies to adjust priorities in order to ensure that projects can be completed on schedule.

Through the study of the progress management of Tight county-level medical community project in County A of Company D, a reasonable progress plan and timely adjustment measures not only improve the efficiency of project implementation, but also effectively ensure the achievement of project goals, providing a reference for the management of other medical informatisation projects of Company D.

Key words: Project management; schedule management; software development; Key chain technology

# 目 录

独创性声明.

摘要..

Abstract.. II

# 第 1 章 绪论 ..

1.1 研究背景 .  
1.2 研究目的及意义..  
1.2.1 研究目的...  
1.2.2 研究意义.

1.3 国内外研究现状. 3

1.3.1 国内研究现状. 3  
1.3.2 国外研究现状..

1.4 研究内容及方法.. 6

1.4.1 研究内容. 6  
1.4.2 研究方法.

# 第 2 章 项目进度管理的相关理论. . 10

2.1 项目进度管理的概念 10

2.1.1 项目进度管理的定义. 10  
2.1.2 项目进度管理的作用.. 10  
2.2 项目进度管理的过程 11  
2.3 项目进度管理的方法与工具. 13

# 第 3 章 D 公司 A 县紧密型县域医共体项目概述. 19

3.1 D 公司简介. 19  
3.2 项目背景情况. 19  
3.2.1 医共体背景介绍. 19  
3.2.2 D 公司 A 县紧密型县域医共体项目介绍. . 20

# 3.3 项目组织与目标.. . 21

3.3.1 项目组织结构.. 21  
3.3.2 项目目标. . 23  
3.3.3 项目主要建设内容.. . 24  
3.4 项目开发概况. . 26  
3.5 项目进度制约因素分析.. . 27  
3.5.1 外部进度制约因素.. . 27  
3.5.2 内部进度制约因素.. 29  
第 4 章 D 公司 A 县紧密型县域医共体项目的进度计划编制..... 31  
4.1 项目里程碑计划. . 31  
4.2 项目工作分解与责任分配 . . 31  
4.2.1 项目工作分解.. . 31  
4.2.2 项目工作责任分配. .. 32  
4.3 作业的逻辑关系及时间估算. .. 33  
4.3.1 作业逻辑关系确定.. . 33  
4.3.2 作业时间安排依据. . 34  
4.3.3 活动资源安排. . 35  
4.3.4 项目活动时间估算. . 36  
4.3.5 确定关键路径.. . 38  
4.4 基于关键链技术的进度计划优化. .. 43  
4.4.1 任务工期压缩. .. 43  
4.4.2 优化项目关键路径. .. 44  
4.4.3 项目关键链缓冲区的计算 . 46  
4.4.4 缓冲区管理 . . 50

# 第 5 章 D 公司 A 县紧密型县域医共体项目进度控制 . 52

5.1 项目进度控制过程 52  
5.2 项目进度监测. 53  
5.2.1 每日站立会 . 53  
5.2.2 项目周报和日报. . 53  
5.2.3 前锋进度线法.. . 54  
5.2.4 挣值分析法 . . 55  
5.3 项目进度偏差分析与调整 . 56  
5.3.1 项目进度偏差分析.. . 56  
5.3.2 项目进度调整. . 58  
5.4 项目进度计划实施保障. . 59  
5.4.1 组织保障. . 59  
5.4.2 激励保障. . 60  
5.4.3 技术保障.. . 61  
5.4.4 知识经验保障. 61  
第 6 章 结论与展望. .. 63  
6.1 结论 .. . 63  
6.2 展望 .. . 63  
参考文献.. . 65

# 第 1 章 绪论

# 1.1 研究背景

在目前医疗卫生体系改革的广泛背景下，提升基层医疗服务质量和效率已成为政策关注的核心内容之一。D 公司 A 县紧密型县域医共体项目是对这一政策的积极响应，旨在促进 A 县公卫体系的有效发展。通过促进整合县域内的相关医疗资源，构建有效的医疗网络，提高医疗服务的可及性和质量，优化资源配置，来减轻患者就医负担。作为卫生体制改革的一项重要内容，以资源整合和信息共享来不断提高基层医疗卫生服务能力和管理水平。该项目由D公司在A县落地，涵盖县级医院、乡村卫生院、村卫生室和卫生服务中心在内的众多医疗机构都参与其中。该项目涉及系统设计、软件开发、数据集成以及信息系统的开发。在软件开发阶段，进度管理是项目成功的重要因素之一，直接影响到各项工作的顺利进行。

“千县工程”是中国政府于 2018 年启动的一项重大举措，研究借鉴“千县工程”重点任务，构建适用于县级医院的综合能力评价指标体系，旨在为相关政策实施跟踪评估提供参考依据，助推县级医院和县域医共体高质量发展[1]。县域医共体建设在该工程中占有重要地位，通过建立县域医共体，整合各级卫生资源，构建合理的卫生服务网络，尤其注重加强基层医疗卫生机构的建设和配套，提高基层医生和医务人员的培训水平和技术能力，以提高整体服务质量。项目注重在医患之间建立强有力的沟通机制，加强医患关系管理和调解，在提高县域居民对基层医疗卫生服务的信任度和满意度的同时，简化就医流程，简化服务的各个环节，提高就医效率，使县域居民更方便地接受医疗服务。随着医疗信息化的推广，该项目部署了电子病历系统和远程医疗服务等平台，以提高医疗服务的可及性。然而，医疗信息化涉及的内容复杂多样，给项目进度管理带来了巨大挑战。在开发医疗信息系统的过程中，既要保证能够满足不同层级医疗机构的实际需求，又要灵活应对快速变化的技术要求、政策法规等，因此，制定科学合理的进度计划，建立有效的进度管理机制，及时管理潜在的进度风险，将是确保项目顺利进行的重要保障。在实际工作中，项目管理人员面临着任务复杂、资源分配不均、进度延误等诸多困难，严重影响了项目的整体效果和效率。

在这种情况下，深入研究 D 公司 A 县紧密型县域医共体项目的进度管理就显得尤为重要和有意义。从中发现和分析项目中进度领域的主要问题，通过系统化的进度管理研究，概括出有效的进度控制方法，为今后类似项目的实施提供有益的参考。不仅可以提高 D 公司 A 县紧密型县域医共体项目的部署效果，还可以为全国各县建立紧密型医共体提供宝贵的经验和理论依据。随着医疗信息化的不断深入，如何在保证项目质量的前提下对项目进度进行有效管理，是医疗信息系统开发领域的一个重要研究课题。该研究课题不仅对未来的医疗信息化项目至关重要，而且有可能为整个医疗行业提供先进的理念和技术。

# 1.2 研究目的及意义

# 1.2.1 研究目的

D 公司 A 县紧密型县域医共体项目与项目进度管理的实践相结合，解决进度管理中遇到的问题和挑战对于提高紧密型县域医共体发展的可持续性至关重要。如果软件开发过程中采取的进度管理方式不科学、不完善，不仅无法起到上述的作用，甚至会给企业发展产生阻碍，带来不利影响。产品质量的高低会影响客户的满意度，影响企业自身品牌形象，甚至影响企业未来发展。通过设计和实施有效的管理策略和机制，促进跨界合作和资源整合，可以大大增强项目的协同效应，促进项目的健康发展。在项目进度管理相关问题进行详细分析的基础上，制定更符合实际需求和可行性的优化措施和提供有价值的建议。本研究将这些优化策略和技术应用到 D 公司 A 县紧密型县域医共体项目的管理中，结合实地调研和案例分析，验证其有效性和可行性，为项目的进度安排提供切实可行的指导。

通过优化进度管理可以有效提高公司项目的整体管理能力，丰富理论范围，改善项目管理中进度领域的组织和协调，进而提高项目交付的能力。在合理的开发期限内，降本增效，从多个方面证明该项目结合项目进度管理理论和方法的必要性。在开发过程中结合 D 公司软件开发现状，确保该项目进度管理能够顺利完成，改善公司的软件开发质量、开发效率，进而提高公司的竞争力。还更能改善病人的就医体验，满足县域内居民对优质医疗服务日益增长的需求。

# 1.2.2 研究意义

在“十四五”期间，D 公司积极响应国家重大战略，特别是国家相关政策文件中提出的县域内发展医共体的目标和路径。在该大背景下，A 县医疗卫生体制改革和发展始终受到相关政策和指导性文件的影响。其中在《关于全面推进紧密型县域医疗卫生共同体建设的指导意见》等文件中，明确规定了加强县域医疗卫生服务能力建设、优化医疗资源配置、提高基层医疗卫生服务质量的具体任务和要求。二三级医院要通过专家派驻、专科共建、临床带教、远程协同、科研和项目协作等方式，提升县域医共体服务能力和管理水平[2]。

作为医疗系统改革的一项重要工作，在 A 县的紧密型医共体项目旨在建立一个以整合和优化现有医疗资源的系统，从而提高医疗卫生服务的范围和质量。然而，该项目的有效实施仍面临诸多挑战。例如，参与的机构众多，医疗资源相对分散，各机构之间的管理体制也存在差异，这些都给项目的进度管理带来了困难。不同层级的组织和卫生部门之间需要进行有效的合作和互动，以协调各方利益，统一行动，确保项目实施的一致性，这本身就是一项重大挑战。其次，鉴于该县紧密型医共体项目的复杂性和多样性、以及严格的交付期限，进度管理面临着相当大的压力。此外，由于医疗卫生服务的特殊性，进度管理需要充分考虑服务的连续性和安全性，这显然增加了管理的难度。同时，当前紧密型县域医共体项目在资源配置、信息共享、合作共建等方面还存在一定差距和不完善的地方，这些难题给项目的进度和绩效管理的效果带来了一定的质量影响。此外，受 A县当地经济的发展、医疗资源的分布和相关法规支持的程度等，进度管理难度也较大。在这种情况下，深入项目的进度管理问题，分析其根本原因和影响因素，提出有针对性的优化策略和方法，是促进 D 公司 A 县紧密型县域医共体项目顺利实施，提高医疗服务水平，推动医疗卫生体制改革进程的重要举措。因此，本研究具有较高的现实意义和价值，而且符合我国医疗体制改革的发展趋势和紧密型县域医共体项目的实际需要。

# 1.3 国内外研究现状

# 1.3.1 国内研究现状

早在 60 年代，华罗庚就引进了项目管理的思想[3]。并将这些理论知识有效地应用到许多实际项目中，取得了非常明显的效果。同一时期，老一代科学家钱学森等人致力于推广一些项目管理的理论和方法，为我国早期工程建设项目管理理论的形成奠定了基础[4]，对系统工程的理论和方法做出了重要贡献。彭宇行等研究人员对资源约束现金流调度进行了深入研究，并建立了相关数学模型[5]。这些科研成果不断促进工程项目进度管理水平的提高，特别是复杂工程项目的实际应用。在模糊环境分析方面，胡志根提出了不确定性预测模糊办法和灰色理论[6]。这一理论对于调整传统网络规划方法的应用模式，同时提高在不确定环境下的适应性具有重要的理论意义。运用模糊优选模型建立了一种综合定量和定性分析的造价管理成效评价方法[7]。

随着国内项目管理事业迅速发展，更多的许多学者和专家从不同角度对项目进度管理理论和方法进行了广泛研究，以解决进度不可预测和各种影响因素带来的问题。袁剑波等针对公路桥梁施工的技术与管理特点，使用网络分析法对公路桥梁施工的安全风险进行评价[8]。这些研究不仅深化了项目管理的理论深度，也为各种项目实践提供了支持。同时，马国丰等针对进度管理过程中的不确定且多因素的动态环境，提出柔性管理的概念，通过总结柔性评价的研究成果，建立了进度管理柔性的评价指标体系[9]。胡晨等人在基于多资源约束和活动调整优先级的关键链识别方法研究利用灰色关联分析，综合考虑直接活动链中资源、工期和进度的影响，确定活动协调的优先级[10]。通过将多个单元的识别结合到一个共同的识别过程中，他们能够识别出项目中的关键链。这是在复杂环境中展示项目管理优势的一种方式。

在真正的项目管理中，必须分析与项目进度相关的风险，以促进相关方的沟通和协调，项目中由于利益不同，会面临不同的进度安排任务，多方协调将是进度安排的重要环节。赵野在工程项目的进度控制方面提出对进度节点和重要任务进行监控，对非关键任务合理利用空余时间，进一步强调进度管理的合理性和有序性[11]。李帅芳等总结了使用 Crystal Ball 软件进行项目管理风险分析的步骤,并结合实例对项目管理风险进行分析,获得了有效的分析结果[12]。此外，林勇和马士华根据贝叶斯风险概率的提出并建立了风险预警和应急系统模型，分析风险敏感程度，提高项目进度管理的能力[13]。

信息技术行业始终伴随着我国项目管理的发展，近年来不断涌出更多的信息技术工具，随着对项目管理研究的不断深入，不断提升着进度管理的效率。

# 1.3.2 国外研究现状

泰勒被公认为“科学管理之父”，他 1911 年经典著作《科学管理原理》的出版是现代管理理论的里程碑事件。在这本书中，泰勒指出流程管理在企业经营中发挥着至关重要的作用。在这本书中，他强调了系统化管理的必要性，并强调通过建立完善的流程管理体系，企业可以实现生产率的大幅提高和成本的有效降低，同时还能有力地支持和促进技术创新。泰勒的理论不仅从科学的角度深刻揭示了管理规则的内在逻辑，而且为如何提高企业效率提供了切实可行的方法论建议。他的著作对后来的管理实践产生了深远的影响。例如，著名商人亨利·福特运用他的理论成功建立了一条高效的汽车生产线，彻底改变了流水线生产模式。这种模式极大地提高了汽车生产的质量和效率，彻底改变了传统制造业的面貌，使福特公司成为全球标杆企业。当时，流程管理的定义还相对狭窄，主要集中在对生产和运输的具体环节进行管理。它包括特定任务的优化和组织，如处理原材料、组装零件和组织物流，但在理论和实践方面，流程管理仍处于起步阶段[14]。1917年，美国科学家亨利·甘特发明了甘特图，这一工具在项目进度安排方面取得了突破性进展，为现代高效工业发展奠定了基础，并成为后来工业发展的重要基石。甘特图以其清晰直观的视觉效果，帮助管理者轻松了解项目进度和时间安排[15]。

美国海军特别计划办公室于 1958 年开发的计划评审技术（PERT）方法[16]，主要用于北极星导弹潜艇的计划，PERT 基于不确定性估计，使用概率的方法来估算时间，PERT 技术后来扩展到工作分解结构(WBS)。20 世纪 60 年代，随着大型工业和工程项目的增加，项目管理的理论和实践不断深化。标准和方法开始广泛应用于项目管理，1969 年美国成立了项目管理协会（PMI），推广项目管理的最佳实践，1987 年出版了PMBOK指南，将项目管理的知识和流程编辑成册。在整个 20 世纪 80 年代，信息技术的飞速发展彻底改变了项目管理的工具和软件，计算机辅助项目管理（CAPM）工具（如 Microsoft Project）的出现将项目管理的过程具象化。工具的出现使项目管理过程更加高效和准确。与此同时，项目管理的模式也从传统的瀑布模型转向了更加灵活的敏捷方法。敏捷方法尤其适用于需要对变化做出快速反应的领域，如软件开发过程。

20 世纪90年代初，麻省理工学院的迈克·哈默发起了一项重组业务流程的创新计划。该计划不仅以传统的科学管理理念为基础，还注重不断优化产品设计和制造流程。哈默的理论强调，技术和管理创新可导致业务流程的根本性变革以及生产和管理组织的根本性改进。这种以流程为导向的方法不仅大大提高了企业效率，而且增强了市场竞争力。因此，自 20 世纪90年代中后期以来，新产品开发管理研究进入了快速发展阶段，美国大型企业，尤其是汽车和发动机制造行业，采用流程变革的比例达到 $7 5 \%$ ，1994年在此领域发表的相关论文超过 700篇，充分反映了学术界对这一课题的重视和兴趣。1994 年，库珀的研究成果推动了新产品开发领域的进一步发展。他研究了新产品开发过程的独特框架，该框架有别于传统规范，重点关注更加灵活和复杂的过程对承担风险、参与决策和获取知识的影响。库珀强调，产品开发流程的优化必须转向以结果为导向，通过识别客户需求、整合所有相关职能、选择和优化流程管理来提高产品开发的成功率。值得注意的是，流程再造不仅在企业界产生了重大影响，在学术界也掀起了前所未有的波澜，推动了相关理论的不断发展和演变。项目管理协会（PMI）在 1996年出版了完整版的《项目管理知识体系指南》（PMBOK），为项目管理奠定了系统的理论基础[17]。1997 年，以色列出生的物理学家和商业权威埃利亚胡·戈德拉特在其著作《关键链》中首次描述了关键链项目管理技术。它强调在项目管理中需要考虑不确定性，以便更好地分配资源和时间。

2016 年，詹姆斯·哈灵顿基于关键链理论开发了业务流程改进（BPI）持续改进模型。该模型强调，应持续优化和改进业务流程，并将流程管理的概念应用到现实世界的管理实践中[18]。通过实际反馈，企业可以及时发现并弥补管理漏洞，实现持续优化和改进。2020 年，美国项目管理协会（PMI）将《项目管理知识体系指南》（PMBOK Guide）更新至第 6 版，项目管理的基本框架包括项目启动、规划、执行、监控以及收尾五个基本过程组[19]，有效提高了项目管理的整体绩效。在这个复杂的管理过程中，及时性尤为重要，因为项目并不是要无限期地持续下去，而是要在预定的时间内完成任务[20]。从这个意义上说，进度管理的重要性怎么强调都不为过。戈特利布博士倡导的关键链项目管理技术考虑到了学生综合症和墨菲定律的影响，特别是在项目顺序中加入了项目缓冲区（PB）、汇入缓冲区（FB）和资源缓冲区（RB），从而使项目顺序更加切合实际[21]。将它们包括在内，可以使进度规划更切合实际。这些创新的管理理念和方法被广泛应用于资源受限的项目中，有效提高项目的成功率和管理效率。

# 1.4 研究内容及方法

# 1.4.1 研究内容

第 1章为绪论。作为现代医疗管理的一种新的组织形式，医共体项目正受到越来越多的关注。随着基层医疗卫生体制改革的不断深入，如何有效实施紧密型县域医共体项目对于提高医疗质量、降低医疗成本、合理利用资源的作用日益明显。本章叙述了 D 公司在 A 县项目进度管理中面临的主要问题，并探讨了如何有效推动项目实施以实现预期目标，进而服务和支持A县的医疗健康体系发展。此外，本章还回顾了国内外的相关研究，分析本论文引入研究的意义和局限性，阐明本论文引入研究的内容、方法和技术路线。

第 2章为研究理论基础。它介绍了项目进度管理的基本概念和特点，为后续的进度管理研究提供了理论依据。对项目进度进行系统的计划、组织、管理和控制，其过程具有独特性和复杂性。本章为进度计划的创建和管理提供了指导，并强调了进度管理对项目成功的重要性。

第 3 章关注 D 公司 A 县紧密型县域医共体项目的背景分析。本章详细介绍了该项目的背景、所处环境，特别是 A 县紧密型县域医共体建设的必要性和紧迫性，并明确了项目面临的需求和挑战。还介绍了项目的具体目的和范围、预期成果和医共体项目的组织机构和团队组成，以确保项目管理的有效性。并分析影响项目进度的各种因素，如内外部进度制约因素，为后期的进度计划编制和控制提供参考。

第 4章着重讨论项目进度计划的编制。确定项目的关键点和重要事件是本章的基础。将整个项目的目标分解为可管理的工作包，并对每个工作包进行详细描述，使任务清晰明了，便于管理。同时，明确每个参与者的职责和任务分配，以促进团队合作和责任制。在优化和调整项目进度方面，采用动态协调机制，确保及时应对项目过程中可能出现的变化和风险，保证项目顺利实施。

第 5章为项目进度控制。本章介绍了进度管理的流程，包括进度监控、问题识别和变更管理，以有效管理项目进度。建立进度管理机制，定期对项目进度进行监控和评估，根据实际情况进行调整和优化，确保整体进度的稳定性。此外，引入资源分配、问题应对、风险管理等具体保障措施，确保项目进度，使项目能够按时按质完成。

第 6章为研究结论与展望。对研究结果进行总结，探讨进度管理在紧密型县域医共体建设中如何赋能，为今后的研究和实践工作提供了坚实的基础和方向。同时，也指出了本研究的局限性，并对进一步的研究进行了展望，希望今后的研究者能够继续探索进度管理与医疗项目发展之间的关系，促进县域医疗卫生事业的发展。

本文将 D 公司 A 县紧密型县域医共体项目的实际进度管理情况进行结合，开展了以下三个方面的研究：

$\textcircled{1}$ 分析 A 县紧密型县域医共体项目的项目管理情况，从项目进度管理入手，讨论分析项目从启动到最终验收交付中不同环节遇到的问题和困难。这种分析不仅是监控项目进度的有用参考，也是有效执行项目的基础。详细列出项目在执行阶段所面临的各种障碍，深入讨论其原因，并提出解决这些问题的建议和想法，使项目按计划进行。

$\textcircled{2}$ 影响软件开发项目编制项目进度计划的情况有很多，以 D 公司 A 县紧密型县域医共体项目为案例，在项目进度计划编制时，确认了项目的目的和范围，明确项目的里程碑，运用了工作分解结构、责任分配矩阵，得到任务活动的逻辑关系，依托关键链技术、甘特图等工具，并根据 D公司组织过程资产和事业环境因素的显性知识和专家顾问的隐性知识来进行模拟和预测，估算出项目中不同阶段各任务活动的工期，从而制定出项目的进度计划[22]

$\textcircled{3}$ 通过研究得到 D 公司 A 县紧密型县域医共体项目有效的控制进度方法。分析项目在执行过程期间的进度情况，以按期按质交付为出发点。采用了进度压缩等方法，并为项目设置关键链缓冲区来优化进度计划[23]，定期进行项目进度的数据采集来监控，保障项目进度处于合理状态。另外，为项目提供了组织保障、激励保障、技术保障和知识经验保障，更确保了项目的顺利完成。

通过以上三个方面的深入研究，本文为 D 公司 A 县紧密型县域医共体项目的进度管理提供切实的指导。

# 1.4.2 研究方法

（1）文献研究法

文献研究法是通过收集和整理与项目管理和进度规划相关的文献，如期刊论文、学位论文、研究报告等。这种方法可以为本研究提供良好的理论基础，有助于分析和了解现有的研究成果和理论框架，为进一步的研究工作奠定基础。

# （2）现场调研法

在掌握相关理论知识后，对项目进行实地观察、访谈、问卷调查，以获取第一手资料。结合D 公司A县紧密型县域医共体项目的特点，详细分析 D公司在项目进度安排方面所面临的问题，分析比较各种项目进度管理方法的优缺点，包括不同项目进度管理方法的适用条件和局限性，为制定项目进度计划提供支撑。

# （3）经验总结法

本人在 D 公司工作了 5 年多，前后参与了 10 多个大大小小的软件开发项目，在此期间，通过学习和吸收工作经验，培养了自己发现问题和解决问题的能力，并根据所学的知识和经验，试图找到一种有效的解决方案来有效处理 D 公司软件开发项目延期的问题。

通过结合这些方法，本论文旨在为项目进度管理提供一个更加系统的分析框架和解决方案，以支持 D 公司 A 县紧密型县域医共体项目的顺利实施。同时，这些方法的应用也为其他类似项目的管理提供了宝贵的基准和经验。通过不断完善进度管理体系，可以提高项目管理的整体效率，最终实现项目的成功。

本文使用的技术路线图，如图 1.1 所示。

![](images/deb7d1abae9252c8b205f02cbba04fabc9814ba8256669260e40c6518e27f172.jpg)  
图1.1技术路线图  
Fig. 1.1 Technological Roadmap

# 第 2 章 项目进度管理的相关理论

# 2.1 项目进度管理的概念

# 2.1.1 项目进度管理的定义

项目进度管理是在项目活动中综合运用进度管理领域的知识、技能、工具和方法，在有限的资源和时间内实现项目的目标和要求。科学的规划、高效的组织、密切的协调和严格的管理是保证项目顺利进行的重要因素。其核心概念包括制定详细的进度计划，明确项目各任务和活动的时间安排，设定关键路径和里程碑以跟踪项目进展。进度管理首先要对项目任务进行分解，合理规划每项任务开始和完成所需的时间，同时考虑可用资源和任务之间的依赖关系；其次，项目进度管理要监控实际进度与计划进度之间的差异，及时发现并解决差异，确保项目按计划进行；第三，进度管理包括动态调整计划，以应对项目执行过程中的变化和风险，同时保持进度管理计划的灵活性和适应性。有效的进度管理不仅要注重时间管理，还要加强与团队和利益相关方的沟通，通过利益相关方之间顺畅的信息流和协调，确保项目目标的实现。

项目进度管理的一个主要特点是以时间为基础，旨在按计划完成项目。项目进度管理创建了一个详细的计划，其中定义了任务的时间安排，并确定了关键路径和里程碑，从而可以清晰地跟踪项目进度。进度管理应是动态的，根据实际进度和变化调整计划，以应对潜在的风险和挑战。有效的进度管理还注重任务分解和资源优化，以简化任务顺序和资源分配，提高项目效率。此外，进度管理需要实时监控和反馈，以便及时发现和解决进度差距，并确保团队和利益相关者之间的有效沟通，来保持进度的透明度和一致性。

# 2.1.2 项目进度管理的作用

在当今复杂多变的商业环境中，项目进度管理扮演着至关重要的角色。它不仅是确保项目按时完成的关键手段，还对项目的成功实施有着多方面的深远影响。

项目进度管理有助于明确项目目标和阶段任务。通过制定详细的项目进度计划，可以将整体项目目标分解为具体的、可操作的阶段性任务。例如，在软件开发项目中，项目进度管理可以明确需求分析、设计、编码、测试等各个阶段的具

体任务和时间节点。

项目进度管理能够提高资源利用效率。合理的进度安排可以确保资源在合适的时间分配到合适的任务上，避免资源的闲置或过度使用。以建筑工程项目为例，如果没有有效的进度管理，可能会出现开发人员和设备在某些时段闲置，而在另一些时段又过度紧张的情况。通过精确的进度规划，可以根据项目的不同阶段合理调配人力、物力和财力资源，实现资源的优化配置，降低项目成本。

项目进度管理有助于及时发现和解决问题。例如，在新产品研发项目中，如果发现某个关键技术难题导致进度延迟，项目团队可以及时组织专家进行攻关，或者调整项目计划，优先完成其他不受该问题影响的任务。这样可以将问题的影响降到最低，确保项目能够按计划推进。

项目进度管理还能增强项目团队的协作和沟通。在项目进度计划的指导下，不同部门和岗位的团队成员能够明确彼此的工作关系和时间要求，从而更好地进行协作。同时，进度管理过程中的定期会议和报告机制，也为团队成员提供了沟通交流的平台，有助于及时分享信息、解决问题，提高团队的凝聚力和战斗力。

随着全球化、信息技术和市场经济的快速发展，项目进度管理已成为各行各业管理工作的重要组成部分。除建筑、航空航天、国防等传统行业外，项目进度管理已在教育、金融、医疗卫生、信息技术、公共管理等领域得到广泛应用，成为推动组织变革和创新的重要手段[24]。同时，项目进度管理不断引入现代管理科学的新理论和新方法，并广泛应用于现代信息技术，成为提高组织管理效率和效益的重要手段。在实际项目管理中，不断探索和创新进度管理方法和技术，以适应不断变化的项目环境和需求。

# 2.2 项目进度管理的主要内容及步骤

项目进度管理主要由多个重要方面共同构成。其中包括规划进度管理；对项目活动进行明确的定义，确定各个活动的具体内容和范围；对项目活动进行合理排序，明确活动之间的先后关系；对项目活动所需的资源进行准确估算，确保资源能够满足项目需求；对项目活动的时间进行科学预估，以便更好地规划项目进度；制定详细的项目进度计划，为项目的推进提供明确的路线图；以及进行有效的进度控制，确保项目能够按照计划顺利进行。这几个方面相辅相成、紧密相连，它们共同对项目进行高效的计划、组织、指导和控制，从而形成项目的全过程、动态化管理。通过这样的管理方式，最终能够确保项目按照既定计划完成任务。

项目进度管理流程图,如图 2.1 所示。

![](images/b662363b881b0c13f62fd67662a423d6702a066582d76021facee26e9d2d997e.jpg)  
图 2.1项目进度管理流程图  
Fig. 2.1 Project Progress Management Flowchart

（1）规划进度管理

进度管理计划的首要任务是制定全面的进度管理计划，明确规定如何系统地计划、管理、跟踪和控制项目进度。在此过程中，团队中的每个人都必须了解进度计划的规则和流程。确定要使用的方法和工具，这包括将计划和进度监控流程标准化，以及应用适当的工具。在制定计划时，应详细说明评估进度的标准，设计变更管理流程，并确定报告结构和工具使用规范。适当的规划和工具选择有助于项目团队有效协作，并在项目进展过程中始终专注于项目目标。

# （2）定义活动

确定并定义实现项目目标所需的具体活动。以项目的工作分解结构为起点，将可交付成果分解为具体的任务和活动。这些具体步骤对于实现项目目标至关重要。确保每项活动都有明确的开始时间和结束时间，为今后的规划和管理铺平道路。通过创建活动清单和定义工作包，项目团队可以使项目进度清晰可见并分配责任。

# （3）排列活动顺序

确定活动之间的逻辑联系和依赖关系。网络图用于描述活动之间的相互作用，并回答哪些活动可以并行开展，哪些活动必须按顺序开展的问题。活动之间的依赖关系包括排序关系（“完成-开始”、“开始-开始”等）以及外部依赖关系。正确定义活动顺序不仅有助于确定项目的关键路径，还能确保项目的每个阶段顺利进行，并对整个项目的持续时间产生直接影响。

（4）估算活动持续时间

对每项活动的持续时间进行合理估计。使用历史数据法、专家判断法、类比估算法和三点估算法来估算工期，从而提高估算的准确性。在估算过程中，必须关注业务的潜在风险和不确定性，尤其是业务的复杂性和资源的可用性。准确评估每项活动的持续时间是制定切实可行的时间表的基础，可以有效降低后续延误的风险。

# （5）制定进度计划

考虑到活动顺序、持续时间和资源限制，为项目制定详细的日程安排。这一阶段的主要任务包括制定基本进度表，包括关键路径分析、制定详细进度表和确定项目阶段。进度规划不仅涉及时间表，还涉及到项目的资源分配，以便按计划完成每个阶段。这些不同要素的正式顺序将成为日后管理和监控项目进度的指南。

# （6）控制进度

对照既定进度表，监控和管理项目的实际进度，发现偏差并纠偏。通过系统地报告和监控实际进度，团队可以及时发现问题并采取适当的纠正措施。这包括重新安排任务、调整资源分配和改变活动顺序。系统化的进度控制不仅能确保项目顺利进行并在可接受的范围内，还能及时应对可能影响项目按时完成的因素。

# 2.3 项目进度管理的方法与工具

（1）关键路径法

关键路径法，用于帮助项目经理制定和执行项目计划。它主要通过计算项目任务之间的相互依赖关系以及完成这些任务所需的时间来确定项目所需的最长时间，并确定项目的关键路径[25]。以下概念在关键路径法的应用中起着重要作用：

活动：作为项目一部分，需要执行的具体任务或行动。

前置任务：在开始任务前需要完成的其他任务。

后置任务：任务完成后应启动的其他任务。

总时差：在不影响整个项目工期的情况下，任务可以推迟的时间。

自由时差：表示在不推迟后续任务的情况下，某项任务可以推迟多长时间。

最早开始时间：可以开始任务的最早时间。

最早结束时间：任务完成的最早结束时间最晚开始时间：任务不影响关键路径的可开始的最晚时间。

最晚结束时间：为完成项目而必须完成任务的最后日期。要确定项目的关键路径，可以按照以下步骤依次进行：

$\textcircled{1}$ 确定活动的持续时间和前置条件：针对每项活动，确定活动的持续时间及其所依赖的活动。前一项活动是指在这项活动开始之前必须完成的另一项活动。

$\textcircled{2}$ 绘制网络图：根据每项活动的前后关系，将所有活动绘制成网络图。在该图中，每个活动用一个圆圈表示，活动之间用箭头连接，箭头表示活动的顺序。

$\textcircled{3}$ 计算每个节点的最早开始时间和最早结束时间：每个活动都需要计算任务的最早开始时间加上持续时间和最早结束时间加上持续时间。

$\textcircled{4}$ 计算每个节点的最晚开始时间和最晚结束时间：最后一个任务的最早结束时间等于整个项目工期，而任务的最晚结束时间等于最早结束时间，可以用最晚开始时间减去任务工期，依次得出其余任务的最晚开始时间和最晚结束时间。

$\textcircled{5}$ 计算总时差和自由时差：计算每个任务的最早开始时间、最早结束时间、最晚开始时间和最晚结束时间后，可以计算每个任务的总时差和自由时差，总时差等于最晚开始时间减去最早开始时间。自由时差等于该任务所有后续任务最早开始时间的最小值减去该任务最早结束时间。

$\textcircled{6}$ 确定关键路径：如果任务的总时差为零，则标记为关键路径任务，并将关键任务连接起来，按箭头方向追踪关键路径[26]。

（2）计划评审技术

有助于项目团队估算工作持续时间和制定项目进度表，以及识别和管理潜在的问题和风险。项目经理使用计划评审技术评估项目的可行性，确定完成项目所需的资源和时间，并确定成功完成项目的最佳方法。同时，计划评审技术分析每项活动的持续时间、相互依存性和风险，帮助项目经理制定最佳进度表，使项目按计划进行[27]。

为了使得进度计划顺利进行，计划评审技术还能帮助项目经理识别最长的关键路径，并确定完成项目所需的最长时间。这有助于项目经理识别对项目成功至关重要的任务，并确定如何分配资源和调整进度以满足项目要求。计划评审技术还可帮助项目经理识别和管理潜在的问题和风险，以确保项目按计划进行，并在必要时采取行动解决问题。在计划评审技术中，每项活动的持续时间都是根据三个时间估算来计算的，也称为三点估算：

$\textcircled{1}$ 乐观时间：在最有利的条件下开展活动所需的时间。

$\textcircled{2}$ 最可能时间：在正常情况下开展活动所需的时间。

$\textcircled{3}$ 悲观时间：在最不利的情况下完成活动所需的时间。

三点估算法是一种高效的主动控制方法，它假定每个项目活动的持续时间都遵循 $\beta$ 分布，通过以上三个参数可以计算出活动的期望工期，有了活动工期，就可以在此基础上找到项目的关键路径[28]。

# （3）甘特图

1917 年，亨利·甘特在管理实践中提出了以自己名字命名的甘特图方法。甘特图是一种项目管理工具，通过在时间轴上显示项目任务的进度，为项目经理提供决策和项目进度管理框架。其主要特点是在水平条形图中显示项目任务、开始时间、结束时间和任务持续时间等信息[29]。

甘特图通过在时间轴上显示任务和时间等项目信息，将项目进度和规划可视化。甘特图允许项目经理和相关方有效沟通项目任务和进度，促进相关方之间的交流与合作。在项目执行过程中，项目经理使用甘特图分析项目任务之间的顺序关系，确定项目任务的完成时间，从而确定项目持续时间和里程碑。同时，甘特图还能帮助项目经理了解任务是如何执行的，如何及时发现问题，以及如何解决问题，从而按计划完成项目。

但是，由于甘特图是基于计划任务的时间表，因此无法实时使用，项目经理必须不断更新以确保准确性。使用甘特图管理复杂任务也存在一些挑战。例如，当多个任务之间存在复杂的依赖关系、资源限制等情况时，使用甘特图可能会导致任务混乱。同时，甘特图无法准确预测完成一项任务所需的时间，尤其是考虑到与任务相关的变化和潜在风险。因此，甘特图非常适合小型项目，但任务过多的大型项目可能会变得难以管理，而且可能无法很好地处理资源限制或项目延迟等问题。甘特图与其他工具结合使用会更有效[30]。

（4）里程碑法

项目进度是通过确定项目中的关键节点或重要交付成果（称为里程碑）来管理的。这些里程碑通常代表项目的重要步骤，如完成设计、获得关键审批或实施特定功能。确定所有里程碑，并明确定义每个里程碑的日期。随着项目的进展，定期检查实际进度是否与里程碑一致，并注意任何差异。根据实际进度调整项目计划和资源分配非常重要，采用里程碑方法可以让项目团队更加专注于实现里程碑，同时还能提供明确的检查点来监控进度，从而更有效地评估和管理里程碑。

它还能及时发现和解决潜在问题，确保项目按计划进行。

（5）进度压缩

压缩进度是缩短项目周期的一种有效策略。主要有两种方法：快速跟进和赶工。快速跟进可以让项目经理缩短完成特定任务所需的时间。完成特定任务所需的时间可以通过增加额外资源来缩短，如额外人员、改进设备或加班。成本效益分析是这一过程的重要组成部分，以确保额外资源的成本不会超过项目的预期效益。作为减少执行的任务所需时间的一部分，通常计划采用并行方法，即同时执行多项任务，而不依赖于任务的先后顺序。这种方法可以加快项目进度，但必须仔细分析并行执行的潜在风险，如协调和依赖性管理问题，以确保并行执行不会带来新的问题。如果应用得当，进度压缩可以有效缩短时间紧迫的项目的持续时间，但会增加项目的复杂性和成本，因此必须注意确保项目按时完成，同时保持高质量。

# （6）工作分解结构

工作分解结构是一种项目管理工具，可以将大型项目分解成更小、更易于管理的任务。工作分解结构是一种层次结构，它将项目划分为若干可管理的子任务，每个子任务都相对独立，可单独分配和跟踪，并可分配给负责完成任务的个人或团队。工作分解结构中可包含任何类型的任务，包括具体任务、子任务和里程碑。工作分解结构的层次可以是无限的，但为了便于管理，项目工作分解结构通常由六个或更少的层次组成。

工作分解结构可以根据所提供的服务对工作进行细分。例如，成立电子商务服务公司的项目可以细分为选址、建筑装修、电子商务运营等；电子商务运营可以细分为任务定义、员工招聘、开业前培训、系统实施等。软件开发可按流程细分为需求开发、系统设计、系统编码、系统测试、试运行和验收；需求开发可细分为需求调研、需求分析、规范编写、需求验证和需求确认等。

无论使用哪种方法分解工作，都应该以易于理解、管理和监控的方式分配任务。同时，应将已完成的任务整合在一起，使其与之前的工作水平保持一致，不留空白，也不超出之前的工作水平。工作分解结构可用于计划、监控和控制项目进度，并可帮助项目经理识别和管理关键路径和项目风险。它可以帮助项目团队分配资源和控制预算，确保项目在预算范围内按时交付；它还可以帮助项目团队了解项目的总体目标和具体目标，从而改善团队的沟通和协作。不过，在使用这种方法时，应注意以下几点：

$\textcircled{1}$ 工作分解结构必须完整准确，每项活动的细化程度必须足以进行有效的工作分配和控制，一般为六个层级内的细化程度。$\textcircled{2}$ 在分解项目任务的过程中，要认真考虑不同任务之间的依赖关系，不能随意分解，“前”与“后”之间要有逻辑联系。$\textcircled{3}$ 如果项目工作分为多个部分，则应将每项任务分配给特定的团队成员。分配任务有助于项目管理，并使项目经理更容易监控进度。

# （7）关键链技术

关键链技术是在 20 世纪 80 年代提出的一种基于约束理论的项目管理方法，是对传统项目管理方法的补充和改进。关键链技术认为传统项目管理方法中的关键路径法造成了许多问题，如任务完成时间不确定、资源利用率低等[31]。因此，关键链理论来解决传统项目管理方法的问题。在关键链理论中，关键链是一系列相互依存的任务链，这些任务链受到关键资源的制约。完成关键链中某项任务所需的时间直接影响整个项目的完成时间，其中某项任务的延误会拖慢整个项目的进度。项目完成时间的不确定性往往是由资源限制造成的。当资源充裕时，对任务完成时间的估计会比较准确，但当资源有限时，完成一项任务所需的时间就会有所不同。因此，要管理项目进度，需要识别以下三个缓冲区，分别为:

项目缓冲区（PB）：项目缓冲区是一个基于时间的缓冲区，位于关键链的末端，即项目中最后一项关键任务之后。项目缓冲区的作用是保护整个项目的进度，确保关键任务的延迟不会影响项目结束时间。当项目缓冲区的时间耗尽时，项目就会延迟。

汇入缓冲区(FB)：非关键链任务通常有一定的浮动时间，但如果这些任务出现延迟，可能会影响到关键链任务的按时开始。通过在释放缓冲区分配适当的时间，可以最大限度地减少资源浪费和项目延误。

资源缓冲区（RB）：资源缓冲区是一种基于资源的缓冲区，放置在关键资源的末端，即对项目完成时间有重大影响的关键资源后。资源缓冲区的作用是确保关键资源及时可用，以免资源瓶颈影响项目进度。如果资源缓冲时间用完，关键资源就会延迟，从而影响项目完成时间。

# （8）前锋进度线法

通过绘制一条能够准确反映项目实际进展情况的前锋线。这基于对项目各个工作环节实际完成情况的详细考察和准确记录。在绘制过程中，需要收集和整理大量的实际进度数据，确保前锋线能够真实地反映项目的实际状态。绘制完成后，将这条前锋线与计划进度线进行对比，来判断项目进度的偏差情况。

（9）挣值分析法

在项目执行过程中，按一定的时间周期（如每周、每月）收集实际完成工作的数据，包括已完成任务的数量等，同时记录实际发生的成本。收集数据，计算挣值法的关键参数，即计划价值（PV）、挣值（EV）和实际成本（AC）等。将各阶段的SV值绘制成图表，直观地展示项目进度偏差的变化趋势。若偏差曲线持续在零轴上方，说明项目整体进度控制较好且有超前趋势；若曲线在零轴下方且偏离程度逐渐增大，则需重点关注进度落后问题。

根据挣值分析的结果，采取相应的调整措施。如果进度超前且资源允许，可以适当增加后续工作的任务量或提前安排一些原本计划稍后进行的工作；如果进度落后，则需要分析原因并采取措施，如增加资源投入、优化工作流程、调整任务优先级等。

# 第 3 章 D 公司 A 县紧密型县域医共体项目概述

# 3.1 D 公司简介

D公司是中国领先的 IT解决方案和服务提供商之一。作为一家综合性的 IT企业，D公司致力于提供全面的技术解决方案和服务，涵盖了软件开发、系统集成、IT咨询以及相关行业解决方案等多个领域。D公司的业务涉及金融、医疗、交通、能源、政府等多个行业。通过提供定制化的软件产品和系统集成服务，帮助客户优化业务流程、提升运营效率，并在全球范围内建立了广泛的客户基础。在金融领域，解决方案广泛应用于银行、证券和保险等多个子行业，助力这些领域的数字化转型。在医疗领域，D公司开发了多种医疗信息化解决方案，涵盖了医院信息系统、电子病历系统、影像设备管理等方面，致力于提高医疗服务的效率和质量。交通和能源领域也是重要业务方向，公司通过技术创新和系统集成，推动智能交通和能源管理系统的发展。D 公司在技术研发方面也取得了显著成绩。公司设有多个研发中心，专注于人工智能、大数据、云计算、物联网等前沿技术的研究和应用。通过持续的技术创新，D 公司不断推出符合市场需求的高质量产品和服务，保持了在行业中的竞争优势。此外，D 公司还注重国际化发展，在全球范围内建立了多个分支机构和子公司。通过全球化布局，进一步拓展了国际市场，增强了在全球 IT 服务市场中的影响力。D 公司凭借其雄厚的技术实力和广泛的行业经验，在全球 IT服务和解决方案市场中占据了重要地位。

# 3.2 项目背景情况

# 3.2.1 医共体背景介绍

医疗卫生服务共同体（简称医共体），以县级医院为枢纽，整合县乡医疗卫生资源，形成覆盖县、乡、村的三级服务网络。通过集团化管理，构建服务一体化、利益一体化、责任一体化、发展一体化、信息一体化、文化一体化的共同体，使医疗机构紧密合作，成为“一家人”。该模式旨在优化医疗资源配置，提升基层医疗机构服务能力，让居民享受到更加便捷、实惠、全面的医疗服务。医共体建设是深化医疗卫生体制改革的一项重要研究，对促进医疗卫生事业发展具有重要意义。医共体将通过建立基层首诊、双向转诊、急诊急救等分级诊疗机制和上升环节，整合优化医疗资源，增强基层医疗服务能力，为人民群众提供更好的医疗服务。医共体将通过统一工作安排、资源配置、绩效管理等措施，有效促进医疗资源的使用和共享，提高基层医疗机构服务效率，促进卫生事业均衡发展。

尽管 A 县在卫生和医疗服务方面引入信息技术的工作取得了一定进展，但仍面临许多挑战。主要问题包括：缺乏系统的顶层设计，信息化基础设施薄弱，纵向信息系统因独立建设而互不兼容，难以实现信息互通，形成信息孤岛，严重影响医疗卫生服务信息的整合与交换。因此，推进县域医共体建设，促进医疗卫生工作的下沉和资源的有效配置，对于增强地方的服务能力，实现医疗资源的上下联动，提高整体效率，更好地满足人民群众的健康需求势在必行。

为配合国家关于组建县域医疗卫生共同体的工作重点，A县通过借助云计算、人工智能、大数据、物联网、5G、移动互联网等新技术，提高县域内就诊率（最高可达 $9 0 \%$ ）。整合信息系统，以标准化管理为基础，通过信息技术实现医共体内资源共享和分配，打通医疗服务上下游，实行集团化统一管理，提高基层医疗机构的整体服务能力。同时，该系统确保县级医院与上级医院之间的信息互联互通，全面覆盖互联网医院、区域医技中心、基层医疗服务、家庭医疗等多个应用场景，最大限度地扩大优质医疗资源的服务覆盖面，确保所有需要医疗资源的患者和医生都能得到及时、恰当的帮助，保证医疗卫生工作的有效开展。医疗服务共同体的建设，为通过资源整合、服务提升、技术应用，为实现更高效的医疗服务体系提供了新的方向。它不仅可以促进整个医疗卫生行业的发展，也为普通市民提供更好、更便捷的医疗服务奠定了坚实的基础。未来，随着政策的不断落实和技术的不断创新，县域医共体将在提高医疗服务质量和效率方面发挥更大的作用。

# 3.2.2 D 公司 A 县紧密型县域医共体项目介绍

D 公司 A 县紧密型县域医共体项目于 2024 年 10 月签订合同，交付周期为5 个月，合同金额 1228 万。A 县拥有 2 个县级医院、9 个乡镇卫生院和 56 个村卫生室。这种医共体模式以县级公立医院为龙头，以城市医院为核心，以乡镇卫生院为基础。目的是通过全面整合县域医疗和公共卫生资源，实现县级医疗和公共卫生体系的转型和现代化。将医共体平台、县内医院、县外医疗资源进行有效整合，构建覆盖县、乡、村的综合服务框架。同时，A 县还设立了影像诊断中心、临床检验中心、心电诊断中心、病理诊断中心等各类专科中心，充分整合了县、乡医疗资源。这不仅实现了检查和治疗结果的相互认可，还有效降低了患者的医疗费用。

在基层医疗卫生机构信息化建设方面，A县通过远程医疗、远程教育等方式提升基层医生的服务能力，加强家庭医生签约服务的实施和完善，推动公共卫生服务向更高层次发展，逐步实现医疗卫生由治疗向预防转变。优先推进医保支付方式改革，在“全额预付、差额留存、费用合理分担”的支付模式基础上，发挥医保基金引导医疗服务供需、控制医疗费用的作用，减轻人民群众健康支出的直接经济压力，减轻经济负担。为进一步提高医保管理效率，A县建立了统一的医保管理平台，鼓励药品和医用耗材集中采购，督促落实医疗服务价格调整，推行远程药学服务，提高医保基金使用效率，加强医保经办机构之间的交流。加强医保经办机构交流。医共体通过多层次、多方位的措施，努力为居民提供更加方便快捷的医疗服务。

# 3.3 项目组织与目标

# 3.3.1 项目组织结构

项目组织结构是指公司内部为实现特定目标而进行的部门和层级安排。这种结构决定了人力资源的组合和部署，合理的组织结构可以有效地集中和释放企业的全部能力。常见的企业项目管理组织结构一般包括：职能式组织结构、项目式组织结构、矩阵式组织结构，各组织结构因其特点适用于不同需求和组织文化的企业，其中矩阵式组织结构因其叠加了职能式组织的垂直层次结构和项目式组织结构，能最大限度地发挥两种组织结构的优势同时规避弱点，在多数大中型企业中得到推崇[32]。

D 公司 A 县紧密型县域医共体项目组织结构为矩阵式结构，项目经理可以与其他部门合作，协调其他部门的工作，从而更好地实现项目目标。在平衡矩阵结构中，管理人员来自职能部门，但拥有适当的管理权限，主要负责管理项目的总体目标[33]。

在该项目中，团队由来自开发部、产品部、测试部、销售部和项目管理 PMO等人员组成。这些不同部门的成员共同分析需求、设计界面和编写代码等。团队成员向项目经理汇报工作。项目组织架构图，如图 3.1所示。

![](images/80affaf3407c808f429fb0b159efa58fad6115ae08c7ef2abcb58ce73289316a.jpg)  
图 3.1项目组织架构图  
Fig. 3.1 Project Organization Chart

各部门的主要任务是：

$\textcircled{1}$ 开发部：设计、开发和维护公司的软件系统、平台和产品。其中包括内容的需求分析、系统设计、编码实现和全流程的技术保障。同时关注不同的编程语言和开发工具，持续提升代码质量和性能优化。

$\textcircled{2}$ 产品部：与项目经理密切合作，完成开发产品的用户界面设计、网页和宣传材料并与测试部门合作，验证设计结果的质量。

$\textcircled{3}$ 测试部：有关软件开发的测试计划、测试用例、功能测试、性能测试以及安全测试都需要测试部门来完成。发现缺陷后，开发部门进行修复，执行回归测试来检测相关功能是否正常。测试期间将结合手动测试和自动化测试工具来提高测试效率。

$\textcircled{4}$ 销售部：该部门负责拓展行业客户群，获得外部项目，并向客户提供产品演示。同时，销售部门应向项目开发部门提供客户需求信息，满足计划开发需求，并参与合同签订和后续尾款结算。

$\textcircled{5}$ 项目管理 PMO：制定项目管理标准与流程，协调资源分配，监控项目进展与绩效，评估项目风险，并提供支持与培训以提升项目管理能力。为高层决策提供数据分析和报告，促进战略目标实现。各部门岗位角色的主要任务，见表3.1。

由于这是 A 县的重要项目之一，因此成立了一个临时指挥中心，以确保顺利实施。指挥部由副县长任指挥长，电子政务局局长任副指挥长。指挥部下设项目办公室，由项目管理部门负责人、工程师、技术助理和采购经理组成。他们将与项目经理和项目开发团队密切合作，确保项目取得成功。同时，工程师、技术助理和产品经理将提供技术支持和辅助活动，负责质量控制，并提供后备支持，以确保项目各方面的顺利互动。设计部门和系统开发团队负责提供支持项目执行所需的技术保证。这一中间指挥链的建立不仅提高了项目管理的效率，还确保了项目按计划进行。明确的角色分工和有效的团队合作明确了每个项目成员的职责，促进了所有任务的高效开展。

表3.1各部门岗位角色的主要任务  
Table 3.1 Main tasks of positions and roles in each department   

<table><tr><td>部门</td><td>岗位角色</td><td>主要任务</td></tr><tr><td rowspan="5">开发部</td><td>前端开发工程师</td><td>实现从静态页面的设计到动态交互功能，保证开发 的兼容性和响应性，优化页面加载速度和构建前端</td></tr><tr><td>后端开发工程师</td><td>应用。 负责处理数据存储、业务逻辑和服务器架构，实现 前端与后端的链接。管理数据库的设计与操作，并</td></tr><tr><td>系统工程师</td><td>对系统性能优化、故障排查和相关技术文档撰写。 评估用户需求，选择合适的实现技术，对系统进行</td></tr><tr><td>数据库工程师</td><td>集成、配置管理和性能监测。 选择合适的数据库技术，并编写复杂的查询和存储 过程，优化数据库性能。监控数据库活动，识别和</td></tr><tr><td>UI设计师</td><td>解决数据库问题。 收集客户UI需求，基于此信息设计用户界面原型， 确定视觉稿、交互原型和最终设计方案，确保设计</td></tr><tr><td>测试部</td><td>软件测试工程师</td><td>符合视觉效果。 通过设计和执行测试用例，识别和报告开发中的缺 陷与问题，并分析缺陷的根源，并与开发团队合作</td></tr><tr><td>销售部</td><td>客户经理</td><td>解决问题。 与客户沟通，了解其需求和期望，收集反馈并向项 目团队传达。负责合同签署，处理客户问题，推动</td></tr><tr><td>项目管理PMO项目管理人员</td><td></td><td>销售机会。 负责规划、组织、指导和控制项目从启动到完成的 整个过程。</td></tr></table>

# 3.3.2 项目目标

A县近年来积极利用信息化手段，推动县、乡、村三级医疗机构之间的资源共享与协作，旨在为居民提供一站式、无缝隙的医疗服务。以强县、稳乡、活村为导向，夯实基层医疗卫生体系基础，促进资源配置和上下游医疗机构有效整合，提升全县医疗服务整体水平。医共体平台建设是实施分级诊疗的重要基础工作，是实现优质医疗服务资源合理配置的重要技术手段。通过该平台的建设和实施，可以实现以下主要目标：

通过建立远程门诊、远程医疗、互动诊疗、远程医学、远程诊断、远程教学等多种医疗领域的商业应用和服务，构建无浪费的医疗卫生服务体系。该系统将有效衔接上下级医疗机构的线上活动和商业支持，促进优质医疗资源的利用，扩大优质医疗服务供给，切实落实医保扶贫任务。

形成县、乡、村一体化的诊疗服务网络，并建立地区诊断中心。通过信息交流和服务协同，提高医疗资源的整体利用效率。这种模式可以有效避免重复检查和过度治疗，不仅节约了医疗费用，也降低了患者的治疗成本，提高了居民在就医和购药过程中的满意度和获得感。

与医保、医疗等相关政策组织协调，建立一体化的医疗卫生体系。利用医保的杠杆效应，强化县域医共体成员医疗机构之间的关系，增强基层医疗服务的吸引力。通过推动县、乡、村三者之间的管理一体化，完善家庭医生签约服务，强化公共卫生服务水平，最终构建可管理、动态、连续、个性化的终身健康地图，确保医疗卫生服务的便捷性，使居民能够在不同层面获得连续的全科和专科医疗服务。确保医疗和专科保健服务的连续性。通过这些多层次、多方位的措施，A县医共体建设的目标不仅是提高医疗服务质量，更要提升人民群众的便捷度和满意度，努力构建更加高效、便捷、可及的医疗服务体系。

# 3.3.3 项目主要建设内容

D 公司 A 县紧密型县域医共体项目的总体设计由“1 个基地、8 个枢纽、5个联动、1个互认、1个智慧、1个门户、2 个整合、1套台账、1个屏幕、1个龙头、1个融合、3个创新”组成。”

即夯实“基于数字化的发现、联动、共治一体化平台基础，打造资源共享的八大枢纽”，努力构建医疗卫生综合服务平台，打造丰富的优质医疗资源。确保各医疗机构之间的有效衔接，特别是重点做好胸痛、脑卒中、创伤、重症妇儿、重大疾病患者的五联机制。实现区域内检验检查结果互认，有效减少患者重复检查，降低医疗费用。利用人工智能（AI）技术，为基层医生配备智能辅助诊疗工具，提高基层医疗机构的工作效率。利用人工智能技术为基层医生配备智能辅助诊疗工具，提高基层医疗机构的诊疗能力，以“互联网 $^ +$ 医疗”的理念打造线上线下融合的医疗平台。使居民无论住院还是通过互联网，都能方便地获得医疗服务和健康信息。妇幼服务和慢病筛查管理一体化，旨在实现医疗卫生服务的双融合，依托医共体人力、财力的同质性，实现集体管理的统管，建立健康运行管理中心，让整个医共体在一个屏幕上掌握情况。建设医疗运行管理中心，实现全医共体一屏掌握。目的是强化和进一步巩固医院龙头作用，引领社会高质量发展。

统一建设地级信息系统，促进县、镇、企一体化。引入三创模式，实现集团管理一本账，助力医共体成为高效、可持续发展的医共体。

（1）改善医疗信息技术基础设施

为医共体建立一个单一的数据资源中心，以满足三级安全保障要求。云端系统为全县医疗信息技术的持续运行以及医共体的日常数据备份和灾难恢复提供可靠的基础环境。

在现有卫生信息网络的基础上建设卫生专网，将医共体数据中心与各级医疗机构网络连接，构建纵向覆盖乡镇，横向连接公共卫生机构、政府部门（财政、医保、民政、公安等）网络的多层次信息服务网络。

信息标准体系是医疗信息化建设的基础。根据国家和省级出台的医疗信息化规范和标准，逐步制定和完善与区域智慧医疗信息化建设相关的规范和标准体系。

（2）为医共体建立一个信息平台，支持资源的协调和整合

以经济、实用、共享为原则，建立一系列服务平台，支持分级诊疗制度和医共体商业合作的引入，满足地方医共体集团化管理、一体化工作、无缝隙服务的需求；进一步促进地方政府与城市之间的商业合作和资源共享。推动形成多层次、多感知、多医联体的网络，实现医院之间的共驻和集中共享共同诊疗服务，创新医疗资源协调服务模式，引入新型点对点网络服务。整合和综合管理区域医疗服务资源，实现各企业融合发展，重构医疗生态。

（3）为地方医院建设智慧医院云 $^ +$

在加强公立医院建设和管理考核中，《关于印发深化医药卫生体制改革 2021年重点工作任务的通知》明确提出，推动公立医院高质量发展，深入实施公立医院绩效考核，开展公立医院高质量发展试点。利用最新的信息化技术建设和完善一套现代化医院运营管理平台，开展医院精细化绩效管理，满足智慧医院的管理需求，公立医院能够高质量的发展，是当前医院信息化建设工作的重要任务[34]。高水平设计智慧医院分级评价，这可以支撑线上线下一体化的医疗服务新模式。

（4）建设基层医疗智慧医院云

加强基层建设是中国医改的重中之重，也是地方医疗信息化建设的目标和挑战。信息化赋能县级智慧医院建设的路径，县级智慧医院建设是基层医疗组织发展趋势，其中医院信息化是重要组成部分，也是“互联网+”时代的必然选择[35]。医护人员进入基层医疗卫生系统后，能够在基层医疗卫生系统共同工作。同时，基层机构应基于云信息系统和医共体信息服务平台，实现与大型医共体医院、公共卫生机构、政府部门的健康数据共享与合作。

（5）建立资源共享与合作中心，共享和吸纳优质医疗资源

建立资源共享合作中心，将地方医院的优质资源和服务传播到地方，扩大优质服务的覆盖面，让老百姓就近享受到地方医院的优质服务，达到“人在哪里，数据就流通到哪里”的目的。

（6）建立慢性病管理中心，整合医疗和预防活动，在地方一级对慢性病患者进行智能和准确的管理

根据国家公共卫生评估和家庭医生签约服务，为居民提供健康管理计划，包括警报、风险评估和分析、干预方案、健康教育、自动监测和医疗咨询、个性化和准确的干预措施、根据病历数据变化进行实时动态调整，以及促进公共卫生数据的收集、更新和利用。为居民提供护理计划。通过自动化和智能化服务，增强全科医生和家庭医生的能力，减轻他们的负担。

（7）建立互联网服务中心，提高公众对医疗服务的认识

充分利用移动互联网的便捷性，利用互联网建立统一的医疗服务公众号，让授权辖区内的居民（患者）利用移动终端完成就诊和医疗服务全过程，建立便捷的“互联网 $^ +$ 医疗”服务区体系。

（8）建立集团公司管理中心，以监督整个医疗行业

随着医共体平台的建设和大量应用系统的逐步投入使用，卫生管理部门在信息获取和处理能力上得到了极大的提升。通过建立集团公司管理中心，各级卫生管理机构不仅能够及时、高效地获取各类卫生统计信息，还能实现从终端统计向过程统计的转变。卫生管理部门不仅能够掌握当前的医疗服务现状，还能对未来的趋势进行预测和分析，从而制定更加科学合理的政策和决策进行监管。

# 3.4 项目开发概况

随着多年经验的积累，开发人员越来越意识到，市场相关性和客户满意度是整个产品开发过程中的关键因素。只有通过调研和分析，才能真正了解市场的需求，实现新产品开发的目标，然后结合公司的人力、财力和先进的科学技术，根据客户的要求、规格、功能和设计方案，规划和制定完整的新产品设计方案，完成新产品的设计和开发。明确设计的主要内容，包括以下几个阶段：

$\textcircled{1}$ 初始状态：产品项目启动后开发相关产品线的过程，通常使用嵌入式操作系统。

$\textcircled{2}$ 软件设计：为开发部门实现新产品的开发和设计提供主要条件和依据，需要进行系统分析，嵌入式操作系统也是实现系统管理程序、应变能力和可视化显示的基础。软件设计的实现还应充分考虑到其他方面的应用功能，包括软件功能和后续维护等。为了制定明确的设计理念，保证项目的顺利开展，要从软件的整体结构出发，对各个模块的功能进行完整详细的了解，在界面功能和分解功能明确的前提下，做好清晰的标注，如果开发的新产品比较复杂或者涉及的情况比较广泛，可以将软件设计功能进行拆分。

为了提高工作效率，避免重复劳动，软件设计可以在贴标的数字编码完成后再进行，无论数字编码是大是小，都要避免出现嵌套操作的现象。标识的编制应与代码的范围相一致，以更好地描述标识所提供的信息，同时还要提出代码编制的相关数据。

$\textcircled{3}$ 软件评审：软件工程师应先撰写《软件功能说明书》，并对相关信息进行汇总和编辑，然后将申请评审请求发送给专业评审人员，对功能设计进行评审。

$\textcircled{4}$ 编码：根据软件的功能规范，开发人员也可能使用软件代码进行注释，在这种情况下，注释期应与代码注释的范围一致，且不应与要求相矛盾。在准备代码注释程序时，应仔细阅读《软件功能说明书》。在产品开发和功能测试过程中，如果在初始内容设计期间对功能面板进行了扩展，则必须修改软件产品的功能设计。

$\textcircled{5}$ 软件调试与优化：软件设计环节主要通过测试、完成代码展示等体现在产品功能上。

$\textcircled{6}$ 结束环节：新产品开发和功能验证完成后，整个产品开发周期就结束了。

# 3.5 项目进度制约因素分析

# 3.5.1 外部进度制约因素

（1）信息系统分散，缺乏有效的互动

A县建立了区县通用卫生信息平台（虚拟平台），主要向县级平台传输数据。虽然 A 县基本建立了覆盖县乡两级医疗机构的信息基础设施，但整体应用水平仍然较低，特别是联动服务仍显不足，县域内卫生服务数据信息的壁垒尚未打通，难以对县域间的工作联动应用形成有效支撑，分级诊疗和县域间医疗卫生联动难以落实。

另外，此前的各信息系统建设，由不同的开发单位独立建设，没有统一的规划标准，各单位的系统都是以自身标准的信息资源建设为支撑，如果需要在各单位应用系统之间建立数据流，就会出现数据标准的差异和混乱。

（2）医疗机构的信息化程度较低，中小型医院相对薄弱

全县二级、三级医院信息化已具备一定基础；县人民医院已通过电子病历四级、三级等保，正在积极推进四级互联建设；全县中医医院已完成核心系统升级，正在力争完成升级电子病历等级、互联等级。但 A 县妇幼保健院及县内其他医院的信息化水平仍以 HIS 为基础，临床信息化仍处于初级阶段。全县各级医院的发展水平和功能现状参差不齐，与新时代“发展高质量公立医院”的需求存在巨大差距。

（3）基层组织基础薄弱，急需提高服务能力

基层医疗卫生机构正在使用省里建立的基层医疗卫生信息系统，但该系统主要侧重于医疗费用管理和病历保存，不支持医共体和医卫服务一体化，难以有效利用优质资源，省级层面仍有很大的能力提升空间。

（4）缺乏整合机制，医疗保健管理仍处于起步阶段

目前，A县的便民惠民服务分散在一些二、三级医院搭建的线上医疗服务平台（微信小程序、公众号），A 县居民没有一个统一的服务入口，影响了居民的就医体验。同时，县级尚未建立个人病历数据库，基层医生和居民难以对健康和慢性病进行有效管理。

（5）部门间管理工具有限，缺乏用于决策的科学数据

现有的全民健康信息平台主要是向省级平台传输数据，尚未建立统一、权威、互联互通的支撑保障体系。此外，地市级人民的病历和患者信息往往还是手工交换，需要定期向主管医疗机构索取数据，无法为管理部门提供有效的信息化监管、数据科学支撑和决策依据。将行业管理模式转变为大规模数字化模式是一个巨大的挑战。

（6）没有充分利用数据，也没有考虑数据的价值

由于业务应用层与数据层之间缺乏有效分离，产生了大量零散的数据信息，难以有效连接数据、挖掘数据价值、有效应用数据，导致基础统计数据不完整，医疗系统无法获得统计数据。目前，医疗系统的上层并不直接接收数据，而是从下层收集数据，工作繁琐。即使能获得数据，也难以综合利用，无法进行整体监管。同时，难以开展以数据为驱动的管理活动。此外，平台的运行和维护各自为

政，难以统一管理。

# 3.5.2 内部进度制约因素

（1）项目的持续时间相对较短且工作量较大

需制定详尽的时间表，使得所有项目相关人员能够清晰地理解各自的职责和时间节点，从而迅速地开展各项工作。在当前竞争激烈的软件行业中，项目对截止日期的要求尤为严苛，因此，合理的角色分工和有效的资源分配对于确保在设定的期限内完成工作显得至关重要。清晰的时间管理不仅能够提高工作效率，还能够增强团队的协作能力，使每个团队成员都能在其负责的领域内发挥最大效能。

（2）软件项目通常具有高度的不确定性

不确定性尤其是在需求方面，用户需求常常是不断变化且模糊不清的。这种不确定性可能导致项目进度的延误。需求的变化不仅会影响开发时间，还可能导致额外的返工和质量问题。

此外，项目团队在条件尚不完备的情况下过早开始工作，往往会增加潜在的风险和缺陷的几率。因此，项目团队务必要建立一套有效的需求管理流程，包括需求的识别与确认、需求变更的评估以及充分的需求验证。同时，还需定期审视不稳定需求的潜在风险，提出相应的应对策略，以便及时调整开发方向，降低项目的不可预见性。

（3）软件项目结构复杂

开发过程相对独立，因此项目的可视性和可测量性通常较低。这就给跟踪项目进度、评估进度和进行质量检查带来了困难。有必要在项目过程中检查代码并评估其质量。只有在软件质量保证的基础上，进度管理才是有效的。

# （4）人力资源有限

有完整的项目系统开发经验的人相对较少，因此，在项目规划中应充分考虑这些人力资源方面的制约因素，确保有效、合理地利用现有资源。由于软件项目是智力密集型领域，依赖于软件工程师的高度创造力和脑力劳动强度，人力资源成本是项目的主要支出，为了控制项目成本，合理分配人力资源尤为重要。

（5）开发过程中往往存在隐性浪费

开发人员通常会在一项任务上花费足够多的时间，而当他们提前完成任务时往往不会报告。偶尔也会出现意外情况延误工作的风险。有些开发人员习惯把任务拖到最后一刻，在准备工作上浪费不必要的时间。开发人员在估算任务时往往会考虑各种风险。因此，每项任务的计划都包含了过多的安全时间，这进一步浪费了项目时间。为了优化项目进度，任务估算应根据已耗时间进行合理估算，避免过多的安全时间积累，可以减少隐性浪费，缩短项目周期，降低成本。

# 第 4 章 D 公司 A 县紧密型县域医共体项目的进度计划编制

# 4.1 项目里程碑计划

在推进项目的过程中，借助里程碑计划项目管理工具，可以对项目的进度和资源进行实时的监管与调整，以便及时识别并解决项目中出现的各种问题和潜在风险，确保项目能够顺畅无阻地向前推进[36]。所有项目里程碑都应集中存储在里程碑列表中，并明确指出单个里程碑是可选的还是必须的。里程碑是项目规划过程中的重要关键事件，主要关系到项目目标的实现和关键交付成果的完成，以确保对项目进度进行有效合理的管理。作为一个软件开发项目，里程碑通常是这个项目渐进明细的标志。考虑到本软件开发项目的目的和技术参数，项目团队决定确定以下里程碑：项目启动、项目规划、系统设计、编码阶段、测试阶段、试运行与交付。项目里程碑计划表，见表 4.1。

表4.1项目里程碑计划表  
Table 4.1 Project milestone schedule   

<table><tr><td>序号</td><td>里程碑事件</td><td>交付成果</td><td>完成时间</td></tr><tr><td>1</td><td>项目启动</td><td>可行性报告、章程</td><td>2024年10月18日</td></tr><tr><td>2</td><td>项目规划</td><td>管理计划、说明书</td><td>2024年11月07日</td></tr><tr><td>3</td><td>系统设计</td><td>架构文档、原型</td><td>2024年11月28日</td></tr><tr><td>4</td><td>编码阶段</td><td>源代码、注释文档</td><td>2025年01月23日</td></tr><tr><td>5</td><td>测试阶段</td><td>测试计划、报告</td><td>2025年02月25日</td></tr><tr><td>6</td><td>试运行与交付</td><td>验收项目</td><td>2025年03月13日</td></tr></table>

# 4.2 项目工作分解与责任分配

# 4.2.1 项目工作分解

首先应识别项目的主要要素,项目的主要要素就是项目的主要交付物,然后对识别出的主要阶段作进一步细化,分解出更详细的有形的、可检验的活动, 制定详细的工作分解结构(WBS),将整个项目细化有助于更准确地估算和分配成本，同时也便于资源的有效管理[37]。其中项目共包含 6 个阶段。各阶段活动的主要任务，见表4.2。

# 表4.2各阶段活动的主要任务

Table 4.2 Main tasks of activities in each phase   

<table><tr><td>阶段</td><td>活动名称</td><td>主要任务</td></tr><tr><td>项目启动</td><td>合同签订</td><td>明确双方权利义务，提供法律保障</td></tr><tr><td></td><td>项目立项</td><td>获得批准以正式启动，与组织战略一致</td></tr><tr><td></td><td>项目启动仪式</td><td>正式宣布项目启动，明确目标，分配责任</td></tr><tr><td></td><td>组建项目团队</td><td>集合专业人才，共同完成项目目标</td></tr><tr><td>项目规划</td><td>需求调研</td><td>了解用户需求，为项目规划提供依据</td></tr><tr><td></td><td>需求确认</td><td>审核需求文档、征求反馈与意见，达成共识</td></tr><tr><td></td><td>需求评审</td><td>评估需求是否满足业务目标要求的过程</td></tr><tr><td></td><td>制定项目计划</td><td>设定阶段目标、资源分配和进度等安排</td></tr><tr><td>系统设计</td><td>配置分析设计</td><td>对系统配置需求进行分析，设计配置方案</td></tr><tr><td></td><td>数据库设计</td><td>规划数据结构、关系和存储方式，支持运行</td></tr><tr><td></td><td>代码架构设计</td><td>制定软件系统的结构和模块划分</td></tr><tr><td></td><td>设计评审</td><td>设计审查，确认符合需求规范</td></tr><tr><td>编码阶段</td><td>开发环境配置</td><td>为项目搭建必要的软件、工具和硬件平台</td></tr><tr><td></td><td>基础支撑平台配置</td><td>提供运行环境、资源管理和保障的底层架构</td></tr><tr><td></td><td>一体化能力中心配置</td><td>整合开发技术、流程和资源</td></tr><tr><td></td><td>县级医院信息系统开发</td><td>电子病历、检验管理、药品管理等功能</td></tr><tr><td></td><td>基层运营服务平台开发</td><td>资源调配、患者服务、数据分析等功能模块</td></tr><tr><td></td><td>共享与协同中心开发</td><td>跨机构协作、信息交互与远程医疗等功能</td></tr><tr><td></td><td>代码走查</td><td>检查代码规范性、逻辑正确性、潜在错误</td></tr><tr><td>测试阶段</td><td>集成测试</td><td>验证模块间接口、数据流及整体系统性能</td></tr><tr><td></td><td></td><td>验证系统整体功能、性能、安全性及兼容性</td></tr><tr><td></td><td>系统测试</td><td>确定修复方案并实施修复</td></tr><tr><td></td><td>缺陷修正</td><td></td></tr><tr><td>试运行与交付</td><td>回归测试</td><td>验证代码变更后系统功能是否受影响</td></tr><tr><td></td><td>系统试运行</td><td>实际环境中检验系统功能、性能、稳定性</td></tr><tr><td></td><td>用户培训</td><td>系统功能讲解、操作演示、常见问题解答</td></tr><tr><td></td><td>上线交付 项目总结</td><td>系统部署、数据迁移、文档提供及技术支持 过程回顾、经验教训及未来改进建议</td></tr></table>

# 4.2.2 项目工作责任分配

项目任务责任矩阵是项目管理中的关键工具，建立明确的项目责任分配、定期的项目审查、实时的进度和成本追踪机制[38]。根据项目所处的不同阶段以及任务的具体差异，每一个活动都被分配了明确的具体负责人。这种精细的责任划分模式，可以极大程度地提升团队成员之间协同工作的效率，有效减少责任模糊不清的地带，为项目的顺利推进提供坚实保障，最终促使项目目标得以成功实现。项目任务责任矩阵，见表 4.3。

# 表4.3项目任务责任矩阵

Table 4.3 Project task responsibility matrix   

<table><tr><td>任务编码</td><td>任务名称</td><td>负责人</td></tr><tr><td>A1</td><td>合同签订</td><td>客户经理</td></tr><tr><td>A2</td><td>项目立项</td><td>项目发起人</td></tr><tr><td>A3</td><td>项目启动仪式</td><td>项目发起人</td></tr><tr><td>A4</td><td>组建项目团队</td><td>项目经理</td></tr><tr><td>B1</td><td>需求调研</td><td>项目经理、客户经理</td></tr><tr><td>B2</td><td>需求确认</td><td>项目经理</td></tr><tr><td>B3</td><td>需求评审</td><td>项目经理、PMO</td></tr><tr><td>B4</td><td>制定项目计划</td><td>项目经理、PMO</td></tr><tr><td>C1</td><td>配置分析设计</td><td>开发部工程师</td></tr><tr><td>C2</td><td>数据库设计</td><td>开发部工程师</td></tr><tr><td>C3</td><td>代码架构设计</td><td>开发部工程师</td></tr><tr><td>C4</td><td>设计评审</td><td>开发部工程师、QA</td></tr><tr><td>D1</td><td>开发环境配置</td><td>开发部工程师</td></tr><tr><td>D2</td><td>基础支撑平台配置</td><td>开发部工程师</td></tr><tr><td>D3</td><td>一体化能力中心配置</td><td>开发部工程师</td></tr><tr><td>D4</td><td>县级医院信息系统开发</td><td>开发部工程师、UI设计师</td></tr><tr><td>D5</td><td>基层运营服务平台开发</td><td>开发部工程师、UI设计师</td></tr><tr><td>D6</td><td>共享与协同中心开发</td><td>开发部工程师、UI设计师</td></tr><tr><td>D7</td><td>代码走查</td><td>开发部工程师、QA</td></tr><tr><td>E1</td><td>集成测试</td><td>软件测试工程师、QA</td></tr><tr><td>E2</td><td>系统测试</td><td>软件测试工程师、QA</td></tr><tr><td>E3</td><td>缺陷修正</td><td>软件测试工程师、QA</td></tr><tr><td>E4</td><td>回归测试</td><td>软件测试工程师、QA、第三方检测机构</td></tr><tr><td>F1</td><td>系统试运行</td><td>项目经理、开发部工程师</td></tr><tr><td>F2</td><td>用户培训</td><td>项目经理、开发部工程师</td></tr><tr><td>F3</td><td>上线交付</td><td>项目经理、开发部工程师</td></tr><tr><td>F4</td><td>项目总结</td><td>项目经理</td></tr></table>

# 4.3 作业的逻辑关系及时间估算

# 4.3.1 作业逻辑关系确定

活动依赖关系有强制性逻辑、选择性逻辑、外部依赖和内部依赖关系[39]。本文的研究的重点是项目的内部依赖关系。

根据 WBS 分解的工作任务，项目经理与项目团队成员一起识别本项目生命周期中各项任务活动之间的依赖关系和逻辑关系，最终讨论得出项目活动排序，见表 4.4。

# 表4.4项目活动排序

Table 4.4 Sequencing of project activities   

<table><tr><td>任务阶段</td><td>任务编码</td><td>任务名称</td><td>前置任务</td></tr><tr><td rowspan="4">项目启动</td><td>合同签订</td><td>A1</td><td></td></tr><tr><td>项目立项</td><td>A2</td><td>A1</td></tr><tr><td>项目启动仪式</td><td>A3</td><td>A2</td></tr><tr><td>组建项目团队</td><td>A4</td><td>A3</td></tr><tr><td rowspan="4">项目规划</td><td>需求调研</td><td>B1</td><td>A1</td></tr><tr><td>需求确认</td><td>B2</td><td>A4、B1</td></tr><tr><td>需求评审</td><td>B3</td><td>B2</td></tr><tr><td>制定项目计划</td><td>B4</td><td>B3</td></tr><tr><td rowspan="4">系统设计</td><td>配置分析设计</td><td>C1</td><td>B4</td></tr><tr><td>数据库设计</td><td>C2</td><td>B4</td></tr><tr><td>代码架构设计</td><td>C3</td><td>C1、C2</td></tr><tr><td>设计评审</td><td>C4</td><td>C3</td></tr><tr><td rowspan="7">编码阶段</td><td>开发环境配置</td><td>D1</td><td>C4</td></tr><tr><td>基础支撑平台配置</td><td>D2</td><td>D1</td></tr><tr><td>一体化能力中心配置</td><td>D3</td><td>D1</td></tr><tr><td>县级医院信息系统开发</td><td>D4</td><td>D2、D3</td></tr><tr><td>基层运营服务平台开发</td><td>D5</td><td>D4</td></tr><tr><td>共享与协同中心开发</td><td>D6</td><td>D4</td></tr><tr><td>代码走查</td><td>D7</td><td>D5、D6</td></tr><tr><td rowspan="4">测试阶段</td><td>集成测试</td><td>E1</td><td>D7</td></tr><tr><td>系统测试</td><td>E2</td><td>E1</td></tr><tr><td>缺陷修正</td><td>E3</td><td>E2</td></tr><tr><td>回归测试</td><td>E4</td><td>E3</td></tr><tr><td rowspan="4">试运行与交付</td><td>系统试运行</td><td>F1</td><td>E4</td></tr><tr><td>用户培训</td><td>F2</td><td>F1</td></tr><tr><td>上线交付</td><td>F3</td><td>F2</td></tr><tr><td>项目总结</td><td>F4</td><td>F3</td></tr></table>

# 4.3.2 作业时间安排依据

（1）事业环境因素

是指项目团队不能控制的，将对项目产生影响、限制或指令作用的各种条件，可以分为组织内部和组织外部两部分[40]。

# （2）组织过程资产

是组织内的无形资产，包括标准流程、指南等，以及项目档案、经验教训等知识库。能帮助组织有效管理项目，提高项目管理效率和成功率。

（3）项目范围说明书

用来明确项目的目标、可交付物、需要完成的主要任务以及约束条件，明确项目的边界，使得项目团队和项目相关方共同理解项目内容的项目管理文档[41]。

（4）活动属性及清单

活动属性和清单是管理和组织活动的两个核心概念。活动属性则指的是关于每个具体活动的详细信息或特征，它们是对单个活动的深入描述。活动属性包括但不限于活动的名称、开始和结束时间、地点、参与者、优先级、状态、所需资源等。这些属性为活动的具体执行提供了明确的指导，有助于对活动进行更精细的管理和监控。例如，通过设定活动的优先级和状态，可以帮助团队识别当前进展、识别潜在的问题以及调整资源配置。

活动属性在微观层面上详细描述了每个活动的具体特征，而活动清单提供了一个宏观的视图，列出了所有计划中的活动及其基本安排，两者结合使用，可以大大提升活动管理的效率，确保各项任务有序进行，减少遗漏和冲突，提高整体工作效果。活动清单则是一个包含所有计划活动的详细列表，通常用于个人、团队或项目的时间管理与组织。它可以包括活动的名称、时间、地点、参与者等基本信息。通过活动清单，用户能够直观地看到所有预定的活动安排，帮助优化时间分配、确保任务的按时完成，并且便于进行整体规划和调整。例如，在一个项目管理中，活动清单可能会列出所有项目阶段、关键任务和里程碑，为团队提供清晰的执行路线图。

（5）活动资源需求

项目不同阶段需要的资源集合，包括人力资源、资金、技术工具等。明确资源需要的数量、时间、取得途径，有利于项目经理对资源的合理规划和分配[42]。

# （6）项目管理计划

作为项目经理指导和管理项目全流程的关键计划，它整合了项目的范围、进度、成本、质量、资源、沟通、风险和采购等十个层面的计划，来全面的指导和支持项目工作。

# 4.3.3 活动资源安排

活动资源安排是根据项目范围、资源分配和具体信息估算特定活动持续时间的过程。它是项目进度安排过程中不可或缺的一部分，对整个项目网络的时间安排和整个项目进度有重大影响，需考虑的内容如下：

（1）资源的适用性

指资源在特定项目中的有效性和适当性，包括人力、财力、物力和时间等多方面。适用性确保资源能够满足项目各阶段的需求，并具备良好的执行能力。

# （2）资源的可用性

在确定项目活动的资源需求时，考虑项目能够提供哪些资源，什么时候提供，如何提供，否则资源需求规划做得再好也没有实际意义，项目活动所需要的资源一般由于各种条件的限制不可能随时都有，所以在确定具体资源需求的过程中，一定要保证项目活动的资源是可用的，所以在确定具体资源需求时，一定要保证项目活动能够在实际可能的范围内进行，选择一些常用的资源类型，保证项目实施过程中资源的可用性。

# （3）资源质量

是指用于实现项目目标的各类资源的有效性和可靠性。例如人力资源的质量体现在团队成员的技能和经验，财力资源则涉及资金的合理分配和及时到位，物力资源包括设备和技术工具的性能。

（4）资源利用的规模经济

通过增加项目规模或重复利用资源，以降低单位成本，提高项目效率的经济现象。使用集中采购、共享资源、标准化操作等方式，减少资源浪费，提升资源使用效率。另外有助于项目降低运营成本，增加项目的利润空间。

（5）关键活动的资源需求

为确保核心任务顺利执行而所需的各类资源。这其中包括人力资源、财务资源、物资资源和时间等。人力资源必须具备专业知识和实践经验，以便高效完成任务；财务资源需要合理规划和及时到位，以支持活动开展；物资资源应符合技术要求，保障工作质量；时间的合理安排则有助于确保各项关键任务按时进行。

（6）时间成本和资源成本约束下的影响

为了获得必要的资源，项目不仅要充分考虑资源的基本属性，还要从资源利用管理的角度进行综合安排，并考虑制约影响。

# 4.3.4 项目活动时间估算

要获得准确的活动时间估算，需要对项目人员的工作内容有透彻的了解以及丰富的管理知识，这样才能提高时间估算的准确性。管理人员对活动内容越熟悉，估算就越准确；反之，估算就不准确。

在完成项目的工作任务分解和活动排序之后,采用三点估算法对项目活动时间进行估算。三点估算及期望时间表，见表 4.5。

表4.5三点估算及期望时间表  
Table 4.5 Three-point estimates and expected timetable   

<table><tr><td>任务编码</td><td>任务名称</td><td>乐观时间</td><td>最可能时间</td><td>悲观时间</td><td>期望时间</td></tr><tr><td>A1</td><td>合同签订</td><td>Ta 1</td><td>Tm 2</td><td>Tp 3</td><td>Te 2.0</td></tr><tr><td>A2</td><td>项目立项</td><td>1</td><td>1</td><td>4</td><td>1.5</td></tr><tr><td>A3</td><td>项目启动仪式</td><td>1</td><td>1</td><td>2</td><td>1.2</td></tr><tr><td>A4</td><td>组建项目团队</td><td>1</td><td>2</td><td>3</td><td>2.0</td></tr><tr><td>B1</td><td>需求调研</td><td>9</td><td>11</td><td>18</td><td>11.8</td></tr><tr><td>B2</td><td>需求确认</td><td>2</td><td>3</td><td>4</td><td>3.0</td></tr><tr><td>B3</td><td>需求评审</td><td>1</td><td>1</td><td>2</td><td>1.2</td></tr><tr><td>B4</td><td>制定项目计划</td><td>2</td><td>3</td><td>3</td><td>2.8</td></tr><tr><td>C1</td><td>配置分析设计</td><td>2</td><td>3</td><td>4</td><td>3.0</td></tr><tr><td>C2</td><td>数据库设计</td><td>7</td><td>8</td><td>8</td><td>7.8</td></tr><tr><td>C3</td><td>代码架构设计</td><td>4</td><td>5</td><td>6</td><td>5.0</td></tr><tr><td>C4</td><td>设计评审</td><td>2</td><td>2</td><td>2</td><td>2.0</td></tr><tr><td>D1</td><td>开发环境配置</td><td>1</td><td>1</td><td>2</td><td>1.2</td></tr><tr><td>D2</td><td>基础支撑平台配置</td><td>3</td><td>5</td><td>6</td><td>4.8</td></tr><tr><td>D3</td><td>一体化能力中心配置</td><td>1</td><td>1</td><td>2</td><td>1.2</td></tr><tr><td>D4</td><td>县级医院信息系统开发</td><td>12</td><td>15</td><td>18</td><td>15.0</td></tr><tr><td>D5</td><td>基层运营服务平台开发</td><td>13</td><td>15</td><td>16</td><td>14.8</td></tr><tr><td>D6</td><td>共享与协同中心开发</td><td>6</td><td>9</td><td>13</td><td>9.2</td></tr><tr><td>D7</td><td>代码走查</td><td>2</td><td>3</td><td>3</td><td>2.8</td></tr><tr><td>E1</td><td>集成测试</td><td>2</td><td>3</td><td>3</td><td>2.8</td></tr><tr><td>E2</td><td>系统测试</td><td>5</td><td>7</td><td>7</td><td>6.7</td></tr><tr><td>E3</td><td>缺陷修正</td><td>8</td><td>10</td><td>11</td><td>9.8</td></tr><tr><td>E4</td><td>回归测试</td><td>1</td><td>3</td><td>4</td><td>2.8</td></tr><tr><td>F1</td><td>系统试运行</td><td>2</td><td>3</td><td>3</td><td>2.8</td></tr><tr><td>F2</td><td>用户培训</td><td>4</td><td>5</td><td>5</td><td>4.8</td></tr><tr><td>F3</td><td>上线交付</td><td>2</td><td>3</td><td>4</td><td>3</td></tr><tr><td>F4</td><td>项目总结</td><td>1</td><td>1</td><td>1</td><td>1</td></tr></table>

在 D 公司 A 县紧密型县域医共体项目中，项目的最小时间度量单位被精确划定为1天。为了切实确保项目进度能够得到最为精准的控制，并且为了实现管理的便利性，项目团队采用特定的时间单位来重新调整项目的期望时间。

项目团队通过分析和计算，对每一项活动的期望时间进行修正和全面优化，以此确保项目每一阶段的时间安排都是基于最为精确的时间单位，从而为项目的顺利推进提供有力保障。调整后的期望时间被标记为 $\mathrm { T } _ { e }$ ，并在项目的进度工期中进行应用，项目进度期望时间表，见表4.6。

表 4.6项目进度期望时间表  
Table 4.6 Expected schedule of project progress   

<table><tr><td>任务名称</td><td>期望时间</td><td>前置任务</td></tr><tr><td>任务编码</td><td>Te</td><td></td></tr><tr><td>A1</td><td>合同签订</td><td>2 2</td></tr><tr><td>A2</td><td>项目立项</td><td>A1</td></tr><tr><td>A3</td><td>项目启动仪式</td><td>2 A2</td></tr><tr><td>A4</td><td>组建项目团队</td><td>2 A3</td></tr><tr><td>B1</td><td>需求调研</td><td>12 A1</td></tr><tr><td>B2</td><td>需求确认</td><td>3 A4、B1</td></tr><tr><td>B3</td><td>需求评审</td><td>2 B2</td></tr><tr><td>B4</td><td>制定项目计划</td><td>3 B3</td></tr><tr><td>C1</td><td>配置分析设计</td><td>3 B4</td></tr><tr><td>C2</td><td>数据库设计</td><td>8 B4</td></tr><tr><td>C3</td><td>代码架构设计</td><td>5 C1、C2</td></tr><tr><td>C4</td><td>设计评审</td><td>2 C3</td></tr><tr><td>D1</td><td>开发环境配置</td><td>2 C4</td></tr><tr><td>D2</td><td>基础支撑平台配置</td><td>5 D1</td></tr><tr><td>D3</td><td>一体化能力中心配置</td><td>2 D1</td></tr><tr><td>D4</td><td>县级医院信息系统开发 15</td><td>D2、D3</td></tr><tr><td>D5</td><td>基层运营服务平台开发</td><td>15 D4</td></tr><tr><td>D6</td><td>共享与协同中心开发</td><td>10 D4</td></tr><tr><td>D7</td><td>代码走查</td><td>3 D5、D6</td></tr><tr><td>E1</td><td>集成测试 3</td><td>D7</td></tr><tr><td>E2</td><td>系统测试 7</td><td>E1</td></tr><tr><td>E3</td><td>缺陷修正 10</td><td>E2</td></tr><tr><td>E4</td><td>回归测试 3</td><td>E3</td></tr><tr><td>F1</td><td>系统试运行 3</td><td>E4</td></tr><tr><td>F2</td><td>用户培训</td><td>5 F1</td></tr><tr><td>F3</td><td>上线交付</td><td>3 F2</td></tr><tr><td>F4</td><td>项目总结</td><td>1 F3</td></tr></table>

# 4.3.5 确定关键路径

通过项目网络图可以了解到不同活动之间的关系，有利于项目经理判断各活动的顺序是否符合逻辑。项目网络图有利于各活动按预期计划执行，确保在规定工期内完成。通过分析，绘制出项目网络图，如图4.1所示。

从表 4.4 中可以看出各活动之间的逻辑关系，通过单代号项目网络图，有利于关键路径的确定。根据各活动的持续时间，算出各活动的最早开始时间和最早完成时间，之后通过反向算出各活动的最晚开始时间和最晚完成时间，最后计算出项目的总时差。

![](images/d94c2986182907409d1475919a59ec814b0eed7bdfca8800d3e98c09779cfbde.jpg)  
图 4.1项目网络图  
Fig. 4.1 Project network diagram

其中总时差为 0 的活动就构成了 D 公司 A 县紧密型县域医共体项目的关键路径。项目进度时间参数表，见表4.7。

表 4.7项目进度时间参数表  
Table 4.7 Project Schedule Time Parameter Table   

<table><tr><td>任务编码</td><td>工作持续 时间</td><td>最早开始 时间</td><td>最早完成 时间</td><td>最晚开始 时间</td><td>最晚完成 时间</td><td>总时差</td></tr><tr><td>A1</td><td>2</td><td>0</td><td>2</td><td>0</td><td>2</td><td>0</td></tr><tr><td>A2</td><td>2</td><td>2</td><td>4</td><td>8</td><td>10</td><td>6</td></tr><tr><td>A3</td><td>2</td><td>4</td><td>6</td><td>10</td><td>12</td><td>6</td></tr><tr><td>A4</td><td>2</td><td>6</td><td>8</td><td>12</td><td>14</td><td>6</td></tr><tr><td>B1</td><td>12</td><td>2</td><td>14</td><td>2</td><td>14</td><td>0</td></tr><tr><td>B2</td><td>3</td><td>14</td><td>17</td><td>14</td><td>17</td><td>0</td></tr><tr><td>B3</td><td>2</td><td>17</td><td>19</td><td>17</td><td>19</td><td>0</td></tr><tr><td>B4</td><td>3</td><td>19</td><td>22</td><td>19</td><td>22</td><td>0</td></tr><tr><td>C1</td><td>3</td><td>22</td><td>25</td><td>27</td><td>30</td><td>5</td></tr><tr><td>C2</td><td>8</td><td>22</td><td>30</td><td>22</td><td>30</td><td>0</td></tr><tr><td>C3</td><td>5</td><td>30</td><td>35</td><td>30</td><td>35</td><td>0</td></tr><tr><td>C4</td><td>2</td><td>35</td><td>37</td><td>35</td><td>37</td><td>0</td></tr><tr><td>D1</td><td>2</td><td>37</td><td>39</td><td>37</td><td>39</td><td>0</td></tr><tr><td>D2</td><td>5</td><td>39</td><td>44</td><td>39</td><td>44</td><td>0</td></tr><tr><td>D3</td><td>2</td><td>39</td><td>41</td><td>42</td><td>44</td><td>3</td></tr><tr><td>D4</td><td>15</td><td>44</td><td>59</td><td>44</td><td>59</td><td>0</td></tr><tr><td>D5</td><td>15</td><td>59</td><td>74</td><td>59</td><td>74</td><td>0</td></tr><tr><td>D6</td><td>10</td><td>59</td><td>69</td><td>64</td><td>74</td><td>5</td></tr><tr><td>D7</td><td>3</td><td>74</td><td>77</td><td>74</td><td>77</td><td>0</td></tr><tr><td>E1</td><td>3</td><td>77</td><td>80</td><td>77</td><td>80</td><td>0</td></tr><tr><td>E2</td><td>7</td><td>80</td><td>87</td><td>80</td><td>87</td><td>0</td></tr><tr><td>E3</td><td>10</td><td>87</td><td>97</td><td>87</td><td>97</td><td>0</td></tr><tr><td>E4</td><td>3</td><td>97</td><td>100</td><td>97</td><td>100</td><td>0</td></tr><tr><td>F1</td><td>3</td><td>100</td><td>103</td><td>100</td><td>103</td><td>0</td></tr><tr><td>F2</td><td>5</td><td>103</td><td>108</td><td>103</td><td>108</td><td>0</td></tr><tr><td>F3</td><td>3</td><td>108</td><td>111</td><td>108</td><td>111</td><td>0</td></tr><tr><td>F4</td><td>1</td><td>111</td><td>112</td><td>111</td><td>112</td><td>0</td></tr></table>

项目关键活动持续时间表，见表 4.8。

表 4.8项目关键活动持续时间表Table 4.8 Duration of Key Activities in the Project Table  

<table><tr><td>任务编码</td><td>任务名称</td><td>工期</td><td>开始时间</td><td>完成时间</td></tr><tr><td>A1</td><td>合同签订</td><td>2</td><td>2024年10月9日</td><td>2024年10月10日</td></tr><tr><td>A2</td><td>项目立项</td><td>2</td><td>2024年10月11日</td><td>2024年10月14日</td></tr><tr><td>A3</td><td>项目启动仪式</td><td>2</td><td>2024年10月15日</td><td>2024年10月16日</td></tr><tr><td>A4</td><td>组建项目团队</td><td>2</td><td>2024年10月17日</td><td>2024年10月18日</td></tr><tr><td>B1</td><td>需求调研</td><td>12</td><td>2024年10月11日</td><td>2024年10月28日</td></tr><tr><td>B2</td><td>需求确认</td><td>3</td><td>2024年10月11日</td><td>2024年10月28日</td></tr><tr><td>B3</td><td>需求评审</td><td>2</td><td>2024年11月1日</td><td>2024年11月4日</td></tr><tr><td>B4</td><td>制定项目计划</td><td>3</td><td>2024年11月5日</td><td>2024年11月7日</td></tr><tr><td>C1</td><td>配置分析设计</td><td>3</td><td>2024年11月8日</td><td>2024年11月12日</td></tr><tr><td>C2</td><td>数据库设计</td><td>8</td><td>2024年11月8日</td><td>2024年11月19日</td></tr><tr><td>C3</td><td>代码架构设计</td><td>5</td><td>2024年11月20日</td><td>2024年11月26日</td></tr><tr><td>C4</td><td>设计评审</td><td>2</td><td>2024年11月27日</td><td>2024年11月28日</td></tr><tr><td>D1</td><td>开发环境配置</td><td>2</td><td>2024年11月29日</td><td>2024年12月2日</td></tr><tr><td>D2</td><td>基础支撑平台配置</td><td>5</td><td>2024年12月3日</td><td>2024年12月9日</td></tr><tr><td>D3</td><td>一体化能力中心配置</td><td>2</td><td>2024年12月3日</td><td>2024年12月4日</td></tr><tr><td>D4</td><td>县级医院信息系统开发</td><td>15</td><td>2024年12月10日</td><td>2024年12月30日</td></tr><tr><td>D5</td><td>基层运营服务平台开发</td><td>15</td><td>2024年12月31日</td><td>2025年1月20日</td></tr><tr><td>D6</td><td>共享与协同中心开发</td><td>10</td><td>2024年12月31日</td><td>2025年1月13日</td></tr><tr><td>D7</td><td>代码走查</td><td>3</td><td>2025年1月21日</td><td>2025年1月23日</td></tr><tr><td>E1</td><td>集成测试</td><td>3</td><td>2025年1月24日</td><td>2025年1月28日</td></tr><tr><td>E2</td><td>系统测试</td><td>7</td><td>2025年1月29日</td><td>2025年2月6日</td></tr><tr><td>E3</td><td>缺陷修正</td><td>10</td><td>2025年2月7日</td><td>2025年2月20日</td></tr><tr><td>E4</td><td>回归测试</td><td>3</td><td>2025年2月21日</td><td>2025年2月25日</td></tr><tr><td>F1</td><td>系统试运行</td><td>3</td><td>2025年2月26日</td><td>2025年2月28日</td></tr><tr><td>F2</td><td>用户培训</td><td>5</td><td>2025年3月3日</td><td>2025年3月7日</td></tr><tr><td>F3</td><td>上线交付</td><td>3</td><td>2025年3月10日</td><td>2025年3月12日</td></tr><tr><td>F4</td><td>项目总结</td><td>1</td><td>2025年3月13日</td><td>2025年3月13日</td></tr></table>

根据项目网络图、项目进度时间参数表和项目关键活动持续时间表，绘制出D 公司 A 县紧密型县域医共体项目甘特图，如图 4.2 所示。

通过项目甘特图，可以直观看出项目的总工期为112个工作日。项目所需周期的最长路径，即为： $\mathrm { A l \to B 1 \to B 2 \to B 3 \to B 4 \to C 2 \to C 3 \to C 4 \to D 1 \to D 2 \to D 4 \to D 5 }$ $ \mathrm { D 7  E 1  E 2  E 3  E 4  F 1  F 2  F 3  F 4 }$ ，对应到的项目任务名称为：合同签订 $\twoheadrightarrow$ 需求调研 $\twoheadrightarrow$ 需求确认 $\twoheadrightarrow$ 需求评审 $\twoheadrightarrow$ 制定项目计划 $\twoheadrightarrow$ 数据库设计 $\twoheadrightarrow$ 代码架构设计 $\twoheadrightarrow$ 设计评审 $\twoheadrightarrow$ 开发环境配置 $\twoheadrightarrow$ 基础支撑平台配置 $\twoheadrightarrow$ 县级医院信息系统开发$\twoheadrightarrow$ 基层运营服务平台开发 $\twoheadrightarrow$ 代码走查 $\twoheadrightarrow$ 集成测试 $\twoheadrightarrow$ 系统测试 $\twoheadrightarrow$ 缺陷修正 $\twoheadrightarrow$ 回归测试 $\twoheadrightarrow$ 系统试运行 $\twoheadrightarrow$ 用户培训 $\twoheadrightarrow$ 上线交付 $\twoheadrightarrow$ 项目总结。

![](images/62951771582c45c9ce4c4795dde1e6b76f9c200452ffb8b1732947f4368ed5ae.jpg)  
图 4.2项目甘特图  
Fig. 4.2 Project Gantt chart

结合三点估算及期望时间表（表4.5）计算出项目的乐观时间、最可能时间、悲观时间，得到计算关键路径任务标准差，见表4.9。其计算见公式 4.1。

$$
\sigma = \frac { ( \mathrm { ~ } T _ { P } - T _ { a } ) } { 6 }
$$

公式4.1

通过采用正态统计分布图来展开进一步分析[43]，就工期层面来看，如果预计其持续的时间是 1 个标准差的话，那么在该时间内结束的概率是 $6 8 . 2 6 \%$ ；如果预计其持续的时间是 2 个标准差的话，那么在该时间内结束的概率是 $9 5 . 4 6 \%$ ；如果预计其持续的时间是3个标准差的话，那么在该时间内结束的概率是 $9 9 . 7 3 \%$ 。正态统计分布图,如图 4.3所示。

从表 4.9 可以看出，D 公司 A 县紧密型县域医共体项目总工期的标准差为7.51 个工作日，调整后为 8 个工作日。根据规划分析法的理论知识，项目总工期服从正态分布函数，根据正态分布规律，结合本项目计算出的标准差，项目的完成时间具有正态分布特性，均值为 112 个工作日，标准差为 8个工作日。

表 4.9关键路径任务标准差  
Table 4.9 Standard deviation of critical path tasks   

<table><tr><td>任务编码</td><td>任务名称</td><td>乐观时间 Ta</td><td>最可能时间 Tm</td><td>悲观时间 Tp</td><td>标准差</td></tr><tr><td>A1</td><td>合同签订</td><td>1</td><td>2</td><td>3</td><td>0 0.33</td></tr><tr><td>A2</td><td>项目立项</td><td>1</td><td>1</td><td>4</td><td>0.50</td></tr><tr><td>A3</td><td>项目启动仪式</td><td>1</td><td>1</td><td>2</td><td>0.17</td></tr><tr><td>A4</td><td>组建项目团队</td><td>1</td><td>2</td><td>3</td><td>0.33</td></tr><tr><td>B1</td><td>需求调研</td><td>9</td><td>11</td><td>18</td><td>1.50</td></tr><tr><td>B2</td><td>需求确认</td><td>2</td><td>3</td><td>4</td><td>0.33</td></tr><tr><td>B3</td><td>需求评审</td><td>1</td><td>1</td><td>2</td><td>0.17</td></tr><tr><td>B4</td><td>制定项目计划</td><td>2</td><td>3</td><td>3</td><td>0.17</td></tr><tr><td>C1</td><td>配置分析设计</td><td>2</td><td>3</td><td>4</td><td>0.33</td></tr><tr><td>C2</td><td>数据库设计</td><td>7</td><td>8</td><td>8</td><td>0.17</td></tr><tr><td>C3</td><td>代码架构设计</td><td>4</td><td>5</td><td>6</td><td>0.33</td></tr><tr><td>C4</td><td>设计评审</td><td>2</td><td>2</td><td>2</td><td>0</td></tr><tr><td>D1</td><td>开发环境配置</td><td>1</td><td>1</td><td>2</td><td>0.17</td></tr><tr><td>D2</td><td>基础支撑平台配置</td><td>3</td><td>5</td><td>6</td><td>0.50</td></tr><tr><td>D3</td><td>一体化能力中心配置</td><td>1</td><td>1</td><td>2</td><td>0.17</td></tr><tr><td>D4</td><td>县级医院信息系统开发</td><td>12</td><td>15</td><td>18</td><td>1</td></tr><tr><td>D5</td><td>基层运营服务平台开发</td><td>13</td><td>15</td><td>16</td><td>0.50</td></tr><tr><td>D6</td><td>共享与协同中心开发</td><td>6</td><td>9</td><td>13</td><td>1.17</td></tr><tr><td>D7</td><td>代码走查</td><td>2</td><td>3</td><td>3</td><td>0.17</td></tr><tr><td>E1</td><td>集成测试</td><td>2</td><td>3</td><td>3</td><td>0.17</td></tr><tr><td>E2</td><td>系统测试</td><td>5</td><td>7</td><td>7</td><td>0.33</td></tr><tr><td>E3</td><td>缺陷修正</td><td>8</td><td>10</td><td>11</td><td>0.50</td></tr><tr><td>E4</td><td>回归测试</td><td>1</td><td>3</td><td>4</td><td>0.50</td></tr><tr><td>F1</td><td>系统试运行</td><td>2</td><td>3</td><td>3</td><td>0.17</td></tr><tr><td>F2</td><td>用户培训</td><td>4</td><td>5</td><td>5</td><td>0.17</td></tr><tr><td>F3</td><td>上线交付</td><td>2</td><td>3</td><td>4</td><td>0.33</td></tr><tr><td>F4</td><td>项目总结</td><td>1</td><td>1</td><td>1</td><td>0</td></tr><tr><td colspan="2"></td><td>合计</td><td></td><td></td><td>7.51</td></tr></table>

据此，项目在 $1 1 2 \pm 8$ 个工作日（1 个标准差）内完成的概率为 $6 8 . 2 7 \%$ ，在$1 1 2 \pm 1 6$ 个工作日（2个标准差）内完成的概率为 $9 5 . 4 5 \%$ ，而在 $1 1 2 \pm 2 4$ 个工作日（3个标准差）内完成的概率高达 $9 9 . 7 3 \%$ 。

项目甘特图（图 4.2）显示，项目于2024年10月9日开始，计划在 2025年3 月 13 日结束。由于略晚于合同签订交付日期完成项目，因此需要对项目进度计划进行优化。

![](images/e25addc122c77b5734df0d3c287a5840a384b8bf04f05d4ef8c50f00383abbd6.jpg)  
图 4.3正态统计分布图Fig. 4.3 Normal statistical distribution

# 4.4 基于关键链技术的进度计划优化

# 4.4.1 任务工期压缩

关键链技术考虑的是项目流程的逻辑关系。然而，在 D 公司 A 县紧密型县域医共体项目中，它并不仅仅局限于逻辑层面，还充分考虑到了项目中所涉及在项目过程中可能出现供不应求情况并且极有可能成为项目瓶颈的资源。通过对这些稀缺资源的合理规划和管理，关键链技术能够使项目计划更加贴近项目的实际情况，从而提高项目的执行效率和成功率。

鉴于项目的紧迫性，项目发起人高度重视项目的顺利推进。为了确保项目能够在紧张的时间内高质量地完成，聘请了一位具有丰富项目经验的顾问，对项目现有的各个环节进行了全面、深入的优化和改进。通过对项目流程的重新梳理、资源的合理分配以及潜在风险的有效识别和应对，为项目的成功实施提供了有力的保障。

在设计和编码阶段，需要投入大量的时间来利用统计技术进行手工收集和输入数据。这个过程不仅繁琐，而且容易出现人为错误。项目团队人员需要筛选、整理和录入各种数据，确保系统能够反映出预期的功能和性能。使得现有信息系统中已经包含了大量与本项目相关的数据，将导出的数据转换为适合本项目的格式。最后，将转换后的数据导入系统，使得系统能够快速地获取所需的数据，从而减少了手工收集和输入数据所需的大量时间。

在测试阶段，确定测试的目的和范围，并制定测试计划，确保团队充分了解测试的目的和优先事项。通过持续集成，集成自动化测试工具，自动执行测试用例，并快速发现和解决问题，以减少手动测试时间。利用虚拟化和容器技术优化测试环境，以加快配置、管理数据并确保一致性和效率。实施持续执行和风险管理策略，优先处理高风险测试用例，并为并行测试分配充足的资源，以进一步缩短执行时间。

本项目选择了 PERT三点估算法里的乐观时间 $\mathrm { T } _ { a }$ 作为D公司 A县紧密型县域医共体项目进度优化的工期。压缩后的项目任务工期，见表4.10。

表4.10 压缩后的项目任务工期  
Table 4.10 Compressed project task duration   

<table><tr><td>任务编码</td><td>任务名称</td><td>压缩后的工期</td><td>前置任务</td></tr><tr><td>A1</td><td>合同签订</td><td>1</td><td></td></tr><tr><td>A2</td><td>项目立项</td><td>1</td><td>A1</td></tr><tr><td>A3</td><td>项目启动仪式</td><td>1</td><td>A2</td></tr><tr><td>A4</td><td>组建项目团队</td><td>1</td><td>A3</td></tr><tr><td>B1</td><td>需求调研</td><td>9</td><td>A1</td></tr><tr><td>B2</td><td>需求确认</td><td>2</td><td>A4、B1</td></tr><tr><td>B3</td><td>需求评审</td><td>1</td><td>B2</td></tr><tr><td>B4</td><td>制定项目计划</td><td>2</td><td>B3</td></tr><tr><td>C1</td><td>配置分析设计</td><td>2</td><td>B4</td></tr><tr><td>C2</td><td>数据库设计</td><td>7</td><td>B4</td></tr><tr><td>C3</td><td>代码架构设计</td><td>4</td><td>C1、C2</td></tr><tr><td>C4</td><td>设计评审</td><td>2</td><td>C3</td></tr><tr><td>D1</td><td>开发环境配置</td><td>1</td><td>C4</td></tr><tr><td>D2</td><td>基础支撑平台配置</td><td>3</td><td>D1</td></tr><tr><td>D3</td><td>一体化能力中心配置</td><td>1</td><td>D1</td></tr><tr><td>D4</td><td>县级医院信息系统开发</td><td>12</td><td>D2、D3</td></tr><tr><td>D5</td><td>基层运营服务平台开发</td><td>13</td><td>D4</td></tr><tr><td>D6</td><td>共享与协同中心开发</td><td>6</td><td>D4</td></tr><tr><td>D7</td><td>代码走查</td><td>2</td><td>D5、D6</td></tr><tr><td>E1</td><td>集成测试</td><td>2</td><td>D7</td></tr><tr><td>E2</td><td>系统测试</td><td>5</td><td>E1</td></tr><tr><td>E3</td><td>缺陷修正</td><td>8</td><td>E2</td></tr><tr><td>E4</td><td>回归测试</td><td>1</td><td>E3</td></tr><tr><td>F1</td><td>系统试运行</td><td>2</td><td>E4</td></tr><tr><td>F2</td><td>用户培训</td><td>4</td><td>F1</td></tr><tr><td>F3</td><td>上线交付</td><td>2</td><td>F2</td></tr><tr><td>F4</td><td>项目总结</td><td>1</td><td>F3</td></tr></table>

# 4.4.2 优化项目关键路径

结合进度计划的甘特图、责任分配矩阵和关键链，发现任务逻辑存在变更情况。由于聘请了专业的项目顾问，原本系统设计的前置阶段为项目规划阶段，调

整后项目规划和系统设计可以并行。

![](images/e1d3c5db6f6dac1b94469dfaeb90b93f89f07735a9d90758d2734da5dd880808.jpg)  
图 4.4压缩工期后的项目网络图  
Fig. 4.4 Project network diagram after compression period

![](images/df7c424babff4861aa800731a51ded72910401799b016fc5a03fd3f2f7586f1f.jpg)  
图 4.5压缩工期后的项目甘特图  
Fig. 4.5 Gantt chart of the project after the compressed duration

B2 需求确认后便可进行 C1 配置分析设计和 C2 数据库设计工作。由此 B3需求评审、C1 配置分析设计、C2 数据库设计成为并行任务。根据压缩后的最新项目活动持续时间和资源调整情况，重新绘制 D 公司 A 县紧密型县域医共体项目的网络图，并得到压缩工期后的项目网络图，如图 4.4 所示，压缩工期后的项目甘特图，如图 4.5 所示。

通过优化项目关键路径中的任务持续时间，更新了项目甘特图，使总体工期时间的 112 个工作日，降低至 81 个工作日，共缩短了 31 个工作日。

# 4.4.3 项目关键链缓冲区的计算

上述限制项目工期的资源冲突和瓶颈已根据子任务的时间存量进行了调整。在实际操作中，项目计划经过修正，获得了更大的灵活性，并纳入了所有随机的风险因素，例如需求审查阶段可能出现的误解、相关历史数据的不足以及需求的变更。为了降低进度的延误带来的影响，在项目末端设置了项目缓冲区，同时在关键链路径与非关键链路径的交界处设置了汇入缓冲区。在本文中，缓冲区的计算囊括了任务的复杂性、风险弹性系数以及每个项目活动的潜在不确定性系数。这些元素的综合考量确保了缓冲区设计的科学合理性，有效提升了项目实施过程的稳定性和成功率。

（1）任务复杂性的计算

每项活动的任务复杂性将被考虑，活动及其前置任务决定了任务的复杂程度。如果环节中的任务总数保持不变，则任务的复杂程度与活动距离关键链末端的远近呈正相关。在本文中，项目的复杂度被定义为 $\alpha _ { \mathrm { i } }$ ，其计算公式为 4.2。

$$
\alpha _ { i } = \frac { n _ { i } } { g _ { i } }
$$

公式4.2

在项目中，任务的序号用 $i$ 表示， $\alpha _ { i }$ 则代表第 $i$ 个活动任务在项目链路上的复杂程度。对于第 $i$ 个任务，紧前任务的数量总和用 $n _ { i }$ 表示，其总和为 $n$ ，而 $g _ { i }$ 则表示第 $i$ 个任务所在项目链路上的任务总数，定义为 $g$ 。

（2）风险弹性系数的计算

风险弹性系数 $\beta$ 是衡量项目风险水平的指标，三点估算法被采用，其中， $a$ 代表最乐观时间， $m$ 代表最可能时间， $p$ 则代表最悲观时间，被纳入计算公式之中。其计算见公式 4.3。

$$
\beta _ { i } = \frac { T _ { m } - T _ { a } } { T _ { p } - T _ { a } }
$$

公式 4.3

（3）不确定影响因子的计算

为了更好地识别不确定性对关键链任务的影响，不确定影响因子??被分类为不同的类型，活动工期影响对照表，见表 4.11。通过这样的分类，有助于针对性地管理和应对这些潜在风险，活动工期的影响因子，见表4.12。

表 4.11活动工期影响对照表  
Table 4.11 Comparison table of influence of activity duration   

<table><tr><td>任务对工期的影响</td><td>无</td><td>低</td><td>相对较低</td><td>相对较高</td><td>高</td><td>很高</td></tr><tr><td>影响因子k</td><td>0</td><td>0.2</td><td>0.4</td><td>0.6</td><td>0.8</td><td>1</td></tr></table>

表4.12活动工期的影响因子  
Table 4.12 Influence factors of activity duration   

<table><tr><td>任务编码</td><td>任务名称</td><td>任务对工期的影响因子</td></tr><tr><td>A1</td><td>合同签订</td><td>0.2</td></tr><tr><td>A2</td><td>项目立项</td><td>0.6</td></tr><tr><td>A3</td><td>项目启动仪式</td><td>0</td></tr><tr><td>A4</td><td>组建项目团队</td><td>0.2</td></tr><tr><td>B1</td><td>需求调研</td><td>0.8</td></tr><tr><td>B2</td><td>需求确认</td><td>0.2</td></tr><tr><td>B3</td><td>需求评审</td><td>0.2</td></tr><tr><td>B4</td><td>制定项目计划</td><td>0.2</td></tr><tr><td>C1</td><td>配置分析设计</td><td>0.2</td></tr><tr><td>C2</td><td>数据库设计</td><td>0.2</td></tr><tr><td>C3</td><td>代码架构设计</td><td>0.2</td></tr><tr><td>C4</td><td>设计评审</td><td>0.2</td></tr><tr><td>D1</td><td>开发环境配置</td><td>0.2</td></tr><tr><td>D2</td><td>基础支撑平台配置</td><td>0.4</td></tr><tr><td>D3</td><td>一体化能力中心配置</td><td>0.2</td></tr><tr><td>D4</td><td>县级医院信息系统开发</td><td>0.6</td></tr><tr><td>D5</td><td>基层运营服务平台开发</td><td>0.2</td></tr><tr><td>D6</td><td>共享与协同中心开发</td><td>0.8</td></tr><tr><td>D7</td><td>代码走查</td><td>0.2</td></tr><tr><td>E1</td><td>集成测试</td><td>0.2</td></tr><tr><td>E2</td><td>系统测试</td><td>0.4</td></tr><tr><td>E3</td><td>缺陷修正</td><td>0.4</td></tr><tr><td>E4</td><td>回归测试</td><td>0.4</td></tr><tr><td>F1</td><td>系统试运行</td><td>0.2</td></tr><tr><td>F2</td><td>用户培训</td><td>0.2</td></tr><tr><td>F3</td><td>上线交付</td><td>0.2</td></tr><tr><td>F4</td><td>项目总结</td><td>0</td></tr></table>

运用 $\alpha$ 、 $\beta$ 、 $k$ 这三个指标以及三点估算法，就能得到项目任务的项目缓冲（Project Buffer，PB）和汇入缓冲（Feeding Buffer，FB），其中项目缓冲 PB 和汇入缓冲FB的计算方法分别见公式 4.4，公式 4.5。

$$
\mathrm { P B } = \sum _ { j \in M } K _ { j } ( \alpha _ { j } + \beta _ { j } ) ( T _ { e _ { j } } + T _ { a _ { j } } )
$$

$$
\mathrm { F B } = \sum _ { j \in N } K _ { j } ( \alpha _ { j } + \beta _ { j } ) ( T _ { e _ { j } } + T _ { a _ { j } } )
$$

项目缓冲 PB 汇总了关键路径所有任务活动缓冲值的数值，汇入缓冲 FB 汇总了非关键路径上所有任务活动缓冲值的数值。M为关键路径任务活动的集合，N 为非关键路径的任务活动的集合。把 PB 和 FB 定义为关键路径和非关键路径的位置。由于关键路径会因条件值的引入而发生变化，因此有必要重新验证和控制关键路径并检查每个数值，以确保项目能够按时完成。上述方法步骤用于计算D公司 A县紧密型县域医共体项目中每项任务的缓冲值，见表 4.13。

表4.13任务缓冲值计算  
Table 4.13 Calculation of task buffer value   

<table><tr><td>任务编码</td><td>Ta</td><td>Tm</td><td>Tp</td><td>Te</td><td>n</td><td>g</td><td>α</td><td>β</td><td>k</td><td>缓冲值</td></tr><tr><td>A1</td><td>1</td><td>2</td><td>3</td><td>2</td><td>0</td><td>19</td><td>0</td><td>0.50</td><td>0.2</td><td>0.10</td></tr><tr><td>A2</td><td>1</td><td>1</td><td>4</td><td>2</td><td>1</td><td>21</td><td>0.05</td><td>0</td><td>0.6</td><td>0.03</td></tr><tr><td>A3</td><td>1</td><td>1</td><td>2</td><td>2</td><td>2</td><td>21</td><td>0.10</td><td>0</td><td>0</td><td>0</td></tr><tr><td>A4</td><td>1</td><td>2</td><td>3</td><td>2</td><td>3</td><td>21</td><td>0.14</td><td>0.50</td><td>0.2</td><td>0.13</td></tr><tr><td>B1</td><td>9</td><td>11</td><td>18</td><td>12</td><td>1</td><td>19</td><td>0.05</td><td>0.22</td><td>0.8</td><td>0.22</td></tr><tr><td>B2</td><td>2</td><td>3</td><td>4</td><td>3</td><td>2</td><td>19</td><td>0.11</td><td>0.50</td><td>0.2</td><td>0.12</td></tr><tr><td>B3</td><td>1</td><td>1</td><td>2</td><td>2</td><td>3</td><td>20</td><td>0.15</td><td>0</td><td>0.2</td><td>0.03</td></tr><tr><td>B4</td><td>2</td><td>3</td><td>3</td><td>3</td><td>4</td><td>20</td><td>0.20</td><td>1</td><td>0.2</td><td>0.24</td></tr><tr><td>C1</td><td>2</td><td>3</td><td>4</td><td>3</td><td>3</td><td>19</td><td>0.16</td><td>0.50</td><td>0.2</td><td>0.13</td></tr><tr><td>C2</td><td>7</td><td>8</td><td>8</td><td>8</td><td>3</td><td>19</td><td>0.16</td><td>1</td><td>0.2</td><td>0.23</td></tr><tr><td>C3</td><td>4</td><td>5</td><td>6</td><td>5</td><td>4</td><td>19</td><td>0.21</td><td>0.50</td><td>0.2</td><td>0.14</td></tr><tr><td>C4</td><td>2</td><td>2</td><td>2</td><td>2</td><td>5</td><td>19</td><td>0.26</td><td>0</td><td>0.2</td><td>0</td></tr><tr><td>D1</td><td>1</td><td>1</td><td>2</td><td>2</td><td>6</td><td>19</td><td>0.32</td><td>0</td><td>0.2</td><td>0.06</td></tr><tr><td>D2</td><td>3</td><td>5</td><td>6</td><td>5</td><td>7</td><td>19</td><td>0.37</td><td>0.67</td><td>0.4</td><td>0.83</td></tr><tr><td>D3</td><td>1</td><td>1</td><td>2</td><td>2</td><td>7</td><td>19</td><td>0.37</td><td>0</td><td>0.2</td><td>0.07</td></tr><tr><td>D4</td><td>12</td><td>15</td><td>18</td><td>15</td><td>8</td><td>19</td><td>0.42</td><td>0.50</td><td>0.6</td><td>1.66</td></tr><tr><td>D5</td><td>13</td><td>15</td><td>16</td><td>15</td><td>9</td><td>19</td><td>0.47</td><td>0.50</td><td>0.2</td><td>0.39</td></tr><tr><td>D6</td><td>6</td><td>9</td><td>13</td><td>10</td><td>9</td><td>19</td><td>0.47</td><td>0.43</td><td>0.8</td><td>2.88</td></tr><tr><td>D7</td><td>2</td><td>3</td><td>3</td><td>3</td><td>10</td><td>19</td><td>0.53</td><td>1</td><td>0.2</td><td>0.31</td></tr><tr><td>E1</td><td>2</td><td>3</td><td>3</td><td>3</td><td>11</td><td>19</td><td>0.58</td><td>1</td><td>0.2</td><td>0.32</td></tr><tr><td>E2</td><td>5</td><td>7</td><td>7</td><td>7</td><td>12</td><td>19</td><td>0.63</td><td>1</td><td>0.4</td><td>1.30</td></tr><tr><td>E3</td><td>8</td><td>10</td><td>11</td><td>10</td><td>13</td><td>19</td><td>0.68</td><td>0.67</td><td>0.4</td><td>1.08</td></tr><tr><td>E4</td><td>1</td><td>3</td><td>4</td><td>3</td><td>14</td><td>19</td><td>0.74</td><td>0.67</td><td>0.4</td><td>1.13</td></tr><tr><td>F1</td><td>2</td><td>3</td><td>3</td><td>3</td><td>15</td><td>19</td><td>0.79</td><td>1</td><td>0.2</td><td>0.36</td></tr><tr><td>F2</td><td>4</td><td>5</td><td>5</td><td>5</td><td>16</td><td>19</td><td>0.84</td><td>1</td><td>0.2</td><td>0.37</td></tr><tr><td>F3</td><td>2</td><td>3</td><td>4</td><td>3</td><td>17</td><td>19</td><td>0.89</td><td>0.50</td><td>0.2</td><td>0.28</td></tr><tr><td>F4</td><td>1</td><td>1</td><td>1</td><td>1</td><td>18</td><td>19</td><td>0.95</td><td>0</td><td>0</td><td>0</td></tr></table>

计算出各任务缓冲值后，将关键路径的任务汇总，得到项目关键路径缓冲值

PB，见表4.14。除去关键路径的任务为汇入缓冲 FB，分别为：A2-A3-A4、B3-B4、C1、D3、D6，得到项目汇入缓冲值 FB，见表4.15。

表4.14 项目关键路径缓冲值PB  
Table 4.14 Project critical path buffer value PB   

<table><tr><td>任务编码</td><td>任务名称</td><td>缓冲值</td></tr><tr><td>A1</td><td>合同签订</td><td>0.10</td></tr><tr><td>B1</td><td>需求调研</td><td>0.22</td></tr><tr><td>B2</td><td>需求确认</td><td>0.12</td></tr><tr><td>C2</td><td>数据库设计</td><td>0.23</td></tr><tr><td>C3</td><td>代码架构设计</td><td>0.14</td></tr><tr><td>C4</td><td>设计评审</td><td>0</td></tr><tr><td>D1</td><td>开发环境配置</td><td>0.06</td></tr><tr><td>D2</td><td>基础支撑平台配置</td><td>0.83</td></tr><tr><td>D4</td><td>县级医院信息系统开发</td><td>1.66</td></tr><tr><td>D5</td><td>基层运营服务平台开发</td><td>0.39</td></tr><tr><td>D7</td><td>代码走查</td><td>0.31</td></tr><tr><td>E1</td><td>集成测试</td><td>0.32</td></tr><tr><td>E2</td><td>系统测试</td><td>1.30</td></tr><tr><td>E3</td><td>缺陷修正</td><td>1.08</td></tr><tr><td>E4</td><td>回归测试</td><td>1.13</td></tr><tr><td>F1</td><td>系统试运行</td><td>0.36</td></tr><tr><td>F2</td><td>用户培训</td><td></td></tr><tr><td>F3</td><td>上线交付</td><td>0.37</td></tr><tr><td>F4</td><td>项目总结</td><td>0.28</td></tr><tr><td></td><td>合计</td><td>0 8.90</td></tr></table>

采用四舍五入的方式，得到 D 公司 A 县紧密型县域医共体项目的项目缓冲PB值为9个工作日，加上乐观估计的总工期 81个工作日，该项目的最终工期为90 个工作日，与最初制定的项目进度计划中的总工期 112 个工作日相比，减少了 22 个工作日。

表4.15项目汇入缓冲值 FBTable 4.15 Project import buffer value FB  

<table><tr><td>任务编码</td><td>任务名称</td><td>缓冲值</td></tr><tr><td>A2</td><td>项目立项</td><td>0.03</td></tr><tr><td>A3</td><td>项目启动仪式</td><td>0</td></tr><tr><td>A4</td><td>组建项目团队</td><td>0.13</td></tr><tr><td>B3</td><td>需求评审</td><td>0.03</td></tr><tr><td>B4</td><td>制定项目计划</td><td>0.24</td></tr><tr><td>C1</td><td>配置分析设计</td><td>0.13</td></tr><tr><td>D3</td><td>一体化能力中心配置</td><td>0.07</td></tr><tr><td>D6</td><td>共享与协同中心开发</td><td>2.88</td></tr></table>

A2-A3-A4、B3-B4、C1、D3自由浮动时间较多，且按照四舍五入的方式，不予设置缓冲值，仅 D6需输入3个工作日的缓冲值 FB。

![](images/7ccdd3761f86d0d917f69a1a3824fe74a82b4eefdcb6cfa0b97b46a1dd0d2394.jpg)  
图 4.6设置关键链缓冲的项目甘特图  
Fig. 4.6 Project Gantt chart for setting key chain buffering

汇总后不影响项目进度的关键路径，输入项目缓冲值 PB 和汇入缓冲值 FB的甘特图,如图4.6 所示。

# 4.4.4 缓冲区管理

缓冲消耗与剩余量的情况需要被项目经理时刻关注，对缓冲区的管控可以被视为基于关键链的项目管理控制的核心[44]。通过采纳高德拉特提出的三色控制法，项目进展情况得以被清晰地区分，缓冲区消耗图，如图4.7所示。这种方法为项目经理提供了一个直观的框架，以更好地监控和评估项目的进展状态。

$\textcircled{1}$ 绿色区域代表项目的各项任务都在按计划顺利进行，没有出现延误或者潜在风险，资源分配合理。

$\textcircled{2}$ 黄色区域代表项目进度出现了一些小的偏差或者潜在风险，此时项目虽然在可控范围内，但需要引起注意。

$\textcircled{3}$ 红色区域代表项目已经出现了重大延误，或者遭遇到无法在短时间内解决的难题，必须立即采取紧急措施进行补救，否则项目很可能无法按时完成。

![](images/4504275b6d075731b0cb95fd00acf8d4576788b01022a21ec3099aa52930509e.jpg)  
图 4.7 缓冲区消耗图  
Fig. 4.7 Buffer consumption diagram

# 第 5 章 D 公司 A 县紧密型县域医共体项目进度控制

# 5.1 项目进度控制过程

项目进度控制包括进度的计划、实施、监管、分析、调整、再计划，需要在项目开展的全过程进行跟踪调查，将实际进度信息与计划目标进行对比分析，寻找偏差，最终寻求解决方法，调整原有计划，形成封闭循环[45]。

D公司A县紧密型县域医共体项目计划为期 90个工作日。项目的整体执行的初期只能提供活动阶段的大致情况，而无法进一步了解，尤其是项目细节、执行程序、需要准备的资源等。因此，项目进度管理计划表只能作为参考，后续的活动在每个执行阶段确定。此外，没有明确识别和定义具体的项目时间活动，也无法对项目的整个宏观过程进行跟踪，以便进一步管理。通过对项目管理计划的优化，进一步细化了项目工作活动，鉴于项目单体众多、工期紧迫、任务实施复杂。不断改进和加强分类收集状态信息、完善组织结构、改进目标报告管理、监控项目过程中的关键里程碑，确保项目状态数据得到持续管理，为项目未来发展提供充分保障。除了管控举措、持续改进、人员配备、沟通协作改进、及时监控、项目状态监控、工作水平监控等项目状态管理的整改措施外，制定了项目进度控制流程图，如图5.1所示。

在该项目中，进度控制流程有着清晰的步骤和逻辑。充分考虑项目的独特性质，着手进行工作的细致分解，明确各个环节的责任分配，合理安排工序的先后顺序，同时进行精确的时间估算。通过这些举措，制定出切实可行的项目进度计划。严格按照计划开展项目活动，对开发进展情况进行全面、实时的监控。这样可以及时掌握项目的实际进度状态，为后续的决策提供准确的数据支持。

将监控得到的结果与预先制定的计划进度进行对比分析。如果发现进度计划没有出现偏差，或者虽然存在偏差但对总工期并无影响，那么就继续按照计划稳步推进下一步工作。然而，如果检测出现了进度偏差，就必须立即组织专业人员进行深入的偏差分析。通过仔细研究和探讨，找出导致偏差的根本原因，并在此基础上提出合理、有效的建议和措施。如此一来，就能够最终确保工期不会出现拖延的情况，保障项目的顺利进行和按时完成。

![](images/34a8b7b102693f932ad3455ec64f105329bd8dc619b2dbd70a9bacf07ed1fa9f.jpg)  
图 5.1项目进度控制流程图  
Fig. 5.1 Project schedule control flow chart

# 5.2 项目进度监测

在 D 公司 A 县紧密型县域医共体项目期间，项目经理通过记录项目的实际进度来监控整个项目的动态。鉴以往的项目管理经验和本项目的性质，采用了以下方法来监控项目进度。

# 5.2.1 每日站立会

D 公司 A 县紧密型县域医共体项目所有成员在工作日上午 9 点召开 10-15分钟的会议。团队成员轮流汇报前一个工作日完成的工作、进度、挑战和今天要完成的任务。根据团队成员的报告，项目经理可以了解实际完成了多少工作，并及早发现项目问题，以便及时处理和解决。团队成员还可以在每日例会上分享经验，这有助于防止同样的问题再次发生，并提高积极性。

# 5.2.2 项目周报和日报

每周五下班前，项目经理都要为 D 公司 A 县紧密型县域医共体项目准备一份项目周报，并提交给项目管理办公室。项目周报的基本内容包括项目的每周和每日的进展情况遇到的问题，以及下周工作重点等。同时定期召开项目会议，并能协调到D公司的领导参加,项目经理随时了解项目进展情况。项目经理介绍项目的现状，提出任何问题，并在会议上讨论后，确定可以解决这些问题的部门和联系人。

项目经理以 D 公司 A 县紧密型县域医共体项目进度的具体数据为基础，监控项目进度，并在甘特图中实时记录项目进度。D公司目前也在根据自身的项目管理情况开发员工任务管理系统。项目经理为项目添加第一阶段的工作模块，将其划分为具体的第二阶段任务，并将工作任务分配给具体的团队成员，团队成员填写每日工作情况，然后由部门主管领导审批。项目经理可通过团队成员提交的项目动态了解项目的实际进度，可以更好的评估项目进度并进行优化。

# 5.2.3 前锋进度线法

在项目开始阶段，确定项目的关键路径和关键活动至关重要。借助专业的项目管理软件进行分析，明确那些对项目总工期起决定性作用的一系列活动组成的路径以及关键路径上的具体任务。这一步为后续的进度管理提供了明确的方向和重点关注对象。

实施期间要定期收集实际进度数据。数据内容包括已完成的任务、正在进行任务的进度百分比以及任务的实际开始时间和预计完成时间等。收集数据的方式有多种，一方面可以要求项目团队成员定期汇报各自负责任务的进展情况，确保信息的直接性和准确性；另一方面，利用项目管理软件的进度跟踪功能，实时记录任务状态，为进度分析提供可靠的数据支持。

选择合适的检查时间，在进度计划图表上，标记已完成的任务，并根据正在进行任务的进度百分比计算实际完成的工作量。从检查时刻开始，沿着实际进度方向绘制一条折线，连接已完成任务和正在进行任务的实际进度点。

将前锋进度线与计划进度线进行对比，判断项目进度情况。如果前锋进度线在计划进度线之前，说明项目进度超前，此时需要评估是否存在过度投入资源或质量风险等问题。如果两者重合，说明项目按计划进行，处于良好的推进状态。如果前锋进度线在计划进度线之后，说明项目进度滞后，需要深入分析原因。可能是任务难度超出预期，导致项目团队在执行过程中遇到困难；也可能是资源分配不合理，关键任务缺乏足够的支持；还可能是人员变动等不可预见的因素影响了项目进度。

当发现进度偏差时，要采取相应调整措施。如果进度超前，可以考虑调整资源分配，将多余资源调配到其他任务上，或者提前开始后续任务。如果进度滞后，可以增加资源，如增加人力、物力或延长工作时间等；调整任务安排，重新评估任务优先级和顺序，优先完成关键任务；优化技术方案，引入新的技术工具或改进现有技术流程，提高任务执行效率。

在整个项目过程中，持续定期检查实际进度，更新前锋进度线。通过不断地监控和调整，确保项目始终朝着既定目标前进。

# 5.2.4 挣值分析法

在 D 公司 A 县紧密型县域医共体项目中，运用挣值分析法对项目各个施工阶段施工实际状态与预期状态进行进度和成本对比，分析偏差产生原因并及时调整进度计划[46]。

利用已完工作预算费用、计划工作预算费用以及已完工作实际费用这三个关键数值，对费用和进度状况进行全面衡量。进度偏差（SV）是已完工作预算费用（BCWP）减去计划工作预算费用（BCWS）所得。而进度绩效指数（SPI）则为已完工作预算费用（BCWP）与计划工作预算费用（BCWS）的比值。当 SV 小于0或者 SPI小于 1时，即实际进度落后于计划进度。相反，当 SV 大于0或者SPI大于1时即实际进度快于计划进度。

从 D 公司项目管理 PMO 和财务部门得到的数据，了解到 D 公司 A 县紧密型县域医共体项目从启动阶段的 2024 年 10 月至 2024 年 11 月项目交付期间关于已完成工作预算费用 BCWP（万元）、计划工作预算费用 BCWS（万元）的情况。整理得到项目费用分析表，见表5.1。

表 5.1项目费用分析表  
Table 5.1 Project cost analysis table   

<table><tr><td></td><td>月份</td><td>10</td><td>11</td></tr><tr><td rowspan="2">月度状态</td><td>BCWS</td><td>160</td><td>272</td></tr><tr><td>BCWP</td><td>167</td><td>256</td></tr><tr><td rowspan="2">滚动状态</td><td>BCWS</td><td>227</td><td>499</td></tr><tr><td>BCWP</td><td>234</td><td>483</td></tr><tr><td rowspan="2">滚动比</td><td>SV=BCWP- BCWS</td><td>7</td><td>-16</td></tr><tr><td>SPI=BCWP/BCWS</td><td>1.03</td><td>0.97</td></tr></table>

在项目费用分析表可直观看出，在 2024 年的 11 月末，D 公司 A 县紧密型县域医共体项目的项目进度偏差为负数，项目进度发生了延误。

# 5.3 项目进度偏差分析与调整

# 5.3.1 项目进度偏差分析

对照项目进度计划和收集、整理的项目进度信息，进行项目进度偏差分析和趋势预测，形成项目进度分析报表及进度讲评材料，提出处理意见，为项目进度纠偏、项目决策提供依据[47]。项目进度偏差分析是指项目经理评估项目进度表是否与团队成员在特定时间点报告的项目实际进度相匹配。如果进度表出现偏差，需通过项目经理与项目团队成员共同讨论，并分析偏差及其影响。如对项目造成一定影响，则应讨论如何避免延迟的解决方案，如果无法避免延迟，则应要求调整项目计划。因此，按照了这三个步骤对 D 公司 A 县紧密型县域医共体项目进行对比分析：

（1）检查项目中是否存在偏差

项目经理把 D 公司 A 县紧密型县域医共体项目计划与每日例会确定的实际进度进行比较，并通过比较两者的时间差来确定项目差异。如果同一节点任务的实际完成时间早于计划完成时间，则项目差异为正数，并计算天数。如果实际完成时间晚于计划完成时间，则项目差异为负数，并计算天数。

通过上述方法监控项目进度后发现，D 公司 A 县紧密型县域医共体项目各阶段在关键路径上和非关键路径的活动均受到影响，4 项项目活动差异为负数。在关键路径上有 2 项活动滞后，分别为 D2 基础支撑平台配置和 D4 县级医院信息系统开发。此外，非关键路径上有 2项活动滞后，分别为 C1配置分析设计和D3一体化能力中心配置。偏差活动与进度计划对比，见表5.2。

表 5.2偏差活动与进度计划对比  
Table 5.2 Comparison of deviation activities and schedule   

<table><tr><td>任务编码</td><td>任务名称</td><td>计划工期</td><td>实际工期</td></tr><tr><td>C1</td><td>配置分析设计</td><td>2</td><td>3</td></tr><tr><td>D2</td><td>基础支撑平台配置</td><td>3</td><td>5</td></tr><tr><td>D3</td><td>一体化能力中心配置</td><td>1</td><td>5</td></tr><tr><td>D4</td><td>县级医院信息系统开发</td><td>12</td><td>15</td></tr></table>

# （2）分析偏差的影响

根据 D 公司 A 县紧密型县域医共体项目的偏差分析，确定现有偏差会影响到项目的进度。需要进行具体的偏差活动分析，因为项目活动之间存在着顺序关系，部分任务有多个前置任务，所以即使有些任务提前完成，后续任务也不一定会提前开始，反之，推迟某些任务也不一定会影响项目的整体进度。

关键路径法在项目进度管理中经常被使用，对项目计划的偏差进行评估，关键路径上的 D2 基础支撑平台配置和 D4 县级医院信息系统开发的滞后会导致项目延误；非关键路径的 C1配置分析设计仅滞后 1天，且浮动时间较为充裕，因此不会影响整体项目进度，但 D3一体化能力中心配置滞后时间较长，与关键路径上的D2基础支撑平台配置并行，判断出偏差时间有可能会超过任务的总时差，超过的总时差会转移到项目的关键路径上，也会影响项目总工期。

因此项目经理与团队成员讨论 D2 基础支撑平台配置、D3 一体化能力中心配置和 D4县级医院信息系统开发的滞后问题，根据实际情况调整进度计划，避免影响项目总体进度。

（3）分析偏差产生的原因

项目经理与团队成员和相关方讨论 D 公司 A 县紧密型县域医共体项目出现偏差的原因，避免在项目后续执行中出现类似问题和偏差。对关键路径上的工期滞后时间相加，得到项目滞后总时间为： $2 + 3 = 5$ 个工作日。对以上 4项进度滞后活动的偏差原因进行分析，具体如下：

C1 配置分析设计。项目团队在初期阶段对需求的理解不够深入，不但系统众多而且功能逻辑复杂，在进行配置分析时，若未能明确配置项的分类和关系，在必要的配置项判断不准确，导致了将资源或组件错误的归类，在首次评审时，开发部组内成员在定义基线和配置项工具选择和实现方案存在较大分歧，进行了多次的修改和调整，从而拖延进度。

D2 基础支撑平台配置。由于业务应用层与数据层间缺乏有效的分离，导致了大量零散和分散的数据信息的产生，不利于开发的数据治理。这种信息的混乱状态使得各个数据之间难以建立有效的连接，进而阻碍了数据价值的挖掘和充分利用。此外，由于基础数据整合和管理不当，基础统计数据显得相当不完整，医疗系统对统计数据的获取变得困难。这种状况不仅降低了数据的可用性和准确性，也降低了医疗业务系统集成的效率，对项目进度计划的执行造成了阻碍。

D3 一体化能力中心配置。信息资源中心组件是基层运营服务平台开发的核心模块，涉及卫生资源库、健康档案库、电子病历库、全员人口库等诸多内容，这些内容与县内原有的基础平台交互量较大。但开发团队未能充分考虑到原有平台的扩展性和可用性、支撑环境较差且运营维护管理混乱等原因，导致了交互过程中出现较多问题，花费了较多的工作量进行问题修复和组件的重新定义，使得

任务工期发生滞后。

D4 县级医院信息系统开发。有两个原因导致了该活动进度滞后，首先在需求调研阶段，客户的需求阐述模糊，导致了开发人员对功能模块的理解不全面。在实际开发中，客户对功能的界定理解逐渐清晰，造成多次需求变化，使得先前开发的内容无法满足客户价值，需要重新开发，影响了开发效率。此外，一名关键开发人员由于自身原因休假 3 天，进一步影响项目进度计划的执行。

# 5.3.2 项目进度调整

在确定项目偏差的原因后，D 公司 A 县紧密型县域医共体项目的项目经理采取针对性的措施来调整项目活动，这包括赶工、增加人力资源、缩小项目范围、进度计划变更等手段。项目进度纠偏的关键在于及时发现和响应偏差，以最小化对项目整体时间和成本的影响。有效的进度纠偏通常依赖于详尽的计划和精确的数据分析，确保调整措施的科学性和可行性。通过动态管理，项目进度控制可以更加灵活和适应变化，最终帮助项目按期完成。

（1）赶工

当执行 D 公司 A 县紧密型县域医共体项目时，尤其是关键路径上的任务出现周期性延迟或资源紧张时，加班被认为是弥补损失时间的适当方式。在 D 公司在实施赶工时，权衡了工期缩短带来的收益与成本增加的损失，确保工期提前不会影响到项目质量或其他重要目标。往往赶工适用于关键路径上的任务，且在项目管理中是一种紧急情况下的应对策略，因此慎重考虑了其长期影响。

# （2）增加人力资源

对于关键路径活动，它们之间的相互依赖关系并不是说不等前一个活动完成就可以开始的。在这种情况下，D公司通过增加人力资源的方式来完成这些任务，然后在前一个任务完成后再完成剩余的任务，这样可以在一定程度上缩短整个项目的工期。对于一些开发密集型的任务，如果在之前的工作任务中分配给了一名团队成员，则可以在他们离开项目时将任务拆分，并在添加新人时重新分配。例如，开发人员在编写需求说明时没有参与编码工作，可以在该开发人员有空闲时间时让他们参与进来，这样不仅能加快进度，还能让开发人员先了解项目需求，以便在后续的软件开发中更好地理解用户需求。

# （3）缩小项目范围

在 D2 基础支撑平台配置和 D4 县级医院信息系统开发时，通过限制该项目

的范围，与客户协调商定，根据项目目标和关键成功因素，对所有功能进行优先级排序，保留核心功能，删除或推迟次要功能。确定并实现最小可行产品（MVP），确保基本功能的可用性和稳定性，满足主要用户群体的核心需求。

# （4）计划变更

对于项目中有较多的变更需求，而以上三种方法都无法满足原计划的要求，这个时候通过对剩余的项目任务进行计划变更，具体的任务变更步骤如下：

# （a）确定变更影响

当需求发生变化时，项目经理召集所有团队成员讨论变更的影响，包括但不限于项目的范围、正在开发的功能的质量和完整性，以及团队成员间的工作分配。

# （b）提交变更申请

项目经理填写项目需求变更申请表和项目计划变更申请表。项目需求变更申请表描述了原始需求、修改或增加的项目需求，以及实现这些需求所需的人力资源和工作量。项目计划变更申请表根据所需的人力资源和工作量调整项目计划，包括任务的初始时间节点、变更后的时间节点、变更所需的项目资源，以及最重要的是对整个项目的影响。然后，项目经理将变更申请表发送给业务部门和客户代表。

# （c）变更进度计划

D公司相关负责人和客户代表召开需求变更会议，以确定是否确认计划变更，以及客户是否对需求和计划变更有任何异议。如果有异议，他们会进行讨论，直到双方都满意为止；如果没有异议，客户代表就会签字批准。一旦进度表变更获得批准，项目经理就会更新甘特图以反映新的进度表，将任务与项目信息管理系统同步，并通知项目团队收集变更文件。

# 5.4 项目进度计划实施保障

# 5.4.1 组织保障

组织管理是保证项目顺利实施的重要措施，项目建设期间经常会出现项目进度计划编制不合理、项目进度管控措施执行不到位、项目进度保障措施不健全等问题，最终导致项目出现延期交付的现象[48]。

本项目以进度计划的实施为保障，通过组织措施在内部管理层面提高项目进度计划管理能力，从而促进项目的发展。作为整体规划的责任主体，项目的优秀

项目人员、部门可以通过密切配合、沟通协调，更好地落实项目的实施方案和实施计划，并利用项目管理办公室和咨询服务机构、高端人才和专业机构实施现场监督，完善多项组织保障措施。

（1）制定可行的每周和每月计划

制定更详细的每周和每月运行计划，与整个项目计划保持一致，并将其细分为日常执行活动。项目经理还通过审查各部门的月计划和周计划来跟踪项目发展的规律性。

# （2）合同管理措施

在该项目中，必须严格遵守实施合同，使每个流程节点的进度与合同相符。所有项目管理都以合同为基础。

（3）项目管理信息系统

D公司在项目管理信息系统更是与时俱进，最大限度地发挥信息技术的效益。利用信息系统对 D 公司 A 县紧密型县域医共体项目的各工作节点、重要里程碑的完成情况、进度计划、实际进度与计划进度进行清晰的监控，并进行比较，以便出现问题时，可以立即采取相应的措施。

# 5.4.2 激励保障

将 D 公司 A 县紧密型县域医共体项目总体目标细化为具体的、可衡量的阶段性目标和关键里程碑，如在特定时间内完成系统设计、编码阶段等。团队成员清楚了解每个阶段的工作重点和预期成果，为激励提供明确的参照标准。

设立与项目进度挂钩的奖励基金，根据团队成员在项目中的贡献和表现，给予不同形式的奖励，包括但不限于奖金、奖品、荣誉证书、晋升机会、培训机会等。对于按时或提前完成关键任务和里程碑的团队或个人，给予额外的奖励，如项目奖金的一定比例加成、优先推荐晋升等。

通过组织团队建设活动、定期的项目聚餐等方式，增强团队成员之间的沟通与协作，提高团队凝聚力和归属感。在项目工作环境中，展示项目的愿景和目标，以及团队成员的工作成果和贡献，营造积极向上的工作氛围，激发团队成员的工作热情和积极性。

根据项目成员的技能和兴趣，制定了个性化的职业发展规划，明确在医疗信息化领域的职业上升通道。如对技术人员，规划从初级开发工程师到高级开发工程师、技术主管、架构师等的发展路径，让团队成员看到自己在项目中的成长空间和发展前景，激发为 D 公司 A 县紧密型县域医共体项目努力工作的动力。

# 5.4.3 技术保障

为确保信息系统开发项目的高效实施，建立比较完善的监督和评价机制，并为项目开展提供充分的技术保障，应预先制定统一且符合国家和行业开发标准的技术规范[49]。同时创造良好的内部讨论环境，为开发人员的个人发展和项目建设提供良好的条件和沟通渠道。

（1）版本控制与代码管理

针对 D 公司 A 县紧密型县域医共体项目，使用 Git、SVN 等版本控制系统，对项目代码进行集中管理。开发团队成员可方便地提交、更新和合并代码，同时通过版本控制系统的日志功能，清晰记录代码的变更历史，便于追溯和解决问题，避免因代码冲突导致的进度延误。

# （2）自动化构建与部署

利用 Maven、Gradle 等构建工具和 Jenkins、TravisCI 等持续集成/持续部署（CI/CD）工具，实现代码的自动化构建、测试和部署。开发人员提交代码后，自动触发构建和测试流程，及时发现和解决代码中的问题，确保代码质量，加快开发迭代速度。

（3）测试管理与优化

引入专业的测试管理系统如 TestRail、QualityCenter 等，对测试用例进行集中管理和执行跟踪。测试人员可根据测试计划在系统中创建和执行测试用例，记录测试结果和缺陷，方便开发人员及时查看和处理缺陷，提高缺陷修复效率，确保项目质量和进度。

使用性能测试工具如 LoadRunner、JMeter等，对系统进行性能测试，模拟高并发场景，检测系统的响应时间、吞吐量、资源利用率等性能指标。根据测试结果，对系统进行优化，如优化数据库查询语句、调整服务器配置等，确保系统满足医疗业务的性能需求，避免因性能问题影响项目上线时间。

# 5.4.4 知识经验保障

为了避免在 D 公司 A 县紧密型县域医共体项目进行过程中出现开发信息不对称等现象，在项目开始前对项目团队成员进行培训，使每个成员都了解自己的任务和解决问题的方法。为确保项目顺利进行，提供以下类型的培训：项目活动培训、开发标准培训、开发技能培训和项目管理系统培训。

开发实践的标准化可促进代码的标准化和后续维护，而培训则可确保团队成员能够快速上手并使用软件。项目管理办公室对项目团队成员进行了项目管理系统的培训，包括如何将必要的信息及时上传到共享服务器，以及在审查会议和每周报告中应注意的事项。通过一系列培训，项目组成员不仅了解了 D 公司 A 县紧密型县域医共体项目，还开始了解项目管理流程，这有助于项目的顺利进行。同时，培训还帮助员工建立了项目管理思维，加强了他们对项目进度的关注，并引导他们利用所学到的项目管理知识提高自身绩效。

在项目进行的过程中，难免会遇到一些困难，团队成员之间缺乏沟通，不分享问题和积累的经验，当其他团队成员遇到同样的问题时，就会无奈地自己去寻找解决问题的办法，造成时间浪费，影响项目进度。采用在线飞书文档的形式，对问题、解决方案、创建人和时间进行描述。与此同时，在团队内部建立经验共享机制，鼓励项目团队成员在日常会议上简要描述项目中遇到的问题和解决方案，然后将具体信息输入经验知识库，这样任何面临相同问题的人都可以快速解决问题。同时，可以鼓励那些提供真正有用知识的人，提高他们分享经验的热情和积极性，提高项目的进度和质量。

# 第 6 章 结论与展望

# 6.1 结论

本文将理论联系实际，以 D 公司 A 县紧密型县域医共体项目为对象，把项目进度管理的相关理论切实应用，从项目的进度计划的编制到进度控制结合项目的实际进展，发现其存在的问题，找到原因提出改善措施并持续优化。不仅提高了项目中进度管理的质量，提高开发效率，更进一步为今后类似项目的管理提供了宝贵的经验和依据。研究成果主要结论如下：

$\textcircled{1}$ 通过对 D 公司 A 县紧密型县域医共体项目进度影响因素分析，研究发现项目的主要影响因素为员工技术能力、设计变更和相关方的影响。

$\textcircled{2}$ 项目进度计划的编制是连贯的过程，需要在完整的把项目活动进行分解的基础上，得到任务分配矩阵和活动之间的逻辑关系，进行时间估算、绘制网络图来确认项目的关键路径。项目经理和项目团队成员需深入了解项目特性，把项目管理理念植入到项目执行的全流程中，提升项目进度计划的质量具有不可或缺的重要作用。

$\textcircled{3}$ 进度控制的实施贯穿于项目的整个生命周期，包括预先规划、进程监控和事后评估。其核心策略涵盖组织架构的强化、制度规则的完善以及技术手段的支持等方面。通过对项目实施过程的持续跟踪与评估，对各项资源进行优化配置，并适时采取纠正措施以调整可能出现的进度偏差。此过程严格执行，以确保项目按预定进度目标圆满完成。

# 6.2 展望

本文应用项目进度计划与控制相关知识，系统地分析此项目的进度计划和控制方法和流程，受到本人专业水平以及实践经验的限制，在项目进度控制研究过程中还存在很多不足之处，研究内容也不够到位，需要通过后续的学习以及实践来不断完善。此外，D 公司A县紧密型县域医共体项目其管理范畴极为广泛，涵盖项目质量把控、风险管控、成本节制、采购治理以及沟通协调管理等诸多方面。就本研究而言，相关研究仅仅围绕该项目的进度管理展开，对于其他众多方面均未予以涉及。这无疑为后续的研究提供了丰富的可能性，可以针对这个案例，在

其他各个方面进行更加深入的探索。

在众多值得深入挖掘的方向中，动态进度计划模型的研究显得尤为关键。在实际的项目管理实践中，一旦对进度计划产生的偏差采取纠正举措，很可能会致使活动逻辑关系以及资源约束关系发生变化。鉴于此，对项目的动态进度计划模型展开深入研究迫在眉睫。在面临这些变化的情况下，如何确保项目进度始终保持合理且高效，以及怎样才能更好地应对可能出现的各类挑战。探索如何提前预判这些变化，以便采取更加有效的预防措施，从而保障项目的顺利进行。

# 参考文献

[1] 刘智明,张良文,韩耀风,袁满琼,方亚.县级公立医院综合能力评价体系研究[J].中国医院,2024,28(04):22-25.  
[2] 彭建军,汪化睿.公共服务均等化促进中华民族共同体建设的内在逻辑——以医疗卫 生为 例 [J].中南民族 大学 学报 (人 文社会 科学 版 ),2024,44(03):28-$3 7 + 1 8 2$ .  
[3] 方淑芬,刘干文,徐亮,杨进意.项目管理技术在水表行业的应用探讨[J].中国仪器仪表,2021,(03):35-38.  
[4] 吴振全.谈我国工程建设项目管理发展阶段[J].中国工程咨询,2021,(04):23-27.  
[5] 彭宇行,郭阳,李思昆.费用限制下的工程进度计划[J].计算机工程与科学,2000,(02):12-14.  
[6] 赵国华,赵子鉴,戴磊.基于模糊TOPSIS方法的桥梁工程项目工程造价预测[J].综合运输,2023,45(09):110-116.  
[7] 邢晓霞,薛凯,谷志红,霍成军.基于模糊优选模型的输变电工程造价管理成效评价研究[J].价值工程,2022,41(07):34-36.  
[8] 张可,张政,金伟.数据-知识融合的水利工程建设安全风险灰色因子分解机预测模型[J].水利水电技术(中英文),2024,55(01):134-143.  
[9] 王艳彤,苏义坤,苏伟胜,何廷全.高速公路建设进度智慧化管理效果评价[J].科学技术与工程,2021,21(30):13071-13077.  
[10] 胡晨,徐哲,李明.基于多资源约束和活动调整优先级的关键链识别方法研究[J].数学的实践与认识,2015,45(23):48-56.  
[11] 赵野.谈工程项目的进度控制[J].工程建设与设计,2013,(12):170-172.  
[12] 梁婉清,莫俊文.基于 Crystal Ball 的改进 PERT 项目工期分析[J].项目管理技术,2020,18(12):23-27.  
[13] 郑称德,赵曙明.面向中断风险防范的准事制供应链——后成本时期供应链管理研究(IV)[J].生产力研究,2003,(06):215-217.  
[14] Tchendji Maurice Tchoupé,Ndadji Milliam Maxime Zekeng,Parigot Didier.AGrammatical Approach for Administrative Workflow Design and their DistributedExecution Using Structured and Cooperatively Edited Mobile Artifacts[J].SNComputer Science,2024,5(5):499.  
[15] Junxiu Tang,Fumeng Yang,Jiang Wu,Yifang Wang,Jiayi Zhou,Xiwen Cai,LingyunYu,Yingcai Wu.A Comparative Study on Fixed-order Event SequenceVisualizations: Gantt, Extended Gantt, and Stringline Charts.[J].IEEE transactionson visualization and computer graphics,2024,30(12):7687-7701.  
[16] S. Sinika,G. Ramesh.Trapezoidal Neutrosophic Program Evaluation and ReviewTechnique Using Interval Arithmetic Operations[J].IAENG International Journalof Applied Mathematics,2024,54(3):324-341.  
[17] Thordur Vikingur Fridgeirsson,Helgi Thor Ingason,Haukur Ingi Jonasson,HelenaGunnarsdottir.A Qualitative Study on Artificial Intelligence and Its Impact on theProject Schedule, Cost and Risk Management Knowledge Areas as PresentedinPMBOK $\textsuperscript { \textregistered }$ [J].Applied Sciences,2023,13(19):11081.  
[18] Zijian Mao,Qiang Han,Yu He,Nan Li,Cong Li,Zhihui Shan,Sheng Han.A SoftwareTesting Workflow Analysis Tool Based on the ADCVMethod[J].Electronics,2023,12(21):4464.  
[19] 徐辛昌,王慢丽.配电网工程施工建设的质量控制与管理方法研究[A]全国绿色数智电力设备技术创新成果展示会论文集(一)[C].中国电力设备管理协会,中国电力设备管理协会,2024:3.  
[20] Imene Jemal,Wilfried Armand Naoussi Sijou,Belkacem Chikhaoui.Multi-modalrecommender system for predicting project manager performance within acompetency-based framework[J].Frontiers in Big Data,2024,71295009-1295009.  
[21] Hongbo L ,Yiwu C ,Qiao L , et al.Data-driven project buffer sizing in criticalchains[J].Automation in Construction,2022,135:104134.  
[22] Hasan Monjurul,Lu Ming.Mitigating Project Schedule Risks by IdentifyingSubcritical Paths and Variance-Critical Activities[J].Journal of ConstructionEngineering and Management,2024,150(8):04024096.  
[23] Figueroa–García Juan Carlos,Hernández–Pérez Germán,Ramos–Cuesta JenniferSoraya.Uncertain project network analysis with fuzzy–PERT and Interval Type–2fuzzy activity durations[J].Heliyon,2023,9(4):14833.  
[24] Nazimko Victor V,Zakharova Ludmila M.Project Schedule Expediting underStructural and Parametric Uncertainty[J].Engineering ManagementJournal,2023,35(1):29-49.  
[25] Blaga F S,Pop A,Hule V,Karczis A,Buzdugan D.Using critical path method for anew project scheduling - the case of a new product launch in production[J].IOPConference Series: Materials Science and Engineering,2021,1009(1):012005.  
[26] Zhu Yi’an,Chen Jie,Li Lian.A critical path task scheduling algorithm based onsequential failure factor[J].The Journal of Supercomputing,2023,80(7):9353-9383.  
[27] Akram Muhammad,Habib Amna.A novel Pythagorean fuzzy PERT approach tomeasure criticality with multi-criteria in project managementproblems[J].Granular Computing,2024,9(2):36.  
[28] M. Navya Pratyusha,Ranjan Kumar.Enhancing Critical Path Problem inNeutrosophic Environment Using Python[J].Computer Modeling in Engineering& Sciences,2024,140(3):2957-2976.  
[29] Oussama Siwane,Robert Pellerin,Issmail El Hallaoui.New visual resource-oriented margins for the Resource Constrained Project SchedulingProblem[J].IFAC PapersOnLine,2024,58(19):1108-1113.  
[30] Woo Jong Hun,Kim Byeongseop,Ju SuHeon,Cho Young In.Automation of loadbalancing for Gantt planning using reinforcement learning[J].EngineeringApplications of Artificial Intelligence,2021,101:104226.  
[31] Zhao Zhang,Bing Guo,Gui Xiang Chen,Jiu Le Song.The Review of ProjectManagement Based on the Theory of Constraints and Critical Chain[J].AppliedMechanics and Materials,2012,1801(174-177):3424-3430.  
[32] 王园璞,黄伟群,刘微,王超,梁亮.平衡矩阵企业项目人力资源管理分析与借鉴[J].现代企业,2023,(11):43-45.  
[33] Efimochkina N.B..Organizational design: meaning, essentialcharacteristic[J].Interaktivnaâ Nauka,2017, 5 (15):107-112.  
[34] 王 韬 . 医 院 绩 效 及 运 营 管 理 信 息 化 发 展 现 状 分 析 [J]. 中国数字医学,2021,16(10):1-4.  
[35] 胡德隆.信息化赋能智慧医院建设的意义和路径[J].中国信息化,2022,(09):76-77.  
[36] 朱 潇 . 工 程 管 理 中 先 进 工 程 技 术 的 应 用 与 实 践 [J]. 中 国 质 量 监管,2024,(03):178-179.  
[37] 刘嘉.基于市场经济下的建筑经济成本管理办法[J].居业,2024,(09):215-217.  
[38] 周兰芳 . 企 业 成 本 管 理 工 作 中 的 全 面 预 算 管 理 措 施 探 析 [J]. 营 销界,2024,(09):161-163.  
[39] 李晖.信息系统项目管理中的进度管理[J].数字技术与应用,2023,41(09):46-48.  
[40] 邓泉,尹红星,韩效锋,刘志宏,夏金瑶,陈俊凌.国家重大科技基础设施建设管理探索与创新——以聚变堆主机关键系统综合研究设施(CRAFT)为例[J].科技管理研究,2023,43(06):190-195.  
[41] Cabezas MaríaFrancisca,Nazar Gabriela.A scoping review of food and nutritionliteracy programs.[J].Health promotion international,2023,38(5): daad090.  
[42] Yao Chuantao,Wang Jian,Sun Hao,Chu Haiyang,Jin Tao,Xiang Quanzhou.A Data-driven method for adaptive resource requirement allocation via probabilistic solarload and market forecasting utilizing digital twin[J].Solar Energy,2023,250368-376.  
[43] Juan Zhao,Yao Xiao.Application Research of Critical Chain Technology in ProjectPortfolio Schedule Management[J].Journal of Engineering System,2024,2(1):51-58.  
[44] Specht Mariusz.Consistency analysis of global positioning system position errorswith typical statistical distributions[J].Journal of Navigation,2021,74(6):1201-1218.  
[45] 刘 桂 林 . 房 地 产 开 发 项 目 进 度 把 控 与 风 险 管 理 [J]. 中 国 住 宅 设施,2023,(02):130-132.  
[46] 丁姣月,张耀宗,王若晨.基于挣值法的高原冻土地区公路施工进度及成本管理[J].科技与创新,2024,(17):90-92.  
[47] 韩庆国,陈新瑜,王光宗.大型炼化项目进度管理措施及实施效果分析[J].建设监理,2021,(01):46-49.  
[48] 龚侠义.数据中心建设项目进度管理优化策略[J].产业创新研究,2022,(22):29-31.  
[49] 陈璐,胡永祥,张执南,奚立峰.产教融合,用“真问题”培养学生创造力和执行力[J].高等工程教育研究,2023,(05):65-69.