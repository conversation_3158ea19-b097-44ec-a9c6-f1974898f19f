# 软件项目进度管理的实用分析

# 宋雪琴 对外经济贸易大学国际商学院管理学研修班

摘 要 由于软件项目的特点具有诸多不确定性的因素，导致很多软件项目无法在计划时间内完成，造成项目成本超支或者交付质量达不到要求。随着软件行业竞争日益激烈，项目管理能力也逐渐在企业内部受到重视。在项目管理中有三大核心要素：时间、质量、成本。可见进度管理是项目管理中的一个重要课题。文章结合多年在互联网公司参与项目的工作经验，介绍了软件项目的特点，分析了实际工作中影响软件项目进度的常见原因，并分别给出了相应的解决方法。希望能够对软件企业的项目进度管理有所帮助。

关键词 软件项目 项目进度 进度管理

中图分类号：TP311 文献标识码：A 文章编号：1674-1145（2023）02-034-03

# 一、软件项目的特点

软件项目与一般的工程项目是有很大区别的，先了解软件项目的特点，才能更好地总结项目进度管理中问题的解决办法。

软件项目的目标通常不明确，经常会遇到一种情况，去和需求方洽谈需求时，需求方自己也无法描述清楚自己想要什么，只能给个大概，然后由项目经理带回来进行梳理，再拿着设计稿去跟需求方反复确认需要开发的具体功能有哪些。但随着开发过程推进，项目会逐渐变得清晰，这时需求方就可能随时提出变更，希望变成他们更想要的样子，因此引发项目延期的风险。

从国内外软件企业的项目实施情况来看，由于软件具有抽象性、复杂性，人力、资金、设备等多种因素都具有不确定性，以及专业项目管理人员的缺乏，导致软件项目失败率较高。

软件项目不像建筑工程项目，能随时看到盖到几层。软件项目的“摩天大楼”是由一行行代码组成的，它是不可见的，通常需要通过开会和检查文档等手段来了解当前的进度，或者根据已完成的模块来验收测试是否符合预期。

软件项目一般迭代速度较快，有的公司甚至每周都会对软件项目进行一次迭代优化。多数公司不止一个项目在开发，而是多个项目并行，目的是小步快跑，尽快占领市场，打败竞争对手。

软件项目管理的发展阶段尚不成熟，虽然越来越多的企业开始重视项目管理，但仍然有一些软件企业对项目管理不够重视，项目经理对项目管理知识体系不够了解，在项目沟通协调、流程规范等方面都存在不足，甚至有些技术出身的项目经理一味地追求简便实现而忽略了产品体验和用户真正的需求。

# 二、影响软件项目进度的常见问题

软件项目通常是由公司中的某个团队或者跨团队协作完成的，每个项目在需求明确以后都要制定项目实施计划。为了在计划时间内按时完成交付，项目管理人员控制好整体项目进度是该项目的决胜条件之一。

要控制好项目进度，就需要考虑项目实施过程中有哪些常见问题会影响到项目进度，并提前制定相应对策做好相关的风险防范。

面对大多数公司的项目特别是敏捷型项目，由于迭代节奏较快，项目管理者很少会用到复杂的技术手段去制定项目计划和跟进项目进展。通常的做法是使用project 或者在 excel 中绘制甘特图跟进项目进展。在这种情况下，如何准确地评估出项目排期，并且如何有效地保证项目各阶段的进展是我们需要思考和解决的问题。

根据项目管理知识体系的五个过程组：启动阶段、计划阶段、执行阶段、控制阶段、收尾阶段的工作内容，我们只有把每个阶段的任务都按时完成，才能保证后面的环节不会顺延[1]。由于大量的工作任务落在计划、执行、控制阶段，所以这里主要针对这几个阶段的工作内容来分析哪些常见问题会影响项目进度。

# （一）需求分析

# 1. 需求确认

洽谈需求时，不论是甲乙方公司合作的情况，还是自己公司内部主导做的项目，最好有项目经理和研发负责人一起在场聆听需求，这样可以从技术角度大致判别该项目的可行性，如果是非产研部门单独与需求方洽谈，很容易拍脑袋答应一个交付时间，给产研部门制造难题。

由于软件项目的需求特点通常是模糊的，首先应当仔细领会需求方的真正意图，去伪存真，然后将这些需求进行整理加工，我们可以利用快速原型法做一版设计demo 拿给需求方确认，哪里有不合适可以及时修正。这样才能保证项目整体的走向不偏离轨道。经用户初步确认的需求，一般需要与产研部门开需求评审会，同步具体要开发的功能模块，确定最终要做的功能模块。会后还有关键的一步是：要与需求方及主要干系人有正式的书面确认，可以是纸质版签字，如果是异地合作的部门或团队也可以邮件确认最终需求。

好的开始是成功的一半，如果没有准确的理解用户的需求，以及具体要做什么不做什么，后续可能浪费了大量时间做的都是无用功。

# 2. 需求变更

由于软件项目的开发过程逐渐清晰，实施过程中需求方随时会提出需求变更，比如今天想把 A 功能增加A1 子功能，刚做完 A1 功能又提出想把 A 变成 B 功能。如果不约束需求变更流程，任由需求方不断提出新的想法，团队成员不断地接受修改，不但会导致原工期延迟，还有可能做出来的结果跟需求评审确定的设计不一致。

需求变更可以说是对项目进度影响最大的因素，ISO9000 认证和 CMM 认证也都十分强调对需求的变更控制，所以我们在开头就要确定好规则：需求一旦确定尽量不变，如果要变必须遵循需求变更流程。这个约定可以写在甲乙双方的合约里，自己公司内部做的项目就约定在项目立项的相关文件中。需求方每提出一次变更，都要让需求方确认并接受可能付出的代价，比如：进度延迟、人力成本的增加等。这有助于将需求变更带来的不利影响减到最低程度。小的需求变更可以定期集中理一次，以节约时间，不影响到当前的项目进度。

# （二）任务分解

需求确认后要制定项目实施计划，包括：设计、开发、测试、验收各里程碑的时间确定。可以利用 WBS 工具将软件项目自上而下分解成一系列目标明确的工作包，并且每一个工作包都要安排一个主要责任人和对应的完成日期。这样出现问题时就能很快找到明确的负责人，避免相互推诿的现象。创建WBS时应由团队成员共同参与，目的让大家了解自己的负责内容，对分配的任务没有歧义。

这里特别说一下开发排期的评估，需要让有经验的研发负责人详细拆分每个模块下包含的具体任务，颗粒度越小越好。不能仅根据过去的项目乐观的大致的估算，这样得出的工期偏差较大。评估开发排期时应预留出在开发过程中问题修改的时间、开会讨论等时间；刨去在公司的休息时间、假期等综合评估，这样评估出来的排期才是相对精确的，对开发阶段的顺利完成是极大的保证。

# （三）项目文档

项目文档是软件项目最主要的输出成果，全面的项目文档也是项目进度的保证。有的企业由于各项流程制度不是很健全，为了追求效率，开发项目时只有口头传达，这样会导致团队成员在实施过程中没有确切的参考，很容易理解错误或遗忘，最终造成项目进度延迟或项目失败。

好记性不如烂笔头，虽然编写项目文档也比较耗时，不过对于后续版本的迭代开发和项目控制提供了便利[2]。比如需求文档、开发计划、概要设计、详细设计、代码走查单、测试计划、产品说明文档、系统上线记录等都是常见的输出成果。有了项目文档，项目干系人可以随时查看每个版本的修订记录，清楚地了解每个阶段做了哪些工作，对于项目管理跟进也都非常有帮助。

# （四）质量保证

在项目生命周期中， $80 \%$ 的质量问题是项目管理造成的。从软件设计、团队组建、角色分工、流程规范的制定，都离不开项目管理的质量把控。特别需要充分重视设计阶段的工作，好的产品设计直接决定了项目质量。如果由开发人员直接编码，走一步看一步，容易造成大量时间返工，产品体验差。项目经理一定要尽量保证需求清晰、设计合理的前提下再进行开发。

有时候由于需求方拍脑袋决定一个完成时间，根本来不及做细致的任务分解，只能粗略地定一下开发计划。这种情况下势必会造成开发质量的下降，过度压缩项目完成时间就无法保证质量。整体交付时间紧张，项目的每个阶段都会被压缩，这样对整个项目质量就会埋下重大隐患。比如代码中存在 bug 没有被发现，个别功能的实现方式为了快速完成改为其他方式实现，但其实与用户需求不符。

如果是正常排期的项目，项目经理一定要分阶段地及时验收已完成的工作是否符合要求，方便及时修改，保证项目质量。如果到了项目快结束才去验收，就可能出现项目早已偏离轨道不可控，此时再返工为时已晚。

# （五）合作沟通

软件项目的实施对人的依赖性比其他行业更为突出 [2]，项目成员能否积极配合、高效完成任务，是项目成败的关键因素之一。因此需要加强团队成员之间的合作沟通。

在一个公司内部，产品、开发、测试、运维可能是不同的部门或团队，一个项目离不开各方的通力合作。有的任务在部门间是依赖串行关系，如：产品设计不出图，前端开发就没法做；开发某功能需要接口，需要等别的团队提供，如果对方迟迟不提供接口，就只能干等着，对项目进度来说是个挑战。

在项目实施过程中，如何保持与他人良好的沟通是一门必修课，让对方愿意积极配合你，是一种情商的体现，也是保证项目进度的必要条件。非正式的口头沟通可以用于私下与他人日常沟通、维护关系的渠道；正式的邮件沟通主要用于需要双方确认和留存证据的渠道，会议沟通可以用于日常各方交换意见、信息同步的渠道。

如果双方沟通不畅，没有强烈的合作意愿，那么当某些任务存在依赖关系时，就会造成时间拖延，影响项目进度。

# （六）团队建设

要让团队成员能高效完成任务，项目经理应充分激发团队成员的主观能动性和积极性，为项目成功助力。有时项目时间紧迫，团队成员经常加班过于疲劳，会导致写出的代码质量差，来回返工修改。或者增加新的人手之后，由于新人对项目代码不熟悉，也会影响正常的开发进度。遇到这种情况，项目经理在组建团队时需要考虑到 AB 角的问题，争取让每个团队成员都能有适当调休的时间。在成本可控情况下，可采用加班费、项目奖金等制度激励项目成员的积极性。有的公司没有条件做到跨团队借调资源，也没有时间给调休，那么项目经理应在需求明确以后跟需求方争取一个合理的上线时间。

每周项目组可以召开一次会议，集中时间为大家解决疑难问题。重要紧急的项目每天可以开个站会了解彼此的任务进度。但为保证项目整体进度，在项目实施过程中尽量减少其他不必要的会议，尽量缩小参会人员范围。有的公司上班时间工作效率不高，而导致员工经常加班。因此，要着力提高工作效率。比如，开会前组织者要想好本次会议的目标是什么，全程围绕目标达成进行讨论，会议主持人注意把握时间节奏，营造会议中的和谐氛围，让团队成员把更多的时间投入到项目实施中。

# （七）人员分配

无论是大厂还是中小企业，项目中资源冲突是经常会碰到的问题。在有的公司是可以借调共用的，有的公司只能用自己团队内部成员，这就需要提前考虑资源的合理配置。比如某公司只有一位前端开发小张，他在 A项目的排期是 1 ～ 7 号，B 项目的排期是 $8 \sim 1 0$ 号，但由于系统环境等原因 A 项目在 6 号测试联调没有通过，需要小张继续配合联调完成上线。如果没有提前合理的安排和严密的监控，那么 B 项目就无法如期开始。

多个项目共用一个资源时，项目经理需要知道该资源在每个项目的时间安排，以便为共用资源合理分配时间。另外项目经理要有一个项目看板，随时监控共用资源在每个项目的任务完成情况，以及跟相关项目的进展情况，因为相关项目的进展延迟也可能造成自己项目的风险。

# （八）流程规范

在软件项目实施过程中，有时会出现这样的现象：UI 设计完成了效果图直接交给开发，没有经过产品经理的审核；开发完之后不知道何时该提测，测试人员测出的 bug 没有在打包范围。团队协作不够顺畅，出现问题互相指责。

解决此问题就需要编制公司的项目管理制度，在制度中明确每一个项目阶段对应的工作内容、负责人，以及相关的解释说明。加强项目流程培训、代码规范等培训，让项目各个任务有明确的边界，也便于项目经理更好的控制项目。

通过规范的流程制度可以更好地实现成功的项目管理，对于软件项目的每一个阶段工作结束之后要对该阶段的工作活动进行评价，并对后续阶段的时间、人员、资金等方面的需求做出估计，每个阶段的工作成果需经项目管理部门审查合格后方能开始下一阶段的工作[2]。

（九）项目跟进

有的公司有专门的项目管理部门，由项目经理来跟进项目，有的公司只有产品经理兼任项目经理来跟进项目。不论是哪种情况，项目经理不一定要懂技术，但作为项目经理的职责本身是及时、深入地了解每个问题的根源所在，最好是能更多地预判风险、避免风险，而不是说解决的问题越多就越合格。

当某类问题第一次出现时就要深入分析并为团队成员解决掉，尽量避免以后再发生同样的问题耽误时间。比如某个功能要求一周开发完成并上线，但到期开发人员反馈由于环境原因、其他系统依赖等无法按时上线，如果这些理由在之前的项目中也遇到过不止一次，那么项目经理就应该提前做好风险预案，保证项目整体进度。

# 三、结语

项目进度管理是决定一个项目成败的关键因素。要引领项目走向成功就要提前判断相关风险，把各种不可控因素的发生概率降到最低。如果能从本文介绍的这几个常见的影响因素入手，识别并加以防范，相信能够帮助软件企业提高效率、节约成本、保证质量，提高在软件行业的竞争力。好的开头是项目顺利完成的基石，要控制好项目整体进度，还是需要从一开始就约定好相关的流程规范，加上团队成员的紧密配合、实时的风险监控，一定可以使软件开发项目取得成功。

★【作者系对外经济贸易大学国际商学院管理学研修班学员。】

# 参考文献 :

[1] 窦燕 . 影响软件项目管理关键因素的探讨 [J]. 燕山大学学报 ,2004(4):370-371.

[2] 杨海 . 软件项目进度控制浅析 [J]. 项目管理技术 ,2009(S1):228-230.