2 周高效完成 30 篇传统进度管理文献综述（MEM 标准）  
Todo List（按天拆分，周末可机动）

第 0 天（今天，周三）：一次完成  
- [ ]  1. 建立文献管理库：Zotero 新建 4 个文件夹  
  – 01-背景问题  – 02-研究意义  – 03-理论框架  – 04-工具方法  
☐ 2. 打开 Google Scholar / Web of Science / CNKI，把上一回答给出的 8 篇代表性文献全部导入 Zotero，打好标签（年份、语言、类别）。  
☐ 3. 在 Excel 建立“文献矩阵”模板：列 = 编号|题目|作者|年份|类别|摘要|可用页码|引用次数|PDF 路径。  
☐ 4. 把本 Todo 打印或同步到手机日历，设置每日 21:00 自动提醒。

第 1 天（周四）：中文背景+意义 6 篇  
☐ 5. CNKI 高级检索：主题=“AI 软件项目 进度延误” AND 年份=2022-2025，选核心期刊 3 篇 → 文件夹 01。  
☐ 6. CNKI 检索：主题=“进度管理预研 价值” 2 篇 + 博硕论文 1 篇 → 文件夹 02。  
☐ 7. 阅读并填矩阵：摘要 100 字以内，高亮“延期率、主要原因、预研收益”。

第 2 天（周五）：英文背景+意义 5 篇  
☐ 8. Web of Science 检索：TS=(“AI project schedule overrun”) PY=2022-2025，筛选 Article & Review，下载全文 3 篇 → 文件夹 01。  
☐ 9. Scopus 检索：TS=(“front-end planning” AND “project success”) 2 篇 → 文件夹 02。  
☐ 10. 用 Zotero 自动抓取引文格式（APA 7th），一键导出到 Word 备用。

第 3 天（周六）：中文理论框架 6 篇  
☐ 11. CNKI：关键词=“关键链缓冲”或“CCPM” AND 年=2022-2025，选 4 篇期刊 + 2 篇硕博论文 → 文件夹 03。  
☐ 12. 阅读→矩阵；标记“缓冲计算方法、适用场景”。

第 4 天（周日）：英文理论框架 3 篇  
☐ 13. Web of Science：TS=(“critical chain project buffer sizing”) 2 篇。  
☐ 14. IEEE Xplore：TS=(“earned value management” AND “software project”) 1 篇 → 文件夹 03。  
☐ 今日机动：补齐不足页码，扫一遍摘要即可。

第 5 天（周一）：中文工具方法 6 篇  
☐ 15. CNKI：主题=“MS Project” OR “Primavera P6” AND “进度计划” 2022-2025，取 6 篇 → 文件夹 04。  
☐ 16. 用 Excel 绘制“工具-功能-优势”对照表，为综述表 4 做准备。

第 6 天（周二）：英文工具方法 2 篇 + 机动 2 篇  
☐ 17. ScienceDirect：检索“project scheduling tools comparison” 2022-2025，选 2 篇 → 文件夹 04。  
☐ 18. 机动：若某类不足，用“滚雪球”法补 2 篇（查已下载文献的参考文献）。

第 7 天（周三）：文献矩阵汇总  
☐ 19. 统计：中文 23 篇，英文 7 篇（≥5 篇达标）。  
☐ 20. 用 Excel 做透视表：按类别、年份、期刊分布，生成图表用于综述“研究趋势”。

第 8 天（周四）：撰写综述 1.研究背景  
☐ 21. 打开综述模板，先写 800-1000 字背景，引用文件夹 01 的 6 篇中文 + 3 篇英文。  
☐ 22. 插入图 1：AI 项目延期率柱状图（来源[1][2]数据）。

第 9 天（周五）：撰写综述 2.问题提出 & 3.研究意义  
☐ 23. 问题提出：200 字，直接引用延期率数据。  
☐ 24. 研究意义：600-800 字，引用文件夹 02 的 3 篇中文 + 2 篇英文。  
☐ 25. 用 EndNote 插入引用，检查格式。

第 10 天（周六）：撰写综述 4.文献回顾 4.1 理论框架  
☐ 26. 按“关键路径-关键链-挣值法”顺序写 1200 字，引用文件夹 03 的 9 篇文献。  
☐ 27. 插入表 1：三种理论优缺点对照表。

第 11 天（周日）：撰写综述 4.2 工具方法  
☐ 28. 写 1000 字“MS Project vs Primavera vs Excel 模板”，引用文件夹 04 的 8 篇文献。  
☐ 29. 插入表 2：工具功能矩阵。

第 12 天（周一）：撰写综述 5.文献评述 & 6.研究展望  
☐ 30. 文献评述 500 字：指出“传统方法在 AI 金融场景应用不足”。  
☐ 31. 研究展望 300 字：提出本研究将用传统方法做预研规划。

第 13 天（周二）：整合全文  
☐ 32. 合并所有章节，检查总字数 ≥ 8000 字（MEM 通常要求）。  
☐ 33. 用 Grammarly/知网查重，确保重复率 < 15%。

第 14 天（周三）：格式与提交  
☐ 34. 按学校模板调整封面、目录、图表编号。  
☐ 35. 最终校对：参考文献格式（APA 7th）、图表清晰度、页脚页码。  
☐ 36. 输出 PDF，文件名：MEM_进度管理文献综述_2025-08-20。

完成！