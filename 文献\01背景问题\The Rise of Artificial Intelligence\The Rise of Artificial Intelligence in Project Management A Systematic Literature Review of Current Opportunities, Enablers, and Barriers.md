Review

# The Rise of Artificial Intelligence in Project Management: A Systematic Literature Review of Current Opportunities, Enablers, and Barriers

S<PERSON><PERSON> 1,\*, <PERSON> $\mathbf { 1 } _ { \textcircled { 1 0 } }$ , <PERSON><PERSON><PERSON><PERSON> $^ 2 \textcircled { | | }$ , <PERSON> 3, <PERSON><PERSON> 4, <PERSON> 5 and <PERSON> 1 $\textcircled{1}$

Academic Editor: <PERSON><PERSON>ceived: 3 March 2025   
Revised: 26 March 2025   
Accepted: 27 March 2025   
Published: 30 March 2025

Citation: Salimimoghadam, S.; Ghanbaripour, A.N.; Tumpa, R.J.; <PERSON><PERSON>, <PERSON>; <PERSON>, M.; <PERSON>, S.; <PERSON>, <PERSON>. The Rise of Artificial Intelligence in Project Management: A Systematic Literature Review of Current Opportunities, Enablers, and Barriers. Buildings 2025, 15, 1130. https://doi.org/10.3390/ buildings15071130

Copyright: $\textcircled{ C } 2 0 2 5$ by the authors. Licensee MDPI, Basel, Switzerland. This article is an open access article distributed under the terms and conditions of the Creative Commons Attribution (CC BY) license (https://creativecommons.org/ licenses/by/4.0/).

Faculty of Society and Design, Bond University, Gold Coast, QLD 4226, Australia; <EMAIL> (A.N.G.); <EMAIL> (M.S.) School of Engineering and Technology, Central Queensland University, Norman Gardens, QLD 4701, Australia; <EMAIL> 3 Faculty of Medicine, The University of Queensland, St. Lucia, QLD 4072, Australia; <EMAIL> 4 Faculty of Engineering, University of Tehran, Tehran **********, Iran; <EMAIL> 5 Faculty of Architecture and Built Environment, Queensland University of Technology, Brisbane, QLD 4001, Australia; <EMAIL> Correspondence: <EMAIL>

Abstract: Artificial Intelligence (AI) and Machine Learning (ML) have transformed the landscape of project management and contributed significantly to improving efficiency, decision-making, and optimizing resource allocation. Although there exists a number of research examining the integration and utilization of AI and ML into project management, the fragmented literature highlights the need for a systematic literature review to consolidate current knowledge, identify emerging trends, and examine AI’s role in project management. This study aims to critically analyze the existing literature to identify opportunities for, enablers of, and barriers to AI adoption, providing a comprehensive framework to guide future research and practice. A systematic literature review (SLR) following PRISMA (Preferred Reporting Items for Systematic Reviews and Meta-Analyses) guidelines revealed three key themes: The Knowledge Ecosystem in Project Management: In the Era of AI, The Intersection of AI and Humanity in Project Management, and Integrating AI into Project Management and Landscaping. The findings highlight AI’s transformative effects on forecasting accuracy, risk mitigation, stakeholder collaboration, and safety management while addressing challenges such as integration with legacy systems, data quality issues, and resistance to change. The research presents valuable insights for both researchers and practitioners, facilitating the navigation of adoption barriers, capitalizing on enablers, and unlocking AI’s potential to reshape project management practices across industries.

Keywords: artificial intelligence; machine learning; project management; AI-driven risk management; resource optimization; predictive analytics

# 1. Introduction

Artificial intelligence has substantially revolutionized the ways projects are planned, executed, and delivered [1]. The significant role of project management across various industries is undeniable as it plays a crucial role in providing the structure and guidance necessary for achieving successful outcomes. In recent years, the use, application, and integration of AI in the landscape of project management has gained attention from researchers, academics and policymakers. AI has the potential to streamline operations by automating repetitive tasks, optimizing resource allocation, and improving risk assessment, resulting in more efficient project execution [2–6].

AI presents numerous opportunities in project management by enhancing efficiency, decision-making, and project outcomes. Such technologies including Artificial Neural Networks (ANNs), Fuzzy Logic, and Genetic Algorithms (GAs) hold the promise to automate routine tasks, advance complex decision-making, optimize scheduling and resource enhancement (GAs) [7–9]. Nonetheless, the successful execution of AI in managing projects is subject to a range of critical drivers.. Key enablers include the need for big data for training AI models, advanced algorithms such as ANNs and Monte Carlo Simulation for risk assessment and performance prediction, and strong institutional or governmental support to establish the necessary infrastructure [10,11]. For example, Smart Dubai’s AI Lab and AI roadmap offer a framework that promotes innovation and supports AI adoption. Moreover, integrating AI into Project Management Information Systems (PMISs) ensures seamless project lifecycle monitoring, enabling real-time updates and improved decision-making processes [12].

Despite its advantages, several barriers hinder the effective use of AI in project management. The inefficient implementation of AI can result from insufficient training and poor data quality [12]. Additionally, the high cost of AI tools and infrastructure presents financial challenges, particularly for smaller organizations [10]. Moreover, some organizations are resistant to change and embrace technologies in projects as AI adoption requires significant adjustments to the business as usual [13]. The obstacles are associated with ethical and regulatory concerns, including trust, privacy, and governance issues, highlighting the call for gradual and carefully monitored adoption processes [14].

AI has transformed and revolutionized various fields by enabling systems to mimic human intelligence for problem-solving and decision-making. A central concept within AI is Machine Learning (ML), a subset that allows systems to identify patterns and relationships from data without explicit programming. ML can process large datasets and generate predictions which are considered key to successful project management, where managing complex information poses significant challenges [15]. ML has the prospect and capacity to offer predictive accuracy, optimize resource allocation, and advance decision-making processes. For instance, ML can utilize the historical project data to forecast timelines and budget requirements, thereby improving planning and enabling data-driven decisions that contribute to more successful project outcomes [16]. It is particularly useful for navigating challenges including defect classification and risk assessment, offering valuable insights into project features and defects [15]. Moreover, being the crucial component of big data revolution, ML empowers systems to analyze extensive datasets, uncover patterns, and make informed predictions, continuously adapting and improving over time [17].

There is a stock of literature which examined the enablers and barriers of the application of ML in construction management, nonetheless, these aspects are often not explored in depth demanding further exploration. Data availability and quality have been highlighted as critical challenges. Macarulla et al. (2013) emphasized the importance of consistent data collection to ensure effective ML application [18]. Algorithm selection is also decisive, with decision trees (DTs), Bayesian networks (BNs), and ANNs frequently utilized. However, the choice of an appropriate algorithm is highly context-dependent [15,19]. Infrastructure limitations for big data analytics and data imbalance further complicate accurate defect classification, as noted by Elmasry et al. (2017) and Das and Chew (2011), respectively [20,21].

Although interest in the intersection of AI and project management is increasing, the literature review reveals a significant gap. While several studies have explored the potential applications of AI in project management frameworks [6,22,23], no comprehensive study has systematically analyzed the barriers and enablers associated with AI integration in project management. Existing research tends to focus on specific aspects, such as automation, decision-making, and risk assessment, without providing a holistic understanding of the challenges organizations face in adopting AI-driven solutions. This gap limits the ability to develop effective strategies for overcoming adoption barriers and leveraging AI’s full potential in project management.

To bridge this gap in the literature, the study systematically examines both the enablers and barriers to AI integration within project management frameworks. This research identifies key factors and provides useful recommendations for researchers, academics, and practitioners. A thorough review of the literature is essential for advancing knowledge in this field and guiding organizations in implementing AI more effectively. Understanding these barriers and enablers will enable stakeholders to develop targeted strategies to mitigate risks, capitalize on opportunities, and optimize AI-driven project management practices. Therefore, the following research questions are formulated:

1. What opportunities exist for integrating AI/ML into project management?   
2. What enablers support the successful implementation of AI/ML in enhancing project management processes?   
3. What barriers hinder the successful implementation of AI/ML in enhancing project management processes?

This study aims to identify and critically evaluate the literature on implementing AI and ML to enhance project management processes. “Implementation” is a deliberate effort to integrate, adapt, or incorporate interventions into established routines [24]. A systematic literature review (SLR) was conducted to analyze published studies, providing a foundation for a comprehensive investigation into the opportunities, barriers, and enablers associated with AI/ML in project management.

Following this introduction, Section 2 outlines the methodology used, Section 3 presents the descriptive and content analysis of the reviewed studies, Section 4 provides a detailed discussion of the findings, Section 5 summarizes the key insights, Section 6 outlines future research directions, and Section 7 offers practical recommendations.

# 2. Methodology

The most relevant materials can be identified effectively and transparently using SLRs and adhering to standardized review processes [25]. SLRs provide an in-depth summary of the literature, enabling the identification of research gaps within the field and serving as a valuable foundation for furthering knowledge. An SLR was conducted to explore how AI can enhance project management and to identify associated challenges. To minimize reporting bias, systematic reviews need to adhere to a clearly defined methodology and protocol prior to initiating the review process. For instance, the protocol ought to specify the research question and provide sufficient methodological detail to ensure replicability (Research Techniques Made Simple: Assessing the Risk of Bias in Systematic Reviews). Research questions were established in line with these principles, and significant steps for addressing them were outlined in advance. The SLR followed the Preferred Reporting Items for Systematic Reviews and Meta-Analyses (PRISMA) guidelines, covering three key phases: identification, screening, eligibility assessment, and inclusion for review [26]. This process is illustrated in Figure 1.

![](images/998fe5549dcc481b17fc08ab8a7bd570947a5de7312649fe430966030fba7367.jpg)  
Figure 1. PRISMA flowchart of selected articles (prepared by the authors).

During the identification phase, Scopus, ProQuest, and Google Scholar were selected During the identification phase, Scopus, ProQuest, and Google Scholar were selected as the primary databases for retrieving relevant papers. Keywords related to “Artificial as the primary databases for retrieving relevant papers.Keywords related to “Artificial Intelligence”, “Machine Learning”, and “Project Management” were chosen for the search. ntelligence”, “Machine Learning”, and “Project Management” werTwo search strings were developed to encompass different synonyms:

search. Two search strings were developed to encompass different synonyms:#1: Artificial Intelligence OR AI OR Machine Learning OR ML OR deep learning OR neural #1: Artificial Intelligence OR AI OR Machine Learning OR ML OR deepnetworks OR logistic regression OR natural language processing OR NLP. networks OR logistic regression OR natural language processing OR NLP.#2: Project management OR PM OR smart manufacturing OR deploy OR integrate OR implementation.

roject management OR PM OR smart manufacturing OR deploy OR integrate OR imple-The final search strategy was defined as #1 AND #2. The search strings were applied mentation.across multiple databases, resulting in the identification of 425 documents from Scopus, The final search strategy was defined as #1 AND #2. The search strings were applied154 from ProQuest, and 235 from Google Scholar. This comprehensive search strategy enacross multiple databases, resulting in the identification of 425 documents from Scopus, sured a thorough examination of the topic by capturing a wide range of relevant literature.

om ProQuest, and 235 from Google Scholar. This comprehensive search strategy en-The overall search produced 814 papers (Figure 1). The inclusion and exclusion criteria ured a thorough examination of the topic by capturing a wide range of relevant literature.were applied during the screening phase to select relevant studies. The criteria included The overall search produced 814 papers (Figure 1). The inclusion and exclusion cri-publication in peer-reviewed journals and conferences, full-text availability, being written eria were applied during the screening phase to select relevant studies. The criteria in-in the English language, publication within the past 12 years, and a focus on AI in project luded publication in peer-reviewed journals and conferences, full-text availability, being management. Gray literature was excluded due to the absence of peer review, which did written in the English language, publication within the past 12 years, and a focus on AI in not meet the rigorous established inclusion criteria. Duplicates across databases were project management. Gray literature was excluded due to the absence of peer review, removed at this stage, leaving 342 papers. From there, the research team carefully reviewed which did not meet the rigorous established inclusion criteria. Duplicates across databasesthe titles to filter out irrelevant studies. Papers that seemed suitable based on their titles were removed at this stage, leaving 342 papers. From there, the research team carefully were then evaluated by reading their abstracts. Those that met the criteria moved on to reviewed the titles to filter out irrelevant studies. Papers that sefull-text review, ultimately narrowing the selection to 68 papers.

titles were then evaluated by reading their abstracts. Those that met the criteriaTo enhance the rigor of the review, quality evaluation criteria were meticulously moved on to full-text review, ultimately narrowing the selection to 68 papers.defined to ensure that only studies directly relevant to the research questions were included. To enhance the rigor of the review, quality evaluation criteria were meticulously de-A scoring method (Table 1) was developed based on insights from previous studies [27] fined to ensure that only studies directly relevant to the research questions were included.and a comprehensive review of the literature. Each criterion was assessed using a binary A scoring method (Table 1) was developeyes/no approach. Studies that received a $" \mathrm { n o } ^ { \prime \prime }$ ed on insights from previous studies [27] on any quality criterion were excluded. For and a comprehensive review of the literature. Each criterion was assessed using a binary instance, articles discussing AI or Machine Learning without addressing their application yes/no approach. Studies that received a “no” on any quality criterion were excluto project management were deemed irrelevant and excluded from the analysis.

Table 1. Quality assessment criteria.   

<table><tr><td>Quality Assessments (QAs)</td><td>Scoring Method</td></tr><tr><td>QA1-Does the study explore opportunities for integrating AI/ML into project management?</td><td>Yes/No</td></tr><tr><td>QA2--Dmentiotudy il/MLinpablers fr thgsutensfsl</td><td>Yes/No</td></tr><tr><td>QA3—Does the study address barriers to the successful implementation of AI/ML in project management?</td><td>Yes/No</td></tr><tr><td>QA4-Does the study provide a detailed description of the research methods, objectives,and analysis?</td><td>Yes/No</td></tr></table>

Ultimately, 68 studies met the inclusion criteria and were selected for data analysis, forming the foundation for the review. Although the literature offers no consensus on the required number of studies for a systematic literature review, it was notable that only 68 papers qualified for inclusion. Studies were excluded during the full-text screening due to either a lack of relevance or failure to meet the predefined quality assessment criteria. Specifically, articles lacking detailed descriptions of research methods, objectives, or analysis (QA4), or those that did not comprehensively explore opportunities, enablers, or barriers to AI/ML adoption in project management (QA1–QA3), were excluded. This process ensured the inclusion of only robust and reliable studies that directly addressed the research questions.

There is little doubt that Artificial Intelligence plays a critical role across multiple sectors and regions globally, with increasing applications transforming industries such as healthcare, finance, education, and transportation [28]. However, the body of empirical literature on AI in project management remains limited. Despite the relatively small number of studies identified, several systematic literature reviews (SLRs) in related fields have been conducted with a similar scope, reflecting a growing interest in the barriers and enablers of AI adoption in project management.

For example, Abanto et al. [29] examined how AI enhances lean construction practices, revealing benefits such as streamlined processes, cost and schedule optimization, and improved collaboration through Building Information Modeling (BIM). Their findings emphasized the role of data-driven decision-making in minimizing risks, improving productivity, and promoting sustainability. Similarly, Liu et al. [30] explored AI’s application in production, operations, and logistics management in modular construction through bibliometric analysis, identifying its potential to improve management efficiency, enhance data quality and security, support real-time decision-making, and highlight future research directions. Integration with the IoT for improved data management and the focus on model performance and sustainability were key recommendations for future research.

These studies suggest that a systematic approach to a literature review is more significant than the number of studies included, as the selection criteria and study focus can vary depending on the topic [31,32]. The final set of articles was analyzed thematically using an established method to ensure rigor in qualitative research. This thematic analysis is widely recognized for identifying meaningful patterns and extracting insights from qualitative data [33]. A content-based coding system was developed for the data analysis [34,35]. The emerging themes were refined through a focus group discussion with five academics and two professionals. The focus group provided a collaborative environment that facilitated diverse perspectives, allowing for the identification of nuanced ideas and gaps. The moderated discussion ensured balanced participation, sharpening the identified themes and aligning them with the research objectives and contextual realities [36].

To ensure consistency and transparency, a structured data extraction process was implemented, documenting key details such as study objectives, AI methodologies, implementation challenges, and findings. This process enabled a systematic comparison across studies and facilitated a deeper understanding of AI adoption in project management.

The extracted data were categorized into three primary themes: The Knowledge Ecosystem in Project Management: In the Era of AI, which explores AI’s impact on knowledge-sharing and decision-making; The Intersection of AI and Humanity in Project Management, which examines human–AI collaboration, ethical considerations, and evolving managerial roles; and Integrating AI into Project Management and Landscaping, which focuses on AI-driven automation, predictive analytics, and risk management.

A three-step thematic analysis was conducted to refine these themes. First, key patterns and recurring topics were coded manually, grouping similar insights across studies. Next, the themes were refined through peer discussions to ensure clarity and alignment with the research objectives. Finally, a focus group discussion with five academics and two industry professionals validated the thematic framework, ensuring its relevance to both academic research and real-world applications.

This structured approach ensured a rigorous and transparent synthesis of AI’s role in project management, showing critical enablers, barriers, and trends shaping its adoption. These themes guided the identification and categorization of solutions, detailed in Section 4.

# 3. Results

The results were organized into two sections. A descriptive analysis was conducted to illuminate the sources of publications and the annual trends of the reviewed papers. Additionally, a content analysis was performed on the 68 selected papers to provide deeper insights.

# 3.1. Descriptive Analysis

Table 2 highlights the distribution of papers by publication source, revealing that certain journals play a pivotal role in shaping the research landscape on AI in project management. The Journal of Construction Engineering and Management leads with four papers, followed by Automation in Construction with three. This concentration suggests an emphasis on applying AI within the construction and project management sectors. Simultaneously, the broader dispersion across engineering, technology, sustainability, and business analytics journals highlights the multidisciplinary nature of AI research in project management. This reflects the diverse applications of AI and the collaborative efforts across multiple fields.

Table 2. The total number of articles published in journals.   

<table><tr><td> Source Name</td><td>Publisher</td><td>No. of Articles</td><td>Location</td></tr><tr><td>Journal of Construction Engineering and Management</td><td>American Society of Civil Engineers (ASCE)</td><td>4</td><td>US</td></tr><tr><td>Automation in Construction</td><td>Elsevier</td><td>3</td><td>Netherlands</td></tr><tr><td>International Journal of Business Analytics and Security</td><td>Global Academic Forum on Technology, Innovation &amp; Management (GAF-TIM)</td><td>2</td><td>UAE</td></tr></table>

Table 2. Cont.   

<table><tr><td> Source Name</td><td>Publisher</td><td>No. of Articles</td><td>Location</td></tr><tr><td></td><td>BngesInte ilences</td><td>2</td><td>India</td></tr><tr><td>Applied Mechanics and Materials</td><td>Trans Tech Publications</td><td>2</td><td>Switzerland</td></tr><tr><td> Irnati ma Jogumalt</td><td>Elsevier</td><td>2</td><td>Netherlands</td></tr><tr><td>ETechneing Jounal</td><td>Fair East Publishers</td><td>2</td><td>Pakistan</td></tr><tr><td> Sustainability</td><td>Muldisiplinary Digsta</td><td>2</td><td>Switzerland</td></tr><tr><td>Applied Sciences</td><td>MrldisipinaryDigital</td><td>2</td><td>Switzerland</td></tr><tr><td>International Journal for Multidisciplinary Research</td><td>Futuristic Research Publications and Journals</td><td>2</td><td>India</td></tr><tr><td>Journal of Advanced Technology</td><td>Spuarakimgu</td><td>1</td><td>Malaysia</td></tr><tr><td>SRN (Socialtwienke</td><td>Elsevier (SSRN)</td><td>1</td><td>Amsterdam</td></tr><tr><td>International Journal of Scientific &amp; Technology Research</td><td> IJSTR Publications</td><td>1</td><td>India</td></tr><tr><td></td><td> Puishigipliarn ia</td><td>1</td><td>Iraq</td></tr><tr><td>ManEnginerioumal</td><td>Taylor &amp; Francis</td><td>1</td><td>UK</td></tr><tr><td> Nenural Computing</td><td> Springer</td><td>1</td><td>Germany</td></tr><tr><td>Life Science Journal</td><td>Marsland Press</td><td>1</td><td>US</td></tr><tr><td> Journal f Aca tumies</td><td>Faculty of Economics Scand Adm is7Airalk</td><td>1</td><td>Turkey</td></tr><tr><td>International Journal of Simulation and Process Modelling</td><td>University Inderscience Publishers</td><td>1</td><td>Switzerland</td></tr><tr><td> TEM Journal</td><td>UIKTEN (Union of Associations of Civil Engineering, Informatics, and Technical Education)</td><td>1</td><td>Serbia</td></tr></table>

Table 2. Cont.   

<table><tr><td> Source Name</td><td>Publisher</td><td>No. of Articles</td><td>Location</td></tr><tr><td>Complexity</td><td>Hindawi</td><td>1</td><td>US</td></tr><tr><td>Expert Systems with Applications</td><td>Elsevier</td><td>1</td><td>Netherlands</td></tr><tr><td></td><td>WestUniversty of</td><td>1</td><td>Romania</td></tr><tr><td>Buildings</td><td>Mubisininary igital</td><td>1</td><td>Switzerland</td></tr><tr><td>Adaned Engingering</td><td>Elsevier</td><td>1</td><td>Netherlands</td></tr><tr><td>Advances in Civil</td><td>Hindawi</td><td>1</td><td>US</td></tr><tr><td>Applied Sciences</td><td>Mubisininary tigital</td><td>1</td><td>Switzerland</td></tr><tr><td>Construction Management and Economics</td><td>Taylor &amp; Francis</td><td>1</td><td>UK</td></tr><tr><td>Safety Science</td><td>Elsevier IEDSR Association</td><td>1</td><td>Netherlands</td></tr><tr><td>Icontech International Journal</td><td>(International Economic Development and Social Research Association)</td><td>1</td><td>Turkey</td></tr><tr><td>Resources, Conservation and Recycling</td><td>Elsevier</td><td>1</td><td>Netherlands</td></tr><tr><td>Adganerin Civil</td><td>Hindawi</td><td>1</td><td>US</td></tr><tr><td>International Journal fivi Cond Structonal</td><td>Ctructurai nalCivitand</td><td>1</td><td>Russia</td></tr><tr><td>Engineering Soft Computing</td><td>Springer</td><td>1</td><td>Germany</td></tr><tr><td>Invenigriaen</td><td>UniversidNacional</td><td>1</td><td>Colombia</td></tr><tr><td>&amp; System Safety Reliability Engineering</td><td>Elsevier</td><td>1</td><td>Netherlands</td></tr><tr><td>International Journal of Machine Learning and Computing</td><td>EJournal Publishing</td><td>1</td><td> Singapore</td></tr><tr><td></td><td></td><td>1</td><td>Malaysia</td></tr></table>

Table 2. Cont.   

<table><tr><td> Source Name</td><td>Publisher</td><td>No. of Articles</td><td>Location</td></tr><tr><td></td><td></td><td>1</td><td>UAE</td></tr><tr><td>Information Fusion</td><td>Elsevier</td><td>1</td><td>Netherlands</td></tr><tr><td>SoftwareX</td><td>Elsevier</td><td>1</td><td>Netherlands</td></tr><tr><td>Project Leadeyhi ad</td><td>Elsevier</td><td>1</td><td>Netherlands</td></tr><tr><td>International Journal ofOnliniceal Engineering</td><td>Kassel University Press</td><td>1</td><td>Germany</td></tr><tr><td>Journal Civil Engineering</td><td>Arya Publishing Company (The Civil Engibering byrmnalis publisher).</td><td>1</td><td>India</td></tr><tr><td>International Journal of Advanced and Applied Sciences</td><td>Corporation Science Publishing</td><td>1</td><td>Pakistan</td></tr><tr><td>AI Indian Journal of</td><td>Meuisiplinary Digsta</td><td>1</td><td>Switzerland</td></tr><tr><td>Software Frojgcering Management (IJSEPM)</td><td></td><td>1</td><td>India</td></tr><tr><td></td><td>Ed Wtpub (Worshing)</td><td>1</td><td>UK</td></tr><tr><td>International Journal of Innovative Science and Research Technology</td><td>IJISRT Publication</td><td>1</td><td>India</td></tr><tr><td></td><td>T</td><td>1</td><td>US</td></tr><tr><td>European Journal of Theoretical and Applied Sciences</td><td>AMO Publisher</td><td>1</td><td>Ukraine</td></tr><tr><td>IEEE Transactions on Engineering Management</td><td>IEEE (Institute of Electrical and Electronics Engineers)</td><td>1</td><td>US</td></tr><tr><td> Symmetry</td><td>MPubishinary digita</td><td>1</td><td>Switzerland</td></tr></table>

Table 2. Cont.   

<table><tr><td> Source Name</td><td>Publisher</td><td>No. of Articles</td><td>Location</td></tr><tr><td>ICEB 2018 Proceedings</td><td>InternaetionalConfrece</td><td>1</td><td>China</td></tr><tr><td>10th International Structural Engineering and Construction Conference (ISEC-10)</td><td>ISEC Press</td><td>1</td><td>US</td></tr></table>

The papers included span the period from 2013 to 2024 and are all journal publications focusing on the application of AI in project management. This timeframe illustrates a growing interest in the intersection of these two fields, highlighting evolving trends and advancements in AI technologies and their integration into project management practices.

Figure 2 presents the annual distribution of papers published from 2013 to 2024, visually representing the research output trends. This figure facilitates an analysis of how interest in AI and project management has evolved over time.

![](images/8036a4575f5e804496e6184e61381bfb08da0071070c1c2b399a76f28338c9e5.jpg)  
Figure 2. Annual distribution of articles throughout the period 2013–2024.

The rising number of publications indicates growing interest and exploration within The rising number of publications indicates growing interest and exploration this field, driven by advancements in AI technologies and their capacity to address the is field, driven by advancements in AI technologies and their capacity to addrecomplexities of modern projects. Techniques such as support vector machines, neural mplexities of modern projects. Techniques such as support vector machines, neuranetworks, and GAs have shown significant potential in improving effort prediction, cost orks, and GAs have shown significant potential in improving effort prediction, coestimation, and project success factor identification. These advancements enable project mation, and project success factor identification. These advancements enable pmanagers to reduce uncertainties and enhance decision-making through logical reasoning and probabilistic calculations.

Moreover, AI’s ability to process and analyze large volumes of data across various project phases facilitates more accurate forecasting and resource optimization. This makes AI indispensable for managing the increasing complexity of global projects. The upward roject phases facilitates more accurate forecasting and resource optimization. This mtrend in publications reflects a broader shift towards leveraging AI to overcome the limitaI indispensable for managing the increasing complexity of global projects. The uptions of traditional project management methodologies, ultimately improving efficiency end in publications reflects a broader shift towards leveraging AI to overcome theand project outcomes. These developments emphasize the growing reliance on AI to tions of traditional project management methodologies, ultimately improving efficitransform project management practices in an increasingly dynamic environment [37].

To better understand how AI is being integrated into project management, a frequency analysis was conducted to identify which enablers and barriers appear most often across the reviewed studies (see Table 3). The results show that many researchers emphasize AI’s ability to improve efficiency, particularly through automation and predictive analysis. These technologies are often linked to better decision-making, streamlined workflows, and more accurate forecasting. However, achieving these benefits largely depends on having access to reliable, high-quality data, as well as fostering collaboration between AI systems and human teams. The studies also highlight the importance of skilled personnel and active knowledge sharing to ensure that AI tools are used effectively.

At the same time, several challenges repeatedly come up. Resistance to change is a common issue, especially in organizations used to traditional ways of managing projects. High costs related to implementing AI and training staff are another significant concern. Many articles also point to difficulties in integrating AI with older systems, along with problems related to poor data quality. Ethical considerations, such as data privacy, security14 of 29 risks, and potential biases in AI algorithms, are also frequently raised. In addition, a lack of awareness about AI and shortages of skilled professionals are seen as ongoing obstacles to broader adoption.

Overall, the literature suggests that while AI offers clear opportunities to enhance project management, addressing technical barriers, ethical concerns, and human factors is essential for its successful and sustainable use.

The reviewed papers come from a wide range of countries, showing that interest in when it comes to applying AI technologies in this field. using AI for project management is truly global. One clear observation is that China leadsllowing China, there is strong representation from the USA, India, Turkey, the contributions by a large margin. This reflects how active and focused research effortsre, and the UK. These countries show a steady and growing interest in exploring are in China when it comes to applying AI technologies in this field. can improve project management practices. Other countries

Following China, there is strong representation from the USA, India, Turkey, Singapore,rea, and Brazil also have multiple papers, which shows that this is not just a and the UK. These countries show a steady and growing interest in exploring how AI canf interest in one part of the world but something that is being studied in many improve project management practices. Other countries like Malaysia, Iraq, Canada, Korea, and Brazil also have multiple papers, which shows that this is not just a topic of interest in one part of the world but something that is being studied in many different contexts.

This mix of countries ranging from both highly developed economies to emerging nations, showing that integrating AI into project management is a universal concern, relevant to organizations and researchers everywhere. provide a clearer picture, Figure 3 presents a v

To provide a clearer picture, Figure 3 presents a visual summary of the country of papers using a bar chart. distribution of papers using a bar chart.

![](images/172ad39315357fc14cc7ed6286e7a79e5969bc6e9b01043e9e7bc33d4c995764.jpg)  
Figure 3. Country distribution of papers on AI in project management.

Table 3. Literature-based mapping of enablers and barriers influencing AI adoption in project management.   

<table><tr><td>No.</td><td>Enablers</td><td>The Knowledge Ecosthtemin Project Management: In Barrier</td><td>Enablers</td><td>TheIntesetde Barriers</td><td>Enablers</td><td>Integrating AIito Project ManagementandLandscaping Barriers</td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td>√</td><td></td><td></td><td></td></tr><tr><td>1 2</td><td>[2] √ [15] √</td><td>√</td><td></td><td>√ √</td><td>√ √ √ √</td><td>√</td></tr><tr><td>3</td><td>[28] √ √</td><td>√</td><td>√ √ √</td><td></td><td>√</td><td>√ √ √</td></tr><tr><td>4</td><td>[29]</td><td>√</td><td>√ √</td><td></td><td>√</td><td>√</td></tr><tr><td>5</td><td>[30] √</td><td></td><td>√ √</td><td></td><td>√</td><td></td></tr><tr><td>6 [38]</td><td>√ [39]</td><td>√</td><td>√</td><td>√</td><td>√</td><td>√</td></tr><tr><td>7</td><td>[40]</td><td>√ √</td><td>√</td><td>√ √ √</td><td>√</td><td>√ √ √ √ √</td></tr><tr><td>8</td><td>[41] √</td><td>√</td><td>√ √</td><td>√</td><td>√</td><td>√ √ √</td></tr><tr><td>9</td><td>[42] √</td><td>√</td><td>√</td><td></td><td>√</td><td>√</td></tr><tr><td>10</td><td>√</td><td>√</td><td>√ √</td><td>√</td><td>√</td><td></td></tr><tr><td>11 12</td><td>[43] [44]</td><td>√</td><td>√</td><td>√</td><td>√</td><td>√</td></tr></table>

Table 3. Cont.   

<table><tr><td rowspan="8">No.</td><td></td><td>The Knowledge Ecosyhtem i ProjectManagement: In</td><td></td><td></td><td> The Intrsectiodaityinrect</td><td></td><td>Integrating Al into Project Management and Landscaping</td></tr><tr><td>Enablers</td><td></td><td>Barrier</td><td>Enablers</td><td>Barriers</td><td>Enablers</td><td>Barriers</td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td>√</td><td>√</td><td></td><td></td><td></td></tr><tr><td>13 [45]</td><td>√ [46]</td><td>√</td><td>√ √ √</td><td>√ √</td><td></td><td>√ √ √</td></tr><tr><td>14 15</td><td>[47] √</td><td>√ √ √</td><td>√ √</td><td></td><td>√</td><td>√</td></tr><tr><td>16</td><td>[48] [49] √</td><td>√ √</td><td>√ √</td><td>√ √ √</td><td>√ √</td><td>√ √ √ √ √</td></tr><tr><td>17 18</td><td>[50]</td><td></td><td></td><td>√</td><td>√</td><td>√</td></tr><tr><td>19</td><td>[51] √</td><td>√</td><td></td><td>√</td><td></td><td>√</td></tr><tr><td>20</td><td>[52]</td><td>√</td><td></td><td>√ √</td><td>√</td><td>√ √ √</td></tr><tr><td>21</td><td>[53] √</td><td></td><td>√ √</td><td></td><td>√ √</td><td></td></tr><tr><td>22</td><td>[54] √ √</td><td>√ √</td><td></td><td></td><td>√</td><td>√</td></tr><tr><td>23</td><td>[55]</td><td>√ √</td><td>√</td><td></td><td></td><td>√</td></tr><tr><td>24</td><td>[56]</td><td></td><td></td><td>√</td><td>√ √</td><td>√</td></tr></table>

Table 3. Cont.   

<table><tr><td rowspan="8">No.</td><td></td><td>The Knowledge Ecostemin Prjec Management In</td><td></td><td></td><td>The Interetio f A iad umaity i rifect</td><td></td><td></td><td>Integrating AI into Project Management and Landscaping</td></tr><tr><td>Enablers</td><td></td><td>Barrier</td><td></td><td>Enablers</td><td>Barriers</td><td>Enablers</td><td>Barriers</td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td>√</td><td>√√</td><td></td><td></td><td></td><td></td></tr><tr><td>25 [57]</td><td>√</td><td></td><td>√ √ √</td><td></td><td>√ √</td><td>√ √</td><td></td></tr><tr><td>26 [58] 27 [59]</td><td>√ √ √ √</td><td></td><td>√ √</td><td></td><td></td><td>√ √</td><td></td></tr><tr><td>28 [60]</td><td>[61] √</td><td>√ √ √</td><td></td><td></td><td>√</td><td></td><td>√ √ √ √ √</td></tr><tr><td>29 30</td><td>[62] √</td><td></td><td>√</td><td>√</td><td></td><td>√</td><td>√</td></tr><tr><td>31</td><td>[63] [64]</td><td>√ √ √ √</td><td>√</td><td>√ √</td><td>√</td><td>√</td><td></td></tr><tr><td>32</td><td>[65]</td><td>√ √</td><td>√</td><td></td><td>√</td><td></td><td></td></tr><tr><td>33</td><td>[66] √</td><td>√ √</td><td>√</td><td></td><td>√</td><td>√</td><td>√</td></tr><tr><td>34</td><td>√</td><td>√</td><td></td><td></td><td></td><td>√</td><td></td></tr><tr><td>35 36</td><td>[67] [68] √</td><td>√</td><td>√ √</td><td></td><td>√</td><td>√</td><td></td></tr></table>

Table 3. Cont.   

<table><tr><td>No.</td><td>Enablers</td><td>The Knowledge Ecosysterin Projet Managemen: Iin</td><td>Barrier</td><td>The Intrsection f Al ad Humanity in Prhjct Enablers Barriers</td></tr><tr><td></td><td>√ √</td><td></td></tr><tr><td></td><td></td></tr><tr><td></td><td></td></tr><tr><td></td><td></td></tr><tr><td></td><td></td></tr><tr><td>37 [69]</td><td>√ √</td></tr><tr><td>38 [70]</td><td>√ √ √</td></tr><tr><td>39 [71]</td><td>√ √</td></tr><tr><td>40 [72]</td><td>√ √ √ √ √</td></tr><tr><td>41 [73]</td><td>√</td></tr><tr><td>42</td><td>√ √</td></tr><tr><td>[74]</td><td>√ √ √ √ √ √ √</td></tr><tr><td>43 [75] 44 [76]</td><td>√ √ √ √ √ √ √ √ √ √</td></tr></table>

Table 3. Cont.   

<table><tr><td rowspan="2">No.</td><td></td><td>Towdg</td><td></td><td>The Ieseity</td><td></td><td></td><td>Integrating Al into Project Management and Landscaping</td></tr><tr><td>Enablers</td><td></td><td>Barrier</td><td>Enablers</td><td>Barriers</td><td>Enablers</td><td>Barriers</td></tr><tr><td>49</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>50</td><td>[81] [82]</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td>√</td><td></td><td>√ √</td><td></td><td>√</td><td>√ √</td><td></td></tr><tr><td>51 [83]</td><td>√</td><td></td><td>√</td><td></td><td>√ √</td><td>√</td><td>√</td><td>√</td></tr><tr><td>52</td><td>[84] √</td><td></td><td>√</td><td>√ √</td><td></td><td></td><td></td><td>√</td></tr><tr><td>53</td><td>[85]</td><td>√ √</td><td>√</td><td></td><td>√</td><td></td><td></td><td></td></tr><tr><td>54</td><td>[86]</td><td></td><td></td><td></td><td>√</td><td>√</td><td>√ √</td><td>√ √ √ √</td></tr><tr><td>55</td><td>[87]</td><td>√ √</td><td>√</td><td></td><td></td><td></td><td>√ √</td><td></td></tr><tr><td>56</td><td>[88]</td><td>√ √</td><td>√ √ √</td><td></td><td>√ √ √</td><td></td><td>√ √</td><td>√ √</td></tr><tr><td>57</td><td>[89] √ √</td><td>√</td><td></td><td></td><td>√</td><td></td><td>√</td><td></td></tr><tr><td>58</td><td>[90] √</td><td></td><td></td><td></td><td></td><td>√</td><td>√</td><td></td></tr><tr><td>59</td><td>[91]</td><td>√</td><td>√</td><td></td><td></td><td>√</td><td>√</td><td>√</td></tr><tr><td>60</td><td>[92] √ √</td><td></td><td></td><td>√ √</td><td>√</td><td></td><td>√ √</td><td></td></tr></table>

Table 3. Cont.   

<table><tr><td>No.</td><td colspan="3">The Knowledge eoshstemin Project Magement in Enablers Barrier</td></tr><tr><td></td><td colspan="3"></td><td colspan="3"></td><td colspan="3"></td><td colspan="3"></td></tr><tr><td></td><td colspan="3"></td><td colspan="3"></td><td colspan="7"></td></tr><tr><td></td><td colspan="3"></td><td colspan="3"></td><td colspan="6"></td><td colspan="7"></td></tr><tr><td>61 [93]</td><td colspan="3">√ √</td><td colspan="3"></td><td colspan="7"></td><td colspan="7"></td></tr><tr><td>62 63 [95]</td><td colspan="10">[94] √ √ √ √</td><td colspan="7">√ √</td></tr><tr><td>64</td><td colspan="10">[96]</td><td colspan="7">√ √ √ √ √ √</td><td colspan="7"></td></tr><tr><td></td><td colspan="3"></td><td colspan="3"></td><td colspan="3">√</td><td colspan="3"></td><td colspan="7"></td><td colspan="7"></td></tr><tr><td>5 [97]</td><td colspan="10">√</td><td colspan="7">√</td><td colspan="7">√</td></tr><tr><td>66</td><td colspan="3">[98] √</td><td colspan="10"></td><td colspan="7"></td><td></td></tr><tr><td>67</td><td>[99]</td><td></td><td>√</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td>√</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr></table>

Additionally, to provide a deeper and more reliable understanding of the research landscape, the team carried out a keyword co-occurrence analysis. This method helped uncover dominant keywords and showed how frequently certain terms appear together, offering a clear picture of how different concepts are connected in the existing literature. The results, illustrated in Figure 4, highlight the major focus areas and relationships between them. Prominent keywords identified include Artificial Intelligence, project management, Artificial Neural Network, Machine Learning, risk management, construction management, Knowledge Management, natural language processing, big data, safety management, Technology Adoption, digital transformation, and cost estimation. Together, these terms reflect the strong emphasis on applying AI techniques to improve various aspects of projectW 15 of 29 management, particularly in areas like construction, safety, and organizational practices.

![](images/eb2791ad498cbc544d68abadccb641e4b69a4c4cc1bb7e4c647bc0a965c81a15.jpg)  
igure 4. Keyword co-occurrence analysis.Figure 4. Keyword co-occurrence analysis.

# 3.2. Content Analysis

The authors organized a focus group including five academics and two professionals to identify and extract the key themes that emerged from the discussion. The insights gathered during this session revealed several significant themes, which are outlined in the following sections:

The Knowledge Ecosystem in Project Management: In the Era of AI;   
The Intersection of AI and Humanity in Project Management;   
ntegrating AI into Project Management and Landscaping.Integrating AI into Project Management and Landscaping.

3.2.1. The Knowledge Ecosystem in Project Management: In the Era of AI

A deeper understanding of how AI affects specific knowledge areas’ technical and social dimensions is critical. Integrating project management expertise with insights from AI enables project managers to assess these impacts better, enhance decision-making, and AI enables project managers to assess these impacts better, enhance decision-making, and address both technical and human factors. Such an interdisciplinary approach encourages more effective strategies for AI implementation across diverse contexts [38].

effective strategies for AI implementation across diverse contexts [38].AI is pivotal in advancing project management practices by identifying future development trajectories and offering valuable insights for researchers and practitioners [39]. As AI technology evolves, project managers with relevant AI knowledge and skills should be As AI technology evolves, project managers with relevant AI knowledge and skills shouldbetter positioned to navigate the changing project management landscape [2]. Keeping pace with the rapid advancements in AI is critical [40], as these developments can render findings obsolete [41] and contribute to the swift progression of science and technology [42].

The selection of appropriate ML models is vital for ensuring effective project management [43]. AI’s flexibility is demonstrated by its applications across various fields, including project management [44], where it supports improved data handling by addressing data quality and security [30]. Additionally, AI enhances data analytics, improving reliability and ensuring access to accurate, real-time information [45].

The design, development, and implementation of AI systems require professionals with specialized training. Expertise in AI principles, programming, and integration techniques is crucial [46]. Collaborative knowledge-sharing is important for adopting and using AI effectively in project management. A solid understanding of AI facilitates improved decision-making and efficiency, allowing organizations to leverage interdisciplinary knowledge and maximize the benefits of AI for successful project outcomes [47].

AI applications in project management are diverse and impactful. In construction management, AI demonstrates significant potential to drive innovation [48]. ML addresses the limitations of traditional analytical methods and enhances overall analysis by simplifying the classification of project features and defects, enabling project managers to manage these elements more effectively [15]. AI algorithms handle the complexity and nonlinearity of construction projects, optimizing processes [49,50]. AI also improves safety performance on industrial construction projects by integrating data efficiently without requiring substantial changes to data warehouse structures [51]. In construction waste management, AI enhances the handling of missing data, leading to more accurate analyses and more effective waste management and recycling of materials [52].

Natural language processing (NLP) is another AI technique in construction management. It improves efficiency in project risk management by retrieving similar cases and enhancing risk case retrieval [53]. NLP also enhances document processing accuracy, reducing revision requirements [54]. It identifies critical risks throughout the lifecycle of construction megaprojects, thereby aiding project managers in risk mitigation [55]. Additionally, NLP eliminates the need for manual analysis of incident reports, enabling large databases to be used for safety improvements through proactive decision-making [56]. ML techniques automate project reporting by generating consolidated reports using text summarization [57].

AI’s ability to manage multiple variables simultaneously supports businesses in processing large volumes of input data [58,59]. ML models analyze vast datasets, leading to more precise predictions and improved project outcome forecasts [15,60]. These algorithms also assist with defect identification, ensuring more reliable project assessments. AI supports project risk management in healthcare by empowering patients with easier access to personal medical data, including electronic medical records [61]. AI’s capability to handle complex, topologically diverse datasets [50] and its use of techniques such as Latent Class Clustering Analysis (LCCA) and ANNs enhances accuracy in predicting incidents and identifying necessary preventative actions [62].

In road construction, AI learns inductively from examples, offering adaptability and flexibility that traditional methods lack [63]. It predicts project durations by analyzing historical or simulated data and responds to changes in activity duration distributions [64,65]. ANNs automatically adjust their weights and improve predictions over time as they process more data [66,67]. AI also personalizes learning paths to optimize knowledge acquisition and retention [68].

Combining BP neural networks with GAs improves prediction accuracy and evaluation efficiency in construction bidding. This AI-driven approach optimizes bid predictions and enhances decision-making and cost estimations [69]. ANN models assist during the project selection phase by predicting project performance and improving assessment accuracy [50,70]. AI also improves forecasting accuracy in Earned Value Management (EVM), particularly during early and mid-stage forecasting [64]. A framework based on Long Short-Term Memory (LSTM) networks enhances prediction reliability, especially for cost index predictions [71]. In tunnel projects, AI provides accurate predictions of cost performance indices [72]. AI can also predict construction dispute outcomes, helping parties resolve conflicts out of court and avoid litigation [73].

Despite these potential benefits, the empirical validation of AI methodologies remains a critical hurdle [64]. Challenges in AI integration include stakeholder resistance due to a preference for traditional methods, insufficient understanding of AI’s advantages, and inadequate strategic planning for its implementation [53,60,74,75]. Robust data management systems are essential as incomplete or poor-quality data can lead to biased outcomes and reduce model accuracy [28,39,40,76]. For example, instance-based learning algorithms and regression models used in software project scheduling require substantial data storage capacity [42].

In construction, AI adoption faces additional obstacles, such as mistrust in AI systems [53,77], non-standardized data formats [61], and the need for specialized skills to operate AI and ML tools effectively [78]. NLP faces challenges in extracting useful information from accident databases for risk management [53], while ANNs for road construction cost prediction must accommodate diverse datasets from various types of projects [63]. Domain expertise is crucial for interpreting AI model results due to the complexity of construction projects [44,55]. For instance, applying ANNs to forecast construction incidents requires extensive datasets and significant training, which increases costs [77]. A study of Brazilian educational public buildings highlighted that improving ANN performance requires substantial training data and predefined rules for network topology selection [70].

AI has also proven beneficial in construction safety management by accurately predicting risk factors, reducing casualties, and enhancing safety [79]. In Agile project management, AI enhances collaboration and decision-making, enabling organizations to integrate AI into their Agile practices and advance project management methodologies [40].

Addressing these challenges through interdisciplinary collaboration, skill development, and strategic planning allows project managers to harness AI’s potential, optimize processes, improve safety, and achieve superior outcomes across various domains.

# 3.2.2. The Intersection of AI and Humanity in Project Management

AI, often called machine intelligence, was introduced to create “thinking machines” capable of replicating human cognitive abilities and intellectual behaviors. Its potential to augment or even replace human intelligence in specific tasks has driven its integration into project management, blending advanced algorithms with human expertise to optimize processes, improve decision-making, and enhance collaboration across diverse teams [80].

A significant impact of AI lies in augmenting human capabilities rather than replacing them. Barcaui and Monat compared GPT-4 with human project managers in project plan development, highlighting AI’s role in generating ideas and optimizing workflows. In contrast, human expertise remains essential for providing context, judgment, and validation [41]. This evolving collaboration reshapes team dynamics as AI adoption alters roles and power structures. Encouraging trust, transparency, and cooperation is critical to maximizing this synergy. Tools such as chatbots illustrate AI’s integration into project management by offering real-time updates, enhancing communication, and increasing stakeholder engagement [58,76]. BIM further demonstrates how AI strengthens stakeholder collaboration, improving efficiency and alignment in project planning [29,81,82].

AI’s influence extends beyond collaboration to leadership and decision-making. By automating routine tasks, AI allows leaders to focus on strategic and complex responsibilities. Tominc et al. demonstrated how AI-driven insights enable more informed, data-based decisions [74]. This is further exemplified by Balcioglu et al.’s application of ANNs in personnel selection, allowing HR managers to identify better-suited candidates more efficiently. Such advancements streamline recruitment processes, particularly in high-stakes banking industries, where project outcomes significantly affect financial results [83].

AI has also transformed communication, a cornerstone of project success. Taboada Puente et al. showed that AI enhances team collaboration by improving communication and engendering stronger teamwork, ultimately leading to better project outcomes [2]. Tubman’s research indicated that automating repetitive tasks with AI frees human resources for activities requiring empathy, creativity, and complex judgment [59]. Similarly, Chen et al. demonstrated using NLP to create an interactive platform for engineering project contract stakeholders, resolving interaction challenges, reducing manual errors, and improving communication efficiency [54].

AI innovations have significantly enhanced cost estimation accuracy in the construction sector. Wang et al. developed an AI-driven estimation model based on a gray BP neural network, integrating MATLAB to optimize data processing. This approach improves prediction precision, reduces errors, and enhances budget reliability [84].

Fang et al. leveraged convolutional neural networks (CNNs) to enhance real-time object detection on construction sites, identifying workers and equipment with improved speed and accuracy. Their work has greatly enhanced safety performance and operational efficiency, minimizing the need for manual monitoring [49].

AI’s utility also extends to dispute resolution and reporting. Chaphalkar, Iyer, and Patil applied a multilayer perceptron neural network to predict construction dispute outcomes, focusing on variation claims. Their research highlighted how AI tools expedite dispute resolution and prevent conflicts by identifying key risk factors [73]. In project reporting, Tan, Chen, and Yeo developed the Project Reporting Management System (PRMS), which automates reporting tasks while maintaining high-quality executive summaries. This reduces manual effort and errors, demonstrating AI’s potential to improve productivity [57].

However, the integration of AI into project management is not without challenges. Ethical concerns, particularly biases in AI decisions arising from flawed or incomplete data, present significant risks [58,85]. While AI can support decision-making, human judgment remains indispensable to ensure sound outcomes. Project managers must critically assess AI-driven recommendations to avoid over-reliance on automation [58,86]. Additionally, the extensive data requirements of AI systems create privacy and security risks. Strong measures, such as encryption and access controls, are essential to safeguard sensitive information [40,61,82,85]. Another pressing challenge involves the potential for job displacement due to AI-driven automation, which carries significant social and economic implications [58].

Despite these concerns, the potential of AI to drive innovation and efficiency is undeniable. AI-powered systems are increasingly used in smart distribution management, predicting and responding to real-time disruptions to optimize efficiency and reduce downtime. Similarly, AI is gaining traction in modular construction, driving innovation and operational improvements [30].

By addressing ethical considerations and balancing automation with human oversight, organizations can harness the transformative power of AI to deliver superior project outcomes. Integrating AI into project management—when guided by trust, transparency, and robust data protection measures—promises to redefine the field, making it more efficient, collaborative, and forward-thinking.

# 3.2.3. Integrating AI into Project Management and Landscaping

Implementing AI in project management requires a high level of maturity and a substantial dataset to ensure efficient adoption [28]. AI enhances performance by automating tasks such as planning, scheduling, and resource allocation, which increases efficiency while reducing the overall workload. Through real-time data insights, AI facilitates more informed decision-making, strengthens team collaboration, and gives organizations a competitive advantage, enabling faster, more cost-effective project delivery with improved quality [2,45,59].

The dynamic and complex nature of the modern business environment requires managers to make swift, well-informed decisions while optimizing the allocation of limited resources. These challenges are magnified in organizations managing multiple projects simultaneously, necessitating a clear strategic focus and advanced problemsolving skills to navigate competing priorities effectively.Numerous studies highlight the role of AI and ML in improving decision-making processes and enhancing project outcomes [29,43,48,52,55,58,71,74–76,85,87–90].

In energy project management, AI provides advanced decision support by improving data analysis and simulation capabilities. ML and optimization algorithms allow managers to compare and evaluate multiple decision scenarios, facilitating more informed and datadriven strategies [39]. Similarly, Bahi et al. [86] integrated generative AI into Agile 2.0 software development to improve decision-making and address uncertainties in Agile project management, thereby enhancing practices and outcomes [86].

ANNs have been applied to predict road construction costs, offering accurate investment estimates that support informed funding decisions [63]. Costantino, Di Gravio, and Nonino developed a decision support system using an ANN model based on critical success factors (CSFs), helping managers assess projects during selection and incorporating filters to address inconsistencies with expert judgments, improving prediction accuracy and reliability [50].

In construction safety management, Tixier et al. introduced a fully-automated NLP system to replace manual analysis of incident reports. This system uses large databases to improve safety performance proactively, enhancing decision-making on construction sites [56]. Amusan and Mosaku used ANNs and regression heuristics to improve decision-making in building projects, addressing challenges related to limited knowledge of investment returns and staff ICT skills [77].

In tunnel construction, Zhang et al. developed a decision support approach for safety risk analysis using Fuzzy Bayesian Networks (FBNs), which identified causal relationships between tunnel damage and influencing factors. This approach combines reasoning and sensitivity analysis to improve safety protocols and management strategies [91]. Wu et al. proposed a construction project bidding method using a back-propagation (BP) neural network enhanced by a GA, creating a more reliable evaluation system for bidding management [69].

AI has also contributed to software project schedule management, with instancebased learning algorithms and regression models simplifying decision-making for managers and improving project execution efficiency [42]. Evaluating project management competence in AI-driven environments is critical as these skills are important for achieving successful outcomes in a rapidly evolving technological landscape. Organizations that leverage AI effectively gain a competitive edge by adapting quickly to market changes [40,86]. AI significantly improves schedule accuracy, offers insights into potential risks, and optimizes resource allocation, enhancing project management efficiency [39,41,43,55,66,70,76,87,92–94,101].

In risk management, Shen and Gao designed a prediction model based on an optimized BP neural network algorithm to enhance construction site safety. This model shifts safety management from reactive to proactive, providing real-time early warnings and targeted preventive measures for each project phase [79]. Zou and Jones applied NLP techniques to retrieve similar cases in construction project risk management, helping to anticipate and mitigate financial risks [53]. Additionally, Zhang developed an AI-driven safety management system in civil engineering that achieved a $9 7 . 4 \%$ effectiveness rate by automating monitoring tasks, reducing manual observation, and improving real-time hazard detection [95].

In project cost estimation, ANNs have proven reliable in preparing accurate road construction cost estimates [61]. These networks also rank ongoing projects based on reported safe work behaviors, identifying projects that require further attention to maintain safety standards [63,96]. Hanafi et al. investigated the use of AI in selecting project managers based on emotional intelligence (EI), demonstrating that AI effectively identifies managers with strong EI traits, thereby improving project outcomes [97].

The challenges associated with AI implementation include integrating AI tools with existing systems, high financial investment requirements, and increased vulnerability to cyber threats. Ensuring stakeholder support and scalability while balancing costs with expected returns is valuable for successful adoption. Legal and ethical considerations also need to be addressed. Despite these challenges, robust infrastructure and strategic planning enable long-term AI investments to yield substantial benefits, enhancing industry project management outcomes [75,88].

# 4. Discussion

This study synthesizes the findings from the SLR to present a comprehensive analysis of how AI and ML can improve project management processes. It highlights the potential of AI/ML analytics in optimizing workflows, identifies key enablers that facilitate successful implementation, and examines the barriers that hinder widespread adoption. A contribution of this SLR is the identification of critical advancements that reveal the transformative potential of AI/ML technologies in project management practices. This section focuses on the most significant findings, linking them to the broader research questions and offering insights that address current challenges while providing a foundation for future research and practical innovations in AI/ML-driven project management.

# 4.1. Implementing AI and ML in Project Management

Implementing AI and ML in project management depends on technological advancements, robust frameworks, data availability, and effective stakeholder collaboration. These elements collectively enhance the adoption and effectiveness of AI/ML, significantly improving project outcomes.

Technological advancements form the basis of AI/ML applications in project management. Recurrent neural networks (RNNs) have been shown to optimize project scheduling by modeling sequential data and addressing complexities in portfolio scheduling [98]. Neural networks integrated with GAs have enhanced cost and effort predictions across diverse domains, such as construction and software development [63,69,89]. When optimized with GAs, BP neural networks demonstrate higher cost estimation accuracy [69]. Deep learning models, such as CNNs, support safety monitoring by identifying hazards, including unsafe worker behaviors and equipment misuse [44,95]. Additionally, Faster R-CNN models facilitate real-time safety management through advanced object detection capabilities [49].

Frameworks and architectures are pivotal in ensuring the practical application of AI/ML tools in project management. The Electron framework supports collaborative project management with cross-platform compatibility, multi-view functionalities such as Kanban boards and knowledge graphs, and distributed deployment features [68]. Advanced AI models such as GPT-4 enhance project planning by complementing human expertise in project management processes [41]. Interactive platforms powered by NLP address communication challenges, reduce errors, and improve document management, particularly in construction projects [54]. NLP-driven rule-based systems also analyze unstructured safety data to extract actionable insights, improving risk identification [56]. In parallel, multilayer perceptron neural networks provide accurate predictions for construction dispute outcomes, promoting proactive conflict resolution [73]. ANNs have also been applied alongside occupational safety and health management systems to predict and evaluate safety behaviors and performance [96,99]. The scalability and adaptability of ANN models further enable their customization for various critical success factors, providing solutions that can be applied across industries [50].

Data availability and effective utilization significantly enhance the performance of AI/ML tools. Large datasets from government systems for cost forecasting in public construction projects and daily work reports for progress monitoring offer a solid foundation for predictive analytics [70,91,93]. ANNs process both structured and unstructured data, delivering accurate predictions for cost indices, safety performance, and earned value metrics [67,72,96]. Sensor data from accelerometers and gyroscopes further improve activity tracking and prediction in construction projects [100]. Scalability is demonstrated by models such as LSTM networks, which enhance prediction accuracy for cost indices [71].

Integrating AI/ML tools with established project management methodologies ensures their practical relevance. For example, the Agile methodology supports the automation of progress reporting and decision-making [57]. AI/ML applications incorporating critical success factors (CSFs) prevent project failures by aligning tools with strategic objectives and leveraging expert input in risk assessment and project selection [50].

Stakeholder collaboration is important for overcoming barriers such as resistance to change and the digital divide. Public–private partnerships and capacity-building initiatives, including workshops, have proven effective in encouraging AI/ML adoption [28,62]. Expert involvement is crucial in assessing critical risk factors and designing ANN models for project evaluation [50,83]. Such collaborative efforts ensure that AI/ML tools are aligned with project goals and effectively implemented.

The adaptability of AI/ML solutions is further demonstrated by their scalability across industries. Frameworks designed for material-based progress monitoring and labor productivity mapping have broad applicability [66,93]. Integration with BIM enhances visualization and simulation capabilities, enabling real-time communication and precise scheduling [81]. Advancements in cloud computing, blockchain, and technological infrastructure further facilitate secure and distributed collaboration, ensuring seamless AI/ML deployment [68].

Ultimately, the collective impact of technological advancements, frameworks, data utilization, and stakeholder collaboration establishes AI/ML as transformative tools in project management. These technologies can significantly improve project outcomes across industries by addressing core challenges such as scheduling, cost estimation, safety, and communication.

# 4.2. Barriers to and Drivers of AI and ML in Project Management

The findings of this SLR emphasize the transformative potential of AI in project management across multiple sectors, particularly in construction and healthcare. The results illustrate how AI—especially ML and NLP—significantly improves data handling, decision-making, risk management, and safety, contributing to more efficient and accurate project outcomes. This review contributes to the expanding body of knowledge on AI integration in project management, offering valuable insights for both academic research and practical implementation.

AI’s application in project management is multifaceted, with advancements across sectors. It enhances accuracy in risk assessments, optimizes project planning, and improves resource allocation. By enabling the management of complex datasets and delivering real-time insights, AI equips project managers to make informed, data-driven decisions that reduce costs, enhance safety, and improve overall project efficiency. Its predictive capabilities are applied in estimating project durations, forecasting costs, and refining bidding processes. Moreover, AI’s ability to facilitate communication through NLP tools and automated reporting systems significantly improves stakeholder collaboration, streamlining workflows and minimizing manual errors [61,64,69,78,79]. AI-driven advancements, such as neural networks integrated with GAs, enhance bidding accuracy and efficiency [69], while safety management transitions from reactive approaches to proactive, real-time hazard prevention [79].

The integration of AI into project management has important theoretical and practical implications. AI supports and, in certain instances, replaces traditional decisionmaking methods by offering superior predictive accuracy and more effective risk management [43,52,64]. From a practical perspective, AI enables project managers to optimize scheduling, implement enhanced safety protocols, and predict costs with greater precision— ultimately improving project delivery speed, efficiency, and quality [44,57,64,79].

This review is methodologically robust, presenting a comprehensive synthesis of diverse industry studies. Despite certain limitations, the findings consistently highlight the growing body of evidence supporting AI’s pivotal role in project management. Future research would benefit from longitudinal studies assessing the long-term impact of AI tools on project outcomes and experimental designs that test the effectiveness of AI interventions in real-world scenarios [28,88].

Despite the transformative potential of AI, its adoption is not without challenges. Integrating AI tools with legacy systems is one of the most significant barriers. Many organizations experience difficulties incorporating AI technologies into existing infrastructures, which delays or hinders successful implementation [75,88]. This challenge is particularly evident in industries with complex, entrenched systems, such as construction, where data may often be siloed or stored in non-digital formats, complicating AI integration [75].

Financial investment poses another considerable challenge. AI solutions typically require high initial costs for training personnel, procuring hardware and software, and upgrading infrastructure to support AI systems. These costs can be prohibitive for smaller organizations or those with limited resources [75]. Furthermore, several studies highlight concerns over job displacement, as automation could replace specific manual tasks, raising social and ethical issues related to workforce changes and the need for retraining [58,85].

Data-related challenges are equally critical. AI systems depend on large, high-quality datasets often unavailable to many organizations. Data quality and completeness significantly influence AI models’ accuracy and effectiveness. Inadequate or biased data can result in flawed predictions and decision-making [85]. Additionally, privacy and security remain pressing concerns, particularly given the sensitive nature of project-related data in industries such as healthcare [61]. Robust data protection mechanisms are important to mitigate risks of unauthorized access or breaches [85].

Stakeholders’ resistance to change impedes AI adoption. Skepticism toward AI, particularly in industries traditionally prioritizing human expertise and intuition, can slow its adoption and reduce its effectiveness [40,58]. Addressing this reluctance requires encouraging a culture that supports collaboration between humans and AI tools.

From a policy perspective, ethical considerations are paramount in AI adoption. Issues related to data privacy, security, and job displacement need to be carefully managed. Policymakers need to develop regulations that promote responsible AI use, ensuring data integrity, reducing bias, and maintaining human oversight in decision-making. Building a culture of trust between human professionals and AI systems is crucial for maximizing the potential benefits of AI technology [40,58,85].

# 5. Conclusions

This SLR provides valuable insights into integrating AI and ML into project management, offering three key contributions. First, it highlights how AI enhances project outcomes by improving forecasting accuracy, encouraging stakeholder collaboration, and mitigating risks. Of the identified enablers and barriers, data quality emerges as a critical factor for successful AI adoption. High-quality, well-structured data ensure accurate predictions and reliable decision-making, establishing a cornerstone for effective AI implementation. Second, the review identifies significant barriers, such as challenges in legacy system integration, high financial costs, and resistance to change. Overcoming these challenges requires a strategic approach, including piloting AI on smaller projects to build trust, refine workflows, and demonstrate its value before broader scaling. Third, actionable recommendations are presented, emphasizing the importance of workforce training to address skill gaps, encouraging interdisciplinary collaboration to maximize AI’s potential, and implementing ethical AI frameworks to ensure responsible and transparent use.

For practitioners, starting with small-scale pilots helps build confidence in AI tools while refining processes and minimizing risks. Ensuring data quality and aligning AI initiatives with organizational objectives is vital for maximizing the benefits. Ethical considerations, including addressing biases and promoting transparency, are equally important for engendering stakeholder trust. This review also demonstrates how AI is reshaping key aspects of project management by enhancing forecasting, automating repetitive tasks, optimizing resource allocation, and improving decision-making processes. By addressing barriers to adoption, organizations can unlock AI’s transformative potential. For instance, piloting AI-driven solutions on smaller projects allows workflows to be refined and scaled gradually, easing resistance to change while delivering measurable benefits.

AI is poised to redefine project management by aligning with broader trends such as digital transformation and sustainability. Beyond improving efficiency, it empowers organizations to innovate, adapt, and remain competitive in dynamic environments. However, stakeholder collaboration is critical for successful implementation. Addressing ethical concerns, ensuring transparency, and developing scalable frameworks for responsible AI use is critical for engendering trust and achieving long-term success

In conclusion, AI and ML represent more than tools: they serve as catalysts for a paradigm shift in project management. Organizations can establish smarter, more sustainable, and future-ready practices by leveraging enablers and addressing barriers. As data quality is the foundation for successful AI adoption, organizations embracing these technologies will transform project management into a more collaborative and forwardlooking discipline. These advancements set new standards for project delivery and align with broader societal and technological trends, offering significant opportunities for industries worldwide.

# 6. Future Research Directions

Based on the findings of this review, future research should focus on addressing existing challenges and identifying effective methods for integrating AI and ML into project management. A key priority is improving data quality, as this is fundamental to ensuring the reliability of AI applications. Research efforts should develop frameworks that enhance data accuracy, consistency, and completeness, while addressing bias reduction and improving data interoperability from diverse systems. Another critical area for investigation is integrating AI tools into existing project management systems. This process requires practical, incremental approaches, such as hybrid AI models, which facilitate smoother transitions and scalable solutions that minimize workflow disruptions. Longterm studies are also needed to understand the sustained impact of AI, while real-world case studies can provide valuable insights into successful applications and solutions to specific challenges. Moreover, future research should focus on conducting empirical studies and real-world pilot projects to validate AI-driven project management tools’ effectiveness and scalability. Longitudinal studies capturing AI’s impact over time, especially in complex project environments like healthcare, infrastructure, and public sector projects, would offer critical insights. Comparative case studies across various industries can help develop sector-specific frameworks, showing best practices and contextual challenges for AI adoption.

Ethical concerns could also be addressed, including algorithmic bias, privacy risks, and workforce displacement. Developing clear guidelines for responsible and transparent AI usage is necessary to ensure fairness and regulatory compliance. In addition, researchers should explore the integration of emerging AI technologies such as reinforcement learning, explainable AI, and digital twins within project management frameworks. These technologies offer potential in enhancing adaptability, transparency, and real-time decision-making. Ethical AI remains a vital area for further investigation, with emphasis on developing governance models to address biases, data privacy, and regulatory compliance across industries.

Furthermore, researchers should explore strategies to encourage effective collaboration between AI systems and human teams, identifying best practices for combining human expertise with AI-driven insights. Given the distinct challenges Small and Medium Enterprises (SMEs) face, such as limited budgets and resources, research should prioritize affordable and scalable AI solutions tailored to their needs. Shared resources and open-source tools could have significant benefits for these organizations. Advanced AI techniques, such as reinforcement learning and generative models, show potential for improving predictive analytics, risk management, and decision-making. However, further evaluation is needed to assess their effectiveness in real-world project scenarios. Addressing resistance to AI adoption, particularly in traditional project management settings, is also important. Research should focus on strategies for engaging stakeholders, building trust, and demonstrating AI’s value. Lastly, AI’s potential to contribute to sustainable project management practices should not be overlooked. Future research should explore how AI can optimize resource use, reduce waste, and promote environmentally-friendly practices.

# 7. Recommendations for Practice

Organizations need to identify specific, high-impact areas within project management where AI can yield quick, measurable benefits. Enhancing forecasting accuracy, automating routine tasks, or improving resource allocation represent practical starting points. These targeted implementations allow teams to evaluate AI’s value and refine workflows without committing excessive resources. AI tool integration could follow a gradual, step-by-step approach to minimize disruptions to existing processes. Rather than overhauling entire systems, organizations can adopt AI tools that complement current workflows, delivering immediate benefits while establishing a foundation for broader integration. This method ensures that teams remain productive while adapting to AI.

Upskilling project management teams is important for bridging the knowledge gap between traditional methods and AI-driven practices. Organizations need to prioritize training programs that not only teach the use of AI tools but also focus on interpreting AI outputs and integrating them into decision-making processes. Enhancing AI literacy within teams promotes trust and encourages effective collaboration. Ethical practices need to be incorporated into AI implementation from the outset. Organizations could establish clear policies regarding data privacy, accountability, and algorithmic fairness. Conducting regular audits and maintaining transparent communication over AI’s role in decision-making can alleviate stakeholder concerns and ensure responsible use.

SMEs could leverage affordable, scalable AI solutions tailored to their specific needs. Cloud-based AI tools or subscription models can reduce upfront costs, making these technologies more accessible. Collaborating with other SMEs or industry groups can facilitate resource sharing and cost reduction, promoting a more inclusive approach to AI adoption. Organizations could engage stakeholders early in the process to address resistance to AI adoption. This includes providing clear communication of the benefits of AI, addressing concerns directly, and involving key stakeholders in implementation decisions. Demonstrating early successes through pilot projects can help build trust and generate momentum for broader adoption.

AI tools need to be integrated into existing risk management frameworks to offer proactive solutions. By analyzing historical data, identifying patterns, and predicting potential project risks, AI can enhance decision-making and enable teams to mitigate risks before they escalate. Sustainability could serve as a guiding principle in AI adoption. Organizations can use AI to monitor and optimize resource consumption, reduce inefficiencies, and design projects with a smaller environmental footprint. Aligning AI strategies with sustainability goals drives operational efficiency and reinforces an organization’s commitment to social responsibility.

In conclusion, practitioners need to establish feedback loops to evaluate AI tool performance and refine implementation strategies continuously. Regular reviews ensure that AI remains aligned with project objectives and adapts to evolving needs, maintaining its relevance and effectiveness over time.

Author Contributions: Conceptualization, S.S., A.N.G., M.S. and A.K.R.; methodology, S.S. and A.N.G.; writing—original draft preparation, S.S.; writing—review and editing, S.S., A.N.G., R.J.T., A.K.R., S.R. and M.S.; visualization, R.J.T.; supervision, A.N.G., R.J.T. and M.S.; investigation, M.G. and S.R.; resources, S.R.; data curation, A.K.R. All authors have read and agreed to the published version of the manuscript.

Funding: This research received no external funding.

Conflicts of Interest: The authors declare no conflict of interest.

# References

1. Zhou, W.; Yan, Z.; Zhang, L. A comparative study of 11 non-linear regression models highlighting autoencoder, DBN, and SVR, enhanced by SHAP importance analysis in soybean branching prediction. Sci. Rep. 2024, 14, 5905. [CrossRef] [PubMed]   
2. Taboada, I.; Daneshpajouh, A.; Toledo, N.; de Vass, T. Artificial Intelligence Enabled Project Management: A Systematic Literature Review. Appl. Sci. 2023, 13, 5014. [CrossRef]   
3. Prifti, V. Optimizing project management using artificial intelligence. Eur. J. Form. Sci. Eng. 2022, 5, 30–38.   
4. Auth, G.; Jöhnk, J.; Wiecha, D.A. A Conceptual Framework for Applying Artificial Intelligence in Project Management. In Proceedings of the 2021 IEEE 23rd Conference on Business Informatics (CBI), Bolzano, Italy, 1–3 September 2021; Volume 1, pp. 161–170.   
5. Polonevych, O.V.; Sribna, I.M.; Mykolaychuk, V.R.; Tkalenko, O.M.; Shkapa, V.V. Artificial Intelligence Applications for Project Management. Connectivity 2020, 146, 054855.   
6. Gil, J.; Torres, J.M.; González-Crespo, R. The application of artificial intelligence in project management research: A review. Int. J. Interact. Multimed. Artif. Intell. 2021, 6, 54–66.   
7. Jiang, F.; Jiang, Y.; Zhi, H.; Dong, Y.; Li, H.; Ma, S.; Wang, Y.; Dong, Q.; Shen, H.; Wang, Y. Artificial intelligence in healthcare: Past, present and future. Stroke Vasc. Neurol. 2017, 2. [CrossRef]   
8. Ribeiro, J.; Lima, R.; Eckhardt, T.; Paiva, S. Robotic process automation and artificial intelligence in industry 4.0—a literature review. Procedia Comput. Sci. 2021, 181, 51–58.   
9. Ghazal, T.M.; Taleb, N. Feature optimization and identification of ovarian cancer using internet of medical things. Expert Syst. 2022, 39, e12987.   
10. Som, A.; Al-Kassem, A.H. Domestic tourism development in Asir region, Saudi Arabia. J. Tour. Hosp. 2013, 2, S5-001.   
11. Nuseir, M.T.; Aljumah, A. The role of digital marketing in business performance with the moderating effect of environment factors among SMEs of UAE. Int. J. Innov. Creat. Change 2020, 11, 310–324.   
12. El Khatib, M.; Al Mulla, A.; Al Ketbi, W. The role of blockchain in E-governance and decision-making in project and program management. Adv. Internet Things 2022, 12, 88–109.   
13. Al-Maroof, R.; Akour, I.; Aljanada, R.; Alfaisal, A.; Alfaisal, R.; Aburayya, A.; Salloum, S. Acceptance determinants of 5G services. Int. J. Data Netw. Sci. 2021, 5, 613–628.   
14. Koroteev, D.; Tekic, Z. Artificial intelligence in oil and gas upstream: Trends, challenges, and scenarios for the future. Energy AI 2021, 3, 100041.   
15. Fan, C. Evaluation of classification for project features with machine learning algorithms. Symmetry 2022, 14, 372. [CrossRef]   
16. Uddin, S.; Ong, S.; Lu, H. Machine Learning in Project Analytics: A Data-Driven Framework and Case Study. Sci. Rep. 2022, 12, 15252. [CrossRef] [PubMed]   
17. El Naqa, I.; Murphy, M.J. What is Machine Learning? Springer: Cham, Switzerland, 2015.   
18. Forcada, N.; Macarulla, M.; Love, P.E. Assessment of residential defects at post-handover. J. Constr. Eng. Manag. 2013, 139, 372–378. [CrossRef]   
19. Cheng, Y.; Leu, S. Integrating data mining with KJ method to classify bridge construction defects. Expert Syst. Appl. 2011, 38, 7143–7150. [CrossRef]   
20. Das, S.; Chew, M.Y. Generic method of grading building defects using FMECA to improve maintainability decisions. J. Perform. Constr. Facil. 2011, 25, 522–533.   
21. Elmasry, M.; Hawari, A.; Zayed, T. Defect based deterioration model for sewer pipelines using Bayesian belief networks. Can. J. Civ. Eng. 2017, 44, 675–690.   
22. Haase, J.; Hanel, P.H. Artificial muses: Generative artificial intelligence chatbots have risen to human-level creativity. J. Creat. 2023, 33, 100066. [CrossRef]   
23. Korzynski, P.; Mazurek, G.; Altmann, A.; Ejdys, J.; Kazlauskaite, R.; Paliszkiewicz, J.; Wach, K.; Ziemba, E. Generative artificial intelligence as a new context for management theories: Analysis of ChatGPT. Cent. Eur. Manag. J. 2023, 31, 3–13. [CrossRef]   
24. Gama, F.; Tyskbo, D.; Nygren, J.; Barlow, J.; Reed, J.; Svedberg, P. Implementation frameworks for artificial intelligence translation into health care practice: Scoping review. J. Med. Internet Res. 2022, 24, e32215. [PubMed]   
25. Ghanbaripour, A.N.; Tumpa, R.J.; Sunindijo, R.Y.; Zhang, W.; Yousefian, P.; Camozzi, R.N.; Hon, C.; Talebian, N.; Liu, T.; Hemmati, M. Retention over Attraction: A Review of Women’s Experiences in the Australian Construction Industry; Challenges and Solutions. Buildings 2023, 13, 490. [CrossRef]   
26. Thomas, J.; Harden, A. Methods for the thematic synthesis of qualitative research in systematic reviews. BMC Med. Res. Methodol. 2008, 8, 45.   
27. Schön, E.; Thomaschewski, J.; Escalona, M.J. Agile Requirements Engineering: A systematic literature review. Comput. Stand. Interfaces 2017, 49, 79–91.   
28. Mahmood, A.; Al Marzooqi, A.; El Khatib, M.; AlAmeemi, H. How Artificial Intelligence can leverage Project Management Information system (PMIS) and data driven decision making in project management. Int. J. Bus. Anal. Secur. IJBAS 2023, 3, 184–195.   
29. Velezmoro-Abanto, L.; Cuba-Lagos, R.; Taico-Valverde, B.; Iparraguirre-Villanueva, O.; Cabanillas-Carbonell, M. Lean Construction Strategies Supported by Artificial Intelligence Techniques for Construction Project Management—A Review. Int. J. Online Biomed. Eng. 2024, 20, 99–114. [CrossRef]   
30. Liu, Q.; Ma, Y.; Chen, L.; Pedrycz, W.; Skibniewski, M.J.; Chen, Z. Artificial intelligence for production, operations and logistics management in modular construction industry: A systematic literature review. Inf. Fusion 2024, 109, 102423.   
31. Kitchenham, B.; Pretorius, R.; Budgen, D.; Brereton, O.P.; Turner, M.; Niazi, M.; Linkman, S. Systematic literature reviews in software engineering—a tertiary study. Inf. Softw. Technol. 2010, 52, 792–805.   
32. Mohamad, U.H.; Ahmad, M.N.; Zakaria, A.M.U. Ontologies application in the sharing economy domain: A systematic review. Online Inf. Rev. 2021, 46, 807–825.   
33. Wang, C.C.; Mussi, E.; Sunindijo, R.Y. Analysing gender issues in the Australian construction industry through the lens of empowerment. Buildings 2021, 11, 553. [CrossRef]   
34. Saldana, J. Fundamentals of Qualitative Research; Oxford University Press: Oxford, UK, 2011.   
35. Wijewickrama, M.; Rameezdeen, R.; Chileshe, N. Information brokerage for circular economy in the construction industry: A systematic literature review. J. Clean. Prod. 2021, 313, 127938. [CrossRef]   
36. Susanto, P.C.; Yuntina, L.; Saribanon, E.; Soehaditama, J.P.; Liana, E. Qualitative method concepts: Literature review, focus group discussion, ethnography and grounded theory. Siber J. Adv. Multidiscip. 2024, 2, 262–275. [CrossRef]   
37. Davahli, M.R. The last state of artificial intelligence in project management. arXiv 2012, arXiv:2012.12262.   
38. Fridgeirsson, T.V.; Ingason, H.T.; Jonasson, H.I.; Jonsdottir, H. An authoritative study on the near future effect of artificial intelligence on project management knowledge areas. Sustainability 2021, 13, 2345. [CrossRef]   
39. Liu, W. Energy Project Management with Artificial Intelligence. Int. J. Electr. Power Energy Stud. 2024, 2, 10–16. [CrossRef]   
40. Oyekunle, D.; Darkwah, J.A.; Olusesi, L.D. Project Management Competencies in AI-Driven Environments: A Qualitative Assessment. Int. J. Innov. Sci. Res. Technol. 2024, 9, 1769–1779.   
41. Barcaui, A.; Monat, A. Who is better in project planning? Generative artificial intelligence or project managers? Proj. Leadersh. Soc. 2023, 4, 100101. [CrossRef]   
42. Wei, W.; Rana, M.E. Software project schedule management using machine learning & data mining. Int. J. Sci. Technol. Res. 2019, 8, 1385–1389.   
43. Prasad, K.; Saradhi, M.V. Comprehensive project management framework using machine learning. Int. J. Recent Technol. Eng. 2019, 8, 1373–1377.   
44. Park, J.; Lee, H.; Kim, H.Y. Risk factor recognition for automatic safety management in construction sites using fast deep convolutional neural networks. Appl. Sci. 2022, 12, 694. [CrossRef]   
45. Gupta, S. Artificial Intelligence, Analytics and Agile: Transforming Project Management in the 21st Century. Int. J. Recent Technol. Eng. 2022, 11, 1–8.   
46. Sahadevan, S. Project Management in the Era of Artificial Intelligence. Eur. J. Theor. Appl. Sci. 2023, 1, 349–359.   
47. Khan, A.N.; Mehmood, K.; Soomro, M.A. Knowledge Management-Based Artificial Intelligence (AI) Adoption in Construction SMEs: The Moderating Role of Knowledge Integration. IEEE Trans. Eng. Manag. 2024, 71, 10874–10884.   
48. Altaie, M.R.; Dishar, M.M. Integration of Artificial Intelligence Applications and Knowledge Management Processes for Construction Projects Management. Civ. Eng. J. 2024, 10, 738–756.   
49. Fang, W.; Ding, L.; Zhong, B.; Love, P.E.; Luo, H. Automated detection of workers and heavy equipment on construction sites: A convolutional neural network approach. Adv. Eng. Inform. 2018, 37, 139–149. [CrossRef]   
50. Costantino, F.; Di Gravio, G.; Nonino, F. Project selection in project portfolio management: An artificial neural network model based on critical success factors. Int. J. Proj. Manag. 2015, 33, 1744–1754.   
51. Pereira, E.; Ali, M.; Wu, L.; Abourizk, S. Distributed simulation–based analytics approach for enhancing safety management systems in industrial construction. J. Constr. Eng. Manag. 2020, 146, 04019091. [CrossRef]   
52. Yang, Z.; Xue, F.; Lu, W. Handling missing data for construction waste management: Machine learning based on aggregated waste generation behaviors. Resour. Conserv. Recycl. 2021, 175, 105809.   
53. Zou, Y.; Kiviniemi, A.; Jones, S.W. Retrieving similar cases for construction project risk management using Natural Language Processing techniques. Autom. Constr. 2017, 80, 66–76.   
54. Chen, J.; Su, M.; Azzizi, V.T.; Wang, T.; Lin, W. Smart project management: Interactive platform using natural language processing technology. Appl. Sci. 2021, 11, 1597. [CrossRef]   
55. Chattapadhyay, D.B.; Putta, J.; Rao, P.R.M. Risk identification, assessments, and prediction for mega construction projects: A risk prediction paradigm based on cross analytical-machine learning model. Buildings 2011, 11, 172.   
56. Tixier, A.J.; Hallowell, M.R.; Rajagopalan, B.; Bowman, D. Automated content analysis for construction safety: A natural language processing system to extract precursors and outcomes from unstructured injury reports. Autom. Constr. 2016, 62, 45–56. [CrossRef]   
57. Tan, J.B.B.; Chen, Q.; Yeo, C.K. Project Reporting Management System with AI based Assistive Features for Text Summarization. Int. J. Mach. Learn. Comput. 2021, 11, 21–27.   
58. Odejide, O.A.; Edunjobi, T.E. AI in project management: Exploring theoretical models for decision-making and risk management. Eng. Sci. Technol. J. 2024, 5, 1072–1085.   
59. Tubman, A. The Use of Artificial Intelligence in International Decision-Making Processes in Project Management. Available online: https://ssrn.com/abstract=4121200 or http://dx.doi.org/10.2139/ssrn.4121200 (accessed on 27 May 2022).   
60. Yahya, M.Y.; Abba, W.A.; Yassin, A.M.; Omar, R.; Sarpin, N.; Orbintang, R. Innovative Strategies for Enhancing Construction Project Performance. J. Technol. Manag. Bus. 2024, 11, 17–31. [CrossRef]   
61. Alzaabi, O.; Al Mahri, K.; El Khatib, M.; Alkindi, N. How big data analytics supports project manager in project risk management– cases from UAE health sector. Int. J. Bus. Anal. Secur. IJBAS 2023, 3, 11–26.   
62. Ayhan, B.U.; Tokdemir, O.B. Accident analysis for construction safety using latent class clustering and artificial neural networks. J. Constr. Eng. Manag. 2020, 146, 04019114.   
63. Tijani´c, K.; Car-Puši´c, D.; Šperac, M. Cost estimation in road construction using artificial neural network. Neural Comput. Appl. 2019, 32, 9343–9355.   
64. Wauters, M.; Vanhoucke, M. A comparative study of Artificial Intelligence methods for project duration forecasting. Expert Syst. Appl. 2016, 46, 249–261. [CrossRef]   
65. Innocent, M.; Wasek, J.S.; Franz, A. Predicting military construction project time outcomes using data analytics. Eng. Manag. J. 2018, 30, 232–246. [CrossRef]   
66. Heravi, G.; Eslamdoost, E. Applying artificial neural networks for measuring and predicting construction-labor productivity. J. Constr. Eng. Manag. 2015, 141, 04015032. [CrossRef]   
67. Patel, D.A.; Jha, K.N. Neural network model for the prediction of safe work behavior in construction projects. J. Constr. Eng. Manag. 2015, 141, 04014066. [CrossRef]   
68. Wen, H.; Wang, S.; Jiang, C.; Zhang, F.; Li, J.; Luo, X. AI-KM: A distributed multi-view and intelligent knowledge management software. SoftwareX 2024, 27, 101840.   
69. Wu, T.B.; Liu, X.; Zhou, T.Q. Study on Construction Bidding Management based on GA-BP Neural Networks. Appl. Mech. Mater. 2014, 584–586, 2423–2426. [CrossRef]   
70. Pessoa, A.; Sousa, G.; Maués, L.M.F.; Alvarenga, F.C.; Santos, D.D.G. Cost forecasting of public construction projects using multilayer perceptron artificial neural networks: A case study. Ing. Investig. 2021, 41, e87737. [CrossRef]   
71. Dong, J.; Chen, Y.; Guan, G. Cost index predictions for construction engineering based on LSTM neural networks. Adv. Civ. Eng. 2020, 2020, 6518147.   
72. Hammoody, O.; Al-Somaydaii, J.; Al-Zwainy, F.; Hayder, G. Forecasting and determining of cost performance index of tunnels projects using artificial neural networks. Int. J. Comput. Civ. Struct. Eng. 2022, 18, 51–60.   
73. Chaphalkar, N.B.; Iyer, K.C.; Patil, S.K. Prediction of outcome of construction dispute claims using multilayer perceptron neural network model. Int. J. Proj. Manag. 2015, 33, 1827–1835.   
74. Tominc, P.; Oreški, D.; Canˇcer, V.; Rožman, M. Statistically significant differences in AI support levels for project management ˇ between SMEs and large enterprises. AI 2024, 5, 136–157. [CrossRef]   
75. Tariq, B.; Ali, A.; Khattak, M.S.; Arfeen, M.I.; Chaudhary, M.A.I.; Iqbal, F. Artificial intelligence and project management maturity: A study of selected project-based organizations in Pakistan. Int. J. Adv. Appl. Sci. 2024, 11, 106–117.   
76. Shamim, M.M.I. Artificial Intelligence in Project Management: Enhancing Efficiency and Decision-Making. Int. J. Manag. Inf. Syst. Data Sci. 2024, 1, 1–6.   
77. Amusan, L.M.; Omuh, I.O.; Mosaku, T.O. Building informatics neural network and regression heuristics protocol for making decisions in building construction projects. Proc. Int. Struct. Eng. Constr. 2019, 6. [CrossRef]   
78. Van, T.N.; Quoc, T.N. Research trends on machine learning in construction management: A scientometric analysis. J. Appl. Sci. Technol. Trends 2021, 2, 124–132.   
79. Shen, T.; Nagai, Y.; Gao, C. Design of building construction safety prediction model based on optimized BP neural network algorithm. Soft Comput. 2019, 24, 7839–7850.   
80. Jia, Q.; Guo, Y.; Li, R.; Li, Y.; Chen, Y. A Conceptual Artificial Intelligence Application Framework in Human Resource Management. In Proceedings of the ICEB 2018 Proceedings, Guilin, China, 2–6 December 2018; pp. 106–114.   
81. Karamthulla, M.J.; Prakash, S.; Tadimarri, A.; Tomar, M. Efficiency unleashed: Harnessing AI for agile project management. Int. J. Multidiscip. Res. 2024, 6, 1–13.   
82. Joshi, H. Artificial Intelligence in Project Management: A Study of The Role of Ai-Powered Chatbots in Project Stakeholder Engagement. Indian J. Softw. Eng. Proj. Manag. IJSEPM 2024, 4, 20–25. [CrossRef]   
83. Balcio ˘glu, Y.S.; Artar, M.; Erdil, P.D.O. Artificial Intelligence in Project Management: An Application in The Banking Sector. Akad. Ara¸stırmalar Çalı¸smalar Derg. AKAD 2022, 14, 323–334.   
84. Wang, B.; Yuan, J.; Ghafoor, K.Z. Research on Construction Cost Estimation Based on Artificial Intelligence Technology. Scalable Comput.-Prac. Exp. 2021, 22, 93–104.   
85. Karamthulla, M.J.; Muthusubramanian, M.; Tadimarri, A.; Tillu, R. Navigating the Future: AI-Driven Project Management in the Digital Era. Int. J. Multidiscip. Res. 2024, 6, 1–11.   
86. Bahi, A.; GHARI, J.; Gahi, Y. Integrating Generative AI for Advancing Agile Software Development and Mitigating Project Management Challenges. Int. J. Adv. Comput. Sci. Appl. 2024, 15. [CrossRef]   
87. Luskatov, N.; Luskatova, O. Using of neural networks for risk-management of state investment projects. Life Sci. J. 2022, 11, 434–443.   
88. Ibrahim, A.A.; Edith, E.A.; Christianah, P.E.; Olajide, S.O.; Henry, O.I. The future of project management in the digital age: Trends, challenges, and opportunities. Eng. Sci. Technol. J. 2024, 5, 2632–2648.   
89. Li, Z.Y. Predicting project effort intelligently in early stages by applying genetic algorithms with neural networks. Appl. Mech. Mater. 2014, 513–517, 2035–2040.   
90. Nguyen, P.T. Application machine learning in construction management. TEM J. 2021, 10, 1385–1389.   
91. Zhang, L.; Wu, X.; Skibniewski, M.J.; Zhong, J.; Lu, Y. Bayesian-network-based safety risk analysis in construction projects. Reliab. Eng. Syst. Saf. 2014, 131, 29–39. [CrossRef]   
92. Ali, Z.H.; Burhan, A.M.; Kassim, M.; Al-Khafaji, Z. Developing an integrative data intelligence model for construction cost estimation. Complexity 2022, 2022, 4285328.   
93. Ko, Y.; Han, S. A duration prediction using a material-based progress management methodology for construction operation plans. Sustainability 2017, 9, 635. [CrossRef]   
94. Pan, Y.; Zhang, L. Roles of artificial intelligence in construction engineering and management: A critical review and future trends. Autom. Constr. 2021, 122, 103517. [CrossRef]   
95. Zhang, Y. Safety management of civil engineering construction based on artificial intelligence and machine vision technology. Adv. Civ. Eng. 2021, 2021, 3769634.   
96. Patel, D.A.; Jha, K.N. Evaluation of construction projects based on the safe work behavior of co-employees through a neural network model. Saf. Sci. 2016, 89, 240–248. [CrossRef]   
97. Hanafi, A.G.; Nawi, M.N.M.; Rahim, M.K.I.A.; Nifa, F.A.A.; Mohamed, O. Project managers selection in the construction industry: Towards the integration with artificial emotional intelligence and technology. J. Adv. Res. Appl. Sci. Eng. Technol. 2022, 29, 160–176.   
98. Tselios, D.C.; Savvas, I.K.; Kechadi, M. Multiple project portfolio scheduling using recurrent neural networks. Int. J. Simul. Process Model. 2013, 8, 227–240. [CrossRef]   
99. Goh, Y.M.; Chua, D. Neural network analysis of construction safety management systems: A case study in Singapore. Constr. Manag. Econ. 2013, 31, 460–470.   
100. Karatas, I.; Budak, A. Prediction of labor activity recognition in construction with machine learning algorithms. Icontech Int. J. 2021, 5, 38–47. [CrossRef]   
101. Dad, A.M.; Khan, A.R.; Jamal, A. Enhancing Project Management Efficiency Through AI Integration, Team Proficiency, and Organizational Support: A Study in the Pakistani Context. Asian Bull. Green Manag. Circ. Econ. 2024, 4, 16–27. [CrossRef]