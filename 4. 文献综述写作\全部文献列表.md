## 范文
[1] 吴家欢. 途虎养车的软件开发项目进度管理研究[D]. 东华大学, 2022.

[2] 许连旭. 基于敏捷开发的J公司平台项目进度管理研究[D]. 北京邮电大学, 2024.

[3] 王聪聪. 基于Scrum敏捷开发方法的A公司PVoC业务数字平台项目进度管理研究[D]. 浙江大学, 2023.

[4] 蔡丹. 基于Scrum敏捷模型的A公司软件项目进度管理优化研究[D]. 南京邮电大学, 2023.

[5] 张丽涛. A公司大数据分析平台研发项目进度管理研究[D]. 北京邮电大学, 2023.

[6] 许连旭. 基于敏捷开发的J公司平台项目进度管理研究[D]. 北京邮电大学, 2024.

[7] 庞绪瑞. S公司W游戏软件研发顶目的进度管理研究[D]. 北京邮电大学, 2023.

[8] 蔡丹. 基于Scrum敏捷模型的A公司软件项目进度管理优化研究[D]. 南京邮电大学, 2023.

## 背景与行业问题 
[1] 唐刚. 基于敏捷方法的多系统软件项目进度管理研究[D]. 中国科学院大学, 2019.
[2] Fathom Blog. Real-Time Task Scheduling in Multi-Agent Systems[N]. Fathom AI, 2025-08-07.
[3] Gilmour K. State of Project Management 2025[N]. Proteus Blog, 2025-04-21.
[4] Asif R. Challenges in Multi-Agent AI Systems: A Deep Dive into the Complexities[N]. Medium, 2024-10-03.
[5] 李川川. S公司软件开发项目进度延误原因分析及对策研究[D]. 华东理工大学, 2018.
[6] 王春瑾, 黄淑君, 鹿洵. 敏捷方法与传统方法相结合的软件项目管理模型研究[J]. 电子产品可靠性与环境试验, 2024(2): 82-88.
[7] 颜功达, 董鹏, 文昊林. 基于多智能体的复杂工程项目进度风险评估仿真建模[J]. 计算机科学, 2019, 46(S1): 523-526.
[8] Steidl M, Felderer M, Ramler R. The Pipeline for the Continuous Development of Artificial Intelligence Models -- Current State of Research and Practice[J]. Journal of Systems and Software, 2023, 199: 111615.
[9] Salimimoghadam S, Ghanbaripour A N, Tumpa R J, et al. The Rise of Artificial Intelligence in Project Management: A Systematic Literature Review of Current Opportunities, Enablers, and Barriers[J]. Buildings, 2025, 15(7): 1130.
[10] Paternoster N, Giardino C, Unterkalmsteiner M, 等. Software development in startup companies: A systematic mapping study[J]. Information and Software Technology, 2014, 56(10): 1200-1218.
[11] Mohammad A, Chirchir B. Challenges of Integrating Artificial Intelligence in Software Project Planning: A Systematic Literature Review[J]. Digital, 2024, 4(3): 555-571.
[12] 石慧. 软件开发项目的进度计划与控制研究[D]. 武汉理工大学, 2007.
[13] 方月, 谢跃文. 项目计划与控制研究综述[J]. 中国建设信息, 2013(20): 72-75.

## 研究意义

[1] 董婷. A公司运营管理平台软件升级项目进度管理研究[D]. 西安电子科技大学, 2020.
[2] 蔡春升. PERT技术在软件开发项目进度管理中的应用研究[D]. 上海交通大学, 2016.
[3] Zadeh M T, Kashef R. The Impact of IT Projects Complexity on Cost Overruns and Schedule Delays[A]. arXiv, 2022.
[4] Ayele Y G. The Significance of Planning and Scheduling on the Success of Projects[J]. Engineering Management International, 2023, 1(1): 66.
[5] Suresh D, Annamalai S. Effect of schedule management plan in project management worth using structural equation modelling[J]. [2025].
[6] Cohen I. Data-driven project planning: An integrated network learning and constraint relaxation approach in favor of scheduling[J]. IEEE Transactions on Engineering Management, 2024, 71: 7719-7729.

## 理论框架类

[1] Audrey Ingram. Ultimate guide to Earned Value Management in 2025[N]. Bonsai, 2025-07-09.
[2] Koichi Ujigawa. TOC Body of Knowledge Agile CCPM Critical Chain for Software Development[N]. TOCICO,  2016-08-08. 
[3] Anbari, Frank T. The earned schedule[C]. PMI research and education conference, 2012: 1-12.
[4] Ren Y, Li J. Research on Software Project Schedule Planning Technology Based on the Integration of PERT and CPM[J]. Procedia Computer Science, 2023, 228: 253-261.
[5] 李鸿, 倪枫, 刘文诚, 等. 约束条件下项目进度关键路径的Petri网建模方法[J]. 软件工程, 2024, 27(4): 54-59.
[6] 朱梦玲. 基于关键链的档案信息系统项目进度管理研究[J]. 项目管理技术, 2025, 23(7): 133-139.
[7] 张玉婷, 杨镜宇. 基于WBS的某决策支持系统开发项目进度管理研究[J]. 项目管理技术, 2023, 21(10): 142-148.
[8] 张琦. 软件研发项目进度管理研究[J]. 华东科技, 2024(3): 106-108.
[9] 杨旻. 软件项目的进度管理[J]. 项目管理技术, 2008(S1): 134-137.
[10] 许薇. IT项目研发过程中的进度管理研究[J]. 项目管理技术, 2009(S1): 26-30.
[11] 肖勇, 管致乾. 基于可靠性理论的关键链缓冲区计算方法[J]. 项目管理技术, 2023, 21(1): 115-120.
[12] 温翔. 进度管理在软件项目中的应用实践[J]. 计算机时代, 2011(6): 69-70.
[13] 田丽蓉. 项目进度管理中BIM技术的价值及应用[J]. 产业创新研究, 2023(16): 132-134.
[14] 宋雪琴. 软件项目进度管理的实用分析[J]. 现代企业文化, 2023(2): 34-36.
[15] 马鑫. 基于关键链理论下项目进度管理的优化路径[J]. 产业创新研究, 2023(15): 166-168.
[16] 贾郭军. 软件项目实施过程中的进度管理研究[J]. 西安科技学院学报, 2004(2): 221-224.
[17] 包冬梅. 基于WBS的软件项目成本估算[J]. 河北企业, 2016(1): 24-25.

## 图书
[1] 项立刚. 项目管理[M]. 北京: 中国建材工业出版社, 2010: 1-320.
[2] 李英龙, 郑河荣. 软件项目管理[M]. 北京: 清华大学出版社, 2015: 1-280.
[3] 项目管理协会. 项目管理知识体系指南: PMBOK指南 [M]. 王勇, 张斌, 译. 第6版. 北京: 电子工业出版社, 2018: 1-756.
