# 基于 WBS 的某决策支持系统开发项目进度管理研究

# 张玉婷1 ，2 杨镜宇3

( 1. 国防大学研究生院，北京 100091  
2. 海军参谋部机要局，北京 10084

3. 国防大学联合作战学院，北京 10009

<table><tr><td>摘要：为保证某决策支持系统开发项目能够在有序管控下运行，并按期按质实现系统交付使用，首先，</td></tr><tr><td>项目进度管理相关概念进行概述：其次，基于WBS制订进度管理计划：最后，基于三点估算法和蒙特卡 X</td></tr><tr><td>方法对工期进行估算仿真。结果显示，工期估算结果具有一定的可行性，该项目进度管理计划能够为管</td></tr><tr><td>人员提供决策参考</td></tr><tr><td>关键词：决策支持系统；项目进度管理；蒙特卡洛法：三点估算去</td></tr></table>

# 0 引言

项目是为创造独特的产品 服务或成果而行的临时性工作，是组织创造价值、 实现效益重要形式［1］ 基于项目管理理论，对项目活动行整合和管理，如项目的进度、 成本、 质量等有助于提高项目管理效率、 缩短项目工期、 节项目成本。 而项目进度管理是项目管理的重要容。 通过分析项目中各工序间的逻辑关系，确科学的施工顺序; 通过编制进度计划和资源供计划，在质量、 费用目标协调的基础上实现工目标。 目前，常用的项目进度管理方法包括甘图 ( Gantt Chart，GC) 、 关键路径法 ( Critical Pat hMethod，CPM) 图示评审技术 ( Graphical Evalution Review Technique， GERT ) 风 险 评 审 技( Venture Evaluation Review Technique，VERT)划评审技术 ( Program Evaluation and Review Technique，PERT) 、 关 键 链 项 目 管 理 ( Critical ChaProject Management，CCPM ) 等［2-3］ 。 C

随着科学技术的进步、 网络信息技术的发展决策支持系统 ( Decision Support System，DSS)从传统的单模型、 人工协调模式发展为多模型计算机自组织协调运行的辅助决策模式。 本文研究的某决策支持系统以管理科学 系统工程理论基础，以仿真模拟技术为技术手段，能够管理人员提供决策参考 该系统涉及与多种设备多个平台的对接，以及各类复杂数据的处理据库和模型库的管理等过程，若进度管理过程现问题，则会影响项目总体进度，甚至会出现联效应，使项目面临严重风险

基于此，本文针对该系统开发项目进度管 甲问题进行分析，制订了项目进度计划并进行工 估算仿真，主要包括创建项目 WBS 设置目标 里程碑 绘制网络计划图以及利用三点估算法 蒙特卡洛法对工期进行估算仿真等

# 1 经典 PERT 方法概述

Malcolm 等［4］于 20 世纪 50 年代末提出了经  
PERT 网络方法 该方法中各项工作间的逻辑关  
明确，工作持续时间为随机变量。 设 a b m  
别表示某项工作的最乐观时间、 最悲观时间、  
可能时间，以 $\mu = \frac { a + 4 m + } { \zeta }$ 表示工序持续时  
期望值， $\boxed { \sigma ^ { 2 } = \frac { \left( \ b - a \right) } { 3 6 } }$ 表示工序持续时间方差经典PERT 方法的假设条件包括: 各工序持

![](images/83399d2553c713e3d95c30499747615459a62f56b2ae5ec6379adde51233ab8c.jpg)

$$
\boxed { \begin{array} { r c l } { T _ { e } = \displaystyle \sum _ { i = 1 } ^ { k } ( \frac { a _ { i } + 4 m _ { i } + b } { \sigma } ) } \\ { \boxed { \sigma _ { e } ^ { 2 } = \displaystyle \sum _ { i = 1 } ^ { k } \left( \frac { b _ { i } - a _ { i } } { \sigma } \right) ^ { 2 } } } \end{array} }
$$

![](images/3b9ce61246502b0f1ab6311a69677543d64d47dc026c4626a145ce2e3c10b319.jpg)

$$
\underline { { z = \frac { T - \lambda } { \sigma } } } ^ { }
$$

![](images/980694ce7188ff09492b29a2abb60c83bb339d37dbc9d522373317837800d894.jpg)

# 2 某决策支持系统开发项目进度计划编制

# 2.1 项目工作分解结 构

![](images/fd3adfc1d64b72593e15c9335580a0158ae2a0fdef32da0f17ce8590c4251267.jpg)

图，如图1 所示。

![](images/b8ac21c500aa270e9c29ab5b321f3a92c5c2c552fee09fc36c71de86a64ab06c.jpg)  
图1 某决策支持系统开发项目 WBS

# 2. 2 项目里程碑

表1 某决策支持系统开发项目里程碑节  

<table><tr><td>某决策支持系统开发项目生命周期管理包</td></tr><tr><td>需求分析、方案设计、系统设计、编码和测试</td></tr><tr><td></td></tr><tr><td>集成系统测试、验收发布、项目总结7个阶段</td></tr><tr><td></td></tr><tr><td>整个项目管理过程复杂且漫长，各阶段在时间</td></tr><tr><td>度呈串行分布，因此，需要设置项目建设的关键</td></tr><tr><td></td></tr><tr><td>性节点对项目进展进行标识，以保证项目按时</td></tr><tr><td></td></tr><tr><td>工。某决策支持系统开发项目里程碑节点见表1</td></tr></table>

<table><tr><td>阶段</td><td>×月×日×月×日×月×日×月×日×月×日×月×日×月×日</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>需求分析</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>方案设计</td><td></td><td>△</td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>系统设计</td><td></td><td></td><td>△</td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>编码和测试</td><td></td><td></td><td></td><td>▲</td><td></td><td></td><td></td><td></td></tr><tr><td>集成系统 测试</td><td></td><td></td><td></td><td></td><td>▲</td><td></td><td></td><td></td></tr><tr><td>验收发布</td><td></td><td></td><td></td><td></td><td></td><td>▲</td><td></td><td></td></tr><tr><td>项目总结</td><td></td><td></td><td></td><td></td><td></td><td></td><td>△</td><td></td></tr></table>

# 2. 3 项目组织管

表2 某决策支持系统人力资源计划表  

<table><tr><td>在项目进度管理中，合理规划人力资源类</td></tr><tr><td>和工作量、制订人力资源计划十分重要。根据</td></tr><tr><td></td></tr><tr><td>目WBS，设置项目管理第一责任人、项目负责人</td></tr><tr><td>系统分析员、程序员、工程师等岗位。其中，</td></tr><tr><td></td></tr><tr><td>目管理第一责任人主要负责追踪项目整体进度</td></tr><tr><td>调配项目负责人员及确定各组分工，牵头项目</td></tr><tr><td>审工作，协调其他难题等：项目负责人主要负</td></tr><tr><td></td></tr><tr><td>与项目管理第一责任人沟通、汇报工作情况，</td></tr><tr><td>接负责项目管理，负责里程碑评审及文档审核等</td></tr><tr><td></td></tr><tr><td>系统分析员主要参与项目早期筹划和项目评审等</td></tr><tr><td></td></tr><tr><td>程序员主要负责软件设计、软件编码、集成与</td></tr><tr><td>试等：工程师主要负责软件需求分析、软件质</td></tr><tr><td></td></tr><tr><td>保证、撰写工作日记等。某决策支持系统人力</td></tr><tr><td>源计划表见表2</td></tr></table>

<table><tr><td>工作类别</td><td>工作内容</td><td>人资资源配置</td></tr><tr><td rowspan="4">1.需求分析</td><td>1.1客户需求获取</td><td>项目负责人、系统分析员</td></tr><tr><td>1.2产品需求说明</td><td>项目负责人、系统分析员</td></tr><tr><td>1.3需求评审</td><td>系统分析员、工程师</td></tr><tr><td>1.4客户确认需求</td><td>系统分析员、工程师</td></tr></table>

<table><tr><td>工作类别</td><td>工作内容</td><td>人资资源配置</td></tr><tr><td rowspan="2">1.需求分析</td><td>1.5 技术方案编评</td><td>系统分析员、工程师</td></tr><tr><td>1.6确认工作量和</td><td>项目负责人、系统分析</td></tr><tr><td rowspan="2">2.方案设计</td><td>时间 2.1总体设计</td><td>员、工程师 项目负责人、系统分析</td></tr><tr><td>2.2概要设计</td><td>员、工程师 系统分析员、工程师</td></tr><tr><td rowspan="6">3．系统设计</td><td>2.3设计评审</td><td>系统分析员、工程师</td></tr><tr><td>3.1界面模块设计</td><td>程序员</td></tr><tr><td>3.2接口模块设计</td><td>程序员</td></tr><tr><td>3.3 管理模块设计</td><td>程序员</td></tr><tr><td>3.4安全系统设计</td><td>程序员</td></tr><tr><td>3.5用例设计 3.6设计规格说明</td><td>程序员、工程师</td></tr><tr><td rowspan="20"></td><td>4.1 界面模块一阶 段编码</td><td>系统分析员、工程师 程序员</td></tr><tr><td>4.2 界面模块二阶 段编码</td><td>程序员</td></tr><tr><td>4.3界面管理交互</td><td>程序员、工程师</td></tr><tr><td>4.4界面模块单元 测试</td><td>系统分析员、程序员</td></tr><tr><td>4.5数据模块一阶 段编码</td><td>程序员</td></tr><tr><td>4.6数据模块二阶</td><td>程序员</td></tr><tr><td>段编码 4.7数据管理交互</td><td>程序员、工程师</td></tr><tr><td>4.8数据模块单元</td><td>工程师</td></tr><tr><td>4.编码和测试 测试 4.9接口模块一阶</td><td></td></tr><tr><td>段编码 4.10 接口模块二阶</td><td>工程师</td></tr><tr><td>段编码 4.11接口模块单元</td><td>工程师</td></tr><tr><td>测试 4.12管理模块一阶</td><td>系统分析员、工程师</td></tr><tr><td>段编码 4.13 管理模块二阶</td><td>程序员</td></tr><tr><td>段编码</td><td>程序员</td></tr><tr><td>4.14 管理模块三阶 段编码</td><td>程序员</td></tr></table>

<table><tr><td></td><td>（终</td></tr></table>

<table><tr><td>工作类别</td><td>工作内容</td><td>人资资源配置</td><td></td></tr><tr><td rowspan="3">4.编码和测试</td><td>4.15管理模块单元 测试</td><td>系统分析员、工程师</td><td></td></tr><tr><td>4.16 安全模块编码</td><td>程序员</td><td></td></tr><tr><td>4.17安全模块单元 测试</td><td>系统分析员、工程师</td><td></td></tr><tr><td rowspan="2">5.集成系统测试</td><td>5.1代码修正</td><td>程序员、工程师</td><td></td></tr><tr><td>5.2集成测试与试 运行</td><td>系统分析员、工程师</td><td></td></tr><tr><td rowspan="2">6.验收发布</td><td>6.1产品发布</td><td>项目负责人、系统分析 员、工程师</td><td></td></tr><tr><td>6.2验收和维护</td><td>项目负责人、系统分析 员、工程师</td><td></td></tr><tr><td>7.项目总结</td><td>7.1项目总结</td><td>项目负责人、系统分析 员、工程师、程序员</td><td></td></tr></table>

# 2. 4 项目网络计划

表3 项目活动逻辑关系  

<table><tr><td>项目网络计划图可以直观展示项目中各项</td></tr><tr><td></td></tr><tr><td>作进度及工作之间的关系，通过网络分析确定</td></tr><tr><td>键路径等[5]。网络计划图一般由工作、事项、</td></tr><tr><td>路三个部分构成。本文采用PERT三点估计法对</td></tr><tr><td>序持续时间进行估算。为编制某决策支持系统</td></tr><tr><td></td></tr><tr><td>发项目网络计划图，首先，确定项目中各项工</td></tr><tr><td>工期和工作间的逻辑关系（表3）；其次，根据</td></tr><tr><td>目活动紧前和紧后关系，绘制某决策支持系统</td></tr><tr><td></td></tr><tr><td>发项目进度网络计划图，如图2所示。由计算</td></tr></table>

<table><tr><td>工作编号</td><td>工作名称</td><td>工期/d</td><td>紧前工作</td></tr><tr><td>1-2</td><td>客户需求获取</td><td>7</td><td></td></tr><tr><td>2-3</td><td>产品需求说明</td><td>5</td><td>1-2</td></tr><tr><td>2-4</td><td>需求评审</td><td>8</td><td>1-2</td></tr><tr><td>3-5</td><td>客户确认需求</td><td>5</td><td>2-3</td></tr></table>

公  

<table><tr><td colspan="6"></td></tr><tr><td>工作编号</td><td>工作名称</td><td>工期/d</td><td></td><td>紧前工作</td></tr><tr><td>4-5</td><td>技术方案编评</td><td>15</td><td>2-4</td><td></td></tr><tr><td>5-6</td><td>确认工作量和时间</td><td>5</td><td>3-5，4-5</td><td></td></tr><tr><td>6-7</td><td>总体设计</td><td>14</td><td>5-6</td><td></td></tr><tr><td>7-8</td><td>概要设计</td><td>35</td><td>6-7</td><td></td></tr><tr><td>8-9</td><td>设计评审</td><td>15</td><td>7-8</td><td></td></tr><tr><td>9-10</td><td>界面模块设计</td><td>5</td><td>8-9</td><td></td></tr><tr><td>9-11</td><td>管理模块设计</td><td>10</td><td>8-9</td><td></td></tr><tr><td>9-12</td><td>用例设计</td><td>25</td><td>8-9</td><td></td></tr><tr><td>10-12</td><td>接口模块设计</td><td>7</td><td>9-10</td><td></td></tr><tr><td>11 -12</td><td>安全系统设计</td><td>8</td><td>9-11</td><td></td></tr><tr><td>12-13</td><td>设计规格说明</td><td>15</td><td>10 -12, 11-12,9-12</td><td></td></tr><tr><td>13-14</td><td>界面模块一阶段编码</td><td>30</td><td>12-13</td><td></td></tr><tr><td>13-16</td><td>数据模块一阶段编码</td><td>60</td><td>12-13</td><td></td></tr><tr><td>13-18</td><td>接口模块一阶段编码</td><td>30</td><td>12-13</td><td></td></tr><tr><td>13-20</td><td>管理模块一阶段编码</td><td>35</td><td>12-13</td><td></td></tr><tr><td>13-23</td><td>安全模块编码</td><td>35</td><td>12-13</td><td></td></tr><tr><td>14-15</td><td>界面模块二阶段编码</td><td>40</td><td>13-14</td><td></td></tr><tr><td>15-21</td><td>界面管理交互</td><td>20</td><td>14-15</td><td></td></tr><tr><td>15-24</td><td>界面模块单元测试</td><td>10</td><td>14 -15</td><td></td></tr><tr><td>16-17</td><td>数据模块二阶段编码</td><td>80</td><td>13-16</td><td></td></tr><tr><td>17-21</td><td>数据管理交互</td><td>30</td><td>16-17</td><td></td></tr><tr><td>17-24</td><td>数据模块单元测试</td><td>15</td><td>16-17</td><td></td></tr><tr><td>18-19</td><td>接口模块二阶段编码</td><td>45</td><td>13-18</td><td></td></tr><tr><td>19-24</td><td>接口模块单元测试</td><td>15</td><td>18-19</td><td></td></tr><tr><td>20-21</td><td>管理模块二阶段编码</td><td>50</td><td>13-20</td><td></td></tr><tr><td>21-22</td><td>管理模块三阶段编码</td><td>70</td><td>15-21, 17 -21, 20 -21</td><td></td></tr><tr><td>22-24</td><td>管理模块单元测试</td><td>25</td><td>21-22</td><td></td></tr><tr><td>23-24</td><td>安全模块单元测试</td><td>15</td><td>13-23</td><td></td></tr><tr><td>24-25</td><td>代码修正</td><td>45</td><td>15-24，17 -24，19-24,</td><td></td></tr><tr><td>25-26</td><td>集成测试与试运行</td><td>20</td><td></td><td>22 -24,23-24 24-25</td></tr><tr><td>26-27</td><td>产品发布</td><td>7</td><td>25-26</td><td></td></tr><tr><td>26-27</td><td>验收和维护</td><td>10</td><td>25-26</td><td></td></tr><tr><td>27-28</td><td>项目总结</td><td>7</td><td>26-27</td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr></table>

![](images/61132c491dbf1f9fbddeaab89e9fbdd27f094daef9f5382f70bae2146b99aad3.jpg)  
图2 某决策支持系统开发项目进度网络计划 冬

![](images/36e8245de2c975a56267bc64e4713667e3fe8326e0c6c62354e62efa1c7ebd0f.jpg)

# 3 基于 MC 的某决策支持系统开发项目工期仿真模拟

表4 基于 PERT 的网络进度计划仿真模   

<table><tr><td>由于该项目中工作数量多，且各工作之间</td></tr><tr><td>在关联依赖关系，因此，在识别关键路径的基</td></tr><tr><td>上，进一步分析工序与环境的关系、工序间的</td></tr><tr><td>源和时间冲突[16-2]等不确定性因素，对项目进</td></tr><tr><td>计划进行优化</td></tr><tr><td></td></tr><tr><td>本文采用蒙特卡洛方法（MonteCarlo，MC）</td></tr><tr><td>对关键路径工期估算进行仿真模拟。MC的核心！</td></tr><tr><td>想是采用随机抽样的方法进行统计模拟试验，</td></tr><tr><td></td></tr><tr><td>到统计特征值。在制订项目进度计划时，首先</td></tr><tr><td>构建概率模型：其次，对概率模型进行抽样试验</td></tr><tr><td></td></tr><tr><td>在此基础上进行工期估计：最后，得到仿真试</td></tr><tr><td>预测结论。本文采用水晶球（CrystalBall）软件</td></tr><tr><td></td></tr><tr><td>行进度管理计划分析，具体步骤如下</td></tr><tr><td></td></tr><tr><td>（1）基于关键路径，采用蒙特卡洛方法构 中</td></tr><tr><td></td></tr><tr><td>项目进度计划仿真模型，见表4</td></tr></table>

<table><tr><td>工作 编号</td><td>工作内容</td><td>工期 B/d</td><td>最早开始 时间C</td><td>最早结束 时间D</td></tr><tr><td>1-2</td><td>客户需求获取</td><td>7</td><td>0</td><td> = C2 + B2</td></tr><tr><td>2-3</td><td>产品需求说明</td><td>5</td><td>= D2</td><td>= C3 + B3</td></tr></table>

![](images/d9906eeba1d25db6da9b0b18819277e60219415ca99ad88f4c7caa116da24d6b.jpg)

<table><tr><td colspan="6"></td></tr><tr><td></td><td></td><td>工</td><td></td><td></td><td></td></tr><tr><td></td><td>工作内容</td><td></td><td>最早始</td><td>最早结</td><td></td></tr><tr><td>2-4</td><td>需求评审</td><td>8</td><td>= D2</td><td></td><td> = C4 + B4</td></tr><tr><td>3-5</td><td>客户确认需求</td><td>5</td><td>= D3</td><td></td><td> = C5 + B5</td></tr><tr><td>4-5</td><td>技术方案编评</td><td>15</td><td>= D4</td><td></td><td> = C6 + B6</td></tr><tr><td>5-6</td><td>确认工作量和时间</td><td>5</td><td>= MAX （D5， D6)</td><td></td><td> = C7 + B7</td></tr><tr><td>6-7</td><td>总体设计</td><td>14</td><td>= D7</td><td></td><td> = C8 + B8</td></tr><tr><td>7-8</td><td>概要设计</td><td>35</td><td>= D8</td><td></td><td> = C9 + B9</td></tr><tr><td>8-9</td><td>设计评审</td><td>15</td><td>= D9</td><td></td><td>= C10 + B10</td></tr><tr><td>9-10</td><td>界面模块设计</td><td>5</td><td>= D10</td><td></td><td>= C11 + B1</td></tr><tr><td>9-11</td><td>管理模块设计</td><td>10</td><td>= D10</td><td></td><td>= C12 + B12</td></tr><tr><td>9-12</td><td>用例设计</td><td>25</td><td>= D10</td><td></td><td>= C13 + B13</td></tr><tr><td>10-12</td><td>接口模块设计</td><td>7</td><td>= D11</td><td></td><td>= C14 + B14</td></tr><tr><td>11 - 12</td><td>安全系统设计</td><td>8</td><td>= D12</td><td></td><td>= C15 + B15</td></tr><tr><td>12-13</td><td>设计规格说明</td><td>15</td><td>= MAX（D10, D11，D12)</td><td></td><td>= C16 + B16</td></tr><tr><td></td><td>13-14界面模块一阶段编码</td><td>30</td><td>= D16</td><td></td><td>= C17 + B17</td></tr><tr><td></td><td>13-16数据模块一阶段编码</td><td>60</td><td>= D16</td><td></td><td> = C18 + B18</td></tr><tr><td></td><td>13-18接口模块一阶段编码</td><td>30</td><td>= D16</td><td></td><td>= C19 + B19</td></tr><tr><td></td><td>13-20管理模块一阶段编码</td><td>35</td><td>= D16</td><td></td><td>= C20 + B20</td></tr><tr><td>13-23</td><td>安全模块编码</td><td>35</td><td>= D16</td><td></td><td>= C21 + B2</td></tr><tr><td></td><td>14-15界面模块二阶段编码</td><td>40</td><td>= D17</td><td></td><td>= C22 + B22</td></tr><tr><td>15-21</td><td>界面管理交互</td><td>20</td><td>= D22</td><td></td><td> = C23 + B23</td></tr><tr><td>15-24</td><td>界面模块单元测试</td><td>10</td><td>= D17</td><td></td><td>= C24 + B24</td></tr><tr><td></td><td>16-17数据模块二阶段编码</td><td>80</td><td>= D18</td><td></td><td> = C25 + B25</td></tr><tr><td>17-21</td><td>数据管理交互</td><td>30</td><td>= D25</td><td></td><td>= C26 + B26</td></tr><tr><td>17-24</td><td>数据模块单元测试</td><td>15</td><td>= D25</td><td></td><td>= C27 + B2</td></tr><tr><td></td><td>18-19接口模块二阶段编码</td><td>45</td><td>= D19</td><td></td><td>= C28 + B28</td></tr><tr><td>19-24</td><td>接口模块单元测试</td><td>15</td><td>= D28</td><td></td><td>= C29 + B29</td></tr><tr><td></td><td>20-21管理模块二阶段编码</td><td>50</td><td>= D20</td><td></td><td>= C30 + B30</td></tr><tr><td></td><td>21-22|管理模块三阶段编码</td><td>70</td><td></td><td>= MAX（D26,</td><td>= C31 + B3</td></tr><tr><td>22-24</td><td>管理模块单元测试</td><td>25</td><td></td><td>D23） = D31</td><td> = C32 + B32</td></tr><tr><td>23-24</td><td>安全模块单元测试</td><td>15</td><td></td><td>= D21</td><td>= C33 + B33</td></tr><tr><td>24-25</td><td>代码修正</td><td>45</td><td></td><td>= MAX（D32, D27，D24，</td><td>= C34 + B34</td></tr><tr><td>25-26</td><td>集成测试与试运行</td><td>20</td><td>D33，D29)</td><td>= D34</td><td>= C35 + B35</td></tr><tr><td>26-27</td><td>产品发布</td><td>7</td><td>= D35</td><td></td><td> = C36 + B36</td></tr><tr><td>26-27</td><td>验收和维护</td><td>10</td><td>= D35</td><td></td><td>= C37 + B31</td></tr><tr><td></td><td></td><td></td><td>= MAX（D36，</td><td></td><td></td></tr><tr><td>27-28</td><td>项目总结</td><td>7</td><td></td><td>D37)</td><td>= C38 + B38</td></tr></table>

![](images/a892be54c399ecaad6a0239a595622f5884441daa551681697c42cf38104f7f3.jpg)

![](images/ed285036337d4cfcfc2f478bf888783eeacb34dda119299efb8dfa907cf5beb0.jpg)  
图3 项目工期仿真图 ( 截图

![](images/4936eea685ae5876f47fb2382bd61ff36fa307c5e7ce541f0eaff33afef24e4e.jpg)  
图4 敏感度分析结果 ( 截图)

![](images/9518ecbf1a635d79fee690120dcc0c2c92eff1579cf82f053284a08dbd1cd6df.jpg)

# 4 结语

本文采用 WBS 分解某决策支持系统开发项工作单元，明确项目目标和里程碑节点，并根 据项目实际情况制订人力资源分配方案。通过三估算法和 MC 仿真实验方法对项目工期进行估算明确关键路径，制订科学合理的项目进度管理划，实现了项目进度优化，为管理人员提供了策依据

# 参考文

<table><tr><td>[1］李强.G公司运维软件开发项目的进度管理研究[D]</td></tr><tr><td>都：电子科技大学.2022</td></tr><tr><td>[2] MOELLER. Operation planning with VERT [J]. Operation R</td></tr><tr><td>search.1981,29 (4) : 676-697.</td></tr><tr><td>[3]王卓甫，工程项目管理[M]，北京：中国水利水电出</td></tr><tr><td>社，2007</td></tr><tr><td>[4] MALCOLM D G， ROSEBOOM J H. Aplication of a techniq</td></tr><tr><td>for research and development evaluation[J] . Opns. Res. 195</td></tr><tr><td>(7):646-669</td></tr><tr><td>[5]万伟，蔡晨，王长峰，在单资源约束项目中的关键链管</td></tr><tr><td>[]：中国管理科学，2003（2）：71-7</td></tr><tr><td>[6]张俊光，宋喜伟，贾赛可，等．基于梯形模糊数的项目</td></tr><tr><td>冲确定方法研究[]，管理工程学报，2015，29（2</td></tr><tr><td>223-228.</td></tr><tr><td>[7]郭小马，帕金森定律[]：企业管理，1999（3）：55号</td></tr><tr><td>[8]单汨源，龙颖．一种关键链缓冲机制改进方法及其应用</td></tr><tr><td>究[].项目管理技术，2006，4（3）：32-35</td></tr><tr><td></td></tr><tr><td>[9]褚春超：缓冲估计与关键链项目管理[]：计算机集成</td></tr><tr><td>造系统，2008，14（5）：1029-1035.</td></tr><tr><td>[10]周阳，丰景春，基于排队论的关键链缓冲区研究[]</td></tr><tr><td>技进步与对策，2008，25（2)：174-176.</td></tr><tr><td>[11]贾静，关键链项目管理方法中设置缓冲的新思路[]．项</td></tr><tr><td>目管理技术，2011，9（3：30-3</td></tr><tr><td>[12] GOLDRATT E M. Critical chain[M]. Great Barington:T</td></tr><tr><td>North River Press，1997</td></tr><tr><td>[13]谭跃进：公共管理与项目管理[M]，长沙：国防科技</td></tr><tr><td>学出版社，2006.</td></tr><tr><td>[14]Project Management Institute.项目管理知识体系指南（PM</td></tr><tr><td>BOI@指南[M].6版.北京：电子工业出版社，2018</td></tr><tr><td>[15]卢向南，项目计划与控制 [M] ，北京：机械工业出</td></tr><tr><td>社，2006.</td></tr><tr><td>[16]陈赞，张营慧，朱文喜．基于不确定性因素的柔性关键</td></tr><tr><td>缓冲设置方法，长沙理工大学学报（自然科学版</td></tr></table>

![](images/f44a45526750f98dc4ff37bc09255e07fc1b4dc52dd6a846e8eb2b7ca0e963d2.jpg)