# 项目计划与控制研究综述

$\circledcirc$ 方月谢跃文

【摘要】项目计划与控制的理论与方法常常是针对项目的进度与费用进行的，可以理解为项目的进度管理及费用管理。本文在梳理项目进度管理发展历程及已有研究现状、项目进度-费用协调控制研究现状的基础上，指出项目进度控制及费用控制在理论研究及实际应用上的局限性，并指出未来项目计划与控制的发展趋势。

【关键词】进度费用计划控制集中在进度管理、进度-费用协调控制两个方面，但进度管理的研究也常伴随着费用管理，二者是紧密相关的。

![](images/9842b5d036a99a8e94de40e7ce3021606c91ac0cfcb5d26b68eed0a029c8c553.jpg)

# 2项目进度管理发展过程及研究现状

国外对于项目进度管理的技术应用较为成熟。二战前，著名美国科学家亨利·甘特开发设计了甘特图，当时主要用于控制舰船制造开发进程，并用以表示关键事件和节点。通过该种方式，舰船从建造到成型所用的时间大大缩短，建造效率得到了空前的提高。由于甘特图结构简单且便于操作，因此被迅速而广泛的运用于世界各大行业的项目管理计划和控制中，时至今日，甘特图仍广泛流行于各大公司的管理项目群，但其不能表示活动相关性以及不适于作假设分析或建立项目计划模型的缺点，使其并不能完全满足项目开发者对项目计划与控制的要求。

# 1引言

项目管理理论的发展和其在实践生活中的运用现如今非常广泛，并且逐渐成为世界各个行业所关注的焦点问题，已经影响到社会生活的方方面面。项目是通过有效合理的利用自身优势和资源，进而实现某个确定的任务目标。进度、费用、质量是项目管理的核心，项目管理的其他工作，如风险管理、安全与环境管理、沟通管理、采购与合同管理等，虽然也非常重要，但不是最终目的，而是作为辅助手段为项目的进度、费用、质量目标的实现服务。在这些项目管理工作的辅助之下，项目质量目标以严格遵守国家的各项施工质量规范来实现，而项目的进度与费用目标则需要以项目管理者合理有效的计划与控制来实现。

项目计划与控制在项目管理中具有决定性的作用，主要体现在进度与费用两方面，即项目进度管理与费用（或成本、投资）管理两方面。现有研究主要

20世纪60年代，关键路线法(CPM法）与计划评审技术(PERT法)先后被开发出来。参与杜邦公司精炼厂全面检修项目时，杜邦公司和兰德公司员工总结并开发出了关键路线法（CPM法)。在解决具体进度控制技术问题时，他们发现如果进行流程精细化检修工作，检修不同线路上的时间之和是不同的，但如果将检修项目按照一定的顺序进行排列，减少最长线路上的工作时间，就可以减少整个项目检修的总时间，最后，他们把这种方法命名为关键路线法，用箭线图描述项目任务间的先后逻辑顺序。在CPM法的基础上，计划评审技术(PERT法）采取加权平均的思路编制进度计划。其基本思路是给每一个任务估计其悲观时间、乐观时间和可能工期。PERT法设计之初被用于项目时间限制方面，后扩展到成本控制。随后，为满足各种需要，不同形式的网络技术相继出现。例如决策网络技术，该技术将决策点和决策变量引入CPM网络计划主体内容之中。这些技术进一步扩大了网络计划的应用范围。

20世纪70年代，风险评审技术（VERT）将概率论引入到网络计划中，进一步为项目计划、控制和决策提供依据。通过对项目时间、成本、性能等方面定量分析，估计网络计划的风险问题，从而为项目管理者提供决策参考。

20 世纪90年代，一种新的项目计划与控制的方法--关键链项目管理（CCPM）由E.M.戈德拉特博士提出。不同于以往方法，CCPM将约束理论和聚集理论引入到项目计划的前期讨论和制定过程中，主要目的是利用项目缓冲区、运送缓冲区和资源缓冲区的管理来减小甚至消灭会出现项目延误的机会。它不但要将工作执行时间进行约束，而且还要将进行工作过程中资源的冲突考虑在内。此法大大的降低了项目进度受随机性因素的影响，在项目进度和成本管理方面有一定的突破。[在缓冲区的确定方面，目前学术届有两种不同的理论：一是基于概率论，如Taylor等（1999）[2]提出了用蒙特卡洛模拟技术确定项目缓冲区的大小。Shou等（2000）[3]将工程工作里面各个枝节的具体特点和项目管理人员面对风险的态度划分区间，最终确定缓冲区的范围和大小。二是基于模糊理论，如Chen等（2004）[4]主要通过模糊技术的手段，进行缓冲区大小的划分，尽管模糊技术手段有其技术优势，但如果实际工程比较复杂，则通过该种手段对资源约束进行求解显然行不通。

国内对于项目进度管理的技术应用研究也逐渐起步。20世纪50年代，我国就已经在建筑和水利等行业使用甘特图。但由于我国网络技术相对落后，关于网络计划技术的研究相对迟缓，最早是由华罗庚教授引入，并将PERT和CPM等方法统称为“统筹法”。之后随着技术的不断发展，网络计划技术才开始在我国各行各业盛行，尤其在建筑业中得广泛的应用和发展。《工程网络计划规程》（JGT/T1001-91）于1992年颁布。该规程的颁布为工程网络计划技术的编制和进度管控提供了统一的技术标准和指南。现如今，网络计划技术虽然已经在我国的某些重点单位和企业得到一定的应用，但由于某些特殊原因（比如传统习惯），在工程实际管理过程中，许多企业仍然使用较为简单的甘特图等方法，这就直接影响了生产效率的提高，同时影响网络计划技术在我国的应用与发展[5]。目前，我国的学者也从其他角度展开了对项目进度计划优化管理的研究，主要包括：王波清建立的控制系统模型，马国丰创造性的从约束理论的角度对项目进度延迟的原因以及造成的结果进行了系统的分析，汪强以信息技术为工具对项目进度进行分析研究，以上研究对工程进度管理都具有很好的指导意义。

# 3项目进度-费用协调控制研究现状

国外较早地对费用-进度协调控制进行研究，但其对两者协调问题的研究较为缺乏，尽管如此，对于已有的关于费用-进度的理论研究也非常有意义。Lichtenberg 和 Sundy（1995）[假设项目活动连续时间与项目所需费用是线性关系，并认为项目活动连续时间与项目所需的资源并无关系，二人在此假设下，对费用-进度相交换的现金流优化问题的基础上，设计了一种依次检查各个时间段的方法，对项目进行中没有利用资源的时间段，试图缩短活动持续时间，同时根据项目资源的使用量和净现值的变化做出决策。S.S.Erenguc 等（1993）[7]认为对总价承包项目，最适宜在投标阶段利用风险评估法进行综合评价，得出最佳的投标价。

1953年，美国空军首次提出了在计划审批技术中带入成本管理的理论，并在导弹研发的计划中首次使用了挣值管理。1967年，美国国防部也用了挣值管理理论。2000年4月，《挣值管理系统成熟度模型》得以出版，其代表着挣值管理从军用项目逐渐向民用项目的转变。

在国内，关于工程费用-进度协调控制的研究，只有部分期刊和文献中才有少量的涉及，并且仅简单介绍费用-进度问题的基本概念、种类等，很少有文献把费用-进度协调控制的管理方法与实际的工程施工过程相结合。戚安邦（2002）[8]对网络计划的费用-进度协调控制问题的概念、种类等基本情况进行了详细的描述，并进一步深入的分析协调控制的问题和解决方法，为下一步在实际工程领域的研究奠定了坚实的基础。刘景泉和刘伟（2002）[9]为了能够有效的压缩工程施工工期，提出增加资源分配来实现的方案，他提出在有限资源的情况下，采用特殊编码的方式为模型设计启发式遗传算法，建立在资源有限的环境里，费用-进度联系模型，并进一步通过实例验证说明了该种模型在我国的有效性和合理性。

挣值管理法在进入我国初期就得到了国内学者的高度重视，他们不但对该法的原理做了详细而细致的分析总结，同时为该理论的继续完善和充实做了大量工作，例如郭海峰提出第二变量和新指标体系的方法来解决原有挣值管理法中关于其无法反映市场价格变化情况和施工水平变化情况对资源单价变化的影响程度的缺陷。戚安邦对挣值分析方法中出现的错误进行了细致的总结和梳理，并给出了各个缺陷错误的具体解决方案，为挣值分析法的发展做出贡献。丰景春（2003）[] 详细研究了项目计价算法、合计计价法及暂定费用项目的预算计划值计算公式等，并由此发现其中的问题和不足，进而设计出工程项目进度-费用绩效模型。李扬红等（2001）[通过对挣值管理中的指标间的相互关系研究，通过实例分析提出了挣值管理在工程监理中的作用。

# 3项目计划与控制的局限性及发展趋势

3.1我国工程项目费用管理和进度管理的局限性

（1）我国工程项目费用管理的局限性

1）工程项目管理人员记录细则片面。工程管理人员在进行费用数据的收集过程时，并没有直接参与到数据收集的各个环节，而是通过其他渠道获取数据，也没有准确的记录每个时间节点上工程的完工量，而是将观察记录的重点放在了质量安全隐患等方面，因此记录过于片面。

2）费用控制数据的收集周期长。费用控制数据的收集间隔很长，平均在一个月或一季度。由于较长的时间间隔，必然会导致误差的积累，这种误差会影响管理者对费用变动原因的理解。

3）费用控制数据没有针对性。费用控制数据反应的是一个整体的概念，它仅仅表示各个项目中费用开支的总和，无法针对性的指明各个费用指标的合理性。

4）工程项目管理缺少集成管理手段。工程费用管理过程是一个系统的过程，这种管理活动需要精细化管理，因此借助先进管理工具和技术就显得必不可少。而我国在进行工程费用管理的过程中很少使用先进的计算机技术进行费用管理过程，由此导致我国在该方面管理水平较低。

（2）我国工程项目进度管理的局限性

1）对项目工程计划实施过程的检查力度过低。由于外部环境的作用，使得项目计划经常因为一些外部突发事件所影响，从而影响项目总进度，使得原计划无法按时完成。在执行计划的过程中，项目管理者必须时时关注工程进度，掌握工程现状，通过资源配置和适当合理的计划修正，以达到尽快实现计划的目的。

2）没有广泛运用网络计划的新技术。网络计划现如今并没有得到广泛的应用，这主要是因为企业大多都重视技术而轻视管理的缘故。

3）很少将进度与费用水平统筹综合考虑。控制进度与控制费用水平同等重要。如果将两者分开独立考虑，则会在一定程度上影响投资收益。

4）预测技术落后。在项目进行过程中，由于许多不确定因素的影响，会导致项目实际实施过程与原有计划产生重大偏差。但由于我国关于项目预测技术还暂时比较落后，进而影响项目管理者对工程计划的及时调整。

5）网络计划技术与计算机技术缺乏合作。计算机技术可以有效的将工程项目进度控制与费用控制结合起来，实现两者的有效平衡。而将计算机技术与网络计划技术结合起来，可以方便快捷的实现对工程项目的动态管理。

# 3.2项目计划与控制的发展趋势

项目管理理论中的项目计划与控制理论发展至今，已经形成了较为成熟的方法体系，这些方法在实际项目管理工作中发挥了重要的作用，随着项目管理运用到更多的社会生活领域，也对社会的发展进步做出了贡献，但还有较大的发展空间，在未来的研究和运用中可以从以下几个方面深入探讨：

（1）项目进度与费用目标的联合控制或项目目标的集成控制。可以看到项目管理的理论和方法大多是以项目的进度控制为目标，较少结合项目管理中的其他要求。但在操作项目的过程中，对项目进行管理的问题实质上就是进行一种多目标优化的过程，例如费用最小的同时，要求资源消耗水平低，产生的价值最大等，这些问题还需要进一步的研究和探索。

（2）关键链法作为一种新兴的项目管理方法，目前是学者们研究的焦点，已经得到许多有益的研究成果，但还存在许多不成熟的地方，同时在运用关键链进行项目管理时怎样有效解决多资源冲突问题，怎样在多项目管理中应用关键链法等，这些问题都需要深入研究。

（3）项目计划与控制软件的发展与应用。目前项目管理软件的研发已经取得一定的成果，但也存在许多不足之处，项目管理软件并没有在实际工作中得到普遍的推广应用。进一步完善相关软件的功能以推广使用、研发关键链技术的应用软件等是值得关注的问题。

# 【参考文献】

[1]鲍学英,赵延龙.关键链技术在项目进度管理中的应用研究[J].兰州交通大学学报,2009,28(1):34-36.

[2]Taylor S G.Quatifying buffers for project scheduling[J].Production inventation management journal,1999,40(2):43-47.

[3]Shou Y Y,Yao K T.Estimation of project buffers in critical chain project management[C].Proceedings of the IEEE international conference on management of innovation and technology,2000,1(1):162-167.

[4]Chen M K,Hsu S P.Fuzzy-critical-chain-based project management[J].Journal of Chinese institute of industrial engineers,2004,21(2):167-176.

[5]刘耕,王学军.国内外项目进度管理的比较与建议[J].重庆交通大学学报,2003(22):95-96.

[6]L Sundy,S Lichtenberg.Net-present Value Cost/Time Trade Off [J].International journal of project management,1995,13.

[7] S.S.Erenguc,S Tufekci,C J Zappe.Solving Time/Cost Trade-off problems with diseounted cash flows using generalized benders decom position[J].Naval Research Logisties,1993,40.

[8]戚安邦.多要素项目集成管理方法研究[J].南开管理评论,2002,06.

[9]刘伟,刘景全.资源约束下的时间费用交换问题研究[J].系统工程理论与实践,2002.09.

[10]丰景春.合同项目费用／进度绩效模型研究[J].河海大学学报,2003,01.

[11]李扬红,闰修家.工程监理中进度和费用的综合控制[J].人民珠江,2001,01.