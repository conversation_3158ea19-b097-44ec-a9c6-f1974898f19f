#!/bin/bash

echo "开始处理PDF文件..."
echo

# 设置IFS以正确处理包含空格的文件名
IFS=$'\n'

# 使用find命令递归查找所有PDF文件
find . -name "*.pdf" -type f | while read -r pdf_file; do
    # 获取PDF文件的目录路径和文件名（不含扩展名）
    pdf_dir=$(dirname "$pdf_file")
    pdf_name=$(basename "$pdf_file" .pdf)
    
    # 构建输出目录路径（与PDF文件同名的目录）
    output_dir="$pdf_dir/$pdf_name"
    
    # 检查是否已存在同名目录
    if [ -d "$output_dir" ]; then
        echo "[跳过] $pdf_name - 目录已存在，可能已处理过"
    else
        echo "[处理] 正在处理: $pdf_name"
        
        # 创建输出目录
        mkdir -p "$output_dir"
        
        # 执行mineru命令
        if mineru -p "$pdf_file" -o "$output_dir" -d cuda; then
            echo "[成功] $pdf_name 处理完成"
        else
            echo "[错误] $pdf_name 处理失败"
            # 如果处理失败，删除创建的空目录
            rmdir "$output_dir" 2>/dev/null
        fi
        echo
    fi
done

echo
echo "所有PDF文件处理完成！"
