# 基于关键链的档案信息系统项目进度管理研究

朱梦玲

（云南财经大学，云南昆明 650221)

摘要：档案信息系统（Archives Information System，AMIS）的发展不仅展现了技术的革新，也对信息管理提出更高的要求。在数字化时代，档案信息系统正在变得更加智能化、便捷化和安全化，可为各类组织和行业提供更加丰富的功能选择。作为一个软件开发项目，成功实施档案信息系统管理的关键在于制订可靠的进度计划和合理的工期安排。通过分析项目经理的风险偏好、资源紧张度、网络复杂度和任务弹性等因素，计算项目缓冲和输入缓冲，并借助Crystal Ball工具对工期分布进行模拟验证，证明关键链缓冲技术在档案信息系统项目进度管理中的有效性。研究可为档案系统项目的顺利完成提供有力的参考依据。

关键词：关键链；缓冲区；进度管理；档案信息系统；Crystal Ball

# 0引言

随着数字化进程的加速，档案管理从传统纸质方式向数字化方式转型已成趋势。这种转变涵盖了档案的创建、存储、索引和检索等全过程。档案信息系统是为档案的组织、存储、管理和检索提供服务的专业信息系统。当前，以数字化档案资源为核心的档案工作日益普及，其快速增长的数据量和日益复杂的管理需求，使得档案数字化转型成为提高管理效率和信息可用性的必要途径[1]。在开发档案信息系统的过程中，制订和控制项目进度计划是确保开发工作顺利完成的关键环节之一。工期估算作为项目进度计划的重要组成部分，对整个进度管理的准确性有着直接影响。

关键链项目管理是约束理论在项目管理中的应用，主要通过合理估算任务工期及设置缓冲区，在确保项目按时完成的前提下，进一步优化工期。缓冲区大小的科学估算一直是关键链研究的重点领域。然而，针对档案管理行业信息系统项目的缓冲区设置研究较少。档案管理软件的特点要求在缓冲区计算时充分考虑其特性，以提高估算的准确性。在现有的根方差（RSEM）计算框架基础上，通过引入反映特定因素的修正系数，可以更好地适应档案管理软件项目的需求[2-4]。

关键链技术的创新之处在于对资源约束的平衡。通过统一管理安全时间，减少对项目进度的干扰；考虑工序间的逻辑关系，优化资源分配,集中解决项目进度瓶颈问题，降低流程不畅造成的时间浪费；利用缓冲区动态监控项目进展，通过合理调配资源，减少意外因素对进度的影响，确保按时完成项目。本文结合项目经理的风险偏好、资源紧张度、网络复杂度和任务弹性等影响因素，对缓冲区进行计算和设置，并通过CrystalBall工具进行工期分布仿真，验证关键链缓冲区技术在档案信息系统项目进度管理中的可行性和有效性[5]。 。

# 1缓冲区的影响因素

缓冲区设置的核心是通过合理设计缓冲，降低项目中的不确定性，从而确保项目能够在满足既定要求的基础上，按时或提前完成。项目的不确定性可以划分为外部不确定性和内部不确定性两类，其中，外部不确定性包括国家或地方政策等宏观因素，这类不确定性发生概率较低，对项目影响较小，因此通常不予考虑。内部不确定性是项目进度管理的主要挑战，涉及项目经理、资源及项目本身的复杂性等多个方面。

项目经理的不确定性主要与其管理风格、决策偏好及风险应对能力相关。例如，风险偏好高的项目经理可能设置较小的缓冲，而风险规避型项目经理可能倾向于增加缓冲时间。资源的不确定性则体现为资源配置是否充足，以及需要时是否能够及时到位，这会直接影响项目各环节的顺利开展。此外，项目的复杂性由任务数量及其逻辑关系决定，较高的任务复杂性可能会增加工期的不确定性，需要通过缓冲区进行有效管理。

为科学评估和设置缓冲区，本文从项目经理风险偏好水平、资源紧张度、任务复杂度和任务弹性4个方面进行衡量。项目经理风险偏好水平决定了缓冲的松紧度；资源紧张度反映了资源在多任务环境中的供需关系；任务复杂度体现了活动之间的依赖性和链路复杂性；任务弹性反映了活动的时间裕度，即活动对不确定性的应对灵活性。这些因素的综合分析为缓冲区的合理设置提供了量化依据，最终得出了更加精确的工期估算。

# 1.1项目经理风险偏好水平

理想情况下，当项目中活动较多时，项目工期呈正态分布。设项目经理的风险偏好水平为 $\partial$ ，在 $1 ~ \sim ~ \partial$ 的保证率下，对应的标准差倍数为 $f _ { r g }$ ，则由此可以计算项目经理风险偏好调整系数 $\beta =$ $f _ { r g } / 2$ 。当 $\partial = 5 \%$ 时，即项目经理在 $9 5 \%$ 的保证率下设置缓冲，即有 $5 \%$ 的可能性缓冲超出控制[6]。

# 1.2任务复杂度

项目本身的复杂度通过任务复杂度表示，任务复杂度8表示活动的紧前活动与该活动所经链路上的总活动数的最大值的比值[7]； $N _ { p }$ 表示该活动的紧前活动数； $N$ 表示该活动所经链路上的总活动数的最大值，则任务复杂度 $\delta = N _ { p } / N + 1 / N$ 。

# 1.3资源紧张度

资源是否足够及能否及时到位，通常由资源紧张度来衡量，资源紧张度可以以单一资源和多资源紧张度等情形进行衡量。本文采用多资源紧

张度来衡量。在多资源的条件下，可以利用最乐观值法，将任务进行差分考虑，提出某活动利用多资源条件下资源紧张度的计算方法为

$$
\gamma = \frac { 1 } { n } \sum _ { j = 1 } ^ { n } \operatorname* { m a x } \{ \frac { R ( j ) } { R _ { m } ( j ) } \}
$$

式中， $n$ 表示活动 $i$ 使用资源种类的数量; $R ( j )$ 表示活动 $i$ 所使用的某种资源j; $R _ { { \mathfrak { m } } } ( j )$ 表示资源 $j$ 的供应限量[6]。

# 1.4任务弹性

在三点估算中， $t _ { o } \setminus  &  t _ { m } \setminus t _ { p }$ 分别表示任务 $i$ 的乐观时间、最可能时间和悲观时间。在极端情况下，当 $t _ { m }$ 足够接近 $t _ { o }$ 时，说明任务 $i$ 基本不会在 $t _ { p }$ 时间完成，任务可提取的安全时间较为充裕。反之，说明任务没有太多安全时间可以提取。任务弹性系数计算公式如下

$$
\lambda _ { i } ~ = ~ 1 ~ - { \frac { t _ { m } ~ - ~ t _ { o } } { t _ { p } ~ - ~ t _ { m } } }
$$

式中， $\lambda _ { i }$ 表示任务弹性系数[7]。

# 1.5安全时间计算

根据根方差原理可知，通常将单个工序的安全时间裕量按其标准差的2倍计算[8]，则消除的安全时间裕量 $\Delta t = 2 \sigma$ 。

# 1.6缓冲区设置

缓冲区的计算公式如下

$$
P B = \sqrt { \sum _ { k = 1 } ^ { n } \ \left[ \left( 1 \ + \lambda _ { k } \right) \ \gamma _ { k } \beta _ { k } \delta _ { k } \Delta \ t _ { k } \right] ^ { 2 } }
$$

式中， $n$ 表示关键链或非关键链上的活动数; $\beta _ { k }$ 表示活动 $k$ 的项目经理风险偏好系数； $\delta _ { k }$ 表示活动 $k$ 的任务复杂度； $\Delta t _ { k }$ 表示活动 $k$ 所需消除的安全时间裕量; $\lambda _ { k }$ 表示任务 $k$ 的弹性系数[9]。

# 2档案信息系统开发项目工期计算

# 2.1项目WBS任务分解

在复杂项目启动前，通常需要绘制工作分解结构（WBS），对工作流程进行详细分解，将总体目标拆解为更小、更易管理的子项目和小目标。通过明确任务名称、编号及分配任务负责人和执行团队，WBS 图是一种资源分配约束的重要工具。档案信息系统的项目开发工作分解流程如下：

（1）需求分析与确定。需求分析团队在收到客户需求文档后，与客户约定首次会面时间，详细沟通需求内容。项目组成员在充分理解客户需求的基础上，运用专业知识设计软件项目的功能模块及界面原型，对功能模块中的每个细节进行具体设计。形成需求规格说明书后，与客户确认并交流，直到界面功能板块、界面设计及功能模块完全满足客户需求为止。随后，公司内部审核需求规格说明书，审核通过后，交由客户进行最终确认。

（2）产品设计。产品研发团队根据需求规格说明书，首次进行概要设计，包括软件功能划分、数据库设计及数据接口设计。在完成概要设计后，进一步细化功能模块设计，并通过详细的算法设计，为后续代码编写奠定基础。

（3）代码编写和测试。根据产品设计方案，分解功能板块并按模块编写代码程序，确保界面与功能按计划完成。代码开发完成后，软件产品需通过公司内部的严格测试，以保证其质量和功能的完整性。

（4）项目交付及验收。在完成整体设计后，公司项目团队和技术人员前往客户公司进行软件安装与调试，与客户方使用人员一起进行反复测试，确保系统无缺陷且能正常运行。随后，为客户方使用人员提供系统化培训，并移交《用户安装手册》《使用指南》等相关文档。在客户验收并签字确认后，正式宣布软件项目完成[10]。具体项目任务分解如图1所示：

# 2.2项目基本信息

对项目进度管理过程中的各项任务量进行具体的分解、排序和编号，并按照项目任务量做好资源分配和人员分配。在此过程中，要明确项目进度管理中各项任务的前后的逻辑顺序，形成具体的任务分解表。项目的主要任务见表1，项目资源的主要内容见表2。其中， $R _ { 1 }$ 为项目经理， $R _ { 2 }$ 为需求分析人员， $R _ { 3 }$ 为 UI设计人员， $R _ { 4 }$ 为测试人员， $R _ { 5 }$ 为方案设计人员， $R _ { 6 }$ 为开发人员。各活动所需资源数量表见表3。

表1项目的主要任务 (单位：d)  

<table><tr><td>项目任务</td><td>任务 编号</td><td>乐观 时间</td><td>最可能 时间</td><td>悲观 时间</td><td>紧前 任务</td></tr><tr><td>需求调研</td><td>A</td><td>5</td><td>7</td><td>10</td><td>二</td></tr><tr><td>编写需求规格说明</td><td>B</td><td>3</td><td>7</td><td>10</td><td>A</td></tr><tr><td>界面原型设计</td><td>C</td><td>5</td><td>7</td><td>10</td><td>B</td></tr><tr><td>需求确认</td><td>D</td><td>3</td><td>4</td><td>5</td><td>C</td></tr><tr><td>功能模块设计</td><td>E</td><td>6</td><td>7</td><td>8</td><td>D</td></tr></table>

![](images/ad13f5f908f43f5afcb345c8675286c60a9ddb4de9edbb93afb52f1be02ff32f.jpg)  
图1项目任务分解图

(续)

(续)

<table><tr><td>项目任务</td><td>任务 编号</td><td>乐观 时间</td><td>最可能 时间</td><td>悲观 时间</td><td>紧前 任务</td></tr><tr><td>数据库设计</td><td>F</td><td>8</td><td>10</td><td>12</td><td>D</td></tr><tr><td>外部交互接口设计</td><td>G</td><td>3</td><td>4</td><td>6</td><td>D</td></tr><tr><td>详细设计</td><td>H</td><td>12</td><td>16</td><td>20</td><td>EFG</td></tr><tr><td>代码编写及单元测试</td><td>I</td><td>40</td><td>47</td><td>55</td><td>H</td></tr><tr><td>档案挂接</td><td>J</td><td>15</td><td>21</td><td>27</td><td>H</td></tr><tr><td>功能确认</td><td>K</td><td>5</td><td>10</td><td>14</td><td>I</td></tr><tr><td>集成测试</td><td>L</td><td>7</td><td>10</td><td>15</td><td>K</td></tr><tr><td>用户培训</td><td>M</td><td>3</td><td>4</td><td>5</td><td>L</td></tr><tr><td>数据转换到新系统</td><td>N</td><td>3</td><td>7</td><td>10</td><td>L</td></tr><tr><td>验收</td><td>0</td><td>2</td><td>5</td><td>7</td><td>LKI</td></tr></table>

表2项目资源的主要内容(单位：人)  

<table><tr><td>资源名称</td><td>R</td><td>R</td><td>R</td><td>R4</td><td>R</td><td>R6</td></tr><tr><td>数量</td><td>1</td><td>2</td><td>2</td><td>2</td><td>3</td><td>4</td></tr></table>

表3各活动所需资源数量表（单位：人)  

<table><tr><td>资源名称</td><td>R</td><td>R</td><td>R</td><td>R4</td><td>R</td><td>R6</td></tr><tr><td>需求调研</td><td>0</td><td>1</td><td>0</td><td>0</td><td>0</td><td>0</td></tr><tr><td>编写需求规格说明</td><td>0</td><td>1</td><td>0</td><td>0</td><td>0</td><td>0</td></tr><tr><td>界面原型设计</td><td>0</td><td>0</td><td>1</td><td>0</td><td>0</td><td>0</td></tr><tr><td>需求确认</td><td>1</td><td>2</td><td>2</td><td>2</td><td>0</td><td>0</td></tr><tr><td>功能模块设计</td><td></td><td>0</td><td>0</td><td>0</td><td>1</td><td>0</td></tr><tr><td>数据库设计</td><td>0</td><td>0</td><td>0</td><td>0</td><td>1</td><td>0</td></tr><tr><td>外部交互接口设计</td><td>0</td><td>0</td><td>0</td><td>0</td><td>1</td><td>0</td></tr><tr><td>详细设计</td><td>0</td><td>0</td><td>0</td><td>0</td><td>3</td><td>0</td></tr><tr><td>代码编写及单元测试</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>4</td></tr><tr><td>档案挂接</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>1</td></tr></table>

<table><tr><td>资源名称</td><td>R</td><td>R</td><td>R</td><td>R4</td><td>R</td><td>R</td></tr><tr><td>功能确认</td><td>0</td><td>2</td><td>0</td><td>0</td><td>0</td><td>4</td></tr><tr><td>集成测试</td><td>0</td><td>0</td><td>0</td><td>2</td><td>0</td><td>4</td></tr><tr><td>用户培训</td><td>1</td><td>1</td><td>0</td><td>0</td><td>0</td><td>0</td></tr><tr><td>数据转换到新系统</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>1</td></tr><tr><td>验收</td><td>1</td><td>0</td><td>0</td><td>2</td><td>0</td><td>0</td></tr></table>

根据项目信息采用三点估算法计算出项目工期的期望值，项目持续时间估算见表4，项目活动网络图如图2所示，项目甘特图如图3所示。可得出关键链A—B—C—D-F—H—I—K-L—N—O,项目工期为130天。

表4项目持续时间估算(单位：d)  

<table><tr><td>项目任务</td><td>任务 编号</td><td>乐观 时间</td><td>最可能 时间</td><td>悲观 时间</td><td>期望值</td></tr><tr><td>需求调研</td><td>A</td><td>5</td><td>7</td><td>10</td><td>7</td></tr><tr><td>编写需求规格说明</td><td>B</td><td>3</td><td>7</td><td>10</td><td>7</td></tr><tr><td>界面原型设计</td><td>C</td><td>5</td><td>7</td><td>10</td><td>7</td></tr><tr><td>需求确认</td><td>D</td><td>3</td><td>4</td><td>5</td><td>4</td></tr><tr><td>功能模块设计</td><td>E</td><td>6</td><td>7</td><td>8</td><td>7</td></tr><tr><td>数据库设计</td><td>F</td><td>8</td><td>10</td><td>12</td><td>10</td></tr><tr><td>外部交互接口设计</td><td>G</td><td>3</td><td>4</td><td>6</td><td>4</td></tr><tr><td>详细设计</td><td>H</td><td>12</td><td>16</td><td>20</td><td>16</td></tr><tr><td>代码编写及单元测试</td><td>I</td><td>40</td><td>47</td><td>55</td><td>47</td></tr><tr><td>档案挂接</td><td>J</td><td>15</td><td>21</td><td>27</td><td>21</td></tr><tr><td>功能确认</td><td>K</td><td>5</td><td>10</td><td>14</td><td>10</td></tr><tr><td>集成测试</td><td>L</td><td>7</td><td>10</td><td>15</td><td>10</td></tr><tr><td>用户培训</td><td>M</td><td>3</td><td>4</td><td>5</td><td>4</td></tr><tr><td>数据转换到新系统</td><td>N</td><td>3</td><td>7</td><td>10</td><td>7</td></tr><tr><td>验收</td><td>0</td><td>2</td><td>5</td><td>7</td><td>5</td></tr></table>

![](images/c4e55f5a398cf53905eb7adcc56a4db2add9a82bfd775a59bf3bcfaca0406ba6.jpg)  
图2项目活动网络图

![](images/5244e4c1d0aba7f6d4a87cdf97d949a13c96c0ab075ac23339ecc148e0d3d37b.jpg)  
图3项目甘特图 (截图)

# 2.3项目缓冲计算

根据项目信息分别计算资源紧张度、任务复杂度、工序弹性、安全时间等信息。假设项目经理的风险偏好度为 $5 \%$ ，则需要在 $9 5 \%$ 的保证率下完成项目。通过查阅正态分布表可得，在 $9 5 \%$ 的保证率下所对应的标准差倍数 $f _ { r g } = 1 . 6 5$ ，则项目经理的风险偏好度调整系数为 $\beta _ { k } = f _ { r g } / 2 = 0 . 8 2 5$ 。缓冲区计算表见表5。汇入缓冲区后的活动网络图如图4所示。由表可以得出 $\mathrm { P B } = 4 . 3 2 \mathrm { d }$ ，非关键链A—B—C—D—E 的缓冲 FB1 为0.48d，A—B—C—D—E—H—J的缓冲 FB2 为1.90d，A—B—C—D—E—H—I—K—L—M的缓冲 FB3 为 $4 , 0 9 \mathrm { d }$ 。项目缓冲FB的插入可能会使非关键链的长度超过关键链，但因为它包含有缓冲，不能认为该非关键链变成了关键链。此时，可以看作是该非关键链的部分入口缓冲FB融入了最终的项目缓冲PB 中，由 PB 最终为项目整体的安全性提供保证。

表5缓冲区计算表  

<table><tr><td>任务编号</td><td>（Yi）</td><td>资源紧张度任务复杂度 任务弹性 安全时间 （8）</td><td>（𝜆:）</td><td>△t</td><td>缓冲区值</td></tr><tr><td>A</td><td>0.5</td><td>0.09</td><td>0.60</td><td>1. 67</td><td>0.01</td></tr><tr><td>B</td><td>0.5</td><td>0.18</td><td>0.43</td><td>2.33</td><td>0.05</td></tr><tr><td>C</td><td>0.5</td><td>0.27</td><td>0.60</td><td>1. 67</td><td>0.11</td></tr><tr><td>D</td><td>0.75</td><td>0.36</td><td>0.50</td><td>0.67</td><td>0.03</td></tr><tr><td>E</td><td>0.3</td><td>0.45</td><td>0.50</td><td>0.67</td><td>0.03</td></tr><tr><td>F</td><td>0.3</td><td>0.45</td><td>0.50</td><td>1.33</td><td>0.11</td></tr><tr><td>G</td><td>0.3</td><td>0.45</td><td>0.67</td><td>1. 00</td><td>0.11</td></tr><tr><td>H</td><td>1</td><td>0.55</td><td>0.50</td><td>2. 67</td><td>1.44</td></tr><tr><td>I</td><td>1</td><td>0.64</td><td>0.53</td><td>5.00</td><td>7.84</td></tr><tr><td>J</td><td>0.25</td><td>0.88</td><td>0.50</td><td>4.00</td><td>3.26</td></tr><tr><td>K</td><td>1</td><td>0.64</td><td>0.44</td><td>3.00</td><td>1.96</td></tr><tr><td>L</td><td>1</td><td>0.82</td><td>0.63</td><td>2. 67</td><td>5.06</td></tr><tr><td>M</td><td>0.75</td><td>0.82</td><td>0.50</td><td>0.67</td><td>0.16</td></tr><tr><td>N</td><td>0.25</td><td>0.91</td><td>0.43</td><td>2.33</td><td>0.88</td></tr><tr><td>0</td><td>1</td><td>1. 00</td><td>0.40</td><td>1. 67</td><td>1. 21</td></tr></table>

![](images/0002df278b99fdd89bbed06b88def0a3f2211a69e73563f7b7495143aa1fba31.jpg)  
图4汇入缓冲区后的活动网络图

# 2.4仿真模拟与分析

利用Crystal Ball对项目进行仿真。Crystal Ball简单实用，可以借助Excel直接进行仿真分析，CrystalBall实质上是利用蒙特卡洛法构造概率模型，进行重复抽样的基础上得到近似结果。利用PERT方法对该项目进行仿真，利用Excel建立仿真模型。由于设置了缓冲区，工期取乐观时间,采用正态分布，仿真次数为10000次，项目数据设置图如图5所示。模拟仿真概率分布图如图6所示。从结果可以看出，完工率为 $9 5 \%$ 时，项目工期为112d，相较于传统的计算方式工期130d，其工期缩短 $1 8 \mathrm { d }$ 。因此，本文根据项目特点设置的关键链和缓冲区的设置可以有效缩短工期[11]。

# 2.5敏感性分析

由敏感性分析图（图7）可知，项目能否顺利完成影响因素最大的任务活动为活动I，即代码编写预测试任务，影响因素达到 $4 4 . 3 \%$ ，其余活动影响相对较小。软件项目不同于其他类型项目的特性,软件项目受人为影响较大。档案信息系统开发的代码编写与测试阶段的风险最大，通常面临技术风险，关键开发人员离职风险及需求理解不清的风险。因此，在档案信息系统开发过程中，需着重关注代码编写与测试阶段的进度，不断监控、及时纠正，保证进度计划顺利进行和项目成功交付[12] 。

<table><tr><td>编号</td><td>工作任务名称</td><td>前置任务</td><td></td><td>最乐观时间</td><td>最可能时间</td><td>最悲观时间</td><td>三点估计值</td><td>假设值</td><td>标准差</td><td>最早开始时间</td><td>最早结束时间</td><td></td><td>最晚开始时间最晚结束时间</td><td></td></tr><tr><td>A</td><td>需求调研</td><td></td><td>5</td><td></td><td>7</td><td>10</td><td>7.17</td><td>5.00</td><td>0.83</td><td>0.00</td><td>5.00</td><td>2.04</td><td>7.04</td><td></td></tr><tr><td>B</td><td>编写需求规格说明</td><td>A</td><td>3</td><td></td><td>7</td><td>10</td><td>6.83</td><td>3.00</td><td>1.17</td><td>5.00</td><td>8.00</td><td>7.04</td><td></td><td>10.04</td></tr><tr><td>C</td><td>界面原型设计</td><td>B</td><td>5</td><td></td><td>7</td><td>10</td><td>7.17</td><td>5.00</td><td>0.83</td><td>8.00</td><td>13.00</td><td>10.04</td><td></td><td>15.04</td></tr><tr><td>D</td><td>需求确认</td><td>C</td><td>3</td><td></td><td>4</td><td>5</td><td>4.00</td><td>3.00</td><td>0.33</td><td>13.00</td><td>16.00</td><td>15.04</td><td></td><td>18.04</td></tr><tr><td>E</td><td>功能模块设计</td><td>D</td><td>6</td><td></td><td>7</td><td>8</td><td>7.00</td><td>6.00</td><td>0.33</td><td>16.00</td><td>22.00</td><td>18.04</td><td></td><td>24.04</td></tr><tr><td>FB1</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td>4.32</td><td>0.00</td><td>22.00</td><td>26.32</td><td>24.04</td><td></td><td>28.36</td></tr><tr><td>F</td><td>数据库设计</td><td>D</td><td>8</td><td></td><td>10</td><td>12</td><td>10.00</td><td>8.00</td><td>0.67</td><td>16.00</td><td>24.00</td><td>20.36</td><td></td><td>28.36</td></tr><tr><td>G</td><td>外部交互接口设计</td><td>D</td><td>3</td><td></td><td>4</td><td>6</td><td>4.17</td><td>3.00</td><td>0.50</td><td>16.00</td><td>19.00</td><td>21.36</td><td></td><td>24.36</td></tr><tr><td>H</td><td>详细设计</td><td>EFG</td><td>12</td><td></td><td>16</td><td>20</td><td>16.00</td><td>12.00</td><td>1.33</td><td>26.32</td><td>38.32</td><td>28.36</td><td></td><td>40.36</td></tr><tr><td>I</td><td>代码编写及单元测试</td><td>H</td><td>40</td><td></td><td>47</td><td>55</td><td>47.17</td><td>40.00</td><td>2.50</td><td>38.32</td><td>78.32</td><td>40.36</td><td></td><td>80.36</td></tr><tr><td>J</td><td>档案挂接</td><td>H</td><td>15</td><td></td><td>21</td><td>27</td><td>21.00</td><td>15.00</td><td>2.00</td><td>78.32</td><td>93.32</td><td>80.36</td><td></td><td>95.36</td></tr><tr><td>FB2</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td>2.19</td><td>0.00</td><td>93.32</td><td>95.51</td><td>95.36</td><td></td><td>97.55</td></tr><tr><td>K</td><td>功能确认</td><td>J</td><td>5</td><td></td><td>10</td><td>14</td><td>9.83</td><td>5.00</td><td>1.50</td><td>78.32</td><td>83.32</td><td>78.32</td><td></td><td>83.32</td></tr><tr><td>L</td><td>集成测试</td><td>K</td><td>7</td><td></td><td>10</td><td>15</td><td>10.33</td><td>7.00</td><td>1.33</td><td>83.32</td><td>90.32</td><td>83.32</td><td></td><td>90.32</td></tr><tr><td>M</td><td>用户培训</td><td>L</td><td>3</td><td></td><td>4</td><td>5</td><td>4.00</td><td>3.00</td><td>0.33</td><td>90.32</td><td>93.32</td><td>90.32</td><td></td><td>93.32</td></tr><tr><td>FB3</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td>4.23</td><td>0.00</td><td>93.32</td><td>97.55</td><td>93.32</td><td></td><td>97.55</td></tr><tr><td>N</td><td>数据转换到新系统</td><td>K</td><td>3</td><td></td><td>7</td><td>10</td><td>6.83</td><td>3.00</td><td>1.17</td><td>90.32</td><td>93.32</td><td>94.55</td><td></td><td>97.55</td></tr><tr><td>0</td><td>验收</td><td>LKI</td><td>2</td><td></td><td>5</td><td>7</td><td>4.83</td><td>2.00</td><td>0.83</td><td>97.55</td><td>99.55</td><td>97.55</td><td></td><td>99.55</td></tr><tr><td>PB</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td>4.32</td><td>0.00</td><td>99.55</td><td>103.87</td><td>99.55</td><td></td><td>103.87</td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr></table>

![](images/b7e70841b52a946d068cc3053163b6215b8c779892d3928cf9eceb77de00c0e3.jpg)  
图5项目数据设置图 (截图)  
图6模拟仿真概率分布图 (截图)

![](images/9abf214eb4740d015f7dc55889d9436b47c407873e74bd12a68f98bde5114b7b.jpg)  
图7敏感性分析图 (截图)

室业务，2014（17)：210.  
[2]YU J,XU Z,HU C. Bufer sizing approach in critical chain pro-ject management under multiresource constraints ［C]//20136th International Conference on Information Management,Innova-tion Management and Industrial Engineering. IEEE.2013.  
[3］TUKEL OI,ROM WO,EKSIOGLU SD. An investigation ofbuffer sizing techniques in critical chain scheduling [J]．Euro-pean Journal ofOperational Research，2006，172（2）：401-416.  
[4］李洪波，林俏，曹依武．考虑技能因素的软件项目关键链缓冲区大小计算方法［J]．工业工程与管理，2021，26（4)：195-201.  
[5］杭宇．基于关键链技术的项目进度管理模式研究［J]．世界石油工业，2023，30（1)：37-42.  
[6］郭恒栋，高琦，巩高铄．基于改进的资源紧张度缓冲区设置方法研究［J]．组合机床与自动化加工技术，2018(12)：142-144，149.  
[7］李辉山，高中钰．基于工序不确定性的缓冲区计算方法[J]．项目管理技术，2023，21（4)：105-111.  
[8］商艳娟．工程项目文档管理现状及展望［J]．石油化工建设，2021,43（5)：30-31，90.  
[9］彭军龙，刘泽鹏．基于关键链法的工程施工进度优化［J].长沙理工大学学报（自然科学版)，2020，17（4)：62-69.  
[10］赵中华．基于关键链技术的Y公司A软件开发项目进度管理研［D]．太原：中北大学，2023.  
[11］李帅芳，肖果平．Crystal Ball在项目管理风险分析中的应用［J]．项目管理技术，2013，11（4)：40-44.  
[12］张恒宇．基于Crystal Ball与CPM/PERT的某管片厂项目建设工期风险分析[J]．价值工程，2020，39（8)：54-56.  
[13］王莹．基于关键链的实景三维建模项目进度管理研究［D]．北京：北京建筑大学，2023.PMT

# 3结语

本文应用关键链技术进行档案信息系统项目的进度管理。首先，对档案信息系统项目进行WBS 任务分解；其次，对项目基本情况和存在的不确定因素进行分析，通过任务的时间参数识别出项目的关键链；再次，并将项目经理风险偏好水平、资源紧张度、网络复杂度和任务弹性作为缓冲区的影响因素，根据计算公式设置缓冲区；最后，通过蒙特卡洛模拟得出项目在 $9 5 \%$ 的完工率下的工期。对比传统的工期计算方法，新方法大大缩短了工期。该研究对档案信息系统项目的进度管理和工期预测具有参考意义[13]。

# 参考文献

收稿日期：2025-01-13

[1］郭军生．中小型档案管理信息系统通用需求分析［J]．办公

作者简介：朱梦玲（1997—），女，研究方向：档案信息化。