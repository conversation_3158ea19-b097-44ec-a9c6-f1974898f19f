import os
from datetime import datetime
import glob

def count_markdown_files(directory):
    # 获取所有.md文件
    md_files = glob.glob(os.path.join(directory, "**/*.md"), recursive=True)
    
    # 获取当前时间
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # 准备日志内容
    log_content = f"统计时间: {current_time}\n"
    log_content += "-" * 50 + "\n"
    log_content += "Markdown文件列表:\n"
    
    # 添加文件列表
    for file in md_files:
        log_content += f"- {os.path.relpath(file, directory)}\n"
    
    log_content += "-" * 50 + "\n"
    log_content += f"文件总数: {len(md_files)}\n"
    
    # 写入日志文件
    log_file = "markdown_count.log"
    with open(log_file, "a", encoding="utf-8") as f:
        f.write(log_content + "\n")
    
    print(f"统计完成! 共找到 {len(md_files)} 个Markdown文件")
    print(f"日志已写入: {log_file}")

if __name__ == "__main__":
    # 指定要扫描的目录
    target_dir = os.path.join(os.path.dirname(__file__), "文献")
    
    # 检查目录是否存在
    if not os.path.exists(target_dir):
        print(f"错误: 目录 '{target_dir}' 不存在!")
    else:
        count_markdown_files(target_dir)