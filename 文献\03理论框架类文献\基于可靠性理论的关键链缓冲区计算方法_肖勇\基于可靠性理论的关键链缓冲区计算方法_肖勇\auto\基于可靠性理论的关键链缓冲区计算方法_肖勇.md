# 基于可靠性理论的关键链缓冲区计算方法

肖勇管致乾（上海电力大学，上海200082）

摘要：为了设置有效的关键链缓冲区防止项目延期，首先，将资源的可用性定义为项目进度管理中的一个随机变量；其次，基于资源可靠性比例系数确定缓冲区，计算在每个独立资源的可靠性下及时完成连锁工序的概率；最后，提出一种基于项目资源可靠性的关键链缓冲区计算方法。通过算例的对比研究和分析可知，该方法能够有效降低项目进度计划中的工期风险，有效缩短项目计划工期。

关键词：资源可靠性；关键链项目管理（CCPM)；缓冲区；进度管理

# 0引言

# 1理论基础

在项目实施过程中，工期延误问题屡见不鲜，其主要原因在于对关键资源的管理缺乏足够的重视[1-]。关键链项目管理（Critical Chain Project Man-agement，CCPM）方法能够有效降低项目工期的不确定性影响，并缩短项目计划工期[3-4]。与传统的项目进度管理方法相比，CCPM具有三个特点[5-6]：$\textcircled{1}$ 通过考虑人的行为学假设消除非必要的安全时间，保证安全时间不会被滥用[7]； $\textcircled{2}$ 强调资源的可用性，能够在两个工序之间发生资源冲突时合理分配资源[8]； $\textcircled{3}$ 通过在项目流程中插入应急缓冲区，降低项目完工的超时风险[9]。

不同的缓冲区（项目缓冲区、驳接缓冲区和资源缓冲区）在关键控制点具有不同的作用。项目缓冲区位于项目网络末端，用来保证项目完成时间免受单个任务工期变化的影响。驳接缓冲区位于交点上，用来保证非关键工序即使发生变化也不会影响关键链任务。非关键工序和关键工序之间的资源缓冲区[10]用来保护关键链免受资源不可用的影响。这些缓冲区作为警告信号，能够确保及时完成关键链上的工序所需的资源是可用的[11]。

当前，经典的缓冲区计算方法是剪切粘贴法（CutandPasteMethod，C&PM）。该方法将预期活动工期的 $50 \%$ 作为安全时间，再将安全时间之和的 $50 \%$ 作为缓冲区，具有操作简单的优势。然而,非关键链会线性地影响缓冲区大小，导致在较长的非关键链中缓冲区相对较长且不实用。Newbold进一步完善了C&PM方法，提出了根平方误差法（RSEM)[12]。虽然该方法考虑了任务持续时间的已知变化，但可能导致工序多的关键链缓冲区不足[13]。在上述理论的基础上，Tukel和 Eksioglu 开发了两种方法：带密度的适应性程序（APD）和带资源紧密度的适应性程序（APRT)。前者根据进入关键链的子网络结构估算缓冲区大小，后者考虑了通往缓冲区的链上工序所需资源的稀缺性[14]。

在上述方法中，缓冲区的大小是由大于1的系数缩放的缓冲区内的路径合并的标准偏差确定的[15]。De等[16]引入熵理论衡量调度网络的复杂性，以此确定CCPM时间缓冲区大小。Zhang等[17]通过整合资源紧密度和网络复杂性模型，提出估算缓冲区大小的单一框架。王肖文等[18]认为，在实际操作中并非所有项目都存在帕金森定律中所描述的情况，不能对每个工序的安全时间进行平均化，应对每个工序的不确定性影响因素进行分析，通过加入不同属性因子，使缓冲区的设置更加科学合理[20]。同样，张静文等[19]结合时差定义和缓冲区设置原则，在设置项目缓冲区时考虑了不确定性因素对各个工序影响的不同，引入关键工序工期的影响因子。刘士新等[21]认为，在设置缓冲区时，应依据在指定资源分配方式下每个工作的自由时间分别设置缓冲区。周阳等[22]提出，在单资源约束下缓冲区的大小可采用排队论确定，通过消除整个项目实施过程中的不确定性因素，使工期和成本得以平衡。王晶[23]认为，关键链汇入缓冲区的取值具有区间性，采用自由时差法和总时差法确定汇人缓冲区的大小更符合缓冲区设置的本质；采用影响因子法确定项目缓冲区的大小更加符合实际。

虽然上述研究将不同的项目属性纳入缓冲区大小的考量，但没有考虑项目进度中资源获取的随机效应。基于此，本文提出一种嵌入可靠性理论的缓冲区设置方法，以解决项目进度管理中资源可用的随机性问题。

# 2缓冲区设置方法

# 2.1基于资源的可靠性

本文在计算缓冲区大小时，进行以下假设：

（1）资源在有限的供应条件下，项目进度受到资源制约。（2）分配给工序的资源可靠性值在工序执行期间保持不变。（3）同一类型的所有资源具有相同的可靠性值。（4）资源在统计上是独立的，即某一特定资源的可用性并不影响其他资源的可用性。

将工序 $i$ 的资源结构功能定义为一个模型，分配给工序 $i$ 的每种资源的状态用随机变量 $X _ { i j }$ 表示。如果资源可用，则 $X _ { i j } ~ = ~ 1$ ；否则, $X _ { i j } ~ = ~ 0$ 。根据以下二元函数公式评估每种工序在所需资源方面的状态（资源结构功能），即

$\theta ~ ( { \cal X } _ { i 1 } , { \cal X } _ { i 2 } , \cdots , { \cal X } _ { i N } ) ~ =$ (1，如果所有所需的资源都是可用的l0，其他

考虑到每个工序的资源结构函数，计算工序 $i$ 的基于资源的可靠性 $R _ { i \mathrm { ~ \scriptsize ~ o ~ } } R _ { i }$ 被定义为完成工序 $i$ 所需资源的可用概率。事实上，它是资源结构函数等于1的概率。公式如下

$$
R _ { i } = \mathrm { P r } \left[ \theta \left( X _ { i 1 } , X _ { i 2 } , \cdots , X _ { i N } \right) \right. \left. = 1 \right]
$$

为了完成工序 $i$ ，必须在整个工序期间拥有所有的资源。即当且仅当工序 $i$ 的所需的 $n$ 个资源在其执行期间都可用时，工序 $i$ 才能完成。资源结构函数如下

$\begin{array} { r l } { \theta \mathrm { ( } X _ { i 1 } , \mathrm { ) } \mathrm { , } \mathrm { ~ } X _ { i 2 } \mathrm { , ~ } \cdots , \mathrm { ~ } X _ { i N } \mathrm { ) } } & { { } = X _ { i 1 } \times X _ { i 2 } \times \cdots \times X _ { i N } = } \end{array}$ $\prod _ { j = 1 } ^ { N } X _ { i j }$ (3）

若每个 $X _ { i j } ~ = ~ 1$ ，则 $\begin{array} { c c c } { \theta } & { ( X _ { i 1 } , \ X _ { i 2 } , \ X \dots , \ X _ { i N } ) } & { = 1 _ { \mathfrak { c } } } \end{array}$ 或者说，任何一个资源的不可用性都会导致工序延时。因此, $R _ { i }$ 可以用资源可靠性的乘积表示，公式如下

$$
R _ { i } = r _ { i 1 } \times r _ { i 2 } \times \cdots \times r _ { i N } = \prod _ { j = 1 } ^ { N } r _ { i j }
$$

式中， $R _ { i }$ 是工序 $i$ 整体基于资源的可靠性; $r _ { i j }$ 是工序 $i$ 对分配给该工序的资源类型 $j$ 的可靠性。

此外，式（4）成立的条件是假设只有一个单位的资源 $j$ 是可用的，同时需要一个单位的这种资源来完成工序 $i$ 。然而，在实践中，对于某些工序而言，资源的可用量可能高于对该资源的需求量，因此, $n _ { i j } > k _ { i j }$ 。其中， $n _ { i j }$ 和 $k _ { i j }$ 分别是分配给工序 $i$ 的资源类型 $j$ 的可用单位和所需单位的数量。在这种情况下，如果 $n _ { i j }$ 个资源中至少有 $k _ { i j }$ 个可用于执行工序，那么工序 $i$ 就可以进行。或者，如果所需资源中的 $k _ { i j }$ 或更多的 $n _ { i j }$ 资源不可用，则工序 $i$ 将被延迟。假设所有 $j$ 类型的可用资源具有相同的可靠性 $r _ { i j }$ ，那么 $j$ 类型的 $n _ { i j }$ 个资源中的 $k _ { i j }$ 的可靠性$r ( k _ { i j } / n _ { i j } )$ 可以通过以下公式计算，即

$$
r ( k _ { i j } / n _ { i j } ) ~ = ~ \sum _ { k = k _ { i j } } ^ { n _ { i } } \left( { n _ { i } \atop { n _ { i j } } } \right) r _ { i j } ^ { k } ~ ( 1 ~ - { r _ { i j } } ) ^ { ( n _ { i j } - k ) }
$$

对于一个需要分配 $N$ 种不同类型资源的工序,式（5）可以扩展为

$$
R _ { i } \ = \ \prod _ { j = 1 } ^ { N } \left[ \sum _ { k = k _ { i j } } ^ { n _ { i j } } { \binom { n _ { i j } } { k _ { i j } } } r _ { i j } ^ { \ k } \left( 1 \ - \ r _ { i j } \right) ^ { ( n _ { i j } - k ) } \right]
$$

需要注意的是，当 $n _ { i j } = k _ { i j } = 1$ 时，式（6）可以简化为式（4）。式（6）中表示的分配给项目工序的多种资源的总体可靠性的概念可以通过逻辑图表示，即资源可靠性框图（ $\mathrm { R B D } ^ { \mathrm { [ R ] } }$ )，如图1所示。RBD是较为常用的可靠性图表， $\mathrm { R B D } ^ { \mathrm { [ R ] } }$ 对可靠性框图（RBD）进行了改进和完善。

RBD 显示了一个系统中各组成部分之间的逻辑联系，说明了系统的功能状态，给定了功能各组成部分的状态，能够直观地分析项目工序所需资源的总体可靠性，并考虑到每个单独资源的可靠性[25]。 $\mathrm { R B D } ^ { \mathrm { [ R ] } }$ 中的矩形表示一个工序所需的资源，线条表示资源之间的关系。 $\mathrm { R B D } ^ { \mathrm { [ R ] } }$ 直观地展示了如何将资源分配给一个工序，以便在其目标结束日期内完成该工序。

# 2.2基于资源可靠性的缓冲区估算

综上所述，本文将项目工序的资源可靠性进行量化，将数值设定为一个比例系数以调整时间缓冲区大小。换言之，缓冲区的大小是在一个关键链采用RSEM算法后所得到的数值上分配一个由各个工序可用资源的可靠性函数构成的比例系数确定的。

工序 $i$ 的资源不可靠性 $\overline { { R _ { i } } }$ 的计算公式如下

$$
\overline { { R _ { i } } } \ = \ 1 \ - \ R _ { i }
$$

对工序持续时间采用悲观和乐观估计。如果 $T$ 是工序 $i$ 的悲观工期，则与 $90 \%$ 的估计置信度有关；如果 $T$ 是工序 $i$ 的乐观工期，则与 $50 \%$ 的置信度有关。考虑到 $R _ { i }$ 的工序资源的不可靠性，假设工序完成时间为对数正态分布，将有 $m$ 个工序的驳接关键链的缓冲区大小定义为 $B S _ { R }$ ，公式如下

$$
B S _ { R } \ = \ \sqrt { \sum _ { i = 1 } ^ { m } \ \big [ \ : ( 1 \ : + \ : \overline { { { R _ { i } } } } ) \ : ( \ : T _ { i } ^ { 9 0 \% } \ : - \ : T _ { i } ^ { 5 0 \% } \ : ) \ : \big ] ^ { 2 } }
$$

通过用资源可靠性代替资源不可靠性，公式如下

$$
B S _ { R } \ = \ \sqrt { \sum _ { i = 1 } ^ { m } \ \left[ \left( 2 \ - { R _ { i } } \right) \left( T _ { i } ^ { 9 0 \% } \ - \ T _ { i } ^ { 5 0 \% } \right) \right] ^ { 2 } }
$$

由此可见，资源可靠性值是从一个常量中减去的，这意味着缓冲区的大小随着资源可靠性的降低而增加。显然，若 $R _ { i } \mathrm { ~  ~ \Gamma ~ } = 1$ ，则比例因子 $\left( 2 \mathrm { ~ - ~ } R _ { i } \mathrm { ~ \right. ~ }$ ）对 $B S _ { R }$ 没有影响，故式（9）等同于根平方误差法。

# 3实例应用

# 3.1案例概况

某项目关键链网络图如图2所示，包含10项工序和8项资源。矩形方框表示工序，箭头表示工序执行顺序。该网络包含一个关键链（ $\mathrm { A { \longrightarrow } E { \longrightarrow } }$ $\mathrm { H } { \longrightarrow } \mathrm { J }$ ）和两个非关键链。非关键链 $\mathrm { \bf B } \longrightarrow \mathrm { C } \longrightarrow \mathrm { D } \longrightarrow \mathrm { F }$ 与关键链上的工序H接驳，非关键链 $\mathrm { G } \mathrm { \longrightarrow } \mathrm { I }$ 通过工序J与关键链接驳。在工序J和项目到期日之间的项目网络末端增加一个项目缓冲区（PB)。为了确保非关键链 $\mathrm { \bf B } { \longrightarrow } \mathrm { C } { \longrightarrow } \mathrm { D } { \longrightarrow } \mathrm { F }$ 中的任何变化不会影响关键链上的工序H，在工序I和J之间增加缓冲区FB2。其中，非关键链 $\mathrm { G } { \longrightarrow } \mathrm { I }$ 和关键链 $\mathrm { A { \longrightarrow } E { \longrightarrow } H { \longrightarrow } J }$ 在J工序处进行接驳。

![](images/ad88556ffb46bfab6879ff629ad5030a3d16c375900534f88d3da710e400d3aa.jpg)  
图1资源可靠性框图（RBD[R])

![](images/07085f56427095b298f632b8391847a9ec36e111fc80d7d22209de4f15470f1a.jpg)  
图2某项目关键链网络图

# 3.2结果分析

对工序A和工序B的计算过程进行分析。首先，完成这项工序所需的资源被映射成RBD。工序A和工序B的RBD被表示为其所需资源（4、6和1、2）的系统，以及由该资源所有可用单元的平行组合构成的每个资源的功能块，如图3所示。

首先，计算工序A和工序B网络中每个工序的 $r _ { i j }$ 值。资源类型为4的可靠性是通过计算该资源的4个单位中至少有3个单位被分配给工序A的概率进行估计，公式如下

$r _ { _ { A 4 } } = C _ { 4 } ^ { 2 } ( 0 . 9 8 ) ^ { 2 } ( 0 . 0 2 ) ^ { 2 } + C _ { 4 } ^ { 3 } ( 0 . 9 8 ) ^ { 3 } ( 0 . 0 2 ) ^ { 1 } +$ $C _ { 4 } ^ { 4 } \left( 0 . 9 8 \right) ^ { 4 } \left( 0 . 0 2 \right) ^ { 0 } = 0 . 9 9 9 9$

同理，得到

$$
{ \begin{array} { r l } & { r _ { _ { A 6 } } \ = \ 0 . \ 9 0 2 \ 5 } \\ & { r _ { _ { B 1 } } \ = \ 0 . \ 9 9 5 \ 3 } \\ & { r _ { _ { B 2 } } \ = \ 0 . \ 9 9 6 \ 4 } \end{array} }
$$

![](images/8d57c0bb5ce1171f162bed1d59d6b8893a59232f2782d82b5aec6a640a09ccfd.jpg)  
图3工序A和工序B的RBD

其次，估计工序J的整体资源可靠性，即2、4、5、8类资源都可用的概率。计算公式如下

$R _ { \scriptscriptstyle A } \ = \ 0 . 9 9 9 \ 9 \times 0 . 9 0 2 \ 5 \ = 0 . 9 0 2 \ 4$ $R _ { B } \ = \ 0 . 9 9 5 \ 3 2 8 \times 0 . 9 9 6 \ 4 \ = \ 0 . 9 9 1 \ 7$

同理，计算其他项目工序的整体资源可靠性,结果见表1。

表1其他工序关键链相关数据  

<table><tr><td>工序</td><td>R</td><td></td><td>（T-T）[(2-R)(T-T）]²（）</td><td></td></tr><tr><td>A</td><td>0.902 4</td><td>2</td><td>4.818 0</td><td>4</td></tr><tr><td>B</td><td>0.9917</td><td>1</td><td>1.0167</td><td>1</td></tr><tr><td>C</td><td>0.949 5</td><td>2</td><td>4.414 2</td><td>4</td></tr><tr><td>D</td><td>0.997 5</td><td>1</td><td>1.005 0</td><td>1</td></tr><tr><td>E</td><td>0.8551</td><td>3</td><td>11. 7972</td><td>9</td></tr><tr><td>F</td><td>0.989 6</td><td>1</td><td>1. 020 9</td><td>1</td></tr><tr><td>G</td><td>0.8574</td><td>2</td><td>5. 222 1</td><td>4</td></tr><tr><td>H</td><td>0.9500</td><td>2</td><td>4.410 0</td><td>4</td></tr><tr><td>I</td><td>0.993 0</td><td>1</td><td>1.014 0</td><td>1</td></tr><tr><td>J</td><td>0.934 6</td><td>2</td><td>4.540 3</td><td>4</td></tr></table>

利用公式计算出对应链的 $B S _ { R }$ 值。关键链 $\mathrm { A } { \longrightarrow }$ $\mathrm { E \longrightarrow H \longrightarrow J }$ 的缓冲区计算公式如下

$$
B S _ { R } ( \mathrm { A \longrightarrow E \longrightarrow H \longrightarrow J } )
$$

$$
= \ { \sqrt { 4 . 8 1 8 ~ 0 ~ + 1 1 . 7 9 7 ~ 2 ~ + 4 . 4 1 0 ~ 0 ~ + 4 . 5 4 0 ~ 3 } }
$$

同理可得

$$
B S _ { R } ( { \mathrm { B } } \longrightarrow { \mathrm { C } } \longrightarrow { \mathrm { D } } \longrightarrow { \mathrm { F } } ) \ = 2 . 7 3
$$

$$
B S _ { R } ( \mathrm { G } \to \mathrm { I } ) \ = 2 . 5
$$

最后，分别计算出对应链的 $B S _ { \mathrm { R S E M } }$ 和 $B S _ { \mathrm { c \& P M } }$ 结果如下

$\begin{array} { r l } { B S _ { \mathrm { \tiny ~ R S E M } } } & { { } ( \mathrm { \tiny ~ A } \mathrm { \tiny ~ \longrightarrow ~ } \mathrm { \tiny ~ E } \mathrm { \tiny \longrightarrow ~ } \mathrm { \textnormal { H } } \mathrm { \tiny ~ \longrightarrow ~ } \mathrm { \textnormal { J } } ) } \\ { B S _ { \mathrm { \tiny ~ R S E M } } } & { { } ( \mathrm { \tiny ~ A } \mathrm { \tiny ~ \longrightarrow ~ } \mathrm { \tiny ~ E } \mathrm { \tiny \longrightarrow ~ } \mathrm { \textnormal { H } } \mathrm { \tiny ~ \longrightarrow ~ } \mathrm { \textnormal { J } } ) } \end{array}  = 4 . 5 8 3 \mathrm { \tiny ~ , ~ } \ B S _ { \mathrm { \tiny ~ R S E M } } ( \mathrm { \tiny ~ \textnormal { J } } )$ $\mathrm { B } \longrightarrow$ $\begin{array} { r l } { \mathrm { C \longrightarrow D \mathrm { - } F } ) } & { = 2 . 6 4 6 , B S _ { \mathrm { \tiny ~ R S E M } } ( \mathrm { G } \mathrm { \longrightarrow I } ) = 2 . 2 3 6 } \end{array}$

$\begin{array} { r l } { B S _ { \scriptscriptstyle \mathrm { C A P M } } \mathrm { ~  ~ { ~ ( ~ A \to E \to H \to J ~ ) ~ } ~ } } & { { } = 9 , \ B S _ { \scriptscriptstyle \mathrm { C A P M } } \mathrm { ~  ~ { ~ ( ~ B - \delta ~ ) ~ } ~ } } \end{array}$ $\begin{array} { r l r } { \mathrm { C \to D { \longrightarrow } F } ) } & { { } = 5 \ , } & { B S _ { \mathrm { c } \delta \mathrm { P M } } \mathrm { ( ~ G \to I ~ ) } = 3 } \end{array}$ 式中， $B S _ { \mathrm { R S E M } }$ 表示平方根误差法得到的缓冲区大小； $B S _ { \mathrm { c \& P M } }$ 表示剪切粘贴方法得到的缓冲区大小。

综上所述，采用C&PM方法计算得到的关键链 $\mathrm { A { \longrightarrow } E { \longrightarrow } H { \longrightarrow } J }$ 的缓冲区为9周，远远大于通过本文方法计算得到的缓冲区5.06周，误差比例为

$7 7 . 8 \%$ 。使用C&PM方法计算的非关键链 $\mathrm { G } { \longrightarrow } \mathrm { I }$ 的缓冲区大小与本文方法相比，缓冲区误差比例为$20 \%$ 。这是因为在C&PM方法中，缓冲区的大小与工序链的长度呈线性增长，时长较长的工序链产生了较大的缓冲区[26]。

本文提出的方法与RSEM方法相比会产生更大的缓冲区。这是因为资源可靠性在式（9）中的对应比例系数大于1。在非关键链 $\mathrm { \bf B } \longrightarrow \mathrm { C } \longrightarrow \mathrm { D } \longrightarrow \mathrm { F }$ 中，工序的 $R _ { i }$ 值很高 $( 0 . 9 4 9 \ 5 \sim 0 . 9 9 1 \ 7 )$ ，导致两种方法之间误差较小，为 $3 . 1 \%$ 。在关键链 $\mathrm { A } \longrightarrow$ $\mathrm { E \longrightarrow H \longrightarrow J }$ 中，工序E的 $R _ { E }$ 值相对较低，导致本文方法计算得到的缓冲区（5.06周）大于RSEM方法计算的结果（4.583周），误差为 $1 0 . 4 \%$ 。同样，非关键链 $\mathrm { G } \longrightarrow \mathrm { I }$ 上的工序G 的资源可靠性低,导致使用本文的计算方法的结果和RSEM方法计算的平均缓冲区大小误差较大，为 $1 1 . 8 \%$ 。

# 4结语

随着项目规模越来越大、项目内容越来越复杂，项目资源（物质资源和人力资源）对项目进度具有决定性影响。基于此，本文在研究项目工序资源可用性概率的基础上，结合可靠性理论改进了RSEM方法。该方法克服了C&PM和RSEM方法的不足，能够降低资源可靠性对项目进度的影响，更符合项目实际需求。通过案例验证可知,本文提出的项目关键链缓冲区计算方法与C&PM和RSEM方法相比，能够更好地解决基于C&PM方法缓冲区过大的问题。同时，弥补了项目属性的缺失，使缓冲区计算结果更加可靠，解决了基于RSEM方法缓冲区过小的问题。

# 参考文献

[1]LASLO Z．Project portfolio management：an integrated method for resource planning and scheduling to minimize planning/scheduling-dependent expenses ［J］.International Journal of Project Management，2010,28（6）：609-618.

[2]CHRISTODOULOU S，ELLINASG，ASLANI P．Entropy-based scheduling of resource-constrained construction projects[J]．Automation in Construction,2009,18（7）：919-928.  
[3]GOLDRATT E M.Critical chain［M].Great Barrington：TheNorth River Press，1997.  
[4］DEMEULEMEESTEREL，HERROELENWS．Project schedu-ling,a research handbook [M].New York：Springer US,2002.  
［5］胡晨，徐哲，于静．基于工期分布和多资源约束的关键链缓冲区大小计算方法［J］．系统管理学报，2015，24（2）：237-242.  
[6］尹健，代春泉．基于关键链技术的工程项目进度管理［J].建筑经济，2008（11)：60-62.  
[7]RAND G K．Critical chain：the theory of constraints applied toproject management ［J].International Journal of Project Man-agement,2000,18（3）：173-177.  
[8]LI XY，YUE GF，SUR，et al. Research on pisha-sandstone'santi-erodibility based on grey multi-level comprehensive evalua-tion method［j].Journal of Groundwater Science and Engineer-ing，2016，4（2)：103-109.  
[9]STEYN H．An investigation into the fundamentals of criticalchain project scheduling［J]．International Journal of ProjectManagement，2001,19（6)：363-369.  
[10］WATSONKJ，BLACKSTONEJH，GARDINERSC．Theevolution of a management philosophy：the theory of constraints[J]．Journal of Operations Management，2007，25（2）：387-402.  
[11]MAG，WANGA，LIN,et al.Improved critical chain pro-ject management framework for scheduling construction projects[J］．Journal of Construction Engineering & Management,2014，140（12）：04014055.  
[12］ROBERT C N. Project management in the fast lane[M].Taylor and Francis：CRC Press，2015.  
[13]GEEKIE A,STEYN H. Buffer sizing for the critical chain pro-ject management method［J].South African Journal of Indus-trial Engineering，2012，19（1）：73-88.  
[14］TUKEL OI，ROM WO，EKSIOGLU S D．An investigationof buffer sizing techniques in critical chain scheduling[J].European Journal of Operational Research，2006，172（2）：401-416.  
[15］VANHOUCKE M. Integrated project management sourcebook[M]．Switzerland：Springer International Publishing,2016  
[16]DE Y,LIU J,GUO C，et al.A new buffer seting methodbased on activity attributes in construction engineering［J].Applied Mechanics and Materials，2012（1801）：174-177.  
[17］ZHANG J，SONG X，DIAZ E．Buffer sizing of critical chainbased on attribute optimization ［J]． Concurrent Engineering,2014，22 (3):253-264.  
［18］王肖文，刘伊生，仇鹏．关键链法缓冲区设置及其监控方法研究［J]．建筑经济，2013（2)：42-45.  
[19］张静文，胡信布，王茉琴．关键链项目计划调度方法研究[J]．科技管理研究，2008（3)：284-287.  
[20］陈琼妮．基于关键链技术的项目进度管理的研究［D]·重庆：重庆大学,2011.  
［21］刘士新，宋健海，唐加福．基于关键链的资源受限项目调度新方法［J]．自动化学报，2006，32（1)：60-66.  
[22］周阳，丰景春．基于排队论的关键链缓冲区研究［J］．科技进步与对策，2008，25（2)：174-176.  
[23］王晶．关键链管理中关键链识别和缓冲区设置新方法研究[D］．北京：华北电力大学（北京)，2008.  
[24］周杰．基于关键链方法的智能变电站建设项目进度管理研究［D］．北京：华北电力大学（北京)，2014.  
[25］HAINES DJ.Practical reliability engineering-fifth edition［J].The Aeronautical Journal,2012,116（1179)：565-566.  
[26]HERROELEN W，LEUS R．On the merits and pitfalls of crit-ical chain scheduling[J]．Journal of Operations Manage-ment,2001,19（5）：559-577.PMT

收稿日期：2022-08-22

# 作者简介：

肖勇（1970—)，男，副教授，研究方向：电力企业人力资源管理、安全管理、工程项目管理等。管致乾（1994—)，男，研究方向：智能电网工程管理、安全管理。