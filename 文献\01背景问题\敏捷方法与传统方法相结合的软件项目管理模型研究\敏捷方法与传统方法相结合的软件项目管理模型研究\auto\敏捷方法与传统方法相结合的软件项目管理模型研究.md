# 敏捷方法与传统方法相结合的软件项目管理模型研究

王春瑾 1袁 黄淑君 2袁 鹿洵 3渊1. 四川九洲电器集团有限责任公司袁 四川 绵阳 621000曰2. 工业和信息化部电子第五研究所袁 广东 广州 511370曰3. 深圳赛宝工业技术研究院有限公司袁 广东 深圳 518055冤摘 要院 随着社会发展节奏加快袁 客户对于需求的变更越来越频繁地出现在软件开发项目中遥 传统的项目管理方法主要基于线性化开发 过于注重预测 计划和阶段性控制 因此渐渐不适应现代企业的开发节奏 传统软件项目管理注重计划和控制袁 重视详细的文档和规范袁 但是其刚性和缓慢的反应能力不适应快速变化的市场和需求遥 而敏捷方法注重灵活性和快速响应能力袁 重视人员和团队协作袁 但是其不完全规范化的过程和文档化可能会导致项目质量不稳定遥 因此袁 将敏捷和传统软件项目管理相结合袁 可以充分发挥两种方法的优势袁 弥补各自的不足袁 提高软件项目管理的效率和质量袁 增强团队的协作和响应能力遥

关键词院 信息系统曰 传统方法曰 敏捷开发曰 混合项目管理模型

中图分类号院 TP 315 文献标志码院 A 文章编号院 1672-5468 渊2024冤 02-0082-07  
doi:10.3969/j.issn.1672-5468.2024.02.014

# Research on Software Project Management Model Combining Agile Method and Traditional Method

WANG Chunjin1袁 HUANG Shujun2袁 LU Xun3

渊1. Sichuan Jiuzhou Electric Group Co.袁 Ltd.袁 Mianyang 621000袁 China曰 2. CEPREI袁 Guangzhou 511370袁 China曰 3. Shenzhen CEPREI Industry Technology Research Institute Co.袁 Ltd.袁 Shenzhen 518055袁 China冤

Abstract 院 As the pace of social development accelerates袁 changes in customer requirements appear more and more frequently in software development projects. The traditional project management method is mainly based on linear development袁 and pays too much attention to forecasting袁 planning and phased control袁 so it gradually does not adapt to the development rhythm of modern enterprises. Traditional software project management focuses on planning and control袁 as well as detailed documents and specifications袁 but its rigidity and slow response ability cannot adapt to the rapidly changing market and needs. The agile method focuses on flexibility and quick response capabilities袁 as well as personnel and team collaboration袁 but its incompletely standardized process and documentation may lead to unstable project quality. Therefore袁 agile and traditional software project management is combined袁 which can give full play to the advantages of the two methods袁 make up for their respective shortcomings袁 improve the efficiency and quality of software project management袁 and enhance the team爷s collaboration and responsiveness.

Keywords院 information system曰 traditional method曰 agile development曰 hybrid project manage鄄 ment model

# 引 言

随着网络应用和手机 APP 的迅速发展袁 企业必须根据客户的需求袁 不断地开发具有竞争力的系统产品袁 以保持公司的核心竞争力遥 为了适应日益增长的 越来越复杂的计算机应用的需要 软件开发者们正在寻求更高效和高质量的开发途径遥 在快速增长的同时袁 软件开发方法的发展却远远滞后于其重要性的提高遥 为了更好地适应市场需求袁 各大公司都在逐步学习 探索和尝试与自己相匹配的信息系统工程管理模型 [1]遥

目前的经济形势瞬息万变袁 企业要快速响应市场的变化 才有可能在激烈的市场竞争中占据优势袁 甚至不被淘汰遥 在不断上涨的成本和不断增加的竞争压力下袁 各个行业的公司都希望通过信息化技术来提升效率袁 降低成本遥 企业信息化程度是影响企业经营能力的一个重要因素 而信息化程度与企业信息系统管理能力密切相关遥 目前我国的信息化建设还存在着工程进度难以预测和风险控制等问题遥 实际情况是袁 企业的信息系统项目经常会超时尧超预算或不能满足客户的需要袁 或开发的系统功能已经与当前的业务要求不相适应遥 所以袁 要想有效地解决这些问题袁 就必须要有实际的解决办法遥

传统的工程项目管理方式强调计划与控制 但在需求快速变化袁 强调客户体验袁 注重创新的情况下袁 传统的工程管理方式已不适合遥 由于传统的方法侧重于流程与控制袁 而忽略了项目流程的变化遥如果需要重新设计袁 将极大地影响项目的进度和费用袁 从而提高项目的风险遥

自 20 世纪 90 年代兴起的敏捷开发模型为解决上述问题提供了一种行之有效的途径 在信息化建设中 灵活的应用可以提高信息化建设的成功率袁 这是一种相对于传统的瀑布式开发方法更具优越性的方法遥 但是袁 在敏捷开发过程中袁 存在着需求不稳定 团队要求高 风险控制不足和缺乏文档等问题 [2]遥

本文将敏捷与传统的项目管理模型有机地融合在一起 该模型能够更好地满足不断变化的市场和对电脑应用程序的需要遥 在当今的经济形势下袁 企业要想在市场中取得竞争优势袁 就必须对市场的变化做出快速的反应遥 但是袁 随着软件开发手段的不断发展 其重要性的提高越来越慢 传统的项目管理手段已不能适应这种要求遥 为了适应日益增长的尧 越来越复杂的计算机应用的要求袁 企业必须探索更有效尧 更高质量的开发方式遥

# 传统软件开发项目管理

# 1.1 传统软件开发项目管理的定义

软件开发项目管理是指对软件开发项目进行计划尧 组织尧 协调尧 指导和控制袁 以实现项目目标的过程遥 它涉及到项目的各个方面袁 包括需求分析尧设计尧 编码尧 测试尧 部署和维护等遥 软件开发项目管理旨在确保项目能够按时尧 按质且按量地完成袁同时尽可能地控制项目成本和风险遥

传统软件开发项目管理通常采用瀑布模型袁 也称为瀑布开发模型遥 瀑布模型是一种线性的软件开发方法袁 各阶段之间是按顺序进行的袁 每个阶段的输出成果是下一阶段的输入成果袁 没有返工的概念袁 非常适合需求明确尧 稳定尧 开发过程控制要求严格的软件项目 [3]袁 瀑布模型一般包括以下阶段遥

a冤 需求分析阶段院 确定用户需求和产品功能袁  
编写需求文档遥b冤 设计阶段院 依据需求文档袁 设计产品架构尧  
模块和详细设计文档遥c冤 编码阶段院 根据设计文档袁 进行编码和单  
元测试遥d冤 测试阶段院 对编码完成的软件进行测试袁 包  
括单元测试尧 集成测试尧 系统测试和验收测试等遥e冤 部署和维护阶段院 将测试通过的软件发布到  
生产环境中袁 以及做好软件的日常维护和升级工作遥在传统软件开发项目管理中袁 项目经理负责项

目计划和管理袁 负责制定项目计划和时间表袁 安排开发人员和资源袁 监控进度和成本袁 并协调各阶段的工作遥

此外袁 传统软件开发项目管理还会用到一些工具和技术袁 例如院 甘特图尧 计划评审技术 渊PERT院Program Evaluation and Review Technique冤尧 关键路径分析和工作分解结构 渊WBS院 Work BreakdownStructure冤 等遥 甘特图可以直观地表示项目计划和时间表袁 PERT 图可以帮助评估任务完成时间袁 关键路径分析可以帮助找出项目的关键任务和耗时任务袁 WBS 可以将整个项目分解成小的尧 可管理的任务遥

虽然传统软件开发项目管理在过去的很长一段时间内被广泛应用 但也有一些缺点 例如 瀑布模型通常需要在设计和编码之前对所有需求进行明确的定义袁 这对于需求变化频繁的项目来说袁 往往是困难的遥 此外袁 瀑布模型的阶段性和线性结构也容易导致过度控制和缺乏灵活性袁 而且难以在开发过程中处理新的需求和变化遥 因此袁 瀑布模型在处理大型尧 复杂项目时可能会导致时间延迟和预算超支遥

# 1.2 传统软件开发项目管理的优势和劣势

传统软件项目管理是一种基于计划尧 控制和阶段交付的项目管理方法袁 其主要优势和劣势如下所述 [4]遥

#

a冤 优势1冤 可以提前规划项目的进度尧 成本和质量要求袁 有利于实现项目目标遥2冤 采用阶段交付模式袁 可以在每个阶段结束时对项目进度和成果进行评估和控制遥3冤 建立明确的项目组织结构和角色职责袁 有利于协调团队内部的合作与协同遥4冤 可以对项目变更进行有效的管理和控制袁降低项目风险遥5冤 在一定程度上袁 传统软件项目管理可以实现可重复的生产流程袁 提高开发效率和质量遥b冤 劣势1冤 对需求变更的响应速度较慢袁 难以适应市场和客户需求的快速变化遥 如果出现变更袁 传统项目管理变更流程的复杂性会导致极大的资源和时间浪费袁 造成整个项目的延误遥

2冤 在实施过程中袁 可能会出现资源浪费和重复工作袁 降低了开发效率遥

3冤 项目交付周期较长袁 需要投入大量时间和  
资源袁 导致开发效率较低遥4冤 因为固定的计划和流程袁 难以适应复杂的尧  
变化多端的项目需求袁 可能导致项目进度延误和成  
果质量下降遥5冤 由于过多的规划和控制袁 传统项目管理可能  
缺乏创新性和灵活性袁 难以实现项目创新和升级遥

# 敏捷开发/敏捷项目管理

# 2.1 敏捷开发

敏捷开发 渊Agile Development冤 是一种以客户需求为导向 快速迭代的软件开发方法 它强调快速响应变化的需求尧 迭代开发尧 面向人员和团队协作等核心理念遥 敏捷开发注重快速地响应客户的需求袁 可以更加灵活地开发软件袁 减少浪费袁 更好地适应变化袁 提高软件开发的效率和质量遥 相比于传统的瀑布式开发模型袁 敏捷开发更注重团队合作尧用户体验和持续改进袁 更能适应快速变化的市场和技术环境 [5-6]遥

敏捷开发的核心价值观包括院 1冤 个体和交互胜过流程和工具曰 2冤 可运行的软件胜过详尽的文档曰 3冤 客户合作胜过合同谈判曰 4冤 响应变化胜过遵循计划 在敏捷开发中 开发人员通过多次快速迭代尧 测试和反馈的方式袁 不断优化产品袁 使其更符合客户需求和用户体验遥

目前敏捷开发的主流分类包括 Scrum尧 ExtremeProgramming 渊 XP冤 尧 Lean尧 Crystal 和 Feature -Driven Development 渊FDD冤遥 敏捷开发的特点包括迭代式开发尧 持续交付尧 团队协作尧 灵活性尧 快速反馈和可视化管理遥 不同的敏捷开发方法适用于不同的项目类型和团队规模 开发团队可以根据自身需求选择合适的敏捷开发方法遥

# 2.2 敏捷项目管理

敏捷开发和敏捷项目管理是紧密相关的 个概念袁 可以说敏捷项目管理是敏捷开发的衍生物袁 敏捷开发和敏捷项目管理是相互关联的袁 它们共同构成了敏捷软件开发的完整生态系统遥 在实践中袁 敏捷开发和敏捷项目管理的实践需要协同工作袁 以实现高效尧 高质量尧 透明和协同的软件开发流程 [3]遥

敏捷项目管理旨在提高项目的敏捷性和灵活性袁 适应需求变化和快速交付的要求遥 敏捷项目管理强调客户参与 团队协作 迭代交付和持续反馈等核心原则袁 通过这些实践来实现项目管理的敏捷化遥

在敏捷项目管理中袁 项目团队通常采用短周期尧 小规模的迭代开发方式袁 每个迭代通常持续数周至数月袁 每个迭代的结束都会交付一部分可用的软件功能遥 项目团队通过持续反馈来改善软件开发过程 与客户保持紧密沟通 及时响应变化的需求袁 并在整个项目周期中不断优化开发流程袁 提高交付质量遥 敏捷项目管理的核心实践包括 [6] 以下几个方面遥

a冤 迭代开发院 采用迭代式开发方式袁 将项目  
周期分解为多个迭代 每个迭代都包括需求分析  
设计尧 开发尧 测试和交付等阶段袁 每个迭代的结果  
都是一部分可用的软件功能遥b冤 自组织团队院 让团队自主决策袁 自组织协  
作袁 形成高效的工作模式和文化袁 提高团队成员的  
自我管理和自我激励能力遥c冤 快速反馈院 通过快速反馈机制袁 及时发现  
问题尧 优化流程尧 提高质量尧 满足客户需求袁 保证  
软件开发过程的透明度和可控性遥d冤 用户参与院 让客户作为项目的参与者袁 积  
极参与需求分析尧 测试和验收等环节袁 保证开发的  
软件能够满足客户的需求和期望遥e冤 持续集成院 采用自动化工具和技术袁 实现持  
续集成和持续交付袁 确保代码的可靠性和稳定性遥

# 2.3 敏捷项目管理的优势和劣势

敏捷项目管理是一种基于迭代和增量开发的项目管理方法袁 其主要优势和劣势如下所述 [7]遥

# a冤 优势

1冤 对需求变更的响应速度较快袁 可以更好地  
适应客户需求的变化和市场变化遥2冤 采用迭代开发模式袁 可以实现快速交付和  
持续交付袁 提高项目交付速度遥3冤 强调协作和团队精神袁 有利于提高团队协  
同工作效率和成果质量遥4冤 强调项目可见性袁 通过定期演示和检查袁  
可以实时评估项目进展和成果质量遥5冤 采用开放的沟通方式袁 允许团队成员进行  
自我管理和自我协调袁 鼓励创新和变革遥b冤 劣势1冤 对团队成员的技能和素质要求较高袁 需要

具备较好的协作尧 自我管理和创新能力遥

2冤 由于迭代和增量的特点袁 可能存在项目规划和设计的缺失或不完整袁 需要不断调整和完善遥3冤 强调快速交付和用户反馈袁 可能会牺牲一部分项目的整体质量和稳定性遥4冤 对变更的处理可能会造成不可避免的额外成本和时间袁 需要谨慎评估和控制遥5冤 由于敏捷项目管理注重灵活性和创新性袁可能存在一定的风险和不确定性袁 需要及时评估和控制遥

# 敏捷与传统项目管理方法的比较

如图 所示 敏捷开发和传统项目管理注重点不同袁 前者强调适应性和人性化袁 而后者则注重规划和流程遥 传统的软件开发要求对项目需求有清晰的认识袁 并进行复杂评估袁 这需要强大的技术和经验袁 同时袁 团队可能抵制新需求袁 增加计划尧 预算和技术风险袁 导致研发风险加大遥 此外袁 顾客只有在项目结束后才能看到产品袁 增加了风险遥 相比之下袁 敏捷架构更加灵活尧 稳定袁 能够提高项目的控制程度和开发团队的业绩袁 降低非生产性工作袁 提高产品品质袁 快速完成产品交付袁 让用户在每个周期末见到初步成果袁 获得最大利益遥 敏捷方法注重沟通和协作袁 其能够提高员工的创造力和积极性袁更迅速尧 更有效地对变更做出反应袁 降低开发风险袁 从而为客户提供更好的产品 [8]遥

![](images/9f3a7d4cf3ab9f5af5bf58c66514e2f52f989b74a97912ff753e083a266f9526.jpg)  
图 敏捷与传统项目管理理念的比较

敏捷方法在实践和原理上与传统方法有很大的不同遥 传统的项目管理必须遵循预先设计的程序袁其中包括详细的计划编制尧 系统需求分析尧 系统基本结构设计尧 程序编写和软件测试等遥 尽管这些阶段和层次可以控制项目袁 但如果项目一开始就存在不确定性因素 传统方法就不能满足 这样做不仅会增加风险和费用 还会增加项目管理人员的文件要求 [8]遥

相比之下袁 敏捷方法更注重需求的变化袁 采用迭代开发以实现适应性遥 敏捷方法通过直接沟通提高工作效率 从而快速响应变化 同时注重团队合作袁 通过良好的沟通适应各种变化的需求遥 此外袁敏捷方法能够及时更新文件袁 建立一个完善的知识系统遥 尽管敏捷方法不需要大量的文档袁 但它并没有完全放弃文档遥 相反袁 敏捷方法鼓励直接的团队交流袁 以便实现更好的项目管理和交付遥

# 敏捷/传统软件项目管理相结合的模型

敏捷方法和传统项目管理并不是互相排斥的两种方法袁 实际上袁 它们也有一些共同之处遥 通过比较图 2中传统和敏捷开发软件项目的过程袁 可以看出这两个过程都是按照 PMI 的 5 个阶段进行的袁 即院启动尧 规划尧 执行尧 监控和结束遥 然而袁 两者在项目管理的观念和价值观念上存在很大的差别 [8-9]遥

![](images/08b939ed80ce040481c41fccb41b198def232b4937881665bb68db09253533cd.jpg)  
图 2 传统软件项目管理和敏捷软件项目管理流程对比

敏捷开发注重适应性和人性化 但并不排斥传统项目管理遥 实际上袁 将敏捷管理理念融入传统项目管理中袁 二者可以相互补充袁 更好地适应工程需要袁 提高实用性和实效性遥 本文将敏捷理论与传统软件项目管理模型相结合 建立了一套适用于初次应用的软件项目管理模型遥 通过结合敏捷理念和传统管理模型袁 我们可以更好地管理项目袁 提高适应性和灵活性 同时也提高了效率和实际效果 如图

3 所示遥

![](images/66023669700932428e73a93d9900f90097220e936c92ae2c5ddff670ad1786f3.jpg)  
图 3 敏捷/传统软件项目管理相结合的混合模型

敏捷开发注重适应性和人性化袁 此模型将Scrum of Scrum 渊SoS冤 和敏捷 PMI 渊Agile PMI冤 两种模型相结合袁 以实现更灵活的项目管理遥 本系统采用传统项目管理的规划和实施方法 并遵循 精益开发冶 的理念袁 将敏捷的管理思想和实践应用于项目管理的各个环节袁 包括需求分析尧 开发尧 使用维护尧 团队管理尧 需求管理尧 进度管理和质量管理等遥 最终袁 该模型可以充分地利用敏捷和传统软件项目管理的优点袁 以达到最佳效果袁 满足客户的需求[8]遥

以下通过团队管理尧 需求管理尧 进度管理和质量管理 4 个项目管理的关键要素阐述基于该模型进行敏捷项目管理的具体方法遥

# 4.1 团队管理

在本敏捷和传统软件项目管理结合的模型中袁团队管理是一个非常重要的方面遥 以下是团队管理实践中的项目遥

# a冤 职责和角色

团队成员需要明确自己的角色和职责袁 以确保项目进展顺利遥 这包括定义团队成员的职责和角色袁 以及确保每个人都清楚自己的任务和贡献遥

# b冤 团队协作

团队成员需要相互协作袁 共同完成项目任务遥这意味着建立团队协作文化袁 鼓励成员分享知识和经验袁 解决问题袁 以及在需要的时候相互帮助遥

# c冤 沟通和反馈

团队成员需要保持开放的沟通和反馈机制袁 以确保项目进展和风险可以及时被发现和解决遥 这包括建立有效的沟通和协调机制袁 以及鼓励团队成员提供积极的反馈和建设性的批评遥

# d冤 自主性和创新

敏捷和传统软件项目管理结合的混合模型鼓励团队成员拥有自主性和创新精神袁 以便更好地解决问题并实现项目目标遥 这意味着团队成员需要有足

够的自主权和灵活性袁 以便在需要的时候采取适当的行动袁 并提出改进建议遥

# e冤 培训和提高技能

团队成员需要不断学习和提高技能袁 以适应项目需求的变化遥 这包括提供必要的培训和资源袁 以帮助团队成员不断发展自己的技能和知识袁 并探索新的工具和技术来提高工作效率和质量遥

这些团队管理的实践可以帮助团队更好地协作袁 提高工作效率和质量袁 同时也可以提高团队成员的工作满意度和自我发展遥

# 4.2 需求管理

在本敏捷和传统软件项目管理结合模型中袁 需求管理是一个非常关键的方面 以下是一些需求管理方面的实践 [10]遥

# a冤 优先级管理

需要明确和管理需求的优先级 以确保团队在开发过程中集中精力解决最重要和最紧急的需求遥这需要项目经理和客户共同确定需求的优先级袁 并随时进行评估和调整遥

# b冤 需求追踪

需要对需求进行跟踪袁 以确保团队了解需求的状态和进展情况遥 这包括建立跟踪系统袁 记录每个需求的状态 进度和问题 以及及时更新和沟通需求的变更和影响遥

# c冤 需求分析

需要对需求进行充分的分析袁 以确保团队了解客户的真正需求 并能够提出可行的解决方案 这需要建立一个完整的需求分析过程袁 包括需求调研尧 需求规划尧 需求评审和验收遥

# d冤 需求评审

需要进行需求评审 以确保团队能够准确理解需求袁 并根据客户的期望提供满足需求的解决方案遥 这需要建立一个评审过程袁 包括评审人员的确定尧 评审准则的定义和评审结果的反馈遥

# e冤 变更管理

需要对需求的变更进行管理袁 以确保团队能够及时应对需求变更 并确保项目进度不受影响 这需要建立一个变更管理过程袁 包括变更请求的收集尧 评估和批准等环节遥

这些需求管理方面的实践可以帮助团队更好地管理和满足客户的需求袁 提高项目的成功率和客户的满意度遥

# 4.3 进度管理

在本敏捷和传统软件项目管理相结合的模型中袁 进度管理是非常重要的一环遥 以下是一些进度管理方面的实践 [10]

# a冤 制定计划

需要制定一个明确的项目计划袁 包括每个任务的时间和资源估计袁 以及关键里程碑和项目交付时间遥这可以帮助团队更好地管理项目进度和资源分配遥

# b冤 持续跟踪

需要对项目进度进行持续跟踪和监控袁 确保项目在预算和时间范围内完成 可以使用甘特图 进度表等工具进行跟踪和监控遥

# c冤 及时调整

如果项目进度出现偏差袁 需要及时进行调整袁以便重新安排资源和任务袁 避免进一步延误遥 可以使用风险管理工具袁 预测潜在的问题并采取相应的措施遥

# d冤 持续集成

在敏捷开发中袁 持续集成是一个重要的实践遥团队需要在每个迭代期末对代码进行集成和测试袁以确保项目的稳定性和质量 持续集成也有助于减少重复工作和测试时间遥

# e冤 团队协作

团队成员之间需要密切协作 共同努力推进项目进度遥 这包括在每个迭代期末进行回顾和规划会议袁 以及及时沟通项目的进展和问题遥

这些进度管理的实践可以帮助团队更好地管理项目进度袁 确保项目在预算和时间范围内完成遥 同时袁 也可以提高团队协作和质量保障能力袁 使项目更加成功遥

# 4.4 质量管理

质量管理在本敏捷和传统软件项目管理结合的混合模型中同样是一个非常重要的实践遥 以下是一些质量管理方面的实践 [11]遥

# a冤 质量计划

需要制定一个明确的质量计划袁 明确质量标准和度量指标 并与团队成员共享和理解 质量计划需要包括各种测试和验证方法尧 质量度量指标和质量风险管理遥

# b冤 自动化测试

自动化测试可以大大地提高软件开发的效率和质量袁 减少人为误差和漏洞遥 团队需要使用各种自

动化测试工具进行单元测试 集成测试和 测试等测试遥

# c冤 代码评审

代码评审可以帮助团队成员发现并纠正潜在的缺陷和错误 确保代码的质量和稳定性 团队需要定期进行代码评审袁 记录问题并提出相应的解决方案遥

# d冤 预防性质量管理

预防性质量管理可以通过在开发过程中采取适当的措施来避免问题的发生遥 团队需要建立质量标准和过程规范袁 并在开发过程中使用这些标准和规范袁 以确保软件的质量和稳定性遥

# e冤 持续集成和交付

持续集成和交付是敏捷开发的重要实践袁 可以确保软件的稳定性和质量遥 团队需要在每个迭代期末对代码进行持续集成和测试 并自动化地构建和部署软件袁 以减少重复工作和人为误差遥

这些质量管理的实践可以帮助团队提高软件开发的效率和质量袁 同时也可以提高团队协作和质量保障能力袁 使项目更加成功遥

# 结束语

本文深入探讨了传统和敏捷管理的理论袁 并比较了它们的核心过程和实际应用遥 在此基础上袁 提出了一种基于敏捷技术的工程项目管理模型袁 该模型 将 Scrum of Scrums 渊 SoS冤 和 敏 捷 PMI 渊 AgilePMI冤 这两种混合模型应用于需求分析尧 设计尧 编码和测试 4个方面袁 充分发挥各自的优点袁 相互补充遥 该模型不仅能够控制工程进程袁 还能够灵活地应对变更尧 快速响应需求尧 多次发布尧 保证质量袁提高用户和团队的满意度 虽然在某些工程中采用灵活方法袁 但大多数我国公司仍然忽视了自身的特点袁 仍在模仿的过程中遥 本文通过实例说明袁 结合传统和敏捷的发展模型袁 也是一种有效的方式袁 其核心是针对不同企业的特征和需求袁 选择适合自己

的项目管理方式 [11]遥

# 参考文献院

[1] ERICKSON J袁 LYYTINEN K袁 SIAU K. Agile modeling袁agile software development袁 and extreme programming院the state of research [J] . Journal of Database Management渊JDM冤袁 2005袁 16 渊4冤院 88-100.  
[2] 安晓东. 敏捷开发模型的应用研究 [D] . 北京院 对外经济贸易大学袁 2019.  
[3] 蒋丹袁 刘永吉. 基于模型的敏捷软件架构设计方法 [J] .电子技术与软件工程袁 2019 渊4冤院 31-33.  
[4] STOICA M袁 GHILIC-MICU B袁 MIRCEA M袁 et al. Analyzing agile development-from waterfall style to scrumban[ J] .Informatica Economica袁 2016袁 20 渊4冤院 5-14.  
[5] TAM C袁 DA COSTA MOURA EJ袁 OLIVEIRA T袁 et al.The factors influencing the success of on -going agile software development projects [J] . International Journal of Pro-ject Management袁 2020袁 38 渊3冤院 165-176.  
[6] 刘谦. 敏捷方法在软件项目管理中的应用 [D] . 上海院上海交通大学袁 2011.  
[7] 闫帅袁 许鹏翔. 基于瀑布模型与敏捷开发相结合的项目管理方法探讨 [J] . 电子技术与软件工程袁 2013 渊18冤院 67.  
[8] ANWER F袁 AFTAB S袁 WAHEED U袁 et al. Agile softwaredevelopment models tdd袁 fdd袁 dsdm袁 and crystal methods院 a survey [J] . International Journal of MultidisciplinarySciences and Engineering袁 2017袁 8 渊2冤院 1-10.  
[9] 沈成莉. 敏捷项目管理在软件开发中的实践应用 [D] .上海院 复旦大学袁 2009.  
[10] OURIQUES RAB袁 WNUK K袁 GORSCHEK T袁 et al. Knowl鄄edge management strategies and processes in agile soft鄄ware development院 a systematic literature review [J] . In鄄ternational Journal of Software Engineering and KnowledgeEngineering袁 2019袁 29 渊3冤院 345-380.  
[ 11] DHIR S袁 KUMAR D袁 SINGH VB. Success and failurefactors that impact on project implementation using agilesoftware development methodology [ C] //Springer. SoftwareEngi neer ing院 Proceedings of CSI 2015. Singapore院Springer袁 2019院 647-654.